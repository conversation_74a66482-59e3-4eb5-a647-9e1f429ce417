#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
研究生复试系统 - 题库管理API
版权所有 © 2025 王文通
联系方式：<EMAIL>
"""

from flask import Blueprint, request, jsonify
import sqlite3
import json
import os

# 创建蓝图
questions_bp = Blueprint('questions', __name__, url_prefix='/api/questions')

DB_PATH = 'assets/data/interview_system.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

@questions_bp.route('/translation', methods=['GET'])
def get_translation_questions():
    """获取翻译题库"""
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    search = request.args.get('search', '')
    
    offset = (page - 1) * limit
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 构建查询
    query = "SELECT * FROM translation_questions"
    count_query = "SELECT COUNT(*) as total FROM translation_questions"
    params = []
    
    if search:
        query += " WHERE question_data LIKE ?"
        count_query += " WHERE question_data LIKE ?"
        params.append(f'%{search}%')
    
    query += " ORDER BY question_index LIMIT ? OFFSET ?"
    params.extend([limit, offset])
    
    # 执行查询
    cursor.execute(query, params)
    questions = [dict(row) for row in cursor.fetchall()]
    
    # 获取总数
    cursor.execute(count_query, params[:-2] if params else [])
    total = cursor.fetchone()['total']
    
    conn.close()
    
    return jsonify({
        'questions': questions,
        'total': total,
        'page': page,
        'limit': limit
    })

@questions_bp.route('/professional', methods=['GET'])
def get_professional_questions():
    """获取专业题库"""
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    search = request.args.get('search', '')
    
    offset = (page - 1) * limit
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 构建查询
    query = "SELECT * FROM professional_questions"
    count_query = "SELECT COUNT(*) as total FROM professional_questions"
    params = []
    
    if search:
        query += " WHERE question_data LIKE ?"
        count_query += " WHERE question_data LIKE ?"
        params.append(f'%{search}%')
    
    query += " ORDER BY question_index LIMIT ? OFFSET ?"
    params.extend([limit, offset])
    
    # 执行查询
    cursor.execute(query, params)
    questions = [dict(row) for row in cursor.fetchall()]
    
    # 获取总数
    cursor.execute(count_query, params[:-2] if params else [])
    total = cursor.fetchone()['total']
    
    conn.close()
    
    return jsonify({
        'questions': questions,
        'total': total,
        'page': page,
        'limit': limit
    })

@questions_bp.route('/translation', methods=['POST'])
def add_translation_question():
    """添加翻译题目"""
    data = request.json
    
    # 验证数据
    if 'question_data' not in data:
        return jsonify({'error': 'Missing question_data'}), 400
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 获取下一个索引
        cursor.execute("SELECT MAX(question_index) as max_index FROM translation_questions")
        result = cursor.fetchone()
        next_index = (result['max_index'] + 1) if result['max_index'] is not None else 0
        
        # 如果用户提供了特定索引
        if 'question_index' in data and data['question_index'] != -1:
            # 检查索引是否已存在
            cursor.execute("SELECT id FROM translation_questions WHERE question_index = ?", 
                          (data['question_index'],))
            if cursor.fetchone():
                return jsonify({'error': 'Question index already exists'}), 400
            next_index = data['question_index']
        
        # 插入数据
        cursor.execute('''
            INSERT INTO translation_questions (question_index, question_data)
            VALUES (?, ?)
        ''', (next_index, data['question_data']))
        
        conn.commit()
        
        # 获取新插入的ID
        new_id = cursor.lastrowid
        
        # 返回新创建的题目
        cursor.execute("SELECT * FROM translation_questions WHERE id = ?", (new_id,))
        question = dict(cursor.fetchone())
        
        return jsonify(question), 201
    
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/professional', methods=['POST'])
def add_professional_question():
    """添加专业题目"""
    data = request.json
    
    # 验证数据
    if 'question_data' not in data:
        return jsonify({'error': 'Missing question_data'}), 400
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 获取下一个索引
        cursor.execute("SELECT MAX(question_index) as max_index FROM professional_questions")
        result = cursor.fetchone()
        next_index = (result['max_index'] + 1) if result['max_index'] is not None else 0
        
        # 如果用户提供了特定索引
        if 'question_index' in data and data['question_index'] != -1:
            # 检查索引是否已存在
            cursor.execute("SELECT id FROM professional_questions WHERE question_index = ?", 
                          (data['question_index'],))
            if cursor.fetchone():
                return jsonify({'error': 'Question index already exists'}), 400
            next_index = data['question_index']
        
        # 插入数据
        cursor.execute('''
            INSERT INTO professional_questions (question_index, question_data)
            VALUES (?, ?)
        ''', (next_index, data['question_data']))
        
        conn.commit()
        
        # 获取新插入的ID
        new_id = cursor.lastrowid
        
        # 返回新创建的题目
        cursor.execute("SELECT * FROM professional_questions WHERE id = ?", (new_id,))
        question = dict(cursor.fetchone())
        
        return jsonify(question), 201
    
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/translation/<int:question_id>', methods=['PUT'])
def update_translation_question(question_id):
    """更新翻译题目"""
    data = request.json
    
    # 验证数据
    if 'question_data' not in data:
        return jsonify({'error': 'Missing question_data'}), 400
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 检查题目是否存在
        cursor.execute("SELECT * FROM translation_questions WHERE id = ?", (question_id,))
        question = cursor.fetchone()
        
        if not question:
            return jsonify({'error': 'Question not found'}), 404
        
        # 检查表中是否存在tags列
        cursor.execute("PRAGMA table_info(translation_questions)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # 如果没有tags列，添加该列
        if 'tags' not in column_names:
            cursor.execute("ALTER TABLE translation_questions ADD COLUMN tags TEXT DEFAULT '[]'")
            print("已添加tags列到translation_questions表")
        
        # 如果没有difficulty列，添加该列
        if 'difficulty' not in column_names:
            cursor.execute("ALTER TABLE translation_questions ADD COLUMN difficulty TEXT DEFAULT 'medium'")
            print("已添加difficulty列到translation_questions表")
        
        # 如果要修改索引，检查新索引是否可用
        if 'question_index' in data and data['question_index'] != -1 and data['question_index'] != question['question_index']:
            cursor.execute("SELECT id FROM translation_questions WHERE question_index = ? AND id != ?", 
                          (data['question_index'], question_id))
            if cursor.fetchone():
                return jsonify({'error': 'Question index already exists'}), 400
            
            update_index = data['question_index']
        else:
            update_index = question['question_index']
        
        # 处理tags
        tags = data.get('tags', [])
        if isinstance(tags, list):
            tags_json = json.dumps(tags)
        else:
            tags_json = '[]'
        
        # 处理difficulty
        difficulty = data.get('difficulty', 'medium')
        
        # 更新数据
        cursor.execute('''
            UPDATE translation_questions
            SET question_index = ?, question_data = ?, tags = ?, difficulty = ?
            WHERE id = ?
        ''', (update_index, data['question_data'], tags_json, difficulty, question_id))
        
        conn.commit()
        
        # 返回更新后的题目
        cursor.execute("SELECT * FROM translation_questions WHERE id = ?", (question_id,))
        updated_question = dict(cursor.fetchone())
        
        # 解析question_data和tags为JSON
        try:
            updated_question['question_data'] = json.loads(updated_question['question_data'])
        except:
            updated_question['question_data'] = [['txt', '数据格式错误']]
        
        try:
            if 'tags' in updated_question and updated_question['tags']:
                updated_question['tags'] = json.loads(updated_question['tags'])
            else:
                updated_question['tags'] = []
        except:
            updated_question['tags'] = []
        
        return jsonify(updated_question)
    
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/professional/<int:question_id>', methods=['PUT'])
def update_professional_question(question_id):
    """更新专业题目"""
    data = request.json
    
    # 验证数据
    if 'question_data' not in data:
        return jsonify({'error': 'Missing question_data'}), 400
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 检查题目是否存在
        cursor.execute("SELECT * FROM professional_questions WHERE id = ?", (question_id,))
        question = cursor.fetchone()
        
        if not question:
            return jsonify({'error': 'Question not found'}), 404
        
        # 检查表中是否存在tags列
        cursor.execute("PRAGMA table_info(professional_questions)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # 如果没有tags列，添加该列
        if 'tags' not in column_names:
            cursor.execute("ALTER TABLE professional_questions ADD COLUMN tags TEXT DEFAULT '[]'")
            print("已添加tags列到professional_questions表")
        
        # 如果没有difficulty列，添加该列
        if 'difficulty' not in column_names:
            cursor.execute("ALTER TABLE professional_questions ADD COLUMN difficulty TEXT DEFAULT 'medium'")
            print("已添加difficulty列到professional_questions表")
        
        # 如果要修改索引，检查新索引是否可用
        if 'question_index' in data and data['question_index'] != -1 and data['question_index'] != question['question_index']:
            cursor.execute("SELECT id FROM professional_questions WHERE question_index = ? AND id != ?", 
                          (data['question_index'], question_id))
            if cursor.fetchone():
                return jsonify({'error': 'Question index already exists'}), 400
            
            update_index = data['question_index']
        else:
            update_index = question['question_index']
        
        # 处理tags
        tags = data.get('tags', [])
        if isinstance(tags, list):
            tags_json = json.dumps(tags)
        else:
            tags_json = '[]'
        
        # 处理difficulty
        difficulty = data.get('difficulty', 'medium')
        
        # 更新数据
        cursor.execute('''
            UPDATE professional_questions
            SET question_index = ?, question_data = ?, tags = ?, difficulty = ?
            WHERE id = ?
        ''', (update_index, data['question_data'], tags_json, difficulty, question_id))
        
        conn.commit()
        
        # 返回更新后的题目
        cursor.execute("SELECT * FROM professional_questions WHERE id = ?", (question_id,))
        updated_question = dict(cursor.fetchone())
        
        # 解析question_data和tags为JSON
        try:
            updated_question['question_data'] = json.loads(updated_question['question_data'])
        except:
            updated_question['question_data'] = [['txt', '数据格式错误']]
        
        try:
            if 'tags' in updated_question and updated_question['tags']:
                updated_question['tags'] = json.loads(updated_question['tags'])
            else:
                updated_question['tags'] = []
        except:
            updated_question['tags'] = []
        
        return jsonify(updated_question)
    
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/translation/<int:question_id>', methods=['DELETE'])
def delete_translation_question(question_id):
    """删除翻译题目"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 检查题目是否存在
        cursor.execute("SELECT * FROM translation_questions WHERE id = ?", (question_id,))
        question = cursor.fetchone()
        
        if not question:
            return jsonify({'error': 'Question not found'}), 404
        
        # 删除数据
        cursor.execute("DELETE FROM translation_questions WHERE id = ?", (question_id,))
        conn.commit()
        
        return jsonify({'success': True, 'message': 'Question deleted'})
    
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/professional/<int:question_id>', methods=['DELETE'])
def delete_professional_question(question_id):
    """删除专业题目"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 检查题目是否存在
        cursor.execute("SELECT * FROM professional_questions WHERE id = ?", (question_id,))
        question = cursor.fetchone()
        
        if not question:
            return jsonify({'error': 'Question not found'}), 404
        
        # 删除数据
        cursor.execute("DELETE FROM professional_questions WHERE id = ?", (question_id,))
        conn.commit()
        
        return jsonify({'success': True, 'message': 'Question deleted'})
    
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/translation/export', methods=['GET'])
def export_translation_questions():
    """导出翻译题库"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 获取所有题目
        cursor.execute("SELECT * FROM translation_questions ORDER BY question_index")
        questions = []
        
        for row in cursor.fetchall():
            question_data = json.loads(row['question_data'])
            questions.append({
                'id': row['id'],
                'question_index': row['question_index'],
                'question_data': question_data
            })
        
        return jsonify(questions)
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/professional/export', methods=['GET'])
def export_professional_questions():
    """导出专业题库"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 获取所有题目
        cursor.execute("SELECT * FROM professional_questions ORDER BY question_index")
        questions = []
        
        for row in cursor.fetchall():
            question_data = json.loads(row['question_data'])
            questions.append({
                'id': row['id'],
                'question_index': row['question_index'],
                'question_data': question_data
            })
        
        return jsonify(questions)
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/translation/import', methods=['POST'])
def import_translation_questions():
    """导入翻译题库"""
    data = request.json
    
    if 'questions' not in data or not isinstance(data['questions'], list):
        return jsonify({'error': 'Invalid data format'}), 400
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        imported_count = 0
        
        for question in data['questions']:
            # 验证数据格式
            if 'question_data' not in question and 'question_index' not in question:
                # 尝试兼容旧格式
                if isinstance(question, dict) and 'id' in question and 'question_index' in question and 'question_data' in question:
                    # 使用新导出格式
                    question_data = question['question_data']
                    question_index = question['question_index']
                else:
                    continue
            else:
                question_data = question['question_data']
                question_index = question.get('question_index', -1)
            
            # 如果question_data已经是列表，转换为JSON
            if isinstance(question_data, list):
                question_data_json = json.dumps(question_data)
            else:
                question_data_json = question_data
                
            # 检查索引是否存在
            if question_index != -1:
                cursor.execute("SELECT id FROM translation_questions WHERE question_index = ?", (question_index,))
                if cursor.fetchone():
                    # 如果索引已存在，生成新索引
                    cursor.execute("SELECT MAX(question_index) as max_index FROM translation_questions")
                    result = cursor.fetchone()
                    question_index = (result['max_index'] + 1) if result['max_index'] is not None else 0
            else:
                # 生成新索引
                cursor.execute("SELECT MAX(question_index) as max_index FROM translation_questions")
                result = cursor.fetchone()
                question_index = (result['max_index'] + 1) if result['max_index'] is not None else 0
            
            # 插入数据
            cursor.execute('''
                INSERT INTO translation_questions (question_index, question_data)
                VALUES (?, ?)
            ''', (question_index, question_data_json))
            
            imported_count += 1
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': f'Successfully imported {imported_count} questions',
            'imported': imported_count
        })
    
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/professional/import', methods=['POST'])
def import_professional_questions():
    """导入专业题库"""
    data = request.json
    
    if 'questions' not in data or not isinstance(data['questions'], list):
        return jsonify({'error': 'Invalid data format'}), 400
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        imported_count = 0
        
        for question in data['questions']:
            # 验证数据格式
            if 'question_data' not in question and 'question_index' not in question:
                # 尝试兼容旧格式
                if isinstance(question, dict) and 'id' in question and 'question_index' in question and 'question_data' in question:
                    # 使用新导出格式
                    question_data = question['question_data']
                    question_index = question['question_index']
                else:
                    continue
            else:
                question_data = question['question_data']
                question_index = question.get('question_index', -1)
            
            # 如果question_data已经是列表，转换为JSON
            if isinstance(question_data, list):
                question_data_json = json.dumps(question_data)
            else:
                question_data_json = question_data
                
            # 检查索引是否存在
            if question_index != -1:
                cursor.execute("SELECT id FROM professional_questions WHERE question_index = ?", (question_index,))
                if cursor.fetchone():
                    # 如果索引已存在，生成新索引
                    cursor.execute("SELECT MAX(question_index) as max_index FROM professional_questions")
                    result = cursor.fetchone()
                    question_index = (result['max_index'] + 1) if result['max_index'] is not None else 0
            else:
                # 生成新索引
                cursor.execute("SELECT MAX(question_index) as max_index FROM professional_questions")
                result = cursor.fetchone()
                question_index = (result['max_index'] + 1) if result['max_index'] is not None else 0
            
            # 插入数据
            cursor.execute('''
                INSERT INTO professional_questions (question_index, question_data)
                VALUES (?, ?)
            ''', (question_index, question_data_json))
            
            imported_count += 1
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': f'Successfully imported {imported_count} questions',
            'imported': imported_count
        })
    
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/translation/available', methods=['GET'])
def get_available_translation_questions():
    """获取可用的翻译题目（未被抽取过的）"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 获取已使用的题目ID
        cursor.execute('''
            SELECT DISTINCT question_id 
            FROM student_questions 
            WHERE question_type = 'translation'
        ''')
        used_ids = [row['question_id'] for row in cursor.fetchall()]
        
        # 获取可用题目
        if used_ids:
            cursor.execute('''
                SELECT * FROM translation_questions 
                WHERE id NOT IN ({})
                ORDER BY question_index
            '''.format(','.join(['?'] * len(used_ids))), used_ids)
        else:
            cursor.execute("SELECT * FROM translation_questions ORDER BY question_index")
        
        questions = []
        for row in cursor.fetchall():
            questions.append({
                'id': row['id'],
                'index': row['question_index'],
                'question': json.loads(row['question_data'])
            })
        
        return jsonify(questions)
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/professional/available', methods=['GET'])
def get_available_professional_questions():
    """获取可用的专业题目（未被抽取过的）"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 获取已使用的题目ID
        cursor.execute('''
            SELECT DISTINCT question_id 
            FROM student_questions 
            WHERE question_type = 'professional'
        ''')
        used_ids = [row['question_id'] for row in cursor.fetchall()]
        
        # 获取可用题目
        if used_ids:
            cursor.execute('''
                SELECT * FROM professional_questions 
                WHERE id NOT IN ({})
                ORDER BY question_index
            '''.format(','.join(['?'] * len(used_ids))), used_ids)
        else:
            cursor.execute("SELECT * FROM professional_questions ORDER BY question_index")
        
        questions = []
        for row in cursor.fetchall():
            questions.append({
                'id': row['id'],
                'index': row['question_index'],
                'question': json.loads(row['question_data'])
            })
        
        return jsonify(questions)
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

# 导出原始题库数据
@questions_bp.route('/init', methods=['GET'])
def initialize_questions():
    """从专用js文件初始化题库"""
    try:
        # 检查是否已经初始化
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) as count FROM translation_questions")
        if cursor.fetchone()['count'] > 0:
            conn.close()
            return jsonify({'message': 'Questions already initialized'}), 200
        
        # 从单独的js文件中提取题库数据
        try:
            translations = []
            professionals = []
            
            # 读取 translations.js
            with open('src/translations.js', 'r', encoding='utf-8') as file:
                translations_content = file.read()
            
            # 读取 professionals.js
            with open('src/professionals.js', 'r', encoding='utf-8') as file:
                professionals_content = file.read()
            
            # 提取translations数组
            import re
            translations_match = re.search(r'const\s+translations\s*=\s*(\[.*?\]);', translations_content, re.DOTALL)
            professionals_match = re.search(r'const\s+professionals\s*=\s*(\[.*?\]);', professionals_content, re.DOTALL)
            
            if not translations_match:
                raise Exception("Cannot find translations array in translations.js")
            
            if not professionals_match:
                raise Exception("Cannot find professionals array in professionals.js")
            
            # 使用json.loads解析数据（更安全的方法）
            import json
            # 预处理字符串使其符合JSON格式
            json_str = translations_match.group(1).replace("'", '"')
            translations = json.loads(json_str)
            # 预处理字符串使其符合JSON格式
            json_str = professionals_match.group(1).replace("'", '"')
            professionals = json.loads(json_str)
            
            # 保存到数据库
            for i, question in enumerate(translations):
                cursor.execute('''
                    INSERT INTO translation_questions (question_index, question_data)
                    VALUES (?, ?)
                ''', (i, json.dumps(question)))
            
            for i, question in enumerate(professionals):
                cursor.execute('''
                    INSERT INTO professional_questions (question_index, question_data)
                    VALUES (?, ?)
                ''', (i, json.dumps(question)))
            
            conn.commit()
            
            return jsonify({
                'success': True,
                'message': f'Successfully initialized {len(translations)} translation questions and {len(professionals)} professional questions'
            })
            
        except Exception as e:
            return jsonify({'error': f'Failed to initialize questions: {str(e)}'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/translation/<int:question_id>', methods=['GET'])
def get_translation_question(question_id):
    """获取单个翻译题目"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 查询题目
        cursor.execute("SELECT * FROM translation_questions WHERE id = ?", (question_id,))
        question = cursor.fetchone()
        
        if not question:
            return jsonify({'error': 'Question not found'}), 404
        
        # 解析题目数据
        try:
            question_data = json.loads(question['question_data'])
        except:
            question_data = [['txt', '数据格式错误']]
        
        # 返回结果
        return jsonify({
            'id': question['id'],
            'question_index': question['question_index'],
            'question_data': question_data,
            'tags': question.get('tags', []),
            'difficulty': question.get('difficulty', 'medium')
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/professional/<int:question_id>', methods=['GET'])
def get_professional_question(question_id):
    """获取单个专业题目"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 查询题目
        cursor.execute("SELECT * FROM professional_questions WHERE id = ?", (question_id,))
        question = cursor.fetchone()
        
        if not question:
            return jsonify({'error': 'Question not found'}), 404
        
        # 解析题目数据
        try:
            question_data = json.loads(question['question_data'])
        except:
            question_data = [['txt', '数据格式错误']]
        
        # 返回结果
        return jsonify({
            'id': question['id'],
            'question_index': question['question_index'],
            'question_data': question_data,
            'tags': question.get('tags', []),
            'difficulty': question.get('difficulty', 'medium')
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
    finally:
        conn.close()

@questions_bp.route('/tags/translation', methods=['GET'])
def get_translation_tags():
    """获取所有翻译题库的标签"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 检查表中是否存在tags列
        cursor.execute("PRAGMA table_info(translation_questions)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # 如果表中没有tags列，返回空列表
        if 'tags' not in column_names:
            return jsonify({
                'success': True,
                'tags': []
            })
        
        # 获取所有题目的标签
        cursor.execute("SELECT tags FROM translation_questions WHERE tags IS NOT NULL AND tags != '[]'")
        result = cursor.fetchall()
        
        # 收集所有标签
        all_tags = set()
        for row in result:
            tags = json.loads(row['tags']) if row['tags'] else []
            # 确保tags是列表
            if isinstance(tags, list):
                for tag in tags:
                    # URL解码标签
                    try:
                        from urllib.parse import unquote
                        decoded_tag = unquote(tag)
                        all_tags.add(decoded_tag)
                    except:
                        all_tags.add(tag)
            # 如果tags是字符串，可能是单个标签
            elif isinstance(tags, str):
                try:
                    from urllib.parse import unquote
                    decoded_tag = unquote(tags)
                    all_tags.add(decoded_tag)
                except:
                    all_tags.add(tags)
        
        # 如果没有找到标签，返回默认值
        if not all_tags:
            all_tags = ['英译汉', '汉译英', '专业术语', '长句翻译', '文献摘要']
        
        return jsonify({
            'success': True,
            'tags': list(all_tags)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'tags': []
        }), 500
    
    finally:
        conn.close()

@questions_bp.route('/tags/professional', methods=['GET'])
def get_professional_tags():
    """获取所有专业题库的标签"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 检查表中是否存在tags列
        cursor.execute("PRAGMA table_info(professional_questions)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # 如果表中没有tags列，返回空列表
        if 'tags' not in column_names:
            return jsonify({
                'success': True,
                'tags': []
            })
        
        # 获取所有题目的标签
        cursor.execute("SELECT tags FROM professional_questions WHERE tags IS NOT NULL AND tags != '[]'")
        result = cursor.fetchall()
        
        # 收集所有标签
        all_tags = set()
        for row in result:
            tags = json.loads(row['tags']) if row['tags'] else []
            # 确保tags是列表
            if isinstance(tags, list):
                for tag in tags:
                    # URL解码标签
                    try:
                        from urllib.parse import unquote
                        decoded_tag = unquote(tag)
                        all_tags.add(decoded_tag)
                    except:
                        all_tags.add(tag)
            # 如果tags是字符串，可能是单个标签
            elif isinstance(tags, str):
                try:
                    from urllib.parse import unquote
                    decoded_tag = unquote(tags)
                    all_tags.add(decoded_tag)
                except:
                    all_tags.add(tags)
        
        # 如果没有找到标签，返回默认值
        if not all_tags:
            all_tags = ['数学', '物理', '计算机', '经济学', '管理学', '机械', '电子', '通信']
        
        return jsonify({
            'success': True,
            'tags': list(all_tags)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'tags': []
        }), 500
    
    finally:
        conn.close() 