/**
 * ConfigManager - 配置管理器
 * 负责管理应用的所有配置选项，包括默认值、用户设置、环境配置等
 */

class ConfigManager {
    constructor(initialConfig = {}) {
        this.config = new Map();
        this.defaultConfig = this.getDefaultConfig();
        this.userConfig = new Map();
        this.environmentConfig = new Map();
        this.isInitialized = false;
        
        // 配置变更监听器
        this.listeners = new Map();
        
        // 合并初始配置
        this.mergeConfig(initialConfig);
    }

    /**
     * 初始化配置管理器
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }

        try {
            // 1. 加载环境配置
            await this.loadEnvironmentConfig();
            
            // 2. 加载用户配置
            await this.loadUserConfig();
            
            // 3. 合并所有配置
            this.mergeAllConfigs();
            
            this.isInitialized = true;
            console.log('✅ ConfigManager 初始化完成');
            
        } catch (error) {
            console.error('❌ ConfigManager 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            // API配置
            api: {
                baseUrl: '/api',
                timeout: 30000,
                retryAttempts: 3,
                retryDelay: 1000
            },
            
            // 应用配置
            app: {
                name: '研究生复试系统',
                version: '2.0.0',
                debug: false,
                language: 'zh-CN',
                theme: 'light'
            },
            
            // 界面配置
            ui: {
                fontSize: 'medium',
                showAnimations: true,
                compactMode: false,
                autoSave: true,
                autoSaveInterval: 30000
            },
            
            // 考试配置
            exam: {
                autoStartTimer: false,
                showQuestionGrid: true,
                allowStepBack: true,
                confirmBeforeNext: false,
                maxStudentNumber: 999
            },
            
            // 时间配置
            time: {
                chineseTime: 2,
                englishTime: 2,
                translationTime: 5,
                professionalTime: 10,
                comprehensiveTime: 8
            },
            
            // 数据配置
            data: {
                autoBackup: true,
                backupInterval: 24,
                maxBackups: 10,
                exportFormat: 'json'
            },
            
            // 性能配置
            performance: {
                enableMonitoring: true,
                maxEventHistory: 1000,
                debounceDelay: 300,
                throttleDelay: 100
            },
            
            // 安全配置
            security: {
                enableCSRF: true,
                sessionTimeout: 3600000,
                maxLoginAttempts: 5
            },
            
            // 日志配置
            logging: {
                level: 'info',
                enableConsole: true,
                enableStorage: true,
                maxLogEntries: 1000
            }
        };
    }

    /**
     * 加载环境配置
     */
    async loadEnvironmentConfig() {
        try {
            // 检测运行环境
            const isDevelopment = window.location.hostname === 'localhost' || 
                                window.location.hostname === '127.0.0.1';
            
            const environmentConfig = {
                app: {
                    debug: isDevelopment,
                    environment: isDevelopment ? 'development' : 'production'
                },
                api: {
                    baseUrl: isDevelopment ? '/api' : '/api'
                },
                logging: {
                    level: isDevelopment ? 'debug' : 'info',
                    enableConsole: isDevelopment
                }
            };
            
            this.environmentConfig = new Map(Object.entries(environmentConfig));
            
        } catch (error) {
            console.warn('⚠️ 加载环境配置失败:', error);
        }
    }

    /**
     * 加载用户配置
     */
    async loadUserConfig() {
        try {
            // 从localStorage加载用户配置
            const savedConfig = localStorage.getItem('interview_system_config');
            if (savedConfig) {
                const userConfig = JSON.parse(savedConfig);
                this.userConfig = new Map(Object.entries(userConfig));
            }
            
            // 从服务器加载用户设置
            try {
                const response = await fetch('/api/settings');
                if (response.ok) {
                    const serverConfig = await response.json();
                    if (serverConfig.success && serverConfig.data) {
                        this.mergeServerConfig(serverConfig.data);
                    }
                }
            } catch (error) {
                console.warn('⚠️ 从服务器加载配置失败:', error);
            }
            
        } catch (error) {
            console.warn('⚠️ 加载用户配置失败:', error);
        }
    }

    /**
     * 合并服务器配置
     */
    mergeServerConfig(serverConfig) {
        // 将服务器配置转换为本地配置格式
        const configMapping = {
            'timeSettings': 'time',
            'systemSettings': 'app',
            'uiSettings': 'ui',
            'examSettings': 'exam',
            'dataSettings': 'data'
        };
        
        for (const [serverKey, localKey] of Object.entries(configMapping)) {
            if (serverConfig[serverKey]) {
                this.userConfig.set(localKey, serverConfig[serverKey]);
            }
        }
    }

    /**
     * 合并所有配置
     */
    mergeAllConfigs() {
        // 清空当前配置
        this.config.clear();
        
        // 按优先级合并：默认配置 < 环境配置 < 用户配置
        this.mergeConfig(this.defaultConfig);
        
        for (const [key, value] of this.environmentConfig) {
            this.mergeConfigSection(key, value);
        }
        
        for (const [key, value] of this.userConfig) {
            this.mergeConfigSection(key, value);
        }
    }

    /**
     * 合并配置对象
     */
    mergeConfig(configObj) {
        for (const [key, value] of Object.entries(configObj)) {
            this.config.set(key, this.deepClone(value));
        }
    }

    /**
     * 合并配置节
     */
    mergeConfigSection(sectionKey, sectionValue) {
        const existingSection = this.config.get(sectionKey) || {};
        const mergedSection = { ...existingSection, ...sectionValue };
        this.config.set(sectionKey, mergedSection);
    }

    /**
     * 获取配置值
     * @param {string} key - 配置键，支持点号分隔的路径
     * @param {*} defaultValue - 默认值
     * @returns {*} 配置值
     */
    get(key, defaultValue = null) {
        const keys = key.split('.');
        let value = this.config;
        
        for (const k of keys) {
            if (value instanceof Map) {
                value = value.get(k);
            } else if (value && typeof value === 'object') {
                value = value[k];
            } else {
                return defaultValue;
            }
            
            if (value === undefined) {
                return defaultValue;
            }
        }
        
        return value !== undefined ? value : defaultValue;
    }

    /**
     * 设置配置值
     * @param {string} key - 配置键
     * @param {*} value - 配置值
     * @param {boolean} persist - 是否持久化
     */
    set(key, value, persist = true) {
        const keys = key.split('.');
        const lastKey = keys.pop();
        
        let target = this.config;
        for (const k of keys) {
            if (!target.has(k)) {
                target.set(k, {});
            }
            target = target.get(k);
        }
        
        const oldValue = target[lastKey];
        target[lastKey] = value;
        
        // 触发变更事件
        this.notifyConfigChange(key, value, oldValue);
        
        // 持久化到用户配置
        if (persist) {
            this.setUserConfig(key, value);
            this.saveUserConfig();
        }
    }

    /**
     * 设置用户配置
     */
    setUserConfig(key, value) {
        const keys = key.split('.');
        const sectionKey = keys[0];
        
        if (!this.userConfig.has(sectionKey)) {
            this.userConfig.set(sectionKey, {});
        }
        
        const section = this.userConfig.get(sectionKey);
        if (keys.length === 1) {
            this.userConfig.set(sectionKey, value);
        } else {
            const subKey = keys.slice(1).join('.');
            this.setNestedValue(section, subKey, value);
        }
    }

    /**
     * 设置嵌套值
     */
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        
        let target = obj;
        for (const key of keys) {
            if (!target[key]) {
                target[key] = {};
            }
            target = target[key];
        }
        
        target[lastKey] = value;
    }

    /**
     * 保存用户配置
     */
    async saveUserConfig() {
        try {
            // 保存到localStorage
            const configObj = Object.fromEntries(this.userConfig);
            localStorage.setItem('interview_system_config', JSON.stringify(configObj));
            
            // 保存到服务器
            try {
                const serverConfig = this.convertToServerConfig(configObj);
                const response = await fetch('/api/settings', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(serverConfig)
                });
                
                if (!response.ok) {
                    console.warn('⚠️ 保存配置到服务器失败');
                }
            } catch (error) {
                console.warn('⚠️ 保存配置到服务器失败:', error);
            }
            
        } catch (error) {
            console.error('❌ 保存用户配置失败:', error);
        }
    }

    /**
     * 转换为服务器配置格式
     */
    convertToServerConfig(configObj) {
        const serverConfig = {};
        
        const configMapping = {
            'time': 'timeSettings',
            'app': 'systemSettings',
            'ui': 'uiSettings',
            'exam': 'examSettings',
            'data': 'dataSettings'
        };
        
        for (const [localKey, serverKey] of Object.entries(configMapping)) {
            if (configObj[localKey]) {
                serverConfig[serverKey] = configObj[localKey];
            }
        }
        
        return serverConfig;
    }

    /**
     * 重置配置
     * @param {string} section - 配置节，如果不指定则重置所有
     */
    reset(section = null) {
        if (section) {
            // 重置指定节
            const defaultValue = this.defaultConfig[section];
            if (defaultValue) {
                this.config.set(section, this.deepClone(defaultValue));
                this.userConfig.delete(section);
            }
        } else {
            // 重置所有配置
            this.userConfig.clear();
            this.mergeAllConfigs();
        }
        
        this.saveUserConfig();
        this.notifyConfigChange(section || 'all', null, null);
    }

    /**
     * 监听配置变更
     * @param {string} key - 配置键
     * @param {Function} callback - 回调函数
     */
    onChange(key, callback) {
        if (!this.listeners.has(key)) {
            this.listeners.set(key, []);
        }
        this.listeners.get(key).push(callback);
    }

    /**
     * 移除配置变更监听器
     * @param {string} key - 配置键
     * @param {Function} callback - 回调函数
     */
    offChange(key, callback) {
        const callbacks = this.listeners.get(key);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * 通知配置变更
     */
    notifyConfigChange(key, newValue, oldValue) {
        // 通知具体键的监听器
        const callbacks = this.listeners.get(key);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(newValue, oldValue, key);
                } catch (error) {
                    console.error('配置变更回调执行失败:', error);
                }
            });
        }
        
        // 通知通配符监听器
        const allCallbacks = this.listeners.get('*');
        if (allCallbacks) {
            allCallbacks.forEach(callback => {
                try {
                    callback(newValue, oldValue, key);
                } catch (error) {
                    console.error('配置变更回调执行失败:', error);
                }
            });
        }
    }

    /**
     * 获取所有配置
     */
    getAll() {
        const result = {};
        for (const [key, value] of this.config) {
            result[key] = this.deepClone(value);
        }
        return result;
    }

    /**
     * 获取配置节
     */
    getSection(sectionKey) {
        return this.deepClone(this.config.get(sectionKey) || {});
    }

    /**
     * 深度克隆对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const cloned = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }
        
        return obj;
    }

    /**
     * 验证配置
     */
    validate() {
        const errors = [];
        
        // 验证时间配置
        const timeConfig = this.get('time');
        for (const [key, value] of Object.entries(timeConfig)) {
            if (typeof value !== 'number' || value <= 0) {
                errors.push(`时间配置 ${key} 必须是正数`);
            }
        }
        
        // 验证API配置
        const apiConfig = this.get('api');
        if (!apiConfig.baseUrl) {
            errors.push('API基础URL不能为空');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 导出配置
     */
    export() {
        return {
            timestamp: new Date().toISOString(),
            version: this.get('app.version'),
            config: this.getAll()
        };
    }

    /**
     * 导入配置
     */
    import(configData) {
        try {
            if (configData.config) {
                this.mergeConfig(configData.config);
                this.saveUserConfig();
                return true;
            }
            return false;
        } catch (error) {
            console.error('导入配置失败:', error);
            return false;
        }
    }
}

// 导出ConfigManager类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ConfigManager;
} else {
    window.ConfigManager = ConfigManager;
}
