/**
 * StudentModel - 学生数据模型
 * 管理学生相关的数据结构和验证
 */

class StudentModel extends BaseModel {
    constructor(data = {}) {
        super(data);
        
        // 学生基本信息
        this.studentNumber = data.studentNumber || '';
        this.name = data.name || '';
        this.gender = data.gender || '';
        this.age = data.age || null;
        this.phone = data.phone || '';
        this.email = data.email || '';
        
        // 学术信息
        this.major = data.major || '';
        this.undergraduateSchool = data.undergraduateSchool || '';
        this.gpa = data.gpa || null;
        this.englishLevel = data.englishLevel || '';
        
        // 考试相关
        this.examDate = data.examDate || null;
        this.examRoom = data.examRoom || '';
        this.examStatus = data.examStatus || 'pending'; // pending, in_progress, completed, cancelled
        this.currentStep = data.currentStep || 1;
        this.totalSteps = data.totalSteps || 5;
        
        // 成绩信息
        this.scores = data.scores || {};
        this.totalScore = data.totalScore || null;
        this.passed = data.passed || null;
        
        // 备注信息
        this.notes = data.notes || '';
        this.tags = data.tags || [];
    }

    /**
     * 验证属性是否有效
     * @param {string} property - 属性名
     * @returns {boolean} 是否有效
     */
    isValidProperty(property) {
        const validProperties = [
            'studentNumber', 'name', 'gender', 'age', 'phone', 'email',
            'major', 'undergraduateSchool', 'gpa', 'englishLevel',
            'examDate', 'examRoom', 'examStatus', 'currentStep', 'totalSteps',
            'scores', 'totalScore', 'passed', 'notes', 'tags'
        ];
        return validProperties.includes(property);
    }

    /**
     * 自定义验证
     * @returns {Object} 验证结果
     */
    customValidate() {
        const errors = [];

        // 学号验证
        if (!this.studentNumber || this.studentNumber.trim() === '') {
            errors.push('学号不能为空');
        } else if (!/^\d{3,10}$/.test(this.studentNumber)) {
            errors.push('学号格式不正确');
        }

        // 姓名验证
        if (!this.name || this.name.trim() === '') {
            errors.push('姓名不能为空');
        } else if (this.name.length > 50) {
            errors.push('姓名长度不能超过50个字符');
        }

        // 性别验证
        if (this.gender && !['男', '女', 'male', 'female', 'M', 'F'].includes(this.gender)) {
            errors.push('性别值无效');
        }

        // 年龄验证
        if (this.age !== null && (this.age < 18 || this.age > 60)) {
            errors.push('年龄必须在18-60之间');
        }

        // 手机号验证
        if (this.phone && !/^1[3-9]\d{9}$/.test(this.phone)) {
            errors.push('手机号格式不正确');
        }

        // 邮箱验证
        if (this.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.email)) {
            errors.push('邮箱格式不正确');
        }

        // GPA验证
        if (this.gpa !== null && (this.gpa < 0 || this.gpa > 4.0)) {
            errors.push('GPA必须在0-4.0之间');
        }

        // 考试状态验证
        const validStatuses = ['pending', 'in_progress', 'completed', 'cancelled'];
        if (this.examStatus && !validStatuses.includes(this.examStatus)) {
            errors.push('考试状态无效');
        }

        // 步骤验证
        if (this.currentStep < 1 || this.currentStep > this.totalSteps) {
            errors.push('当前步骤超出范围');
        }

        return { errors };
    }

    /**
     * 获取显示名称
     * @returns {string} 显示名称
     */
    getDisplayName() {
        return this.name || `学生${this.studentNumber}`;
    }

    /**
     * 获取考试进度百分比
     * @returns {number} 进度百分比
     */
    getProgressPercentage() {
        if (this.examStatus === 'completed') {
            return 100;
        }
        return Math.round((this.currentStep - 1) / this.totalSteps * 100);
    }

    /**
     * 是否可以开始考试
     * @returns {boolean} 是否可以开始
     */
    canStartExam() {
        return this.examStatus === 'pending';
    }

    /**
     * 是否正在考试中
     * @returns {boolean} 是否在考试中
     */
    isInExam() {
        return this.examStatus === 'in_progress';
    }

    /**
     * 是否已完成考试
     * @returns {boolean} 是否已完成
     */
    isExamCompleted() {
        return this.examStatus === 'completed';
    }

    /**
     * 开始考试
     */
    startExam() {
        this.examStatus = 'in_progress';
        this.currentStep = 1;
        this.examDate = new Date().toISOString();
        this.touch();
    }

    /**
     * 完成考试
     */
    completeExam() {
        this.examStatus = 'completed';
        this.currentStep = this.totalSteps;
        this.touch();
    }

    /**
     * 取消考试
     */
    cancelExam() {
        this.examStatus = 'cancelled';
        this.touch();
    }

    /**
     * 进入下一步
     */
    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.currentStep++;
            this.touch();
        }
    }

    /**
     * 返回上一步
     */
    previousStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.touch();
        }
    }

    /**
     * 设置步骤分数
     * @param {number} stepNumber - 步骤编号
     * @param {number} score - 分数
     */
    setStepScore(stepNumber, score) {
        if (!this.scores) {
            this.scores = {};
        }
        this.scores[`step${stepNumber}`] = score;
        this.calculateTotalScore();
        this.touch();
    }

    /**
     * 计算总分
     */
    calculateTotalScore() {
        if (!this.scores || Object.keys(this.scores).length === 0) {
            this.totalScore = null;
            return;
        }

        const scores = Object.values(this.scores).filter(score => typeof score === 'number');
        if (scores.length === 0) {
            this.totalScore = null;
            return;
        }

        this.totalScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    }

    /**
     * 添加标签
     * @param {string} tag - 标签
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
            this.touch();
        }
    }

    /**
     * 移除标签
     * @param {string} tag - 标签
     */
    removeTag(tag) {
        const index = this.tags.indexOf(tag);
        if (index > -1) {
            this.tags.splice(index, 1);
            this.touch();
        }
    }

    /**
     * 获取格式化的考试日期
     * @returns {string} 格式化的日期
     */
    getFormattedExamDate() {
        if (!this.examDate) {
            return '未设置';
        }
        return new Date(this.examDate).toLocaleString('zh-CN');
    }

    /**
     * 获取状态显示文本
     * @returns {string} 状态文本
     */
    getStatusText() {
        const statusMap = {
            'pending': '待考试',
            'in_progress': '考试中',
            'completed': '已完成',
            'cancelled': '已取消'
        };
        return statusMap[this.examStatus] || '未知状态';
    }
}

// 导出StudentModel类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StudentModel;
} else {
    window.StudentModel = StudentModel;
}
