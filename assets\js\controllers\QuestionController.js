/**
 * QuestionController - 题目管理控制器
 * 负责题目抽取流程控制、题目状态管理、抽题动画协调
 */

class QuestionController extends BaseController {
    constructor() {
        super();
        
        // 依赖的服务
        this.questionService = null;
        this.studentService = null;
        
        // 控制器状态
        this.state = {
            currentQuestionType: null,
            selectedQuestion: null,
            isDrawing: false,
            isAnimating: false,
            gridVisible: false,
            availableQuestions: [],
            usedQuestions: [],
            questionStats: {}
        };

        // 抽题配置
        this.drawConfig = {
            animationDuration: 3000,
            spinInterval: 100,
            autoStartTimer: true,
            showGridAfterDraw: true
        };
    }

    /**
     * 初始化控制器
     */
    async onInitialize() {
        // 获取服务实例
        this.questionService = window.questionService;
        this.studentService = window.studentService;

        if (!this.questionService || !this.studentService) {
            throw new Error('QuestionController 依赖的服务未找到');
        }

        // 监听服务事件
        this.setupServiceListeners();

        // 监听全局事件
        this.setupGlobalListeners();

        this.log('info', 'QuestionController 初始化完成');
    }

    /**
     * 设置服务监听器
     */
    setupServiceListeners() {
        // 监听题目服务事件
        this.questionService.on('questionDrawn', (data) => {
            this.handleQuestionDrawn(data);
        });

        this.questionService.on('questionMarkedAsUsed', (data) => {
            this.handleQuestionMarkedAsUsed(data);
        });

        // 监听学生服务事件
        this.studentService.on('currentStudentChanged', (student) => {
            this.handleStudentChanged(student);
        });

        this.studentService.on('stepChanged', (data) => {
            this.handleStepChanged(data);
        });
    }

    /**
     * 设置全局监听器
     */
    setupGlobalListeners() {
        // 监听考试控制器事件
        this.listenGlobal('ExamController.stepEntered', (data) => {
            this.handleStepEntered(data);
        });

        this.listenGlobal('ExamController.prepareQuestionInterface', (data) => {
            this.prepareQuestionInterface(data);
        });

        // 监听UI事件
        this.listenGlobal('UI.drawQuestionRequested', (data) => {
            this.drawQuestion(data.type, data.method);
        });

        this.listenGlobal('UI.showQuestionGridRequested', (data) => {
            this.showQuestionGrid(data.type);
        });

        this.listenGlobal('UI.hideQuestionGridRequested', () => {
            this.hideQuestionGrid();
        });

        this.listenGlobal('UI.questionNumberSelected', (data) => {
            this.drawQuestionByNumber(data.type, data.number);
        });
    }

    // ==================== 题目抽取 ====================

    /**
     * 抽取题目
     * @param {string} type - 题目类型
     * @param {string} method - 抽取方法 ('random' | 'specific')
     * @param {number} specificNumber - 指定题号（当method为'specific'时）
     * @returns {Promise<Object>} 抽取结果
     */
    async drawQuestion(type, method = 'random', specificNumber = null) {
        return this.executeOperation(async () => {
            // 验证抽题条件
            const conditions = [
                () => type && this.questionService.isValidQuestionType(type),
                () => !this.state.isDrawing,
                () => this.studentService.getCurrentStudent() !== null
            ];

            if (!this.validateConditions(conditions, 'drawQuestion')) {
                throw new Error('抽题条件不满足');
            }

            // 设置抽题状态
            this.updateState({
                isDrawing: true,
                currentQuestionType: type
            });

            try {
                let question;
                
                if (method === 'specific' && specificNumber) {
                    question = await this.drawQuestionByNumber(type, specificNumber);
                } else {
                    question = await this.drawRandomQuestion(type);
                }

                return question;
            } finally {
                this.updateState({ isDrawing: false });
            }
        }, 'drawQuestion');
    }

    /**
     * 随机抽取题目
     * @param {string} type - 题目类型
     * @returns {Promise<Object>} 抽取的题目
     */
    async drawRandomQuestion(type) {
        const currentStudent = this.studentService.getCurrentStudent();
        if (!currentStudent) {
            throw new Error('没有当前学生');
        }

        // 1. 显示题目网格
        await this.showQuestionGrid(type);

        // 2. 执行抽题动画
        const selectedNumber = await this.playDrawAnimation(type);

        // 3. 抽取题目
        const question = await this.questionService.drawQuestionByIndex(
            type, 
            selectedNumber, 
            currentStudent.studentNumber
        );

        // 4. 显示题目内容
        await this.displayQuestion(question);

        // 5. 更新状态
        this.updateState({ selectedQuestion: question });

        return question;
    }

    /**
     * 根据题号抽取题目
     * @param {string} type - 题目类型
     * @param {number} number - 题目编号
     * @returns {Promise<Object>} 抽取的题目
     */
    async drawQuestionByNumber(type, number) {
        const currentStudent = this.studentService.getCurrentStudent();
        if (!currentStudent) {
            throw new Error('没有当前学生');
        }

        // 1. 验证题目是否可用
        const usedNumbers = await this.questionService.getUsedQuestionNumbers(type);
        if (usedNumbers.includes(number)) {
            throw new Error(`题目${number}号已被使用`);
        }

        // 2. 显示选择动画
        this.emit('highlightQuestionNumber', { type, number });

        // 3. 抽取题目
        const question = await this.questionService.drawQuestionByIndex(
            type, 
            number, 
            currentStudent.studentNumber
        );

        // 4. 显示题目内容
        await this.displayQuestion(question);

        // 5. 更新状态
        this.updateState({ selectedQuestion: question });

        return question;
    }

    // ==================== 抽题动画 ====================

    /**
     * 播放抽题动画
     * @param {string} type - 题目类型
     * @returns {Promise<number>} 选中的题号
     */
    async playDrawAnimation(type) {
        return new Promise(async (resolve, reject) => {
            try {
                this.updateState({ isAnimating: true });

                // 1. 获取可用题目
                const availableQuestions = await this.questionService.getAvailableQuestions(type);
                if (availableQuestions.length === 0) {
                    throw new Error(`没有可用的${type}题目`);
                }

                const availableNumbers = availableQuestions.map(q => q.index);

                // 2. 开始动画
                this.emit('startDrawAnimation', {
                    type,
                    availableNumbers,
                    duration: this.drawConfig.animationDuration
                });

                // 3. 模拟旋转选择
                let currentIndex = 0;
                const spinInterval = setInterval(() => {
                    const currentNumber = availableNumbers[currentIndex % availableNumbers.length];
                    this.emit('updateSpinningNumber', { number: currentNumber });
                    currentIndex++;
                }, this.drawConfig.spinInterval);

                // 4. 等待动画时间
                setTimeout(() => {
                    clearInterval(spinInterval);

                    // 5. 随机选择最终题号
                    const randomIndex = Math.floor(Math.random() * availableNumbers.length);
                    const selectedNumber = availableNumbers[randomIndex];

                    // 6. 显示选中结果
                    this.emit('showSelectedNumber', { 
                        type, 
                        number: selectedNumber 
                    });

                    // 7. 高亮选中题号
                    setTimeout(() => {
                        this.emit('highlightSelectedNumber', { 
                            type, 
                            number: selectedNumber 
                        });

                        this.updateState({ isAnimating: false });
                        resolve(selectedNumber);
                    }, 1000);

                }, this.drawConfig.animationDuration);

            } catch (error) {
                this.updateState({ isAnimating: false });
                reject(error);
            }
        });
    }

    // ==================== 题目网格管理 ====================

    /**
     * 显示题目网格
     * @param {string} type - 题目类型
     * @returns {Promise<void>}
     */
    async showQuestionGrid(type) {
        return this.executeOperation(async () => {
            // 1. 生成网格数据
            const gridData = await this.questionService.generateQuestionGrid(type);

            // 2. 获取统计信息
            const stats = await this.questionService.getQuestionStats(type);

            // 3. 更新状态
            this.updateState({
                gridVisible: true,
                currentQuestionType: type,
                questionStats: { ...this.state.questionStats, [type]: stats }
            });

            // 4. 显示网格
            this.emit('showQuestionGrid', {
                type,
                gridData,
                stats
            });

            this.log('info', `显示${type}题目网格，共${stats.total}题`);
        }, 'showQuestionGrid');
    }

    /**
     * 隐藏题目网格
     */
    hideQuestionGrid() {
        this.updateState({ gridVisible: false });
        this.emit('hideQuestionGrid');
    }

    /**
     * 更新题目网格状态
     * @param {string} type - 题目类型
     * @param {number} questionNumber - 题目编号
     * @param {string} status - 新状态
     * @returns {Promise<void>}
     */
    async updateQuestionGridStatus(type, questionNumber, status) {
        // 重新生成网格数据
        const gridData = await this.questionService.generateQuestionGrid(type);
        const stats = await this.questionService.getQuestionStats(type);

        // 更新状态
        this.updateState({
            questionStats: { ...this.state.questionStats, [type]: stats }
        });

        // 更新UI
        this.emit('updateQuestionGrid', {
            type,
            gridData,
            stats,
            updatedNumber: questionNumber,
            newStatus: status
        });
    }

    // ==================== 题目显示 ====================

    /**
     * 显示题目内容
     * @param {Question} question - 题目对象
     * @returns {Promise<void>}
     */
    async displayQuestion(question) {
        return this.executeOperation(async () => {
            // 1. 验证题目对象
            if (!question || !question.id) {
                throw new Error('无效的题目对象');
            }

            // 2. 准备显示数据
            const displayData = {
                question: question.toAPI(),
                type: question.type,
                index: question.index,
                title: question.title,
                content: question.content,
                displayText: question.getDisplayText(),
                answerText: question.getAnswerText()
            };

            // 3. 显示题目
            this.emit('displayQuestion', displayData);

            // 4. 如果配置了自动开始计时，触发计时器
            if (this.drawConfig.autoStartTimer) {
                this.emit('autoStartTimer', {
                    questionType: question.type,
                    questionIndex: question.index
                });
            }

            this.log('info', `显示题目: ${question.type}_${question.index}`);
        }, 'displayQuestion');
    }

    /**
     * 隐藏题目内容
     */
    hideQuestion() {
        this.updateState({ selectedQuestion: null });
        this.emit('hideQuestion');
    }

    // ==================== 题目状态管理 ====================

    /**
     * 准备题目界面
     * @param {Object} data - 准备数据
     * @returns {Promise<void>}
     */
    async prepareQuestionInterface(data) {
        const { questionType, stepNumber } = data;

        // 1. 清除之前的题目
        this.hideQuestion();

        // 2. 更新当前题目类型
        this.updateState({ currentQuestionType: questionType });

        // 3. 预加载题目数据
        await this.preloadQuestionData(questionType);

        // 4. 准备UI界面
        this.emit('prepareQuestionUI', {
            questionType,
            stepNumber,
            canDraw: true
        });

        this.log('info', `准备${questionType}题目界面`);
    }

    /**
     * 预加载题目数据
     * @param {string} type - 题目类型
     * @returns {Promise<void>}
     */
    async preloadQuestionData(type) {
        try {
            // 预加载题目列表和统计信息
            const [questions, stats] = await Promise.all([
                this.questionService.getAllQuestions(type),
                this.questionService.getQuestionStats(type)
            ]);

            this.updateState({
                availableQuestions: questions.filter(q => q.isAvailable()),
                questionStats: { ...this.state.questionStats, [type]: stats }
            });
        } catch (error) {
            this.log('warn', `预加载${type}题目数据失败`, error);
        }
    }

    // ==================== 事件处理 ====================

    /**
     * 处理题目抽取完成
     * @param {Object} data - 题目抽取数据
     */
    handleQuestionDrawn(data) {
        this.updateState({ selectedQuestion: data.question });
        
        // 更新网格状态
        if (this.state.gridVisible) {
            this.updateQuestionGridStatus(
                data.question.type, 
                data.question.index, 
                'used'
            );
        }
    }

    /**
     * 处理题目标记为已使用
     * @param {Object} data - 标记数据
     */
    handleQuestionMarkedAsUsed(data) {
        // 更新本地状态
        if (this.state.currentQuestionType === data.type) {
            this.updateQuestionGridStatus(data.type, data.index, 'used');
        }
    }

    /**
     * 处理学生变更
     * @param {Student} student - 学生对象
     */
    handleStudentChanged(student) {
        // 清除当前题目状态
        this.updateState({
            selectedQuestion: null,
            currentQuestionType: null
        });

        // 隐藏题目界面
        this.hideQuestion();
        this.hideQuestionGrid();
    }

    /**
     * 处理步骤变更
     * @param {Object} data - 步骤变更数据
     */
    handleStepChanged(data) {
        // 如果新步骤不需要题目，清除题目界面
        const stepInfo = data.stepInfo || {};
        if (!stepInfo.hasQuestions) {
            this.hideQuestion();
            this.hideQuestionGrid();
            this.updateState({
                selectedQuestion: null,
                currentQuestionType: null
            });
        }
    }

    /**
     * 处理步骤进入
     * @param {Object} data - 步骤数据
     */
    handleStepEntered(data) {
        const { stepInfo } = data;
        
        // 如果步骤有题目，准备题目界面
        if (stepInfo.hasQuestions) {
            this.prepareQuestionInterface({
                questionType: stepInfo.questionType,
                stepNumber: stepInfo.stepNumber
            });
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 获取当前题目状态
     * @returns {Object} 题目状态
     */
    getQuestionStatus() {
        return {
            currentQuestionType: this.state.currentQuestionType,
            selectedQuestion: this.state.selectedQuestion,
            isDrawing: this.state.isDrawing,
            isAnimating: this.state.isAnimating,
            gridVisible: this.state.gridVisible,
            availableCount: this.state.availableQuestions.length,
            questionStats: this.state.questionStats
        };
    }

    /**
     * 检查是否可以抽题
     * @param {string} type - 题目类型
     * @returns {boolean} 是否可以抽题
     */
    canDrawQuestion(type) {
        const conditions = [
            () => !this.state.isDrawing,
            () => !this.state.isAnimating,
            () => this.studentService.getCurrentStudent() !== null,
            () => this.questionService.isValidQuestionType(type)
        ];

        return conditions.every(condition => condition());
    }

    /**
     * 重置题目状态
     */
    resetQuestionState() {
        this.updateState({
            currentQuestionType: null,
            selectedQuestion: null,
            isDrawing: false,
            isAnimating: false,
            gridVisible: false,
            availableQuestions: [],
            usedQuestions: []
        });

        this.hideQuestion();
        this.hideQuestionGrid();
    }

    /**
     * 销毁控制器
     */
    onDestroy() {
        this.resetQuestionState();
        this.log('info', 'QuestionController 已销毁');
    }
}

// 导出QuestionController类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QuestionController;
} else {
    window.QuestionController = QuestionController;
}
