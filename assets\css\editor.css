/* 
 * 研究生复试系统 - 题库编辑器样式
 * 版权所有 © 2025 王文通
 */

:root {
    --primary-color: #3498db;
    --secondary-color: #2980b9;
    --text-color: #333;
    --border-color: #ddd;
    --background-color: #f5f5f5;
    --card-background: #fff;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}

.image-upload-area {
    border: 2px dashed var(--border-color);
    padding: 20px;
    border-radius: 5px;
    text-align: center;
    margin-top: 10px;
    background: #f9f9f9;
    transition: all 0.3s;
}

.image-upload-area:hover {
    border-color: var(--primary-color);
}

.image-upload-area.dragover {
    background: #e1f0fa;
    border-color: var(--primary-color);
}

.image-upload-area .upload-message {
    margin-bottom: 10px;
    color: #666;
}

.file-input {
    display: none;
}

.upload-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

.upload-btn:hover {
    background: var(--secondary-color);
}

.drag-handle {
    cursor: move;
    color: #777;
    margin-right: 10px;
}

.item-drag-icon {
    width: 16px;
    height: 16px;
    background: #ccc;
    border-radius: 2px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    line-height: 1;
    color: white;
}

.question-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.filter-item {
    flex: 1;
    min-width: 200px;
}

.filter-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 14px;
    color: #555;
}

.statistics-card {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.stat-item {
    flex: 1;
    min-width: 150px;
    background: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-label {
    color: #777;
    font-size: 14px;
}

/* 图片预览样式 */
.uploaded-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.image-preview {
    position: relative;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    padding: 5px;
}

.image-preview img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 3px;
}

.image-preview .image-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 3px;
    padding: 3px;
}

.image-preview .image-url {
    font-size: 12px;
    color: #555;
    word-break: break-all;
    margin-top: 5px;
    background: #f5f5f5;
    padding: 3px;
    border-radius: 3px;
}

.image-preview .copy-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 14px;
}

.image-preview .delete-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .statistics-card {
        flex-direction: column;
    }
    
    .stat-item {
        width: 100%;
    }
    
    .question-filters {
        flex-direction: column;
    }
    
    .filter-item {
        width: 100%;
    }
} 