/**
 * QuestionEditorController - 题库编辑控制器
 * 管理题库编辑的业务逻辑和数据流
 */

class QuestionEditorController extends BaseController {
    constructor() {
        super();
        
        // 控制器状态
        this.state = {
            currentQuestionType: 'translation',
            currentSubject: 'computer',
            questions: [],
            stats: {
                total: 0,
                used: 0,
                unused: 0,
                active: 0
            },
            isLoading: false,
            lastError: null
        };

        // 分页配置
        this.pagination = {
            currentPage: 1,
            pageSize: 20,
            totalPages: 1
        };
    }

    /**
     * 初始化控制器
     */
    async onInitialize() {
        // 获取服务实例
        this.questionService = window.questionService;
        this.databaseService = window.databaseService;

        if (!this.questionService) {
            throw new Error('QuestionEditorController 依赖的 QuestionService 未找到');
        }

        // 绑定事件监听
        this.bindEventListeners();

        this.log('info', 'QuestionEditorController 初始化完成');
    }

    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        // 监听来自View的事件
        this.listenGlobal('QuestionEditorView.loadQuestionsRequested', (data) => {
            this.handleLoadQuestionsRequest(data);
        });

        this.listenGlobal('QuestionEditorView.saveQuestionRequested', (data) => {
            this.handleSaveQuestionRequest(data);
        });

        this.listenGlobal('QuestionEditorView.deleteQuestionRequested', (data) => {
            this.handleDeleteQuestionRequest(data);
        });

        this.listenGlobal('QuestionEditorView.importQuestionsRequested', (data) => {
            this.handleImportQuestionsRequest(data);
        });

        this.listenGlobal('QuestionEditorView.exportQuestionsRequested', (data) => {
            this.handleExportQuestionsRequest(data);
        });

        this.listenGlobal('QuestionEditorView.updateStatsRequested', () => {
            this.handleUpdateStatsRequest();
        });
    }

    /**
     * 处理加载题目请求
     */
    async handleLoadQuestionsRequest(data) {
        try {
            this.updateState({ isLoading: true, lastError: null });

            const { type, subject } = data;
            this.updateState({ currentQuestionType: type, currentSubject: subject });

            let questions = [];

            if (type === 'translation') {
                // 加载翻译题
                questions = await this.questionService.getQuestionsByType('translation');
            } else if (type === 'professional') {
                // 加载专业题
                questions = await this.questionService.getQuestionsBySubject(subject);
            }

            this.updateState({ questions, isLoading: false });

            // 通知View更新
            this.emit('questionsLoaded', { questions });

            // 更新统计信息
            this.updateStats();

            this.log('info', `加载了 ${questions.length} 道${type}题目`);

        } catch (error) {
            this.updateState({ isLoading: false, lastError: error.message });
            this.handleError(error, 'handleLoadQuestionsRequest');
        }
    }

    /**
     * 处理保存题目请求
     */
    async handleSaveQuestionRequest(data) {
        try {
            this.updateState({ isLoading: true, lastError: null });

            const { question, isNew } = data;

            // 验证题目数据
            const validation = this.validateQuestion(question);
            if (!validation.isValid) {
                throw new Error(`题目验证失败: ${validation.errors.join(', ')}`);
            }

            let savedQuestion;

            if (isNew) {
                // 创建新题目
                savedQuestion = await this.questionService.createQuestion(question);
                this.log('info', `创建新题目: ${savedQuestion.title}`);
            } else {
                // 更新现有题目
                savedQuestion = await this.questionService.updateQuestion(question.id, question);
                this.log('info', `更新题目: ${savedQuestion.title}`);
            }

            // 更新本地状态
            if (isNew) {
                this.state.questions.push(savedQuestion);
            } else {
                const index = this.state.questions.findIndex(q => q.id === savedQuestion.id);
                if (index !== -1) {
                    this.state.questions[index] = savedQuestion;
                }
            }

            this.updateState({ isLoading: false });

            // 通知View更新
            this.emit('questionSaved', { question: savedQuestion, isNew });

            // 更新统计信息
            this.updateStats();

        } catch (error) {
            this.updateState({ isLoading: false, lastError: error.message });
            this.handleError(error, 'handleSaveQuestionRequest');
        }
    }

    /**
     * 处理删除题目请求
     */
    async handleDeleteQuestionRequest(data) {
        try {
            this.updateState({ isLoading: true, lastError: null });

            const { questionId } = data;

            // 检查题目是否已被使用
            const question = this.state.questions.find(q => q.id === questionId);
            if (question && question.usageCount > 0) {
                const confirmed = confirm(`题目"${question.title}"已被使用${question.usageCount}次，确定要删除吗？`);
                if (!confirmed) {
                    this.updateState({ isLoading: false });
                    return;
                }
            }

            // 删除题目
            await this.questionService.deleteQuestion(questionId);

            // 更新本地状态
            this.state.questions = this.state.questions.filter(q => q.id !== questionId);

            this.updateState({ isLoading: false });

            // 通知View更新
            this.emit('questionDeleted', { questionId });

            // 更新统计信息
            this.updateStats();

            this.log('info', `删除题目: ${questionId}`);

        } catch (error) {
            this.updateState({ isLoading: false, lastError: error.message });
            this.handleError(error, 'handleDeleteQuestionRequest');
        }
    }

    /**
     * 处理导入题目请求
     */
    async handleImportQuestionsRequest(data) {
        try {
            this.updateState({ isLoading: true, lastError: null });

            const { file, format } = data;

            // 解析导入文件
            const importedQuestions = await this.parseImportFile(file, format);

            // 验证导入的题目
            const validationResults = importedQuestions.map(q => this.validateQuestion(q));
            const invalidQuestions = validationResults.filter(r => !r.isValid);

            if (invalidQuestions.length > 0) {
                throw new Error(`导入失败，${invalidQuestions.length}道题目验证失败`);
            }

            // 批量创建题目
            const createdQuestions = [];
            for (const question of importedQuestions) {
                try {
                    const created = await this.questionService.createQuestion(question);
                    createdQuestions.push(created);
                } catch (error) {
                    this.log('warn', `导入题目失败: ${question.title} - ${error.message}`);
                }
            }

            // 更新本地状态
            this.state.questions.push(...createdQuestions);

            this.updateState({ isLoading: false });

            // 通知View更新
            this.emit('questionsImported', { 
                imported: createdQuestions.length,
                total: importedQuestions.length 
            });

            // 重新加载题目列表
            this.handleLoadQuestionsRequest({
                type: this.state.currentQuestionType,
                subject: this.state.currentSubject
            });

            this.log('info', `成功导入 ${createdQuestions.length} 道题目`);

        } catch (error) {
            this.updateState({ isLoading: false, lastError: error.message });
            this.handleError(error, 'handleImportQuestionsRequest');
        }
    }

    /**
     * 处理导出题目请求
     */
    async handleExportQuestionsRequest(data) {
        try {
            const { format, includeUsed, includeUnused } = data;

            let questionsToExport = [...this.state.questions];

            // 过滤题目
            if (!includeUsed) {
                questionsToExport = questionsToExport.filter(q => q.usageCount === 0);
            }
            if (!includeUnused) {
                questionsToExport = questionsToExport.filter(q => q.usageCount > 0);
            }

            // 生成导出数据
            const exportData = this.generateExportData(questionsToExport, format);

            // 触发下载
            this.downloadFile(exportData, `questions_${Date.now()}.${format}`);

            this.log('info', `导出 ${questionsToExport.length} 道题目`);

        } catch (error) {
            this.handleError(error, 'handleExportQuestionsRequest');
        }
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        const stats = {
            total: this.state.questions.length,
            used: this.state.questions.filter(q => q.usageCount > 0).length,
            unused: this.state.questions.filter(q => q.usageCount === 0).length,
            active: this.state.questions.filter(q => q.isActive).length
        };

        this.updateState({ stats });

        // 通知View更新
        this.emit('statsUpdated', { stats });
    }

    /**
     * 验证题目数据
     */
    validateQuestion(question) {
        const errors = [];

        // 基本验证
        if (!question.title || question.title.trim() === '') {
            errors.push('题目标题不能为空');
        }

        if (!question.content || question.content.trim() === '') {
            errors.push('题目内容不能为空');
        }

        if (!question.type || !['translation', 'professional'].includes(question.type)) {
            errors.push('题目类型无效');
        }

        if (question.type === 'professional' && !question.subject) {
            errors.push('专业题必须指定科目');
        }

        if (!question.difficulty || !['easy', 'medium', 'hard'].includes(question.difficulty)) {
            errors.push('难度级别无效');
        }

        // 编号验证
        if (question.index !== undefined) {
            const existingQuestion = this.state.questions.find(q => 
                q.index === question.index && 
                q.type === question.type && 
                q.subject === question.subject &&
                q.id !== question.id
            );
            
            if (existingQuestion) {
                errors.push('题目编号已存在');
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
