/**
 * ServiceManager - 服务管理器
 * 负责管理所有服务的创建、初始化和生命周期
 */

class ServiceManager {
    constructor() {
        this.services = new Map();
        this.serviceInstances = new Map();
        this.eventBus = null;
        this.isInitialized = false;
        this.isStarted = false;
        
        // 服务配置
        this.serviceConfigs = new Map();
        
        // 服务状态
        this.serviceStates = new Map();
    }

    /**
     * 初始化服务管理器
     * @param {EventBus} eventBus - 事件总线
     * @returns {Promise<void>}
     */
    async initialize(eventBus) {
        if (this.isInitialized) {
            console.warn('ServiceManager 已经初始化');
            return;
        }

        try {
            console.log('🔧 初始化服务管理器...');
            
            this.eventBus = eventBus;
            
            // 1. 注册服务类
            this.registerServiceClasses();
            
            // 2. 设置服务配置
            this.setupServiceConfigs();
            
            // 3. 创建服务实例
            await this.createServiceInstances();
            
            this.isInitialized = true;
            console.log('✅ ServiceManager 初始化完成');
            
        } catch (error) {
            console.error('❌ ServiceManager 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 启动服务管理器
     * @returns {Promise<void>}
     */
    async start() {
        if (!this.isInitialized) {
            throw new Error('ServiceManager 未初始化');
        }

        if (this.isStarted) {
            console.warn('ServiceManager 已经启动');
            return;
        }

        try {
            console.log('🎯 启动服务管理器...');
            
            // 启动所有服务
            await this.startServices();
            
            this.isStarted = true;
            console.log('✅ ServiceManager 启动完成');
            
        } catch (error) {
            console.error('❌ ServiceManager 启动失败:', error);
            throw error;
        }
    }

    /**
     * 停止服务管理器
     * @returns {Promise<void>}
     */
    async stop() {
        if (!this.isStarted) {
            return;
        }

        try {
            console.log('🛑 停止服务管理器...');
            
            // 停止所有服务
            await this.stopServices();
            
            this.isStarted = false;
            console.log('✅ ServiceManager 已停止');
            
        } catch (error) {
            console.error('❌ ServiceManager 停止失败:', error);
        }
    }

    /**
     * 注册服务类
     */
    registerServiceClasses() {
        // 注册基础服务类
        if (window.BaseService) {
            this.services.set('BaseService', window.BaseService);
        }
        
        // 注册业务服务类
        if (window.StudentService) {
            this.services.set('StudentService', window.StudentService);
        }
        
        if (window.QuestionService) {
            this.services.set('QuestionService', window.QuestionService);
        }
        
        if (window.ExamService) {
            this.services.set('ExamService', window.ExamService);
        }
        
        if (window.TimerService) {
            this.services.set('TimerService', window.TimerService);
        }
        
        if (window.SettingsService) {
            this.services.set('SettingsService', window.SettingsService);
        }
        
        console.log(`已注册 ${this.services.size} 个服务类`);
    }

    /**
     * 设置服务配置
     */
    setupServiceConfigs() {
        this.serviceConfigs.set('StudentService', {
            className: 'StudentService',
            singleton: true,
            autoStart: true
        });
        
        this.serviceConfigs.set('QuestionService', {
            className: 'QuestionService',
            singleton: true,
            autoStart: true
        });
        
        this.serviceConfigs.set('ExamService', {
            className: 'ExamService',
            singleton: true,
            autoStart: true
        });
        
        this.serviceConfigs.set('TimerService', {
            className: 'TimerService',
            singleton: true,
            autoStart: true
        });
        
        this.serviceConfigs.set('SettingsService', {
            className: 'SettingsService',
            singleton: true,
            autoStart: true
        });
    }

    /**
     * 创建服务实例
     */
    async createServiceInstances() {
        for (const [serviceName, config] of this.serviceConfigs) {
            await this.createService(serviceName, config);
        }
    }

    /**
     * 创建服务
     * @param {string} serviceName - 服务名称
     * @param {Object} config - 服务配置
     * @returns {Promise<Object>} 服务实例
     */
    async createService(serviceName, config) {
        try {
            // 获取服务类
            const ServiceClass = this.services.get(config.className);
            if (!ServiceClass) {
                console.warn(`服务类不存在: ${config.className}`);
                return null;
            }

            // 创建服务实例
            const serviceInstance = new ServiceClass();
            
            // 设置事件总线
            if (serviceInstance.setEventBus) {
                serviceInstance.setEventBus(this.eventBus);
            }

            // 初始化服务
            if (serviceInstance.initialize) {
                await serviceInstance.initialize();
            }

            // 存储服务实例
            this.serviceInstances.set(serviceName, serviceInstance);
            this.serviceStates.set(serviceName, 'created');

            console.log(`✅ 服务创建成功: ${serviceName}`);
            return serviceInstance;

        } catch (error) {
            console.error(`❌ 服务创建失败: ${serviceName}`, error);
            this.serviceStates.set(serviceName, 'error');
            return null;
        }
    }

    /**
     * 启动服务
     */
    async startServices() {
        for (const [serviceName, serviceInstance] of this.serviceInstances) {
            try {
                if (serviceInstance.start) {
                    await serviceInstance.start();
                }
                this.serviceStates.set(serviceName, 'running');
                console.log(`✅ 服务启动成功: ${serviceName}`);
            } catch (error) {
                console.error(`❌ 服务启动失败: ${serviceName}`, error);
                this.serviceStates.set(serviceName, 'error');
            }
        }
    }

    /**
     * 停止服务
     */
    async stopServices() {
        for (const [serviceName, serviceInstance] of this.serviceInstances) {
            try {
                if (serviceInstance.stop) {
                    await serviceInstance.stop();
                }
                this.serviceStates.set(serviceName, 'stopped');
                console.log(`✅ 服务停止成功: ${serviceName}`);
            } catch (error) {
                console.error(`❌ 服务停止失败: ${serviceName}`, error);
            }
        }
    }

    /**
     * 获取服务实例
     * @param {string} serviceName - 服务名称
     * @returns {Object} 服务实例
     */
    getService(serviceName) {
        return this.serviceInstances.get(serviceName);
    }

    /**
     * 检查服务是否存在
     * @param {string} serviceName - 服务名称
     * @returns {boolean} 是否存在
     */
    hasService(serviceName) {
        return this.serviceInstances.has(serviceName);
    }

    /**
     * 获取所有服务状态
     * @returns {Object} 服务状态
     */
    getServiceStates() {
        return Object.fromEntries(this.serviceStates);
    }

    /**
     * 获取健康状态
     * @returns {Object} 健康状态
     */
    getHealthStatus() {
        const totalServices = this.serviceInstances.size;
        const errorServices = Array.from(this.serviceStates.values()).filter(state => state === 'error').length;
        
        return {
            isHealthy: errorServices === 0,
            totalServices,
            errorServices,
            serviceStates: this.getServiceStates()
        };
    }

    /**
     * 检查是否健康
     * @returns {boolean} 是否健康
     */
    isHealthy() {
        return this.getHealthStatus().isHealthy;
    }
}

// 创建全局服务管理器实例
const serviceManager = new ServiceManager();

// 导出ServiceManager类和实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ServiceManager, serviceManager };
} else {
    window.ServiceManager = ServiceManager;
    window.serviceManager = serviceManager;
}
