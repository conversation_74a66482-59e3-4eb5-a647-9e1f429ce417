<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英文翻译 - 研究生复试系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/modern-ui.css">
</head>
<body>
    <!-- 英文翻译步骤内容 -->
    <div class="modern-step-pane active" id="step3">
        <div class="modern-card mb-4">
            <div class="modern-card-header">
                <h5 class="mb-0"><i class="bi bi-journal-text"></i> 英文翻译（5分钟）</h5>
            </div>
            <div class="modern-card-body">
                <div class="modern-info-section">
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-info-circle info-icon"></i>
                        <h6 class="mb-0">考试说明</h6>
                    </div>
                    <p style="color: var(--gray-700); font-size: var(--font-size-lg); line-height: 1.7;">
                        请点击"抽取题目"按钮随机抽取一道英文翻译题目，然后进行中文翻译。
                    </p>
                </div>
                
                <div class="modern-content-two-column">
                    <!-- 操作区域 -->
                    <div class="modern-action-area">
                        <div class="row mb-3">
                            <div class="col-12 mb-2">
                                <button id="draw-translation-question" class="modern-btn modern-btn-outline w-100 modern-focus-element">
                                    <i class="bi bi-shuffle"></i> 抽取题目
                                </button>
                            </div>
                            <div class="col-12">
                                <button id="start-translation-timer" class="modern-btn modern-btn-success w-100" disabled>
                                    <i class="bi bi-stopwatch"></i> 开始计时
                                </button>
                            </div>
                        </div>
                        
                        <div class="modern-info-tags justify-content-center">
                            <span class="modern-info-tag primary">
                                <i class="bi bi-clock"></i> 5分钟
                            </span>
                            <span class="modern-info-tag success">
                                <i class="bi bi-translate"></i> 英译中
                            </span>
                        </div>
                    </div>
                    
                    <!-- 题目网格区域 -->
                    <div class="modern-question-display-area">
                        <div class="modern-question-grid-container mb-3">
                            <div id="translation-question-grid" class="modern-question-grid">
                                <!-- 题号将动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="translation-loading" class="d-none modern-loading">
                    <div class="modern-loading-spinner"></div>
                    <span>正在抽题...</span>
                </div>

                <!-- 题目显示区域 -->
                <div class="modern-question-container mt-3">
                    <div id="translation-question-container">
                        <div class="modern-question-placeholder">
                            <div class="modern-question-placeholder-icon">
                                <i class="bi bi-file-text"></i>
                            </div>
                            <div class="modern-question-placeholder-text">题目将在这里显示</div>
                            <div class="modern-question-placeholder-hint">请先抽取题目</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/steps.js"></script>
    <script src="../../assets/js/database.js"></script>
</body>
</html>
