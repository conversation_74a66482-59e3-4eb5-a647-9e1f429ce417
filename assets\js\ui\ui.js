/**
 * UI管理类 - 管理所有用户界面更新和显示逻辑
 * 遵循单一职责原则，专门负责界面的显示和更新
 */
class UI extends BaseClass {
    constructor() {
        super('UI');
        this.elements = new Map();
        this.modals = new Map();
        this.notifications = [];
        this.currentStep = 1;
        this.totalSteps = 5;
    }

    /**
     * 初始化UI管理器
     * @returns {Promise<boolean>} 初始化结果
     */
    async initialize() {
        try {
            this.cacheElements();
            this.initializeModals();
            this.setupNotificationContainer();
            this.initialized = true;
            this.log('UI管理器初始化成功');
            return true;
        } catch (error) {
            this.handleError(error, 'UI管理器初始化');
            return false;
        }
    }

    /**
     * 缓存常用DOM元素
     */
    cacheElements() {
        const elementSelectors = {
            // 计时器相关
            mainTimer: '#timer',
            currentStepName: '#current-step-name',
            
            // 步骤相关
            stepIndicators: '.modern-step-indicator',
            stepPanes: '.modern-step-pane',
            progressBar: '.progress-bar',
            
            // 学生信息
            studentNumber: '#studentNumber',
            
            // 按钮
            nextStepBtn: '#next-step',
            examRecordsBtn: '#exam-records',
            nextStudentBtn: '#next-student',
            
            // 题目显示
            translationQuestion: '#translation-question-container',
            professionalQuestion: '#professional-question-container',

            // 题号网格
            translationQuestionGrid: '#translation-question-grid',
            professionalQuestionGrid: '#professional-question-grid',

            // 模态框
            settingsModal: '#settingsModal',
            examRecordModal: '#examRecordModal'
        };

        for (const [key, selector] of Object.entries(elementSelectors)) {
            const element = this.getElement(selector, false);
            if (element) {
                this.elements.set(key, element);
            }
        }

        this.log(`缓存了 ${this.elements.size} 个DOM元素`);
    }

    /**
     * 初始化模态框
     */
    initializeModals() {
        // 初始化Bootstrap模态框
        const settingsModalEl = this.elements.get('settingsModal');
        if (settingsModalEl && window.bootstrap) {
            this.modals.set('settingsModal', new bootstrap.Modal(settingsModalEl));
        }

        const examRecordModalEl = this.elements.get('examRecordModal');
        if (examRecordModalEl && window.bootstrap) {
            this.modals.set('examRecordModal', new bootstrap.Modal(examRecordModalEl));
        }
    }

    /**
     * 获取单个元素
     * @param {string} selector - 选择器
     * @param {boolean} required - 是否必需（如果为true且找不到元素会抛出错误）
     * @returns {Element|null} DOM元素
     */
    getElement(selector, required = true) {
        const element = document.querySelector(selector);
        if (required && !element) {
            throw new Error(`Required element not found: ${selector}`);
        }
        return element;
    }

    /**
     * 获取多个元素
     * @param {string} selector - 选择器
     * @returns {NodeList} DOM元素列表
     */
    getElements(selector) {
        return document.querySelectorAll(selector);
    }

    /**
     * 设置元素内容
     * @param {string} selector - 选择器
     * @param {string} content - 内容
     * @param {boolean} isHTML - 是否为HTML内容
     */
    setElementContent(selector, content, isHTML = false) {
        const element = this.getElement(selector, false);
        if (element) {
            if (isHTML) {
                element.innerHTML = content;
            } else {
                element.textContent = content;
            }
        }
    }

    /**
     * 设置元素属性
     * @param {string} selector - 选择器
     * @param {string} attribute - 属性名
     * @param {string} value - 属性值
     */
    setElementAttribute(selector, attribute, value) {
        const element = this.getElement(selector, false);
        if (element) {
            element.setAttribute(attribute, value);
        }
    }

    /**
     * 添加CSS类
     * @param {string} selector - 选择器
     * @param {string} className - 类名
     */
    addClass(selector, className) {
        const element = this.getElement(selector, false);
        if (element) {
            element.classList.add(className);
        }
    }

    /**
     * 移除CSS类
     * @param {string} selector - 选择器
     * @param {string} className - 类名
     */
    removeClass(selector, className) {
        const element = this.getElement(selector, false);
        if (element) {
            element.classList.remove(className);
        }
    }

    /**
     * 设置通知容器
     */
    setupNotificationContainer() {
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
        this.elements.set('notificationContainer', container);
    }

    /**
     * 更新计时器显示
     * @param {number} seconds - 剩余秒数
     * @param {string} stepName - 当前步骤名称
     */
    updateTimer(seconds, stepName = '') {
        const timeText = Utils.formatTime(seconds);

        // 更新主计时器
        const timerElement = this.elements.get('mainTimer');
        if (timerElement) {
            timerElement.textContent = timeText;
        }

        // 同时更新可拖拽计时器
        const draggableTimerValue = document.getElementById('draggable-timer-value');
        if (draggableTimerValue) {
            draggableTimerValue.textContent = timeText;

            // 同时更新缩小状态下的时间显示
            if (typeof window.updateMinimizedTimer === 'function') {
                window.updateMinimizedTimer();
            }
        }

        if (stepName) {
            const stepNameElement = this.elements.get('currentStepName');
            if (stepNameElement) {
                stepNameElement.textContent = stepName;
            }

            // 同时更新可拖拽计时器的步骤名称
            const draggableStepName = document.getElementById('draggable-step-name');
            if (draggableStepName) {
                draggableStepName.textContent = stepName;
            }
        }
    }

    /**
     * 更新步骤显示
     * @param {number} stepNumber - 步骤编号
     * @param {string} stepName - 步骤名称
     */
    updateStep(stepNumber, stepName = '') {
        this.currentStep = stepNumber;
        
        // 更新步骤指示器
        this.updateStepIndicators(stepNumber);
        
        // 更新步骤面板
        this.updateStepPanes(stepNumber);
        
        // 更新进度条
        this.updateProgressBar(stepNumber);
        
        // 更新当前步骤名称
        if (stepName) {
            this.setElementContent('#current-step-name', stepName);
        }

        this.log(`步骤更新为: ${stepNumber} - ${stepName}`);

        // 更新三栏布局的步骤显示
        this.updateThreeColumnLayout(stepNumber, stepName);
    }

    /**
     * 更新三栏布局的步骤显示
     * @param {number} stepNumber - 步骤编号
     * @param {string} stepName - 步骤名称
     */
    updateThreeColumnLayout(stepNumber, stepName = '') {
        // 更新左侧流程节点
        this.updateProcessSteps(stepNumber);

        // 更新中间内容区域
        this.updateCenterContent(stepNumber, stepName);

        // 更新右侧按钮状态
        this.updateActionButtons(stepNumber);

        // 添加步骤切换动画
        this.animateStepTransition(stepNumber);
    }

    /**
     * 更新流程步骤指示器
     * @param {number} currentStep - 当前步骤
     */
    updateProcessSteps(currentStep) {
        const stepItems = document.querySelectorAll('.step-item');
        stepItems.forEach((item, index) => {
            const stepNumber = index + 1;
            item.classList.remove('active', 'completed');

            if (stepNumber === currentStep) {
                item.classList.add('active');
            } else if (stepNumber < currentStep) {
                item.classList.add('completed');
            }

            // 添加点击事件
            item.onclick = () => this.handleStepClick(stepNumber);
        });
    }

    /**
     * 更新中间内容区域
     * @param {number} stepNumber - 步骤编号
     * @param {string} stepName - 步骤名称
     */
    updateCenterContent(stepNumber, stepName) {
        // 更新内容标题
        const contentTitle = document.getElementById('current-step-title');
        if (contentTitle && stepName) {
            const icons = {
                1: 'bi-person-circle',
                2: 'bi-translate',
                3: 'bi-journal-text',
                4: 'bi-mortarboard',
                5: 'bi-chat-dots'
            };

            contentTitle.innerHTML = `<i class="${icons[stepNumber] || 'bi-circle'}"></i> ${stepName}`;
        }

        // 显示对应的步骤内容
        const stepContents = document.querySelectorAll('.step-content');
        stepContents.forEach((content, index) => {
            content.classList.remove('active');
            if (index + 1 === stepNumber) {
                content.classList.add('active');
            }
        });

        // 显示/隐藏题目操作按钮
        const questionControls = document.getElementById('question-controls');
        if (questionControls) {
            if (stepNumber === 3 || stepNumber === 4) {
                questionControls.style.display = 'block';
            } else {
                questionControls.style.display = 'none';
            }
        }
    }

    /**
     * 更新右侧操作按钮状态
     * @param {number} stepNumber - 步骤编号
     */
    updateActionButtons(stepNumber) {
        // 更新上一步/下一步按钮状态
        const prevBtn = document.getElementById('prev-step-btn');
        const nextBtn = document.getElementById('next-step-btn');

        if (prevBtn) {
            prevBtn.disabled = stepNumber <= 1;
            prevBtn.classList.toggle('disabled', stepNumber <= 1);
        }

        if (nextBtn) {
            nextBtn.disabled = stepNumber >= 5;
            nextBtn.classList.toggle('disabled', stepNumber >= 5);
        }

        // 更新抽题按钮
        this.updateQuestionButton(stepNumber);
    }

    /**
     * 更新抽题按钮状态
     * @param {number} stepNumber - 步骤编号
     */
    updateQuestionButton(stepNumber) {
        const drawBtn = document.getElementById('draw-question-btn');
        const drawnBtn = document.getElementById('question-drawn-btn');

        if (stepNumber === 3 || stepNumber === 4) {
            // 检查是否已抽题
            const questionType = stepNumber === 3 ? 'translation' : 'professional';
            const hasQuestion = this.checkIfQuestionDrawn(questionType);

            if (hasQuestion) {
                if (drawBtn) drawBtn.style.display = 'none';
                if (drawnBtn) drawnBtn.style.display = 'block';
            } else {
                if (drawBtn) drawBtn.style.display = 'block';
                if (drawnBtn) drawnBtn.style.display = 'none';
            }
        }
    }

    /**
     * 处理步骤点击事件
     * @param {number} stepNumber - 步骤编号
     */
    handleStepClick(stepNumber) {
        // 触发步骤切换事件
        const event = new CustomEvent('stepChange', {
            detail: { stepNumber }
        });
        document.dispatchEvent(event);
    }

    /**
     * 添加步骤切换动画
     * @param {number} stepNumber - 步骤编号
     */
    animateStepTransition(stepNumber) {
        // 为中间内容区域添加淡入动画
        const centerContent = document.querySelector('.center-content');
        if (centerContent) {
            centerContent.classList.add('step-transition');
            setTimeout(() => {
                centerContent.classList.remove('step-transition');
            }, 300);
        }
    }

    /**
     * 更新步骤指示器
     * @param {number} activeStep - 激活的步骤
     */
    updateStepIndicators(activeStep) {
        const indicators = this.getElements('.modern-step-indicator');
        indicators.forEach((indicator, index) => {
            const stepNum = index + 1;
            indicator.classList.remove('active', 'completed');

            if (stepNum < activeStep) {
                indicator.classList.add('completed');
            } else if (stepNum === activeStep) {
                indicator.classList.add('active');
            }
        });

        this.log(`步骤指示器已更新到步骤 ${activeStep}`);
    }

    /**
     * 更新步骤面板
     * @param {number} activeStep - 激活的步骤
     */
    updateStepPanes(activeStep) {
        const panes = this.getElements('.modern-step-pane');
        panes.forEach((pane, index) => {
            const stepNum = index + 1;
            pane.classList.remove('active');
            
            if (stepNum === activeStep) {
                pane.classList.add('active');
            }
        });
    }

    /**
     * 更新进度条
     * @param {number} currentStep - 当前步骤
     */
    updateProgressBar(currentStep) {
        const progressBar = this.getElement('.progress-bar', false);
        if (progressBar) {
            const progress = (currentStep / this.totalSteps) * 100;
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }
    }

    /**
     * 显示模态框
     * @param {string} modalName - 模态框名称
     */
    showModal(modalName) {
        const modal = this.modals.get(modalName);
        if (modal) {
            modal.show();
            this.log(`显示模态框: ${modalName}`);
        } else {
            this.log(`模态框不存在: ${modalName}`, 'error');
        }
    }

    /**
     * 隐藏模态框
     * @param {string} modalName - 模态框名称
     */
    hideModal(modalName) {
        const modal = this.modals.get(modalName);
        if (modal) {
            modal.hide();
            this.log(`隐藏模态框: ${modalName}`);
        }
    }

    /**
     * 显示题目
     * @param {string} type - 题目类型 ('translation' 或 'professional')
     * @param {Array|string} content - 题目内容
     */
    displayQuestion(type, content) {
        const elementKey = type === 'translation' ? 'translationQuestion' : 'professionalQuestion';
        const element = this.elements.get(elementKey);

        if (element) {
            let formattedContent = '';

            if (Array.isArray(content)) {
                // 如果是数组格式，格式化显示
                formattedContent = this.formatQuestionContent(content, type);
            } else if (typeof content === 'string') {
                formattedContent = content;
            }

            element.innerHTML = formattedContent;
            this.log(`显示${type}题目`);
        } else {
            this.log(`题目显示元素不存在: ${elementKey}`, 'error');
        }
    }

    /**
     * 清空题目显示，显示空白状态
     * @param {string} type - 题目类型 ('translation' 或 'professional')
     */
    clearQuestionDisplay(type) {
        const elementKey = type === 'translation' ? 'translationQuestion' : 'professionalQuestion';
        const element = this.elements.get(elementKey);

        if (element) {
            const typeName = type === 'translation' ? '翻译题' : '专业课题目';
            element.innerHTML = `
                <div class="question-placeholder">
                    <div class="question-placeholder-icon">
                        <i class="bi bi-file-text"></i>
                    </div>
                    <div class="question-placeholder-text">题目将在这里显示</div>
                    <div class="question-placeholder-hint">请先点击"抽取题目"按钮</div>
                </div>
            `;
            this.log(`清空${type}题目显示`);
        }
    }

    /**
     * 格式化题目内容
     * @param {Array} content - 题目内容数组
     * @param {string} type - 题目类型
     * @returns {string} 格式化后的HTML
     */
    formatQuestionContent(content, type) {
        if (!Array.isArray(content) || content.length === 0) {
            return '<p>题目内容为空</p>';
        }

        let html = '<div class="modern-question-display">';

        if (type === 'translation') {
            html += '<div class="modern-question-header"><h6>翻译题</h6></div>';
        } else if (type === 'professional') {
            html += '<div class="modern-question-header"><h6>专业课题目</h6></div>';
        }

        html += '<div class="modern-question-content">';

        content.forEach((item, index) => {
            if (Array.isArray(item) && item.length >= 2) {
                const [itemType, itemContent] = item;
                if (itemType === 'txt' && itemContent) {
                    html += `<p class="question-item">${index + 1}. ${itemContent}</p>`;
                }
            }
        });

        html += '</div></div>';

        return html;
    }

    /**
     * 生成题号网格
     * @param {string} type - 题目类型 ('translation' 或 'professional')
     * @param {number} totalNumbers - 总题号数量
     * @param {Array} usedQuestions - 已使用的题目ID数组
     * @param {boolean} showAllStates - 是否显示所有题目状态
     */
    generateQuestionGrid(type, totalNumbers = 20, usedQuestions = [], showAllStates = true) {
        const gridKey = type === 'translation' ? 'translationQuestionGrid' : 'professionalQuestionGrid';
        const gridElement = this.elements.get(gridKey);

        if (!gridElement) {
            this.log(`题号网格元素不存在: ${gridKey}`, 'error');
            return;
        }

        const typeName = type === 'translation' ? '翻译题' : '专业课题';

        let html = `
            <div class="question-grid-container">
                <div class="question-grid-header">
                    <h6 class="question-grid-title">${typeName}状态</h6>
                    <div class="question-grid-legend">
                        <span class="legend-item">
                            <span class="legend-dot available"></span>
                            <span class="legend-text">可抽取</span>
                        </span>
                        <span class="legend-item">
                            <span class="legend-dot used"></span>
                            <span class="legend-text">已抽取</span>
                        </span>
                        <span class="legend-item">
                            <span class="legend-dot selected"></span>
                            <span class="legend-text">当前选中</span>
                        </span>
                    </div>
                </div>
                <div class="question-numbers-grid">`;

        for (let i = 1; i <= totalNumbers; i++) {
            const questionId = `${type}_${i}`;
            const isUsed = usedQuestions.includes(questionId) || usedQuestions.includes(i);
            const statusClass = isUsed ? 'used' : 'available';
            const statusIcon = isUsed ? '<i class="bi bi-check-circle-fill"></i>' : '<i class="bi bi-circle"></i>';
            const statusText = isUsed ? '已抽取' : '可抽取';

            html += `<div class="question-number ${statusClass}"
                          data-number="${i}"
                          data-question-id="${questionId}"
                          title="题目 ${i} - ${statusText}">
                        <span class="question-number-text">${i}</span>
                        <span class="question-status-icon">${statusIcon}</span>
                     </div>`;
        }

        html += `</div>
                <div class="question-grid-stats">
                    <span>总计: ${totalNumbers} 题</span>
                    <span>已抽取: ${usedQuestions.length} 题</span>
                    <span>可抽取: ${totalNumbers - usedQuestions.length} 题</span>
                </div>
            </div>`;

        gridElement.innerHTML = html;
        this.log(`生成${type}题号网格，共${totalNumbers}个题号，已使用${usedQuestions.length}个`);
    }

    /**
     * 显示题号选择动画
     * @param {string} type - 题目类型
     * @param {number} selectedNumber - 选中的题号
     */
    async showQuestionSelection(type, selectedNumber) {
        const gridKey = type === 'translation' ? 'translationQuestionGrid' : 'professionalQuestionGrid';
        const gridElement = this.elements.get(gridKey);

        if (!gridElement) {
            this.log(`题号网格元素不存在: ${gridKey}`, 'error');
            return;
        }

        const numberElements = gridElement.querySelectorAll('.question-number');

        // 清除之前的选择
        numberElements.forEach(el => {
            el.classList.remove('selected', 'highlighting');
        });

        // 随机高亮动画
        const animationDuration = 2000; // 2秒动画
        const highlightInterval = 100; // 每100ms切换一次
        const totalSteps = animationDuration / highlightInterval;

        for (let step = 0; step < totalSteps; step++) {
            // 清除之前的高亮
            numberElements.forEach(el => el.classList.remove('highlighting'));

            // 随机选择一个题号高亮
            const randomIndex = Math.floor(Math.random() * numberElements.length);
            numberElements[randomIndex].classList.add('highlighting');

            // 等待
            await new Promise(resolve => setTimeout(resolve, highlightInterval));
        }

        // 最终选中指定题号
        numberElements.forEach(el => el.classList.remove('highlighting'));
        const selectedElement = gridElement.querySelector(`[data-number="${selectedNumber}"]`);
        if (selectedElement) {
            selectedElement.classList.add('selected');
        }

        this.log(`选中${type}题号: ${selectedNumber}`);
    }

    /**
     * 隐藏题号网格
     * @param {string} type - 题目类型
     */
    hideQuestionGrid(type) {
        const gridKey = type === 'translation' ? 'translationQuestionGrid' : 'professionalQuestionGrid';
        const gridElement = this.elements.get(gridKey);

        if (gridElement) {
            gridElement.style.display = 'none';
        }
    }

    /**
     * 显示题号网格
     * @param {string} type - 题目类型
     */
    showQuestionGrid(type) {
        const gridKey = type === 'translation' ? 'translationQuestionGrid' : 'professionalQuestionGrid';
        const gridElement = this.elements.get(gridKey);

        if (gridElement) {
            gridElement.style.display = 'block';
        }
    }

    /**
     * 清空题目显示
     * @param {string} type - 题目类型
     */
    clearQuestionDisplay(type) {
        const elementKey = type === 'translation' ? 'translationQuestion' : 'professionalQuestion';
        const gridKey = type === 'translation' ? 'translationQuestionGrid' : 'professionalQuestionGrid';

        // 清空题目内容
        const element = this.elements.get(elementKey);
        if (element) {
            element.innerHTML = '<p class="text-muted">题目将在这里显示</p><p class="text-muted">请先抽取题目</p>';
        }

        // 清空题号网格
        const gridElement = this.elements.get(gridKey);
        if (gridElement) {
            gridElement.innerHTML = '';
            gridElement.style.display = 'none';
        }

        this.log(`清空${type}题目显示`);
    }

    /**
     * 显示确认对话框
     * @param {string} title - 对话框标题
     * @param {string} message - 对话框消息
     * @param {string} type - 对话框类型 ('danger', 'warning', 'info')
     * @returns {Promise<boolean>} 用户是否确认
     */
    async showConfirmDialog(title, message, type = 'info') {
        return new Promise((resolve) => {
            // 获取确认按钮图标
            const confirmIcon = type === 'danger' ? 'bi-trash' :
                               type === 'warning' ? 'bi-arrow-right-circle' :
                               type === 'success' ? 'bi-check-circle' : 'bi-check';

            // 创建确认对话框HTML
            const dialogHtml = `
                <div class="modal fade" id="confirmDialog" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content border-0 shadow-lg">
                            <div class="modal-header bg-${this.getDialogColor(type)} text-white border-0">
                                <h5 class="modal-title d-flex align-items-center">
                                    <i class="bi ${this.getDialogIcon(type)} me-2"></i>
                                    ${title}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body p-4">
                                <div class="d-flex align-items-start">
                                    <div class="flex-shrink-0 me-3">
                                        <div class="rounded-circle bg-${this.getDialogColor(type)} bg-opacity-10 p-3 d-flex align-items-center justify-content-center">
                                            <i class="bi ${this.getDialogIcon(type)} text-${this.getDialogColor(type)}" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <p class="mb-0 fs-6 lh-base">${message}</p>
                                        ${type === 'warning' ? '<div class="alert alert-warning border-0 bg-warning bg-opacity-10 mt-3 mb-0"><i class="bi bi-exclamation-triangle me-1"></i> 此操作不可撤销，请谨慎操作。</div>' : ''}
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer bg-light border-0 d-flex justify-content-end gap-2">
                                <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">
                                    <i class="bi bi-x-circle me-1"></i>
                                    取消
                                </button>
                                <button type="button" class="btn btn-${this.getDialogColor(type)} px-4" id="confirmDialogBtn">
                                    <i class="bi ${confirmIcon} me-1"></i>
                                    确认
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的对话框
            const existingDialog = document.getElementById('confirmDialog');
            if (existingDialog) {
                existingDialog.remove();
            }

            // 添加新对话框到页面
            document.body.insertAdjacentHTML('beforeend', dialogHtml);

            // 获取对话框元素
            const dialogElement = document.getElementById('confirmDialog');
            const modal = new bootstrap.Modal(dialogElement);

            // 绑定事件
            const confirmBtn = document.getElementById('confirmDialogBtn');
            const cancelBtns = dialogElement.querySelectorAll('[data-bs-dismiss="modal"]');

            confirmBtn.addEventListener('click', () => {
                modal.hide();
                resolve(true);
            });

            cancelBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    modal.hide();
                    resolve(false);
                });
            });

            // 对话框关闭时清理
            dialogElement.addEventListener('hidden.bs.modal', () => {
                dialogElement.remove();
            });

            // 显示对话框
            modal.show();
        });
    }

    /**
     * 获取对话框图标
     * @param {string} type - 对话框类型
     * @returns {string} 图标类名
     */
    getDialogIcon(type) {
        switch (type) {
            case 'danger':
                return 'bi-exclamation-triangle-fill';
            case 'warning':
                return 'bi-exclamation-circle-fill';
            case 'info':
                return 'bi-info-circle-fill';
            default:
                return 'bi-question-circle-fill';
        }
    }

    /**
     * 获取对话框颜色
     * @param {string} type - 对话框类型
     * @returns {string} 颜色类名
     */
    getDialogColor(type) {
        switch (type) {
            case 'danger':
                return 'danger';
            case 'warning':
                return 'warning';
            case 'info':
                return 'info';
            default:
                return 'primary';
        }
    }

    /**
     * 更新学生编号显示
     * @param {number} studentNumber - 学生编号
     */
    updateStudentNumber(studentNumber) {
        const element = this.elements.get('studentNumber');
        if (element) {
            element.textContent = studentNumber.toString().padStart(8, '0');
            this.log(`学生编号更新为: ${studentNumber}`);
        }
    }

    /**
     * 显示通知
     * @param {string} message - 通知消息
     * @param {string} type - 通知类型 ('success', 'error', 'warning', 'info')
     * @param {number} duration - 显示时长（毫秒）
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = this.createNotificationElement(message, type);
        const container = this.elements.get('notificationContainer');
        
        if (container) {
            container.appendChild(notification);
            this.notifications.push(notification);
            
            // 自动移除
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
            
            this.log(`显示${type}通知: ${message}`);
        }
    }

    /**
     * 创建通知元素
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型
     * @returns {HTMLElement} 通知元素
     */
    createNotificationElement(message, type) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${this.getBootstrapAlertClass(type)} alert-dismissible fade show`;
        notification.style.cssText = `
            margin-bottom: 10px;
            animation: slideInRight 0.3s ease-out;
        `;
        
        notification.innerHTML = `
            ${Utils.escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 绑定关闭事件
        const closeBtn = notification.querySelector('.btn-close');
        if (closeBtn) {
            this.addEventListener(closeBtn, 'click', () => {
                this.removeNotification(notification);
            });
        }
        
        return notification;
    }

    /**
     * 获取Bootstrap警告类名
     * @param {string} type - 通知类型
     * @returns {string} Bootstrap类名
     */
    getBootstrapAlertClass(type) {
        const typeMap = {
            success: 'success',
            error: 'danger',
            warning: 'warning',
            info: 'info'
        };
        return typeMap[type] || 'info';
    }

    /**
     * 移除通知
     * @param {HTMLElement} notification - 通知元素
     */
    removeNotification(notification) {
        if (notification && notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                const index = this.notifications.indexOf(notification);
                if (index > -1) {
                    this.notifications.splice(index, 1);
                }
            }, 300);
        }
    }

    /**
     * 清空所有通知
     */
    clearAllNotifications() {
        this.notifications.forEach(notification => {
            this.removeNotification(notification);
        });
    }

    /**
     * 设置按钮状态
     * @param {string} buttonSelector - 按钮选择器
     * @param {boolean} enabled - 是否启用
     * @param {string} text - 按钮文本
     */
    setButtonState(buttonSelector, enabled = true, text = '') {
        const button = this.getElement(buttonSelector, false);
        if (button) {
            button.disabled = !enabled;
            if (text) {
                button.textContent = text;
            }
            
            if (enabled) {
                button.classList.remove('disabled');
            } else {
                button.classList.add('disabled');
            }
        }
    }

    /**
     * 添加CSS动画样式
     */
    addAnimationStyles() {
        const styleId = 'ui-animation-styles';
        if (document.getElementById(styleId)) {
            return;
        }

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
            
            .notification-container {
                pointer-events: none;
            }
            
            .notification-container .alert {
                pointer-events: all;
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * 隐藏开始计时按钮
     * @param {string} type - 题目类型 ('translation' 或 'professional')
     */
    hideStartTimerButton(type) {
        const buttonId = type === 'translation' ? 'start-translation-timer' : 'start-professional-timer';
        const button = document.getElementById(buttonId);
        if (button) {
            button.style.display = 'none';
            this.log(`隐藏${type}开始计时按钮`);
        }
    }

    /**
     * 显示开始计时按钮
     * @param {string} type - 题目类型 ('translation' 或 'professional')
     */
    showStartTimerButton(type) {
        const buttonId = type === 'translation' ? 'start-translation-timer' : 'start-professional-timer';
        const button = document.getElementById(buttonId);
        if (button) {
            button.style.display = '';
            this.log(`显示${type}开始计时按钮`);
        }
    }

    /**
     * 禁用抽取题目按钮
     * @param {string} type - 题目类型 ('translation' 或 'professional')
     */
    disableDrawButton(type) {
        const buttonId = type === 'translation' ? 'draw-translation-question' : 'draw-professional-question';
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-check-circle"></i> 已抽取';
            button.classList.remove('modern-btn-outline');
            button.classList.add('modern-btn-success');
            this.log(`禁用${type}抽取按钮`);
        }
    }

    /**
     * 启用抽取题目按钮
     * @param {string} type - 题目类型 ('translation' 或 'professional')
     */
    enableDrawButton(type) {
        const buttonId = type === 'translation' ? 'draw-translation-question' : 'draw-professional-question';
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = false;
            button.innerHTML = '<i class="bi bi-shuffle"></i> 抽取题目';
            button.classList.remove('modern-btn-success');
            button.classList.add('modern-btn-outline');
            this.log(`启用${type}抽取按钮`);
        }
    }

    /**
     * 销毁UI管理器
     */
    destroy() {
        this.clearAllNotifications();
        this.elements.clear();
        this.modals.clear();

        // 移除动画样式
        const style = document.getElementById('ui-animation-styles');
        if (style) {
            style.remove();
        }

        super.destroy();
    }

    /**
     * 检查是否已抽题
     * @param {string} questionType - 题目类型
     * @returns {boolean}
     */
    checkIfQuestionDrawn(questionType) {
        // 简单检查是否有题目内容显示
        const displayElement = document.getElementById(`${questionType}-question-display`);
        if (displayElement) {
            const hasContent = displayElement.querySelector('.modern-question-display');
            return hasContent !== null;
        }
        return false;
    }
}

// 导出到全局作用域
window.UI = UI;
