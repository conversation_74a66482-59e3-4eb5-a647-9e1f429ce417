/**
 * QuestionService - 题目数据服务
 * 负责题目相关的业务逻辑和数据操作
 */

class QuestionService {
    constructor() {
        this.dbService = window.databaseService;
        this.questionCache = new Map();
        this.eventListeners = new Map();
    }

    // ==================== 题目管理 ====================

    /**
     * 获取所有题目
     * @param {string} type - 题目类型
     * @returns {Promise<Question[]>} 题目数组
     */
    async getAllQuestions(type) {
        try {
            // 检查缓存
            const cacheKey = `questions_${type}`;
            if (this.questionCache.has(cacheKey)) {
                return this.questionCache.get(cacheKey);
            }

            const result = await this.dbService.getQuestions(type);
            if (!result.success) {
                throw new Error(result.error.message);
            }

            // 转换为Question对象
            const questions = result.data.map(q => Question.fromDatabase(q));
            
            // 缓存结果
            this.questionCache.set(cacheKey, questions);
            
            return questions;
        } catch (error) {
            console.error('获取题目列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据编号获取题目
     * @param {string} type - 题目类型
     * @param {number} index - 题目编号
     * @returns {Promise<Question|null>} 题目对象
     */
    async getQuestionByIndex(type, index) {
        try {
            const result = await this.dbService.getQuestion(type, index);
            if (!result.success) {
                if (result.error.code === 'NOT_FOUND') {
                    return null;
                }
                throw new Error(result.error.message);
            }

            return Question.fromDatabase(result.data);
        } catch (error) {
            console.error('获取题目失败:', error);
            throw error;
        }
    }

    /**
     * 获取已使用的题目编号
     * @param {string} type - 题目类型
     * @returns {Promise<number[]>} 已使用的题目编号数组
     */
    async getUsedQuestionNumbers(type) {
        try {
            const result = await this.dbService.getUsedQuestions(type);
            if (!result.success) {
                throw new Error(result.error.message);
            }

            // 提取题目编号
            return result.data.usedQuestions?.map(q => q.question_index || q.questionIndex) || [];
        } catch (error) {
            console.error('获取已使用题目失败:', error);
            return []; // 返回空数组而不是抛出错误
        }
    }

    /**
     * 获取可用题目
     * @param {string} type - 题目类型
     * @returns {Promise<Question[]>} 可用题目数组
     */
    async getAvailableQuestions(type) {
        try {
            const [allQuestions, usedNumbers] = await Promise.all([
                this.getAllQuestions(type),
                this.getUsedQuestionNumbers(type)
            ]);

            return allQuestions.filter(q => !usedNumbers.includes(q.index));
        } catch (error) {
            console.error('获取可用题目失败:', error);
            throw error;
        }
    }

    /**
     * 获取题目统计
     * @param {string} type - 题目类型
     * @returns {Promise<Object>} 题目统计
     */
    async getQuestionStats(type) {
        try {
            const [allQuestions, usedNumbers] = await Promise.all([
                this.getAllQuestions(type),
                this.getUsedQuestionNumbers(type)
            ]);

            const total = allQuestions.length;
            const used = usedNumbers.length;
            const available = total - used;
            const usageRate = total > 0 ? Math.round((used / total) * 100) : 0;

            return {
                type,
                total,
                used,
                available,
                usageRate,
                usedNumbers: usedNumbers.sort((a, b) => a - b),
                availableNumbers: allQuestions
                    .filter(q => !usedNumbers.includes(q.index))
                    .map(q => q.index)
                    .sort((a, b) => a - b)
            };
        } catch (error) {
            console.error('获取题目统计失败:', error);
            throw error;
        }
    }

    // ==================== 题目抽取 ====================

    /**
     * 随机抽取题目
     * @param {string} type - 题目类型
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Question>} 抽取的题目
     */
    async drawRandomQuestion(type, studentNumber) {
        try {
            const availableQuestions = await this.getAvailableQuestions(type);
            
            if (availableQuestions.length === 0) {
                throw new Error(`没有可用的${type}题目`);
            }

            // 随机选择题目
            const randomIndex = Math.floor(Math.random() * availableQuestions.length);
            const selectedQuestion = availableQuestions[randomIndex];

            // 标记为已使用
            await this.markQuestionAsUsed(selectedQuestion.id, type, selectedQuestion.index, studentNumber);

            // 触发事件
            this.emit('questionDrawn', {
                question: selectedQuestion,
                studentNumber,
                type
            });

            return selectedQuestion;
        } catch (error) {
            console.error('抽取题目失败:', error);
            throw error;
        }
    }

    /**
     * 根据编号抽取题目
     * @param {string} type - 题目类型
     * @param {number} index - 题目编号
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Question>} 抽取的题目
     */
    async drawQuestionByIndex(type, index, studentNumber) {
        try {
            // 检查题目是否存在
            const question = await this.getQuestionByIndex(type, index);
            if (!question) {
                throw new Error(`${type}题目${index}号不存在`);
            }

            // 检查题目是否已被使用
            const usedNumbers = await this.getUsedQuestionNumbers(type);
            if (usedNumbers.includes(index)) {
                throw new Error(`${type}题目${index}号已被使用`);
            }

            // 标记为已使用
            await this.markQuestionAsUsed(question.id, type, index, studentNumber);

            // 触发事件
            this.emit('questionDrawn', {
                question,
                studentNumber,
                type
            });

            return question;
        } catch (error) {
            console.error('抽取指定题目失败:', error);
            throw error;
        }
    }

    /**
     * 标记题目为已使用
     * @param {string} questionId - 题目ID
     * @param {string} type - 题目类型
     * @param {number} index - 题目编号
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<void>}
     */
    async markQuestionAsUsed(questionId, type, index, studentNumber) {
        try {
            const usageData = {
                questionId,
                questionType: type,
                questionNumber: index,
                studentNumber
            };

            const result = await this.dbService.markQuestionAsUsed(usageData);
            if (!result.success) {
                throw new Error(result.error.message);
            }

            // 清除相关缓存
            this.clearCache(type);

            // 触发事件
            this.emit('questionMarkedAsUsed', {
                questionId,
                type,
                index,
                studentNumber
            });
        } catch (error) {
            console.error('标记题目为已使用失败:', error);
            throw error;
        }
    }

    // ==================== 题目网格生成 ====================

    /**
     * 生成题目网格数据
     * @param {string} type - 题目类型
     * @returns {Promise<Object[]>} 题目网格数据
     */
    async generateQuestionGrid(type) {
        try {
            const [allQuestions, usedNumbers] = await Promise.all([
                this.getAllQuestions(type),
                this.getUsedQuestionNumbers(type)
            ]);

            const gridData = [];
            const maxIndex = Math.max(...allQuestions.map(q => q.index), 0);

            for (let i = 1; i <= maxIndex; i++) {
                const question = allQuestions.find(q => q.index === i);
                const isUsed = usedNumbers.includes(i);
                
                gridData.push({
                    number: i,
                    exists: !!question,
                    isUsed,
                    isAvailable: !!question && !isUsed,
                    status: !question ? 'missing' : (isUsed ? 'used' : 'available'),
                    statusText: !question ? '不存在' : (isUsed ? '已抽取' : '可抽取'),
                    statusClass: !question ? 'question-missing' : (isUsed ? 'question-used' : 'question-available'),
                    question: question ? question.toGridFormat() : null
                });
            }

            return gridData;
        } catch (error) {
            console.error('生成题目网格失败:', error);
            throw error;
        }
    }

    // ==================== 题目创建和管理 ====================

    /**
     * 创建题目
     * @param {Object} questionData - 题目数据
     * @returns {Promise<Question>} 创建的题目
     */
    async createQuestion(questionData) {
        try {
            const question = new Question(questionData);
            
            // 验证题目数据
            const validation = question.validate();
            if (!validation.isValid) {
                throw new Error(validation.errors.join(', '));
            }

            // 保存到数据库
            const result = await this.dbService.createQuestion(question.toDatabase());
            if (!result.success) {
                throw new Error(result.error.message);
            }

            // 清除缓存
            this.clearCache(question.type);

            // 触发事件
            this.emit('questionCreated', question);

            return question;
        } catch (error) {
            console.error('创建题目失败:', error);
            throw error;
        }
    }

    /**
     * 更新题目
     * @param {Question} question - 题目对象
     * @returns {Promise<Question>} 更新后的题目
     */
    async updateQuestion(question) {
        try {
            // 验证题目数据
            const validation = question.validate();
            if (!validation.isValid) {
                throw new Error(validation.errors.join(', '));
            }

            // 更新数据库
            const result = await this.dbService.updateQuestion(question.id, question.toDatabase());
            if (!result.success) {
                throw new Error(result.error.message);
            }

            // 清除缓存
            this.clearCache(question.type);

            // 触发事件
            this.emit('questionUpdated', question);

            return question;
        } catch (error) {
            console.error('更新题目失败:', error);
            throw error;
        }
    }

    // ==================== 缓存管理 ====================

    /**
     * 清除缓存
     * @param {string} type - 题目类型（可选）
     */
    clearCache(type = null) {
        if (type) {
            this.questionCache.delete(`questions_${type}`);
        } else {
            this.questionCache.clear();
        }
    }

    /**
     * 预加载题目数据
     * @param {string[]} types - 题目类型数组
     * @returns {Promise<void>}
     */
    async preloadQuestions(types = ['translation', 'professional']) {
        try {
            const promises = types.map(type => this.getAllQuestions(type));
            await Promise.all(promises);
            console.log('题目数据预加载完成');
        } catch (error) {
            console.error('预加载题目数据失败:', error);
        }
    }

    // ==================== 事件管理 ====================

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     */
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 重置题目使用状态
     * @param {string} type - 题目类型（可选）
     * @returns {Promise<void>}
     */
    async resetQuestionUsage(type = null) {
        try {
            // 这里需要后端API支持
            // const result = await this.dbService.resetQuestionUsage(type);
            
            // 暂时通过清除缓存来模拟
            this.clearCache(type);
            
            // 触发事件
            this.emit('questionUsageReset', { type });
            
            console.log(`${type || '所有'}题目使用状态已重置`);
        } catch (error) {
            console.error('重置题目使用状态失败:', error);
            throw error;
        }
    }

    /**
     * 获取题目类型列表
     * @returns {Array} 题目类型列表
     */
    getQuestionTypes() {
        return [
            {
                key: 'translation',
                name: '翻译题',
                description: '英文翻译题目'
            },
            {
                key: 'professional',
                name: '专业题',
                description: '专业课问答题目'
            }
        ];
    }

    /**
     * 验证题目类型
     * @param {string} type - 题目类型
     * @returns {boolean} 是否有效
     */
    isValidQuestionType(type) {
        return ['translation', 'professional'].includes(type);
    }
}

// 创建单例实例
const questionService = new QuestionService();

// 导出服务实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = questionService;
} else {
    window.QuestionService = QuestionService;
    window.questionService = questionService;
}
