/**
 * 工具类 - 提供通用的工具函数
 */
class Utils extends BaseClass {
    constructor() {
        super('Utils');
    }

    async initialize() {
        this.setInitialized(true);
        this.logger.info('Utils initialized');
    }

    /**
     * 字符串工具
     */
    static string = {
        /**
         * 首字母大写
         */
        capitalize(str) {
            if (!str) return '';
            return str.charAt(0).toUpperCase() + str.slice(1);
        },

        /**
         * 驼峰命名转换
         */
        toCamelCase(str) {
            return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
        },

        /**
         * 短横线命名转换
         */
        toKebabCase(str) {
            return str.replace(/([A-Z])/g, '-$1').toLowerCase();
        },

        /**
         * 截断字符串
         */
        truncate(str, length = 50, suffix = '...') {
            if (!str || str.length <= length) return str;
            return str.substring(0, length) + suffix;
        },

        /**
         * 移除HTML标签
         */
        stripHtml(str) {
            const div = document.createElement('div');
            div.innerHTML = str;
            return div.textContent || div.innerText || '';
        },

        /**
         * 转义HTML
         */
        escapeHtml(str) {
            const div = document.createElement('div');
            div.textContent = str;
            return div.innerHTML;
        }
    };

    /**
     * 数组工具
     */
    static array = {
        /**
         * 数组去重
         */
        unique(arr) {
            return [...new Set(arr)];
        },

        /**
         * 数组分块
         */
        chunk(arr, size) {
            const chunks = [];
            for (let i = 0; i < arr.length; i += size) {
                chunks.push(arr.slice(i, i + size));
            }
            return chunks;
        },

        /**
         * 数组随机排序
         */
        shuffle(arr) {
            const newArr = [...arr];
            for (let i = newArr.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [newArr[i], newArr[j]] = [newArr[j], newArr[i]];
            }
            return newArr;
        },

        /**
         * 数组求和
         */
        sum(arr) {
            return arr.reduce((sum, num) => sum + num, 0);
        },

        /**
         * 数组平均值
         */
        average(arr) {
            return arr.length ? this.sum(arr) / arr.length : 0;
        },

        /**
         * 数组最大值
         */
        max(arr) {
            return Math.max(...arr);
        },

        /**
         * 数组最小值
         */
        min(arr) {
            return Math.min(...arr);
        }
    };

    /**
     * 对象工具
     */
    static object = {
        /**
         * 深度合并对象
         */
        deepMerge(target, ...sources) {
            if (!sources.length) return target;
            const source = sources.shift();

            if (this.isObject(target) && this.isObject(source)) {
                for (const key in source) {
                    if (this.isObject(source[key])) {
                        if (!target[key]) Object.assign(target, { [key]: {} });
                        this.deepMerge(target[key], source[key]);
                    } else {
                        Object.assign(target, { [key]: source[key] });
                    }
                }
            }

            return this.deepMerge(target, ...sources);
        },

        /**
         * 检查是否为对象
         */
        isObject(item) {
            return item && typeof item === 'object' && !Array.isArray(item);
        },

        /**
         * 获取对象深层属性
         */
        get(obj, path, defaultValue = undefined) {
            const keys = path.split('.');
            let result = obj;
            
            for (const key of keys) {
                if (result == null || typeof result !== 'object') {
                    return defaultValue;
                }
                result = result[key];
            }
            
            return result !== undefined ? result : defaultValue;
        },

        /**
         * 设置对象深层属性
         */
        set(obj, path, value) {
            const keys = path.split('.');
            const lastKey = keys.pop();
            let current = obj;
            
            for (const key of keys) {
                if (!(key in current) || typeof current[key] !== 'object') {
                    current[key] = {};
                }
                current = current[key];
            }
            
            current[lastKey] = value;
            return obj;
        },

        /**
         * 删除对象深层属性
         */
        unset(obj, path) {
            const keys = path.split('.');
            const lastKey = keys.pop();
            let current = obj;
            
            for (const key of keys) {
                if (!(key in current) || typeof current[key] !== 'object') {
                    return false;
                }
                current = current[key];
            }
            
            delete current[lastKey];
            return true;
        }
    };

    /**
     * DOM工具
     */
    static dom = {
        /**
         * 创建元素
         */
        createElement(tag, attributes = {}, children = []) {
            const element = document.createElement(tag);
            
            // 设置属性
            Object.entries(attributes).forEach(([key, value]) => {
                if (key === 'className') {
                    element.className = value;
                } else if (key === 'innerHTML') {
                    element.innerHTML = value;
                } else if (key === 'textContent') {
                    element.textContent = value;
                } else {
                    element.setAttribute(key, value);
                }
            });
            
            // 添加子元素
            children.forEach(child => {
                if (typeof child === 'string') {
                    element.appendChild(document.createTextNode(child));
                } else if (child instanceof Node) {
                    element.appendChild(child);
                }
            });
            
            return element;
        },

        /**
         * 查找元素
         */
        find(selector, context = document) {
            return context.querySelector(selector);
        },

        /**
         * 查找所有元素
         */
        findAll(selector, context = document) {
            return Array.from(context.querySelectorAll(selector));
        },

        /**
         * 添加类名
         */
        addClass(element, className) {
            if (element && className) {
                element.classList.add(className);
            }
        },

        /**
         * 移除类名
         */
        removeClass(element, className) {
            if (element && className) {
                element.classList.remove(className);
            }
        },

        /**
         * 切换类名
         */
        toggleClass(element, className) {
            if (element && className) {
                element.classList.toggle(className);
            }
        },

        /**
         * 检查是否有类名
         */
        hasClass(element, className) {
            return element && className && element.classList.contains(className);
        },

        /**
         * 显示元素
         */
        show(element) {
            if (element) {
                element.style.display = '';
            }
        },

        /**
         * 隐藏元素
         */
        hide(element) {
            if (element) {
                element.style.display = 'none';
            }
        },

        /**
         * 切换显示/隐藏
         */
        toggle(element) {
            if (element) {
                const isHidden = element.style.display === 'none' || 
                    getComputedStyle(element).display === 'none';
                if (isHidden) {
                    this.show(element);
                } else {
                    this.hide(element);
                }
            }
        }
    };

    /**
     * 验证工具
     */
    static validate = {
        /**
         * 邮箱验证
         */
        email(email) {
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },

        /**
         * 手机号验证
         */
        phone(phone) {
            const regex = /^1[3-9]\d{9}$/;
            return regex.test(phone);
        },

        /**
         * URL验证
         */
        url(url) {
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        },

        /**
         * 身份证验证
         */
        idCard(idCard) {
            const regex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
            return regex.test(idCard);
        }
    };

    /**
     * 格式化工具
     */
    static format = {
        /**
         * 格式化文件大小
         */
        fileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        /**
         * 格式化数字
         */
        number(num, decimals = 2) {
            return Number(num).toLocaleString('zh-CN', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        },

        /**
         * 格式化货币
         */
        currency(amount, currency = 'CNY') {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: currency
            }).format(amount);
        }
    };
}

// 创建工具类实例
const utils = new Utils();

// 导出到全局
window.Utils = Utils;
window.utils = utils;
