#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库迁移脚本 - 为专业题添加科目字段
"""

import sqlite3
import os

DB_PATH = 'assets/data/interview_system.db'

def migrate_add_subject():
    """为professional_questions表添加subject字段"""
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(DB_PATH):
            print(f"错误: 数据库文件不存在: {DB_PATH}")
            return False
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("=== 数据库迁移：添加科目字段 ===\n")
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='professional_questions'")
        if not cursor.fetchone():
            print("错误: professional_questions表不存在")
            conn.close()
            return False
        
        # 检查subject字段是否已存在
        cursor.execute("PRAGMA table_info(professional_questions)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'subject' in column_names:
            print("subject字段已存在，无需迁移")
            conn.close()
            return True
        
        print("1. 当前表结构:")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # 添加subject字段
        print("\n2. 添加subject字段...")
        cursor.execute("ALTER TABLE professional_questions ADD COLUMN subject TEXT DEFAULT 'computer_science'")
        
        # 更新现有数据的科目
        print("3. 更新现有数据的科目...")
        cursor.execute("UPDATE professional_questions SET subject = 'computer_science' WHERE subject IS NULL")
        
        # 验证迁移结果
        print("4. 验证迁移结果:")
        cursor.execute("PRAGMA table_info(professional_questions)")
        new_columns = cursor.fetchall()
        for col in new_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) as count FROM professional_questions")
        count = cursor.fetchone()[0]
        print(f"\n5. 专业题总数: {count}")
        
        if count > 0:
            cursor.execute("SELECT id, subject FROM professional_questions LIMIT 3")
            samples = cursor.fetchall()
            print("   示例数据:")
            for sample in samples:
                print(f"     ID {sample[0]}: {sample[1]}")
        
        conn.commit()
        conn.close()
        
        print("\n✅ 迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False

def create_subjects_table():
    """创建科目配置表"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("\n=== 创建科目配置表 ===")
        
        # 创建subjects表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS subjects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 插入默认科目
        subjects = [
            ('computer_science', '计算机科学', '计算机科学与技术相关课程'),
            ('data_structure', '数据结构', '数据结构与算法'),
            ('operating_system', '操作系统', '操作系统原理与应用'),
            ('database', '数据库', '数据库系统原理'),
            ('computer_network', '计算机网络', '计算机网络原理'),
            ('software_engineering', '软件工程', '软件工程方法与实践'),
            ('ai_introduction', '人工智能导论', '人工智能基础理论'),
            ('machine_learning', '机器学习', '机器学习算法与应用'),
            ('programming', '程序设计', '程序设计语言与方法'),
            ('discrete_math', '离散数学', '离散数学基础')
        ]
        
        for code, name, desc in subjects:
            cursor.execute("""
                INSERT OR IGNORE INTO subjects (code, name, description) 
                VALUES (?, ?, ?)
            """, (code, name, desc))
        
        conn.commit()
        
        # 验证创建结果
        cursor.execute("SELECT COUNT(*) FROM subjects")
        count = cursor.fetchone()[0]
        print(f"科目配置表创建完成，共 {count} 个科目")
        
        cursor.execute("SELECT code, name FROM subjects ORDER BY code")
        subjects = cursor.fetchall()
        for code, name in subjects:
            print(f"   - {code}: {name}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建科目表失败: {e}")
        return False

if __name__ == "__main__":
    print("开始数据库迁移...")
    
    # 添加subject字段
    if migrate_add_subject():
        # 创建科目配置表
        create_subjects_table()
        print("\n🎉 所有迁移完成！")
    else:
        print("\n💥 迁移失败！")
