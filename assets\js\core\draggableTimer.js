/**
 * 可拖拽计时器组件
 * 提供可拖拽的浮动计时器功能
 */
class DraggableTimer extends BaseClass {
    constructor() {
        super('DraggableTimer');
        this.element = null;
        this.isDragging = false;
        this.startX = 0;
        this.startY = 0;
        this.offsetX = 0;
        this.offsetY = 0;
        this.timer = null;
        this.timeRemaining = 0;
        this.isRunning = false;
    }

    /**
     * 初始化可拖拽计时器（BaseClass要求的方法）
     */
    async initialize() {
        try {
            this.init();
            this.setInitialized(true);
            this.log('可拖拽计时器初始化成功');
            return true;
        } catch (error) {
            this.handleError(error, 'initialize');
            return false;
        }
    }

    /**
     * 初始化计时器
     */
    init() {
        this.createElement();
        this.bindEvents();
        this.hide();
    }

    /**
     * 创建计时器元素
     */
    createElement() {
        this.element = document.createElement('div');
        this.element.className = 'draggable-timer';
        this.element.innerHTML = `
            <div class="timer-header">
                <span class="timer-title">考试计时</span>
                <div class="timer-controls">
                    <button class="timer-minimize" title="最小化">−</button>
                    <button class="timer-close" title="关闭">×</button>
                </div>
            </div>
            <div class="timer-body">
                <div class="timer-display">
                    <span class="time-text">00:00:00</span>
                </div>
                <div class="timer-buttons">
                    <button class="timer-start">开始</button>
                    <button class="timer-pause">暂停</button>
                    <button class="timer-reset">重置</button>
                </div>
            </div>
        `;
        
        // 设置初始位置
        this.element.style.position = 'fixed';
        this.element.style.top = '20px';
        this.element.style.right = '20px';
        this.element.style.zIndex = '9999';
        
        document.body.appendChild(this.element);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        const header = this.element.querySelector('.timer-header');
        const startBtn = this.element.querySelector('.timer-start');
        const pauseBtn = this.element.querySelector('.timer-pause');
        const resetBtn = this.element.querySelector('.timer-reset');
        const minimizeBtn = this.element.querySelector('.timer-minimize');
        const closeBtn = this.element.querySelector('.timer-close');

        // 拖拽事件
        header.addEventListener('mousedown', this.handleMouseDown.bind(this));
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));

        // 计时器控制事件
        startBtn.addEventListener('click', this.start.bind(this));
        pauseBtn.addEventListener('click', this.pause.bind(this));
        resetBtn.addEventListener('click', this.reset.bind(this));
        
        // 窗口控制事件
        minimizeBtn.addEventListener('click', this.minimize.bind(this));
        closeBtn.addEventListener('click', this.hide.bind(this));
    }

    /**
     * 鼠标按下事件
     */
    handleMouseDown(e) {
        this.isDragging = true;
        this.startX = e.clientX;
        this.startY = e.clientY;
        
        const rect = this.element.getBoundingClientRect();
        this.offsetX = this.startX - rect.left;
        this.offsetY = this.startY - rect.top;
        
        this.element.style.cursor = 'grabbing';
        e.preventDefault();
    }

    /**
     * 鼠标移动事件
     */
    handleMouseMove(e) {
        if (!this.isDragging) return;
        
        const x = e.clientX - this.offsetX;
        const y = e.clientY - this.offsetY;
        
        // 限制在窗口范围内
        const maxX = window.innerWidth - this.element.offsetWidth;
        const maxY = window.innerHeight - this.element.offsetHeight;
        
        const boundedX = Math.max(0, Math.min(x, maxX));
        const boundedY = Math.max(0, Math.min(y, maxY));
        
        this.element.style.left = boundedX + 'px';
        this.element.style.top = boundedY + 'px';
        this.element.style.right = 'auto';
    }

    /**
     * 鼠标释放事件
     */
    handleMouseUp() {
        this.isDragging = false;
        this.element.style.cursor = 'grab';
    }

    /**
     * 开始计时
     */
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.timer = setInterval(() => {
            this.timeRemaining++;
            this.updateDisplay();
        }, 1000);
        
        this.updateButtons();
    }

    /**
     * 暂停计时
     */
    pause() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        clearInterval(this.timer);
        this.updateButtons();
    }

    /**
     * 重置计时器
     */
    reset() {
        this.pause();
        this.timeRemaining = 0;
        this.updateDisplay();
        this.updateButtons();
    }

    /**
     * 设置时间
     */
    setTime(seconds) {
        this.timeRemaining = seconds;
        this.updateDisplay();
    }

    /**
     * 更新显示
     */
    updateDisplay() {
        const timeText = this.element.querySelector('.time-text');
        timeText.textContent = this.formatTime(this.timeRemaining);
    }

    /**
     * 格式化时间
     */
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * 更新按钮状态
     */
    updateButtons() {
        const startBtn = this.element.querySelector('.timer-start');
        const pauseBtn = this.element.querySelector('.timer-pause');
        
        if (this.isRunning) {
            startBtn.disabled = true;
            pauseBtn.disabled = false;
        } else {
            startBtn.disabled = false;
            pauseBtn.disabled = true;
        }
    }

    /**
     * 显示计时器
     */
    show() {
        this.element.style.display = 'block';
    }

    /**
     * 隐藏计时器
     */
    hide() {
        this.element.style.display = 'none';
    }

    /**
     * 最小化
     */
    minimize() {
        const body = this.element.querySelector('.timer-body');
        if (body.style.display === 'none') {
            body.style.display = 'block';
            this.element.querySelector('.timer-minimize').textContent = '−';
        } else {
            body.style.display = 'none';
            this.element.querySelector('.timer-minimize').textContent = '+';
        }
    }

    /**
     * 获取当前时间
     */
    getCurrentTime() {
        return this.timeRemaining;
    }

    /**
     * 检查是否正在运行
     */
    isTimerRunning() {
        return this.isRunning;
    }

    /**
     * 销毁计时器
     */
    destroy() {
        this.pause();
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        super.destroy();
    }
}

// 创建全局实例
window.draggableTimer = new DraggableTimer();

// 导出类
window.DraggableTimer = DraggableTimer;
