<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库题库编辑器 - 研究生复试系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .nav-buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        .btn-light {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        .btn-light:hover {
            background: rgba(255,255,255,0.3);
        }
        .content {
            display: flex;
            height: 600px;
        }
        .sidebar {
            width: 300px;
            border-right: 1px solid #dee2e6;
            padding: 20px;
            overflow-y: auto;
        }
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        .question-type-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #666;
        }
        .tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 14px;
        }
        .question-list {
            margin-bottom: 20px;
        }
        .question-item {
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .question-item:hover {
            background: #f8f9fa;
            border-color: #007bff;
        }
        .question-item.selected {
            background: #e3f2fd;
            border-color: #007bff;
        }
        .question-index {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .question-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .question-content {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .question-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
        }
        .editor-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .status-bar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #666;
        }
        .stats {
            display: flex;
            gap: 20px;
        }
        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .stat-value {
            font-weight: bold;
            color: #007bff;
        }
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 15px;
            min-width: 300px;
            z-index: 1000;
            border-left: 4px solid #007bff;
        }
        .toast.success {
            border-left-color: #28a745;
        }
        .toast.error {
            border-left-color: #dc3545;
        }
        .toast.warning {
            border-left-color: #ffc107;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
        }
        .pagination button {
            padding: 5px 10px;
            border: 1px solid #dee2e6;
            background: white;
            cursor: pointer;
            border-radius: 3px;
        }
        .pagination button:hover {
            background: #f8f9fa;
        }
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .pagination .page-info {
            margin: 0 10px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🗄️ 数据库题库编辑器</h1>
            <div class="nav-buttons">
                <a href="docs/editor.md" target="_blank" class="btn btn-light">📖 帮助文档</a>
                <a href="/" class="btn btn-light">🏠 返回主系统</a>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="content">
            <!-- 左侧边栏 -->
            <div class="sidebar">
                <!-- 题目类型标签 -->
                <div class="question-type-tabs">
                    <div class="tab active" data-type="translation">🌍 翻译题</div>
                    <div class="tab" data-type="professional">🎓 专业题</div>
                </div>

                <!-- 搜索框 -->
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索题目..." id="searchInput">
                </div>

                <!-- 题目列表 -->
                <div class="question-list" id="questionList">
                    <div class="empty-state">
                        <div class="empty-icon">📝</div>
                        <div>正在加载题目...</div>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="pagination" id="pagination" style="display: none;">
                    <button id="prevPage" onclick="changePage(-1)">上一页</button>
                    <span class="page-info">第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页</span>
                    <button id="nextPage" onclick="changePage(1)">下一页</button>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button class="btn btn-primary" onclick="addNewQuestion()">➕ 添加题目</button>
                    <button class="btn btn-secondary" onclick="refreshQuestions()">🔄 刷新</button>
                </div>
            </div>

            <!-- 主要编辑区域 -->
            <div class="main-content">
                <div id="editorArea">
                    <div class="empty-state">
                        <div class="empty-icon">✏️</div>
                        <h3>选择题目进行编辑</h3>
                        <p>从左侧列表中选择一个题目进行编辑，或点击"添加题目"创建新题目</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="stats">
                <div class="stat-item">
                    <span>总题目:</span>
                    <span class="stat-value" id="totalCount">0</span>
                </div>
                <div class="stat-item">
                    <span>当前页:</span>
                    <span class="stat-value" id="currentCount">0</span>
                </div>
                <div class="stat-item">
                    <span>类型:</span>
                    <span class="stat-value" id="currentType">翻译题</span>
                </div>
            </div>
            <div>数据库题库编辑器 v1.0</div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentQuestionType = 'translation';
        let questions = [];
        let selectedQuestion = null;
        let isEditing = false;
        let currentPage = 1;
        let totalPages = 1;
        let totalCount = 0;
        let searchQuery = '';
        let subjects = []; // 科目列表

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据库题库编辑器已加载');
            initializeEditor();
        });

        // 初始化编辑器
        async function initializeEditor() {
            // 绑定事件
            bindEvents();

            // 加载科目列表
            await loadSubjects();

            // 加载题目
            loadQuestions();

            showToast('编辑器已就绪', 'success');
        }

        // 绑定事件
        function bindEvents() {
            // 标签页切换
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    switchQuestionType(this.getAttribute('data-type'));
                });
            });

            // 搜索
            document.getElementById('searchInput').addEventListener('input', function() {
                searchQuery = this.value;
                currentPage = 1; // 重置到第一页
                loadQuestions();
            });
        }

        // 切换题目类型
        function switchQuestionType(type) {
            currentQuestionType = type;
            currentPage = 1; // 重置到第一页
            searchQuery = ''; // 清空搜索
            document.getElementById('searchInput').value = '';

            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');

            // 更新类型显示
            document.getElementById('currentType').textContent = type === 'translation' ? '翻译题' : '专业题';

            // 重新加载题目
            loadQuestions();

            // 清空编辑器
            clearEditor();
        }

        // 加载科目列表
        async function loadSubjects() {
            try {
                const response = await fetch('/api/questions/subjects');
                const data = await response.json();

                if (response.ok && data.subjects) {
                    subjects = data.subjects;
                    console.log('科目列表加载成功:', subjects.length, '个科目');
                } else {
                    console.warn('加载科目列表失败:', data.error);
                    // 使用默认科目列表
                    subjects = [
                        {code: 'computer_science', name: '计算机科学'},
                        {code: 'data_structure', name: '数据结构'},
                        {code: 'operating_system', name: '操作系统'},
                        {code: 'database', name: '数据库'},
                        {code: 'computer_network', name: '计算机网络'},
                        {code: 'software_engineering', name: '软件工程'},
                        {code: 'ai_introduction', name: '人工智能导论'},
                        {code: 'machine_learning', name: '机器学习'},
                        {code: 'programming', name: '程序设计'},
                        {code: 'discrete_math', name: '离散数学'}
                    ];
                }
            } catch (error) {
                console.error('加载科目列表失败:', error);
                // 使用默认科目列表
                subjects = [
                    {code: 'computer_science', name: '计算机科学'},
                    {code: 'data_structure', name: '数据结构'},
                    {code: 'operating_system', name: '操作系统'}
                ];
            }
        }

        // 加载题目
        async function loadQuestions() {
            try {
                showToast('正在加载题目...', 'info');
                setLoading(true);

                // 构建API URL
                let url = `/api/questions/${currentQuestionType}?page=${currentPage}&limit=10`;
                if (searchQuery) {
                    url += `&search=${encodeURIComponent(searchQuery)}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (response.ok && data.questions) {
                    questions = data.questions;
                    totalCount = data.total || 0;
                    totalPages = Math.ceil(totalCount / 10);

                    renderQuestionList();
                    updatePagination();
                    updateStats();
                    showToast(`加载了 ${questions.length} 道题目`, 'success');
                } else {
                    throw new Error(data.error || '加载题目失败');
                }
            } catch (error) {
                console.error('加载题目失败:', error);
                showToast('加载题目失败: ' + error.message, 'error');

                // 显示空状态
                questions = [];
                renderQuestionList();
                updateStats();
            } finally {
                setLoading(false);
            }
        }

        // 渲染题目列表
        function renderQuestionList() {
            const listContainer = document.getElementById('questionList');

            if (questions.length === 0) {
                listContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📝</div>
                        <div>暂无题目</div>
                        <p>点击"添加题目"开始创建</p>
                    </div>
                `;
                return;
            }

            const questionsHTML = questions.map(question => {
                // 解析question_data数组格式
                let questionData = { title: '无标题', content: '', difficulty: 'medium' };

                try {
                    if (Array.isArray(question.question_data)) {
                        // 数组格式：[["txt", "content1"], ["txt", "content2"], ...]
                        const contents = question.question_data
                            .filter(item => Array.isArray(item) && item[0] === 'txt')
                            .map(item => item[1]);

                        questionData.content = contents.join(' ');
                        questionData.title = `翻译题 ${question.id}`;
                    } else if (typeof question.question_data === 'string') {
                        // JSON字符串格式
                        const parsed = JSON.parse(question.question_data);
                        questionData = { ...questionData, ...parsed };
                    } else if (typeof question.question_data === 'object') {
                        // 对象格式
                        questionData = { ...questionData, ...question.question_data };
                    }
                } catch (e) {
                    console.warn('解析题目数据失败:', e);
                    questionData.title = '数据解析错误';
                    questionData.content = String(question.question_data);
                }

                // 获取科目名称
                const subjectName = getSubjectName(question.subject);

                return `
                    <div class="question-item" onclick="selectQuestion(${question.id})">
                        <div class="question-index">#${question.question_index || question.id}</div>
                        <div class="question-title">${questionData.title}</div>
                        <div class="question-content">${truncateText(questionData.content, 80)}</div>
                        <div class="question-meta">
                            <span>难度: ${getDifficultyText(questionData.difficulty)}</span>
                            ${currentQuestionType === 'professional' ? `<span>科目: ${subjectName}</span>` : ''}
                            <span>ID: ${question.id}</span>
                        </div>
                    </div>
                `;
            }).join('');

            listContainer.innerHTML = questionsHTML;
        }

        // 选择题目
        function selectQuestion(questionId) {
            selectedQuestion = questions.find(q => q.id === questionId);

            if (selectedQuestion) {
                // 更新选中状态
                document.querySelectorAll('.question-item').forEach(item => {
                    item.classList.remove('selected');
                });
                event.target.closest('.question-item').classList.add('selected');

                // 显示编辑器
                showEditor(selectedQuestion);
            }
        }

        // 显示编辑器
        function showEditor(question) {
            const editorArea = document.getElementById('editorArea');
            const isTranslation = currentQuestionType === 'translation';

            // 解析question_data
            let questionData = { title: '', content: '', difficulty: 'medium', answer: '', notes: '' };

            try {
                if (Array.isArray(question.question_data)) {
                    // 数组格式：[["txt", "content1"], ["txt", "content2"], ...]
                    const contents = question.question_data
                        .filter(item => Array.isArray(item) && item[0] === 'txt')
                        .map(item => item[1]);

                    questionData.content = contents.join('\n\n');
                    questionData.title = `翻译题 ${question.id}`;
                } else if (typeof question.question_data === 'string') {
                    // JSON字符串格式
                    const parsed = JSON.parse(question.question_data);
                    questionData = { ...questionData, ...parsed };
                } else if (typeof question.question_data === 'object') {
                    // 对象格式
                    questionData = { ...questionData, ...question.question_data };
                }
            } catch (e) {
                console.warn('解析题目数据失败:', e);
                questionData.title = '数据解析错误';
                questionData.content = String(question.question_data);
            }

            editorArea.innerHTML = `
                <div class="editor-form">
                    <h3>${question.id ? '编辑题目' : '添加题目'} #${question.question_index}</h3>

                    <div class="form-group">
                        <label class="form-label">题目编号</label>
                        <input type="number" class="form-input" id="questionIndex" value="${question.question_index || ''}" min="0">
                    </div>

                    <div class="form-group">
                        <label class="form-label">题目标题</label>
                        <input type="text" class="form-input" id="questionTitle" value="${questionData.title || ''}" placeholder="请输入题目标题">
                    </div>

                    <div class="form-group">
                        <label class="form-label">题目内容</label>
                        <textarea class="form-textarea" id="questionContent" placeholder="${isTranslation ? '请输入要翻译的英文句子' : '请输入题目内容'}">${questionData.content || ''}</textarea>
                    </div>

                    ${isTranslation ? `
                    <div class="form-group">
                        <label class="form-label">参考答案</label>
                        <textarea class="form-textarea" id="questionAnswer" placeholder="请输入参考翻译">${questionData.answer || ''}</textarea>
                    </div>
                    ` : `
                    <div class="form-group">
                        <label class="form-label">科目</label>
                        <select class="form-select" id="questionSubject">
                            ${subjects.map(subject => `
                                <option value="${subject.code}" ${(question.subject || 'computer_science') === subject.code ? 'selected' : ''}>${subject.name}</option>
                            `).join('')}
                        </select>
                    </div>
                    `}

                    <div class="form-group">
                        <label class="form-label">难度级别</label>
                        <select class="form-select" id="questionDifficulty">
                            <option value="easy" ${questionData.difficulty === 'easy' ? 'selected' : ''}>简单</option>
                            <option value="medium" ${questionData.difficulty === 'medium' ? 'selected' : ''}>中等</option>
                            <option value="hard" ${questionData.difficulty === 'hard' ? 'selected' : ''}>困难</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <textarea class="form-textarea" id="questionNotes" placeholder="请输入备注信息（可选）" rows="3">${questionData.notes || ''}</textarea>
                    </div>

                    <div class="form-actions">
                        <button class="btn btn-success" onclick="saveQuestion()">💾 保存</button>
                        <button class="btn btn-secondary" onclick="clearEditor()">❌ 取消</button>
                        ${question.id ? '<button class="btn btn-danger" onclick="deleteQuestion()">🗑️ 删除</button>' : ''}
                    </div>
                </div>
            `;

            isEditing = true;
        }

        // 添加新题目
        function addNewQuestion() {
            // 获取下一个编号
            const nextIndex = getNextQuestionIndex();

            const newQuestion = {
                id: null,
                question_index: nextIndex,
                question_data: JSON.stringify({
                    title: '',
                    content: '',
                    answer: '',
                    difficulty: 'medium',
                    notes: ''
                })
            };

            selectedQuestion = newQuestion;
            showEditor(newQuestion);
        }

        // 获取下一个题目编号
        function getNextQuestionIndex() {
            if (questions.length === 0) return 0;
            const maxIndex = Math.max(...questions.map(q => q.question_index || 0));
            return maxIndex + 1;
        }

        // 保存题目
        async function saveQuestion() {
            if (!selectedQuestion) return;

            // 获取表单数据
            const questionData = {
                title: document.getElementById('questionTitle').value.trim(),
                content: document.getElementById('questionContent').value.trim(),
                answer: document.getElementById('questionAnswer')?.value.trim() || '',
                difficulty: document.getElementById('questionDifficulty').value,
                notes: document.getElementById('questionNotes').value.trim()
            };

            const questionIndex = parseInt(document.getElementById('questionIndex').value) || 0;
            const subject = document.getElementById('questionSubject')?.value || 'computer_science';

            // 验证数据
            if (!questionData.title) {
                showToast('请输入题目标题', 'error');
                return;
            }

            if (!questionData.content) {
                showToast('请输入题目内容', 'error');
                return;
            }

            try {
                showToast('正在保存...', 'info');
                setLoading(true);

                const requestData = {
                    question_data: JSON.stringify(questionData),
                    question_index: questionIndex
                };

                // 如果是专业题，添加科目信息
                if (currentQuestionType === 'professional') {
                    requestData.subject = subject;
                }

                let url, method;
                if (selectedQuestion.id) {
                    // 更新现有题目
                    url = `/api/questions/${currentQuestionType}/${selectedQuestion.id}`;
                    method = 'PUT';
                } else {
                    // 添加新题目
                    url = `/api/questions/${currentQuestionType}`;
                    method = 'POST';
                }

                console.log('发送请求:', { url, method, data: requestData });

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('响应状态:', response.status, response.statusText);

                const responseText = await response.text();
                console.log('响应内容:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    throw new Error(`服务器返回非JSON数据: ${responseText.substring(0, 100)}...`);
                }

                if (response.ok) {
                    showToast('保存成功', 'success');

                    // 重新加载题目列表
                    await loadQuestions();

                    // 清空编辑器
                    clearEditor();
                } else {
                    throw new Error(data.error || '保存失败');
                }
            } catch (error) {
                console.error('保存失败:', error);
                showToast('保存失败: ' + error.message, 'error');
            } finally {
                setLoading(false);
            }
        }

        // 删除题目
        async function deleteQuestion() {
            if (!selectedQuestion || !selectedQuestion.id) return;

            // 解析题目数据获取标题
            let questionData = {};
            try {
                questionData = typeof selectedQuestion.question_data === 'string'
                    ? JSON.parse(selectedQuestion.question_data)
                    : selectedQuestion.question_data;
            } catch (e) {
                questionData = { title: '未知题目' };
            }

            if (confirm(`确定要删除题目"${questionData.title}"吗？此操作不可撤销！`)) {
                try {
                    showToast('正在删除...', 'info');
                    setLoading(true);

                    const response = await fetch(`/api/questions/${currentQuestionType}/${selectedQuestion.id}`, {
                        method: 'DELETE'
                    });

                    const data = await response.json();

                    if (response.ok) {
                        showToast('删除成功', 'success');

                        // 重新加载题目列表
                        await loadQuestions();

                        // 清空编辑器
                        clearEditor();
                    } else {
                        throw new Error(data.error || '删除失败');
                    }
                } catch (error) {
                    console.error('删除失败:', error);
                    showToast('删除失败: ' + error.message, 'error');
                } finally {
                    setLoading(false);
                }
            }
        }

        // 清空编辑器
        function clearEditor() {
            const editorArea = document.getElementById('editorArea');
            editorArea.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">✏️</div>
                    <h3>选择题目进行编辑</h3>
                    <p>从左侧列表中选择一个题目进行编辑，或点击"添加题目"创建新题目</p>
                </div>
            `;

            selectedQuestion = null;
            isEditing = false;

            // 清除选中状态
            document.querySelectorAll('.question-item').forEach(item => {
                item.classList.remove('selected');
            });
        }

        // 刷新题目
        function refreshQuestions() {
            currentPage = 1;
            loadQuestions();
        }

        // 分页功能
        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                loadQuestions();
            }
        }

        // 更新分页显示
        function updatePagination() {
            const pagination = document.getElementById('pagination');
            const currentPageSpan = document.getElementById('currentPage');
            const totalPagesSpan = document.getElementById('totalPages');
            const prevButton = document.getElementById('prevPage');
            const nextButton = document.getElementById('nextPage');

            if (totalPages > 1) {
                pagination.style.display = 'flex';
                currentPageSpan.textContent = currentPage;
                totalPagesSpan.textContent = totalPages;

                prevButton.disabled = currentPage <= 1;
                nextButton.disabled = currentPage >= totalPages;
            } else {
                pagination.style.display = 'none';
            }
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('currentCount').textContent = questions.length;
        }

        // 设置加载状态
        function setLoading(loading) {
            const container = document.querySelector('.container');
            if (loading) {
                container.classList.add('loading');
            } else {
                container.classList.remove('loading');
            }
        }

        // 工具函数
        function truncateText(text, maxLength) {
            if (!text) return '';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function getDifficultyText(difficulty) {
            const map = { 'easy': '简单', 'medium': '中等', 'hard': '困难' };
            return map[difficulty] || '未知';
        }

        function getSubjectName(subjectCode) {
            const subject = subjects.find(s => s.code === subjectCode);
            return subject ? subject.name : (subjectCode || '未知');
        }

        function showToast(message, type = 'info') {
            // 移除现有的toast
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 3秒后自动移除
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
