#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
研究生复试系统 - 主应用程序
版权所有 © 2025 王文通
联系方式：<EMAIL>
"""

from flask import Flask, jsonify, request, send_from_directory
import sqlite3
import json
import os
import sys
import webbrowser
import threading
import time

print("=================================")
print("应用程序启动中...")
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")
print("=================================")

# 导入蓝图
try:
    from questions import questions_bp
    print("成功导入questions模块")
except Exception as e:
    print(f"导入questions模块失败: {e}")
    questions_bp = None

try:
    from api_routes import api_bp
    print("成功导入api_routes模块")
except Exception as e:
    print(f"导入api_routes模块失败: {e}")
    api_bp = None

# 创建应用
app = Flask(__name__, 
            static_folder='.', 
            static_url_path='')

# 注册蓝图
if questions_bp:
    app.register_blueprint(questions_bp)
    print("已注册questions蓝图")
else:
    print("WARNING: questions蓝图不可用，跳过注册")

if api_bp:
    app.register_blueprint(api_bp)
    print("已注册API蓝图")
else:
    print("WARNING: API蓝图不可用，跳过注册")

# 配置
DATABASE = 'assets/data/interview_system.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """初始化数据库"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    # 创建翻译题目表 - 添加question_index字段以兼容questions.py蓝图，添加tags和difficulty字段
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS translation_questions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        question_index INTEGER DEFAULT 0,
        question_data TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        tags TEXT DEFAULT '[]',
        difficulty TEXT DEFAULT 'medium'
    )
    ''')
    
    # 创建专业课题目表 - 添加question_index字段以兼容questions.py蓝图，添加tags和difficulty字段
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS professional_questions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        question_index INTEGER DEFAULT 0,
        question_data TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        tags TEXT DEFAULT '[]',
        difficulty TEXT DEFAULT 'medium'
    )
    ''')
    
    # 创建考试记录表 - 确保字段名与API使用的字段一致，添加status字段
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS exam_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_number TEXT NOT NULL,
        translation_question TEXT,
        professional_question TEXT,
        translation_question_content TEXT,
        professional_question_content TEXT,
        exam_date TEXT NOT NULL,
        current_step INTEGER DEFAULT 1,
        status TEXT DEFAULT '进行中',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    print("创建exam_records表成功")

    # 创建设置表 - 更新字段名以匹配sample.db格式
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        chinese_time INTEGER NOT NULL DEFAULT 1,
        english_time INTEGER NOT NULL DEFAULT 1,
        translation_time INTEGER NOT NULL DEFAULT 4,
        professional_time INTEGER NOT NULL DEFAULT 5
    )
    ''')

    # 插入默认设置（如果不存在）
    cursor.execute('''
    INSERT OR IGNORE INTO settings (id, chinese_time, english_time, translation_time, professional_time)
    VALUES (1, 1, 1, 4, 5)
    ''')

    # 创建学生表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS students (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_number INTEGER NOT NULL,
        name TEXT,
        exam_date DATETIME,
        current_step INTEGER DEFAULT 1,
        completed BOOLEAN DEFAULT 0
    )
    ''')
    print("创建students表成功")

    # 创建学生题目表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS student_questions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id INTEGER NOT NULL,
        question_type TEXT NOT NULL,
        question_id INTEGER NOT NULL,
        question_index INTEGER NOT NULL,
        question_data TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    print("创建student_questions表成功")

    # 创建系统状态表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS system_state (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        current_student INTEGER NOT NULL DEFAULT 1,
        current_step INTEGER NOT NULL DEFAULT 1
    )
    ''')

    # 插入默认系统状态（如果不存在）
    cursor.execute('''
    INSERT OR IGNORE INTO system_state (id, current_student, current_step)
    VALUES (1, 1, 1)
    ''')
    print("创建system_state表成功")

    # 创建学生答案表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS student_answers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id INTEGER NOT NULL,
        question_id INTEGER NOT NULL,
        answer_text TEXT,
        score INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    print("创建student_answers表成功")

    conn.commit()
    conn.close()
    print("数据库初始化成功")

# 确保数据库存在的函数
def ensure_db_exists():
    """确保数据库存在并初始化"""
    if not os.path.exists(DATABASE):
        print(f"数据库文件 {DATABASE} 不存在，将创建新数据库")
        init_db()
        print(f"创建了新的数据库文件: {DATABASE}")
        # 加载示例题目
        load_sample_questions()
    else:
        print(f"使用现有数据库文件: {DATABASE}")
        # 无论如何都检查所有表是否存在
        print("检查并确保所有必需的表都存在")
        ensure_tables_exist()

def ensure_tables_exist():
    """确保所有必需的表都存在"""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # 检查translation_questions表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='translation_questions'")
        if not cursor.fetchone():
            print("translation_questions表不存在，正在创建...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS translation_questions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                question_index INTEGER DEFAULT 0,
                question_data TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                tags TEXT DEFAULT '[]',
                difficulty TEXT DEFAULT 'medium'
            )
            ''')
            print("translation_questions表创建成功")
        else:
            # 检查是否需要添加新字段
            cursor.execute("PRAGMA table_info(translation_questions)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'tags' not in column_names:
                print("translation_questions表缺少tags字段，正在添加...")
                cursor.execute("ALTER TABLE translation_questions ADD COLUMN tags TEXT DEFAULT '[]'")
                print("tags字段添加成功")

            if 'difficulty' not in column_names:
                print("translation_questions表缺少difficulty字段，正在添加...")
                cursor.execute("ALTER TABLE translation_questions ADD COLUMN difficulty TEXT DEFAULT 'medium'")
                print("difficulty字段添加成功")
        
        # 检查professional_questions表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='professional_questions'")
        if not cursor.fetchone():
            print("professional_questions表不存在，正在创建...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS professional_questions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                question_index INTEGER DEFAULT 0,
                question_data TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                tags TEXT DEFAULT '[]',
                difficulty TEXT DEFAULT 'medium'
            )
            ''')
            print("professional_questions表创建成功")
        else:
            # 检查是否需要添加新字段
            cursor.execute("PRAGMA table_info(professional_questions)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'tags' not in column_names:
                print("professional_questions表缺少tags字段，正在添加...")
                cursor.execute("ALTER TABLE professional_questions ADD COLUMN tags TEXT DEFAULT '[]'")
                print("tags字段添加成功")

            if 'difficulty' not in column_names:
                print("professional_questions表缺少difficulty字段，正在添加...")
                cursor.execute("ALTER TABLE professional_questions ADD COLUMN difficulty TEXT DEFAULT 'medium'")
                print("difficulty字段添加成功")
        
        # 检查exam_records表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='exam_records'")
        if not cursor.fetchone():
            print("exam_records表不存在，正在创建...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS exam_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_number TEXT NOT NULL,
                translation_question TEXT,
                professional_question TEXT,
                translation_question_content TEXT,
                professional_question_content TEXT,
                exam_date TEXT NOT NULL,
                current_step INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            print("exam_records表创建成功")
        else:
            # 检查是否需要添加字段
            cursor.execute("PRAGMA table_info(exam_records)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'current_step' not in column_names:
                print("exam_records表缺少current_step字段，正在添加...")
                cursor.execute("ALTER TABLE exam_records ADD COLUMN current_step INTEGER DEFAULT 1")
                print("current_step字段添加成功")

            if 'status' not in column_names:
                print("exam_records表缺少status字段，正在添加...")
                cursor.execute("ALTER TABLE exam_records ADD COLUMN status TEXT DEFAULT '进行中'")
                print("status字段添加成功")

            if 'translation_question_content' not in column_names:
                print("exam_records表缺少translation_question_content字段，正在添加...")
                cursor.execute("ALTER TABLE exam_records ADD COLUMN translation_question_content TEXT")
                print("translation_question_content字段添加成功")

            if 'professional_question_content' not in column_names:
                print("exam_records表缺少professional_question_content字段，正在添加...")
                cursor.execute("ALTER TABLE exam_records ADD COLUMN professional_question_content TEXT")
                print("professional_question_content字段添加成功")
        
        # 检查settings表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'")
        if not cursor.fetchone():
            print("settings表不存在，正在创建...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                chinese_time INTEGER NOT NULL DEFAULT 1,
                english_time INTEGER NOT NULL DEFAULT 1,
                translation_time INTEGER NOT NULL DEFAULT 4,
                professional_time INTEGER NOT NULL DEFAULT 5
            )
            ''')

            # 插入默认设置
            cursor.execute('''
            INSERT OR IGNORE INTO settings (id, chinese_time, english_time, translation_time, professional_time)
            VALUES (1, 1, 1, 4, 5)
            ''')
            print("settings表创建成功")
        else:
            # 检查是否需要更新字段名
            cursor.execute("PRAGMA table_info(settings)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            # 如果存在旧字段名，需要重建表
            if 'chinese_intro' in column_names:
                print("settings表字段名需要更新，正在重建表...")
                cursor.execute("DROP TABLE settings")
                cursor.execute('''
                CREATE TABLE settings (
                    id INTEGER PRIMARY KEY CHECK (id = 1),
                    chinese_time INTEGER NOT NULL DEFAULT 1,
                    english_time INTEGER NOT NULL DEFAULT 1,
                    translation_time INTEGER NOT NULL DEFAULT 4,
                    professional_time INTEGER NOT NULL DEFAULT 5
                )
                ''')
                cursor.execute('''
                INSERT OR IGNORE INTO settings (id, chinese_time, english_time, translation_time, professional_time)
                VALUES (1, 1, 1, 4, 5)
                ''')
                print("settings表重建成功")

        # 检查students表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='students'")
        if not cursor.fetchone():
            print("students表不存在，正在创建...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_number INTEGER NOT NULL,
                name TEXT,
                exam_date DATETIME,
                current_step INTEGER DEFAULT 1,
                completed BOOLEAN DEFAULT 0
            )
            ''')
            print("students表创建成功")

        # 检查student_questions表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='student_questions'")
        if not cursor.fetchone():
            print("student_questions表不存在，正在创建...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS student_questions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                question_type TEXT NOT NULL,
                question_id INTEGER NOT NULL,
                question_index INTEGER NOT NULL,
                question_data TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            print("student_questions表创建成功")

        # 检查system_state表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_state'")
        if not cursor.fetchone():
            print("system_state表不存在，正在创建...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_state (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                current_student INTEGER NOT NULL DEFAULT 1,
                current_step INTEGER NOT NULL DEFAULT 1
            )
            ''')
            cursor.execute('''
            INSERT OR IGNORE INTO system_state (id, current_student, current_step)
            VALUES (1, 1, 1)
            ''')
            print("system_state表创建成功")

        # 检查student_answers表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='student_answers'")
        if not cursor.fetchone():
            print("student_answers表不存在，正在创建...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS student_answers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                question_id INTEGER NOT NULL,
                answer_text TEXT,
                score INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            print("student_answers表创建成功")

        conn.commit()
        conn.close()
        print("所有表检查/创建完成")
    except Exception as e:
        print(f"确保表存在时出错: {str(e)}")
        import traceback
        traceback.print_exc()

# 获取翻译题目API
@app.route('/api/questions/translation', methods=['GET'])
def get_translation_questions():
    """获取翻译题目列表"""
    limit = request.args.get('limit', 100, type=int)
    offset = request.args.get('offset', 0, type=int)
    
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row  # 启用字典工厂，这样可以用列名访问数据
        cursor = conn.cursor()
        questions = cursor.execute(
            'SELECT id, question_data FROM translation_questions LIMIT ? OFFSET ?',
            (limit, offset)
        ).fetchall()
        conn.close()
        
        # 确保question_data是有效的JSON
        result = []
        for q in questions:
            try:
                question_data = json.loads(q['question_data']) if isinstance(q['question_data'], str) else q['question_data']
                result.append({'id': q['id'], 'question_data': question_data})
            except json.JSONDecodeError:
                print(f"警告: 题目ID {q['id']} 的question_data不是有效的JSON")
                result.append({'id': q['id'], 'question_data': [['txt', '数据格式错误']]})
        
        return jsonify({
            'success': True,
            'questions': result
        })
    except Exception as e:
        print(f"获取翻译题目失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'questions': []
        }), 500

# 获取专业课题目API
@app.route('/api/questions/professional', methods=['GET'])
def get_professional_questions():
    """获取专业课题目列表"""
    limit = request.args.get('limit', 100, type=int)
    offset = request.args.get('offset', 0, type=int)
    
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row  # 启用字典工厂
        cursor = conn.cursor()
        questions = cursor.execute(
            'SELECT id, question_data FROM professional_questions LIMIT ? OFFSET ?',
            (limit, offset)
        ).fetchall()
        conn.close()
        
        # 确保question_data是有效的JSON
        result = []
        for q in questions:
            try:
                question_data = json.loads(q['question_data']) if isinstance(q['question_data'], str) else q['question_data']
                result.append({'id': q['id'], 'question_data': question_data})
            except json.JSONDecodeError:
                print(f"警告: 题目ID {q['id']} 的question_data不是有效的JSON")
                result.append({'id': q['id'], 'question_data': [['txt', '数据格式错误']]})
        
        return jsonify({
            'success': True,
            'questions': result
        })
    except Exception as e:
        print(f"获取专业课题目失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'questions': []
        }), 500

# 保存考试记录API
@app.route('/api/exams/save', methods=['POST'])
def save_exam_record():
    """保存考试记录"""
    data = request.json
    print(f"接收到保存考试记录请求: {data}")

    if not data or not all(k in data for k in ('studentNumber', 'translationQuestion', 'professionalQuestion', 'date')):
        return jsonify({'success': False, 'error': '缺少必要字段'}), 400

    # 默认为步骤1，如果提供了当前步骤则使用提供的值
    current_step = data.get('currentStep', 1)

    # 获取题目内容
    translation_question_content = data.get('translationQuestionContent', '')
    professional_question_content = data.get('professionalQuestionContent', '')

    # 如果提供了状态则使用提供的值，否则根据当前步骤设置对应状态
    status = data.get('status')
    if not status:  # 如果没有提供状态或状态为空
        # 根据步骤设置默认状态
        if current_step == 1:
            status = '中文介绍'
        elif current_step == 2:
            status = '英文介绍'
        elif current_step == 3:
            status = '英文翻译'
        elif current_step == 4:
            status = '专业测试'
        elif current_step == 5:
            status = '综合问答'
        else:
            status = '进行中'
    
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # 检查exam_records表是否有status列
        cursor.execute("PRAGMA table_info(exam_records)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # 如果没有status列，添加该列
        if 'status' not in column_names:
            cursor.execute("ALTER TABLE exam_records ADD COLUMN status TEXT DEFAULT '进行中'")
            print("已添加status列到exam_records表")
        
        # 先检查是否已有该考生记录
        check_query = 'SELECT id FROM exam_records WHERE student_number = ?'
        cursor.execute(check_query, (data['studentNumber'],))
        existing_record = cursor.fetchone()
        
        if existing_record:
            # 如果已有记录，则更新
            update_query = '''
            UPDATE exam_records
            SET translation_question = ?, professional_question = ?, translation_question_content = ?,
                professional_question_content = ?, exam_date = ?, current_step = ?, status = ?
            WHERE student_number = ?
            '''
            params = (data['translationQuestion'], data['professionalQuestion'],
                      translation_question_content, professional_question_content,
                      data['date'], current_step, status, data['studentNumber'])
            print(f"执行更新SQL: {update_query}, 参数: {params}")
            cursor.execute(update_query, params)
            result_message = '考试记录更新成功'
        else:
            # 如果没有记录，则插入新记录
            insert_query = '''
            INSERT INTO exam_records
            (student_number, translation_question, professional_question, translation_question_content,
             professional_question_content, exam_date, current_step, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (data['studentNumber'], data['translationQuestion'],
                      data['professionalQuestion'], translation_question_content,
                      professional_question_content, data['date'], current_step, status)
            print(f"执行插入SQL: {insert_query}, 参数: {params}")
            cursor.execute(insert_query, params)
            result_message = '考试记录保存成功'
        
        conn.commit()
        conn.close()
        
        print(f"{result_message}: 考生{data['studentNumber']}")
        return jsonify({'success': True, 'message': result_message}), 200
    except Exception as e:
        print(f"保存考试记录失败，详细错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)}), 500

# 获取所有考试记录API
@app.route('/api/exams/records', methods=['GET'])
def get_exam_records():
    """获取所有考试记录"""
    print("接收到获取考试记录请求")
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row  # 启用字典工厂
        cursor = conn.cursor()
        query = 'SELECT * FROM exam_records ORDER BY created_at DESC'
        print(f"执行SQL: {query}")
        
        records = cursor.execute(query).fetchall()
        conn.close()
        
        result = []
        for record in records:
            # 检查current_step字段是否存在
            current_step = 1
            if 'current_step' in record.keys():
                current_step = record['current_step']
            
            # 检查status字段是否存在
            status = '进行中'
            if 'status' in record.keys():
                status = record['status']
                
            # 获取题目内容
            translation_question_content = ''
            professional_question_content = ''
            if 'translation_question_content' in record.keys():
                translation_question_content = record['translation_question_content'] or ''
            if 'professional_question_content' in record.keys():
                professional_question_content = record['professional_question_content'] or ''

            result.append({
                'id': record['id'],
                'studentNumber': record['student_number'],
                'translationQuestion': record['translation_question'],
                'professionalQuestion': record['professional_question'],
                'translationQuestionContent': translation_question_content,
                'professionalQuestionContent': professional_question_content,
                'examDate': record['exam_date'],
                'currentStep': current_step,
                'status': status,
                'createdAt': record['created_at']
            })
        
        print(f"成功获取 {len(result)} 条考试记录")
        return jsonify({
            'success': True,
            'records': result
        })
    except Exception as e:
        print(f"获取考试记录失败，详细错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e),
            'records': []
        }), 500

# 清空考试记录
@app.route('/api/exams/clear', methods=['POST'])
def clear_exam_records():
    """清空所有考试记录"""
    print("接收到清空考试记录请求")
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # 清空考试记录表
        cursor.execute('DELETE FROM exam_records')
        deleted_records = cursor.rowcount

        # 重置自增ID（SQLite特有）
        cursor.execute('DELETE FROM sqlite_sequence WHERE name="exam_records"')

        conn.commit()
        conn.close()

        print(f"已清空所有考试记录，删除了 {deleted_records} 条记录")
        return jsonify({
            'success': True,
            'message': f'已清空所有考试记录，删除了 {deleted_records} 条记录，考生序号从1号开始'
        })
    except Exception as e:
        print(f"清空考试记录失败，详细错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取下一个学生编号
@app.route('/api/students/next-number', methods=['GET'])
def get_next_student_number():
    """获取下一个学生编号（从1开始递增）"""
    print("接收到获取下一个学生编号请求")
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # 获取所有学生编号，然后在Python中过滤纯数字
        cursor.execute('SELECT student_number FROM exam_records')
        results = cursor.fetchall()

        max_number = 0
        for row in results:
            student_number = row[0]
            # 检查是否为纯数字
            if student_number and student_number.isdigit():
                number = int(student_number)
                if number > max_number:
                    max_number = number

        next_number = max_number + 1
        conn.close()

        print(f"下一个学生编号: {next_number}")
        return jsonify({
            'success': True,
            'studentNumber': str(next_number)
        })
    except Exception as e:
        print(f"获取下一个学生编号失败，详细错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e),
            'studentNumber': '1'  # 默认返回1
        }), 500

# 获取当前考生状态
@app.route('/api/students/<student_number>/status', methods=['GET'])
def get_student_status(student_number):
    """获取指定考生的当前状态"""
    print(f"接收到获取考生 {student_number} 状态请求")
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # 查询考生记录
        cursor.execute('SELECT * FROM exam_records WHERE student_number = ?', (student_number,))
        record = cursor.fetchone()
        conn.close()

        if record:
            # 检查current_step字段是否存在
            current_step = 1
            if 'current_step' in record.keys():
                current_step = record['current_step']

            # 检查status字段是否存在
            status = '进行中'
            if 'status' in record.keys():
                status = record['status']

            # 获取题目内容
            translation_question_content = ''
            professional_question_content = ''
            if 'translation_question_content' in record.keys():
                translation_question_content = record['translation_question_content'] or ''
            if 'professional_question_content' in record.keys():
                professional_question_content = record['professional_question_content'] or ''

            return jsonify({
                'success': True,
                'exists': True,
                'student': {
                    'studentNumber': record['student_number'],
                    'currentStep': current_step,
                    'status': status,
                    'translationQuestion': record['translation_question'],
                    'professionalQuestion': record['professional_question'],
                    'translationQuestionContent': translation_question_content,
                    'professionalQuestionContent': professional_question_content,
                    'examDate': record['exam_date'],
                    'createdAt': record['created_at']
                }
            })
        else:
            return jsonify({
                'success': True,
                'exists': False,
                'student': None
            })
    except Exception as e:
        print(f"获取考生状态失败，详细错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e),
            'exists': False,
            'student': None
        }), 500

# 保存学生题目内容
@app.route('/api/students/<student_number>/questions', methods=['POST'])
def save_student_questions(student_number):
    """保存学生的题目内容"""
    data = request.json
    print(f"接收到保存学生 {student_number} 题目内容请求: {data}")

    if not data:
        return jsonify({'success': False, 'error': '缺少数据'}), 400

    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # 检查是否已有该考生记录
        cursor.execute('SELECT id FROM exam_records WHERE student_number = ?', (student_number,))
        existing_record = cursor.fetchone()

        translation_question_content = data.get('translationQuestionContent', '')
        professional_question_content = data.get('professionalQuestionContent', '')
        translation_question = data.get('translationQuestion', '')
        professional_question = data.get('professionalQuestion', '')

        if existing_record:
            # 更新现有记录
            cursor.execute('''
                UPDATE exam_records
                SET translation_question_content = ?, professional_question_content = ?,
                    translation_question = ?, professional_question = ?
                WHERE student_number = ?
            ''', (translation_question_content, professional_question_content,
                  translation_question, professional_question, student_number))
            message = f'学生 {student_number} 题目内容更新成功'
        else:
            # 创建新记录
            from datetime import datetime
            exam_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute('''
                INSERT INTO exam_records
                (student_number, translation_question_content, professional_question_content,
                 translation_question, professional_question, exam_date, current_step, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (student_number, translation_question_content, professional_question_content,
                  translation_question, professional_question, exam_date, 1, '进行中'))
            message = f'创建学生 {student_number} 记录并保存题目内容'

        conn.commit()
        conn.close()

        print(message)
        return jsonify({
            'success': True,
            'message': message
        })
    except Exception as e:
        print(f"保存学生题目内容失败，详细错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取当前正在进行的考试
@app.route('/api/students/current', methods=['GET'])
def get_current_exam():
    """获取当前正在进行的考试"""
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # 查找最近的未完成考试（按创建时间倒序）
        cursor.execute('''
            SELECT * FROM exam_records
            WHERE status != '已完成' AND status != '已结束'
            ORDER BY created_at DESC
            LIMIT 1
        ''')
        record = cursor.fetchone()

        if record:
            # 获取当前步骤和状态
            current_step = record['current_step'] if record['current_step'] else 1
            status = record['status'] if record['status'] else '进行中'

            # 获取题目内容
            translation_question_content = ''
            professional_question_content = ''
            if 'translation_question_content' in record.keys():
                translation_question_content = record['translation_question_content'] or ''
            if 'professional_question_content' in record.keys():
                professional_question_content = record['professional_question_content'] or ''

            conn.close()

            return jsonify({
                'success': True,
                'hasCurrentExam': True,
                'student': {
                    'studentNumber': record['student_number'],
                    'currentStep': current_step,
                    'status': status,
                    'translationQuestion': record['translation_question'],
                    'professionalQuestion': record['professional_question'],
                    'translationQuestionContent': translation_question_content,
                    'professionalQuestionContent': professional_question_content,
                    'examDate': record['exam_date'],
                    'createdAt': record['created_at']
                }
            })
        else:
            conn.close()
            return jsonify({
                'success': True,
                'hasCurrentExam': False,
                'student': None
            })

    except Exception as e:
        print(f"获取当前考试失败，详细错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e),
            'hasCurrentExam': False,
            'student': None
        }), 500

# 测试路由
@app.route('/api/test', methods=['GET'])
def test_route():
    return jsonify({'success': True, 'message': 'Test route works'})

# 更新考生当前步骤
@app.route('/api/students/<student_number>/step', methods=['POST'])
def update_student_step(student_number):
    """更新考生当前步骤"""
    data = request.json
    print(f"接收到更新考生 {student_number} 步骤请求: {data}")

    if not data or 'currentStep' not in data:
        return jsonify({'success': False, 'error': '缺少currentStep字段'}), 400

    current_step = data['currentStep']
    status = data.get('status', '进行中')

    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # 检查考生记录是否存在
        cursor.execute('SELECT id FROM exam_records WHERE student_number = ?', (student_number,))
        existing_record = cursor.fetchone()

        if existing_record:
            # 更新现有记录
            cursor.execute(
                'UPDATE exam_records SET current_step = ?, status = ? WHERE student_number = ?',
                (current_step, status, student_number)
            )
            message = f'考生 {student_number} 步骤更新为 {current_step}'
        else:
            # 创建新记录
            from datetime import datetime
            exam_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute(
                '''INSERT INTO exam_records
                   (student_number, current_step, status, exam_date, translation_question, professional_question)
                   VALUES (?, ?, ?, ?, ?, ?)''',
                (student_number, current_step, status, exam_date, '', '')
            )
            message = f'创建考生 {student_number} 记录，当前步骤 {current_step}'

        conn.commit()
        conn.close()

        print(message)
        return jsonify({
            'success': True,
            'message': message
        })
    except Exception as e:
        print(f"更新考生步骤失败，详细错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 获取时间设置
@app.route('/api/settings/time', methods=['GET'])
def get_time_settings():
    """获取时间设置"""
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row  # 启用字典工厂
        cursor = conn.cursor()
        settings = cursor.execute('SELECT * FROM settings WHERE id = 1').fetchone()
        conn.close()
        
        if settings:
            return jsonify({
                'success': True,
                'settings': {
                    'chineseTime': settings['chinese_time'],
                    'englishTime': settings['english_time'],
                    'translationTime': settings['translation_time'],
                    'professionalTime': settings['professional_time']
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': '未找到设置',
                'settings': {
                    'chineseTime': 1,
                    'englishTime': 1,
                    'translationTime': 4,
                    'professionalTime': 5
                }
            })
    except Exception as e:
        print(f"获取时间设置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'settings': {
                'chineseTime': 1,
                'englishTime': 1,
                'translationTime': 4,
                'professionalTime': 5
            }
        }), 500

# 保存时间设置
@app.route('/api/settings/time', methods=['POST'])
def save_time_settings():
    """保存时间设置"""
    data = request.json
    print(f"接收到保存时间设置请求: {data}")
    
    if not data or not all(k in data for k in ('chineseTime', 'englishTime', 'translationTime', 'professionalTime')):
        print(f"缺少必要字段: {data}")
        return jsonify({'success': False, 'error': '缺少必要字段'}), 400
    
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # 检查settings表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'")
        if not cursor.fetchone():
            print("settings表不存在，正在创建...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                chinese_time INTEGER NOT NULL DEFAULT 1,
                english_time INTEGER NOT NULL DEFAULT 1,
                translation_time INTEGER NOT NULL DEFAULT 4,
                professional_time INTEGER NOT NULL DEFAULT 5
            )
            ''')
            # 插入默认值
            cursor.execute('''
            INSERT INTO settings (id, chinese_time, english_time, translation_time, professional_time)
            VALUES (1, ?, ?, ?, ?)
            ''', (
                data['chineseTime'],
                data['englishTime'],
                data['translationTime'],
                data['professionalTime']
            ))
            print("settings表创建并初始化完成")
        
        # 检查是否存在记录
        cursor.execute('SELECT COUNT(*) as count FROM settings')
        count = cursor.fetchone()[0]
        if count == 0:
            # 如果没有记录，插入一条新记录
            print("settings表中没有记录，插入默认记录")
            cursor.execute('''
            INSERT INTO settings (id, chinese_time, english_time, translation_time, professional_time)
            VALUES (1, ?, ?, ?, ?)
            ''', (
                data['chineseTime'],
                data['englishTime'],
                data['translationTime'],
                data['professionalTime']
            ))
        else:
            # 更新现有记录
            cursor.execute(
                '''UPDATE settings SET
                   chinese_time = ?,
                   english_time = ?,
                   translation_time = ?,
                   professional_time = ?
                   WHERE id = 1''',
                (
                    data['chineseTime'],
                    data['englishTime'],
                    data['translationTime'],
                    data['professionalTime']
                )
            )
        
        # 检查更新是否成功
        cursor.execute('SELECT * FROM settings WHERE id = 1')
        updated_settings = cursor.fetchone()
        print(f"更新后的设置: {updated_settings}")
        
        conn.commit()
        conn.close()
        
        print("保存时间设置成功")
        return jsonify({'success': True, 'message': '时间设置保存成功'}), 200
    except Exception as e:
        print(f"保存时间设置失败，详细错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)}), 500

# 添加导入题目API - 从JS文件导入到数据库
@app.route('/api/questions/import-from-js', methods=['POST'])
def import_questions_from_js():
    """从JS文件导入题目到数据库"""
    data = request.json
    
    if not data or 'type' not in data or 'questions' not in data:
        return jsonify({'success': False, 'error': '缺少必要字段'}), 400
    
    question_type = data['type']  # 'translation' 或 'professional'
    questions = data['questions']
    
    if not isinstance(questions, list):
        return jsonify({'success': False, 'error': '题目数据格式不正确'}), 400
    
    table_name = f"{question_type}_questions"
    
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 获取当前最大的question_index
        cursor.execute(f"SELECT MAX(question_index) as max_index FROM {table_name}")
        result = cursor.fetchone()
        next_index = (result['max_index'] + 1) if result['max_index'] is not None else 0
        
        count = 0
        for question in questions:
            # 题目数据必须是列表形式或可JSON序列化的对象
            question_data = json.dumps(question['data']) if isinstance(question.get('data'), (list, dict)) else json.dumps([['txt', '数据格式错误']])
            
            # 插入数据时包含question_index字段
            cursor.execute(
                f'INSERT INTO {table_name} (question_index, question_data) VALUES (?, ?)',
                (next_index + count, question_data)
            )
            count += 1
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': f'成功导入 {count} 个{question_type}题目',
            'count': count
        }), 201
    except Exception as e:
        print(f"导入题目失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# 手动初始化数据库API（管理员使用）
@app.route('/api/init-db', methods=['POST'])
def initialize_database():
    """初始化数据库"""
    try:
        init_db()
        load_sample_questions()
        return jsonify({'success': True, 'message': '数据库初始化成功'}), 200
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 静态文件服务
@app.route('/<path:path>')
def serve_static(path):
    """提供静态文件服务"""
    return send_from_directory('.', path)

# 主页路由
@app.route('/')
def index():
    """返回主页"""
    return send_from_directory('.', 'index.html')

# 加载一些示例题目
def load_sample_questions():
    """加载示例题目到数据库"""
    # 检查是否已有题目
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row  # 启用字典工厂
    cursor = conn.cursor()
    translation_count = cursor.execute('SELECT COUNT(*) as count FROM translation_questions').fetchone()['count']
    professional_count = cursor.execute('SELECT COUNT(*) as count FROM professional_questions').fetchone()['count']
    
    # 如果已有题目，不再加载示例
    if translation_count > 0 and professional_count > 0:
        conn.close()
        return
    
    # 翻译题目示例
    translation_examples = [
        {
            "data": [
                ["txt", "The rapid development of artificial intelligence has brought both opportunities and challenges to society."],
                ["txt", "While AI technologies have revolutionized many industries, they have also raised concerns about job displacement, privacy, and ethical issues."],
                ["txt", "It is important for policymakers, businesses, and the public to work together to ensure that AI is developed and used responsibly to benefit humanity."]
            ]
        },
        {
            "data": [
                ["txt", "Climate change is one of the most pressing issues facing our planet today."],
                ["txt", "Rising global temperatures have led to more frequent extreme weather events, sea level rise, and disruptions to ecosystems."],
                ["txt", "To address this challenge, countries around the world need to reduce greenhouse gas emissions, transition to renewable energy sources, and develop sustainable practices."]
            ]
        },
        {
            "data": [
                ["txt", "The global pandemic has accelerated the digital transformation of education."],
                ["txt", "Online learning platforms, virtual classrooms, and digital educational resources have become essential tools for students and educators."],
                ["txt", "While this shift has created new opportunities for flexible and personalized learning, it has also highlighted challenges such as the digital divide."]
            ]
        }
    ]
    
    # 专业课题目示例
    professional_examples = [
        {
            "data": [
                ["txt", "请解释计算机网络中的TCP/IP协议栈，并详细描述每一层的主要功能及其相互关系。"],
                ["txt", "分析TCP和UDP的区别，以及它们各自适用的应用场景。"]
            ]
        },
        {
            "data": [
                ["txt", "请分析数据库管理系统中的事务特性（ACID）及实现机制。"],
                ["txt", "讨论在分布式系统中保证事务一致性的挑战与解决方案。"]
            ]
        },
        {
            "data": [
                ["txt", "请详细阐述机器学习中的监督学习、无监督学习和强化学习的区别。"],
                ["txt", "结合实际应用场景进行分析，并讨论各自的优缺点。"]
            ]
        }
    ]
    
    # 导入示例题目
    if translation_count == 0:
        for i, example in enumerate(translation_examples):
            cursor.execute(
                'INSERT INTO translation_questions (question_index, question_data) VALUES (?, ?)',
                (i, json.dumps(example["data"]))
            )
    
    if professional_count == 0:
        for i, example in enumerate(professional_examples):
            cursor.execute(
                'INSERT INTO professional_questions (question_index, question_data) VALUES (?, ?)',
                (i, json.dumps(example["data"]))
            )
    
    conn.commit()
    conn.close()
    print("已加载示例题目到数据库")

# 定义一个函数来打开浏览器
def open_browser():
    """在新线程中打开浏览器"""
    # 延迟1.5秒，确保Flask服务器已经启动
    time.sleep(1.5)
    webbrowser.open('http://localhost:5000')
    print("已自动打开浏览器访问 http://localhost:5000")



# 重置考试状态相关API
@app.route('/api/exam-records/clear', methods=['DELETE'])
def clear_all_exam_records():
    """清空所有考试记录"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 清空考试记录表
        cursor.execute('DELETE FROM exam_records')
        deleted_records = cursor.rowcount

        # 重置自增ID（SQLite特有）
        cursor.execute('DELETE FROM sqlite_sequence WHERE name="exam_records"')

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'所有考试记录已清空，删除了 {deleted_records} 条记录，考生序号从1号开始'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清空考试记录失败: {str(e)}'
        }), 500

@app.route('/api/students/reset-counter', methods=['POST'])
def reset_student_counter():
    """重置学生编号计数器"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 重置学生编号计数器（通过删除所有记录实现）
        cursor.execute('DELETE FROM exam_records')
        deleted_records = cursor.rowcount

        # 重置自增ID（SQLite特有）
        cursor.execute('DELETE FROM sqlite_sequence WHERE name="exam_records"')

        # 如果有单独的计数器表，也需要重置
        # 这里假设学生编号是通过exam_records表的最大ID+1生成的

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'学生编号计数器已重置，删除了 {deleted_records} 条记录，考生序号从1号开始'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'重置学生编号计数器失败: {str(e)}'
        }), 500



if __name__ == '__main__':
    # 检测是否为便携式环境
    portable_python = os.path.exists('python_portable/python.exe')
    if portable_python:
        print("检测到便携式Python环境")

    # 确保应用启动时初始化数据库
    print("开始初始化数据库...")
    ensure_db_exists()
    print("数据库初始化完成，启动Flask应用")

    # 在新线程中启动浏览器，但仅在主进程中执行
    # 使用环境变量检查是否是重载器的主进程
    if os.environ.get('WERKZEUG_RUN_MAIN') != 'true':
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True  # 设置为守护线程，这样主程序退出时线程也会退出
        browser_thread.start()

@app.route('/api/questions/<question_type>/used', methods=['GET'])
def get_used_questions(question_type):
    """获取已使用的题目列表"""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        if question_type == 'translation':
            # 获取所有考试记录中使用的翻译题
            cursor.execute('''
            SELECT DISTINCT translation_question
            FROM exam_records
            WHERE translation_question IS NOT NULL
            AND translation_question != ''
            ''')
        elif question_type == 'professional':
            # 获取所有考试记录中使用的专业题
            cursor.execute('''
            SELECT DISTINCT professional_question
            FROM exam_records
            WHERE professional_question IS NOT NULL
            AND professional_question != ''
            ''')
        else:
            return jsonify({'success': False, 'error': '无效的题目类型'}), 400

        used_questions_raw = cursor.fetchall()
        conn.close()

        # 处理结果，提取题目编号
        used_questions = []
        for row in used_questions_raw:
            question_data = row[0]
            if question_data:
                try:
                    # 尝试解析JSON格式的题目数据
                    import json
                    question_obj = json.loads(question_data)
                    if isinstance(question_obj, dict) and 'question_index' in question_obj:
                        used_questions.append({
                            'question_index': question_obj['question_index'],
                            'question_data': question_data
                        })
                    elif isinstance(question_obj, dict) and 'questionIndex' in question_obj:
                        used_questions.append({
                            'question_index': question_obj['questionIndex'],
                            'question_data': question_data
                        })
                except (json.JSONDecodeError, TypeError):
                    # 如果不是JSON格式，尝试从字符串中提取编号
                    # 假设格式类似 "translation_3" 或 "professional_5"
                    if '_' in question_data:
                        parts = question_data.split('_')
                        if len(parts) >= 2 and parts[1].isdigit():
                            used_questions.append({
                                'question_index': int(parts[1]),
                                'question_data': question_data
                            })

        return jsonify({
            'success': True,
            'usedQuestions': used_questions,
            'count': len(used_questions)
        })

    except Exception as e:
        print(f"获取已使用题目失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    # 检测是否为便携式Python环境
    portable_python = 'python_portable' in sys.executable.lower()

    if portable_python:
        print("检测到便携式Python环境")

    # 确保应用启动时初始化数据库
    print("开始初始化数据库...")
    ensure_db_exists()
    print("数据库初始化完成，启动Flask应用")

    # 在新线程中启动浏览器，但仅在主进程中执行
    # 使用环境变量检查是否是重载器的主进程
    if os.environ.get('WERKZEUG_RUN_MAIN') != 'true':
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True  # 设置为守护线程，这样主程序退出时线程也会退出
        browser_thread.start()

    # 便携式环境下关闭调试模式以提高稳定性
    debug_mode = not portable_python
    app.run(debug=debug_mode, host='0.0.0.0', port=5000)