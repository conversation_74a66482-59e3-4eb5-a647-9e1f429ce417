#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
面试系统主应用 - 重构版
基于MVC架构，提供RESTful API接口
"""

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入API蓝图
from api.students import students_bp
from api.questions import questions_bp
from api.exam_records import exam_records_bp
from api.settings import settings_bp
from api.data import data_bp
from api.base import APIResponse, APIError, ErrorCodes

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    
    # 配置
    app.config['SECRET_KEY'] = 'interview_system_secret_key_2024'
    app.config['JSON_AS_ASCII'] = False
    app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True
    
    # 启用CORS
    CORS(app, resources={
        r"/api/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # 注册API蓝图
    app.register_blueprint(students_bp)
    app.register_blueprint(questions_bp)
    app.register_blueprint(exam_records_bp)
    app.register_blueprint(settings_bp)
    app.register_blueprint(data_bp)
    
    # 全局错误处理
    @app.errorhandler(404)
    def not_found(error):
        if request.path.startswith('/api/'):
            return APIResponse.error(
                ErrorCodes.INVALID_REQUEST,
                f"API端点不存在: {request.path}",
                status_code=404
            )
        return render_template('404.html'), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        if request.path.startswith('/api/'):
            return APIResponse.error(
                ErrorCodes.INVALID_REQUEST,
                f"不支持的请求方法: {request.method}",
                status_code=405
            )
        return jsonify({"error": "Method not allowed"}), 405
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"服务器内部错误: {error}")
        if request.path.startswith('/api/'):
            return APIResponse.error(
                ErrorCodes.UNKNOWN_ERROR,
                "服务器内部错误",
                status_code=500
            )
        return render_template('500.html'), 500
    
    # 主页路由
    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')
    
    # API根路径
    @app.route('/api')
    def api_root():
        """API根路径，返回API信息"""
        return APIResponse.success({
            "name": "Interview System API",
            "version": "2.0.0",
            "description": "研究生复试系统API接口",
            "endpoints": {
                "students": "/api/students",
                "questions": "/api/questions",
                "examRecords": "/api/exam-records",
                "settings": "/api/settings",
                "data": "/api/data"
            },
            "documentation": "/api/docs",
            "status": "active"
        })
    
    # API文档路由
    @app.route('/api/docs')
    def api_docs():
        """API文档"""
        return render_template('api_docs.html')
    
    # 健康检查
    @app.route('/api/health')
    def health_check():
        """健康检查"""
        try:
            # 检查数据库连接
            from api.base import db_manager
            db_manager.execute_query("SELECT 1", fetch_one=True)
            
            return APIResponse.success({
                "status": "healthy",
                "database": "connected",
                "timestamp": "2024-01-01T00:00:00Z"
            })
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return APIResponse.error(
                ErrorCodes.DATABASE_CONNECTION_ERROR,
                "数据库连接失败",
                str(e),
                503
            )
    
    # 系统信息
    @app.route('/api/system/info')
    def system_info():
        """系统信息"""
        import platform
        import psutil
        
        try:
            return APIResponse.success({
                "system": {
                    "platform": platform.system(),
                    "version": platform.version(),
                    "architecture": platform.architecture()[0]
                },
                "python": {
                    "version": platform.python_version(),
                    "implementation": platform.python_implementation()
                },
                "resources": {
                    "cpu_count": psutil.cpu_count(),
                    "memory_total": psutil.virtual_memory().total,
                    "memory_available": psutil.virtual_memory().available,
                    "disk_usage": psutil.disk_usage('/').percent
                },
                "application": {
                    "name": "Interview System",
                    "version": "2.0.0",
                    "environment": "development"
                }
            })
        except ImportError:
            # 如果psutil不可用，返回基本信息
            return APIResponse.success({
                "system": {
                    "platform": platform.system(),
                    "version": platform.version()
                },
                "python": {
                    "version": platform.python_version()
                },
                "application": {
                    "name": "Interview System",
                    "version": "2.0.0"
                }
            })
    
    # 静态文件路由
    @app.route('/assets/<path:filename>')
    def assets(filename):
        """静态资源"""
        return app.send_static_file(f'assets/{filename}')
    
    # 请求日志中间件
    @app.before_request
    def log_request():
        """记录请求日志"""
        if request.path.startswith('/api/'):
            logger.info(f"{request.method} {request.path} - {request.remote_addr}")
    
    @app.after_request
    def log_response(response):
        """记录响应日志"""
        if request.path.startswith('/api/'):
            logger.info(f"{request.method} {request.path} - {response.status_code}")
        return response
    
    return app

def init_database():
    """初始化数据库"""
    try:
        from api.base import db_manager
        
        # 检查数据库是否存在
        db_path = 'assets/data/interview_system.db'
        if not os.path.exists(db_path):
            logger.info("数据库不存在，创建新数据库...")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            # 创建数据库表
            create_database_tables()
            logger.info("数据库创建完成")
        else:
            logger.info("数据库已存在，检查表结构...")
            check_database_schema()
            
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

def create_database_tables():
    """创建数据库表"""
    from api.base import db_manager
    
    # 学生表
    db_manager.execute_query("""
        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_number TEXT UNIQUE NOT NULL,
            name TEXT,
            department TEXT,
            exam_date TEXT,
            current_step INTEGER DEFAULT 1,
            completed INTEGER DEFAULT 0,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 考试记录表
    db_manager.execute_query("""
        CREATE TABLE IF NOT EXISTS exam_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_number TEXT NOT NULL,
            exam_date TEXT NOT NULL,
            end_time TEXT,
            current_step INTEGER DEFAULT 1,
            status TEXT DEFAULT '准备中',
            total_duration INTEGER DEFAULT 0,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 学生题目记录表
    db_manager.execute_query("""
        CREATE TABLE IF NOT EXISTS student_questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            question_type TEXT NOT NULL,
            question_id TEXT,
            question_index INTEGER NOT NULL,
            question_data TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id)
        )
    """)
    
    # 翻译题表
    db_manager.execute_query("""
        CREATE TABLE IF NOT EXISTS translation_questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            question_index INTEGER UNIQUE NOT NULL,
            question_data TEXT NOT NULL,
            tags TEXT DEFAULT '[]',
            difficulty TEXT DEFAULT 'medium',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 专业题表
    db_manager.execute_query("""
        CREATE TABLE IF NOT EXISTS professional_questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            question_index INTEGER UNIQUE NOT NULL,
            question_data TEXT NOT NULL,
            tags TEXT DEFAULT '[]',
            difficulty TEXT DEFAULT 'medium',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 系统设置表
    db_manager.execute_query("""
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT NOT NULL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(category, key)
        )
    """)
    
    # 备份记录表
    db_manager.execute_query("""
        CREATE TABLE IF NOT EXISTS backup_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            backup_id TEXT UNIQUE NOT NULL,
            filename TEXT NOT NULL,
            size INTEGER,
            backup_info TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    """)

def check_database_schema():
    """检查数据库表结构"""
    from api.base import db_manager
    
    # 检查必要的表是否存在
    required_tables = [
        'students', 'exam_records', 'student_questions',
        'translation_questions', 'professional_questions', 'settings'
    ]
    
    for table in required_tables:
        result = db_manager.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
            (table,),
            fetch_one=True
        )
        
        if not result:
            logger.warning(f"表 {table} 不存在，重新创建...")
            create_database_tables()
            break

if __name__ == '__main__':
    try:
        # 初始化数据库
        init_database()
        
        # 创建应用
        app = create_app()
        
        # 启动应用
        logger.info("启动面试系统...")
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            threaded=True
        )
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        sys.exit(1)
