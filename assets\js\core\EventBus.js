/**
 * EventBus - 全局事件总线
 * 负责系统内各模块间的事件通信
 */

class EventBus {
    constructor() {
        this.listeners = new Map();
        this.onceListeners = new Map();
        this.eventHistory = [];
        this.maxHistorySize = 1000;
        this.isDebugMode = false;
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     * @param {Object} options - 选项
     */
    on(event, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('回调函数必须是一个函数');
        }

        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }

        const listener = {
            callback,
            context: options.context || null,
            priority: options.priority || 0,
            id: this.generateListenerId()
        };

        this.listeners.get(event).push(listener);
        
        // 按优先级排序
        this.listeners.get(event).sort((a, b) => b.priority - a.priority);

        if (this.isDebugMode) {
            console.log(`EventBus: 添加监听器 ${event}`, listener);
        }

        return listener.id;
    }

    /**
     * 添加一次性事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     * @param {Object} options - 选项
     */
    once(event, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('回调函数必须是一个函数');
        }

        if (!this.onceListeners.has(event)) {
            this.onceListeners.set(event, []);
        }

        const listener = {
            callback,
            context: options.context || null,
            priority: options.priority || 0,
            id: this.generateListenerId()
        };

        this.onceListeners.get(event).push(listener);
        
        // 按优先级排序
        this.onceListeners.get(event).sort((a, b) => b.priority - a.priority);

        if (this.isDebugMode) {
            console.log(`EventBus: 添加一次性监听器 ${event}`, listener);
        }

        return listener.id;
    }

    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function|string} callbackOrId - 回调函数或监听器ID
     */
    off(event, callbackOrId) {
        // 移除普通监听器
        if (this.listeners.has(event)) {
            const listeners = this.listeners.get(event);
            const index = listeners.findIndex(listener => 
                listener.callback === callbackOrId || listener.id === callbackOrId
            );
            
            if (index > -1) {
                listeners.splice(index, 1);
                if (listeners.length === 0) {
                    this.listeners.delete(event);
                }
                
                if (this.isDebugMode) {
                    console.log(`EventBus: 移除监听器 ${event}`);
                }
                return true;
            }
        }

        // 移除一次性监听器
        if (this.onceListeners.has(event)) {
            const listeners = this.onceListeners.get(event);
            const index = listeners.findIndex(listener => 
                listener.callback === callbackOrId || listener.id === callbackOrId
            );
            
            if (index > -1) {
                listeners.splice(index, 1);
                if (listeners.length === 0) {
                    this.onceListeners.delete(event);
                }
                
                if (this.isDebugMode) {
                    console.log(`EventBus: 移除一次性监听器 ${event}`);
                }
                return true;
            }
        }

        return false;
    }

    /**
     * 移除所有事件监听器
     * @param {string} event - 事件名称（可选）
     */
    removeAllListeners(event = null) {
        if (event) {
            this.listeners.delete(event);
            this.onceListeners.delete(event);
            
            if (this.isDebugMode) {
                console.log(`EventBus: 移除所有 ${event} 监听器`);
            }
        } else {
            this.listeners.clear();
            this.onceListeners.clear();
            
            if (this.isDebugMode) {
                console.log('EventBus: 移除所有监听器');
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     * @param {Object} options - 选项
     */
    emit(event, data = null, options = {}) {
        const eventInfo = {
            event,
            data,
            timestamp: new Date().toISOString(),
            source: options.source || 'unknown'
        };

        // 记录事件历史
        this.addToHistory(eventInfo);

        if (this.isDebugMode) {
            console.log(`EventBus: 触发事件 ${event}`, data);
        }

        let listenerCount = 0;
        const errors = [];

        // 执行普通监听器
        if (this.listeners.has(event)) {
            const listeners = [...this.listeners.get(event)];
            
            for (const listener of listeners) {
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data, eventInfo);
                    } else {
                        listener.callback(data, eventInfo);
                    }
                    listenerCount++;
                } catch (error) {
                    console.error(`EventBus: 监听器执行错误 [${event}]:`, error);
                    errors.push({ listener, error });
                }
            }
        }

        // 执行一次性监听器
        if (this.onceListeners.has(event)) {
            const listeners = [...this.onceListeners.get(event)];
            this.onceListeners.delete(event); // 清除一次性监听器
            
            for (const listener of listeners) {
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data, eventInfo);
                    } else {
                        listener.callback(data, eventInfo);
                    }
                    listenerCount++;
                } catch (error) {
                    console.error(`EventBus: 一次性监听器执行错误 [${event}]:`, error);
                    errors.push({ listener, error });
                }
            }
        }

        // 如果有错误，触发错误事件
        if (errors.length > 0) {
            this.emit('listenerError', { event, errors }, { source: 'EventBus' });
        }

        return {
            event,
            listenerCount,
            errors: errors.length,
            timestamp: eventInfo.timestamp
        };
    }

    /**
     * 异步触发事件
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     * @param {Object} options - 选项
     */
    async emitAsync(event, data = null, options = {}) {
        const eventInfo = {
            event,
            data,
            timestamp: new Date().toISOString(),
            source: options.source || 'unknown'
        };

        // 记录事件历史
        this.addToHistory(eventInfo);

        if (this.isDebugMode) {
            console.log(`EventBus: 异步触发事件 ${event}`, data);
        }

        let listenerCount = 0;
        const errors = [];

        // 执行普通监听器
        if (this.listeners.has(event)) {
            const listeners = [...this.listeners.get(event)];
            
            for (const listener of listeners) {
                try {
                    const result = listener.context 
                        ? listener.callback.call(listener.context, data, eventInfo)
                        : listener.callback(data, eventInfo);
                    
                    if (result instanceof Promise) {
                        await result;
                    }
                    listenerCount++;
                } catch (error) {
                    console.error(`EventBus: 异步监听器执行错误 [${event}]:`, error);
                    errors.push({ listener, error });
                }
            }
        }

        // 执行一次性监听器
        if (this.onceListeners.has(event)) {
            const listeners = [...this.onceListeners.get(event)];
            this.onceListeners.delete(event);
            
            for (const listener of listeners) {
                try {
                    const result = listener.context 
                        ? listener.callback.call(listener.context, data, eventInfo)
                        : listener.callback(data, eventInfo);
                    
                    if (result instanceof Promise) {
                        await result;
                    }
                    listenerCount++;
                } catch (error) {
                    console.error(`EventBus: 异步一次性监听器执行错误 [${event}]:`, error);
                    errors.push({ listener, error });
                }
            }
        }

        return {
            event,
            listenerCount,
            errors: errors.length,
            timestamp: eventInfo.timestamp
        };
    }

    /**
     * 等待事件触发
     * @param {string} event - 事件名称
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise} Promise对象
     */
    waitFor(event, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                this.off(event, listener);
                reject(new Error(`等待事件 ${event} 超时`));
            }, timeout);

            const listener = (data) => {
                clearTimeout(timeoutId);
                resolve(data);
            };

            this.once(event, listener);
        });
    }

    /**
     * 检查是否有监听器
     * @param {string} event - 事件名称
     * @returns {boolean} 是否有监听器
     */
    hasListeners(event) {
        return (this.listeners.has(event) && this.listeners.get(event).length > 0) ||
               (this.onceListeners.has(event) && this.onceListeners.get(event).length > 0);
    }

    /**
     * 获取监听器数量
     * @param {string} event - 事件名称
     * @returns {number} 监听器数量
     */
    getListenerCount(event) {
        let count = 0;
        
        if (this.listeners.has(event)) {
            count += this.listeners.get(event).length;
        }
        
        if (this.onceListeners.has(event)) {
            count += this.onceListeners.get(event).length;
        }
        
        return count;
    }

    /**
     * 获取所有事件名称
     * @returns {Array} 事件名称数组
     */
    getEventNames() {
        const events = new Set();
        
        for (const event of this.listeners.keys()) {
            events.add(event);
        }
        
        for (const event of this.onceListeners.keys()) {
            events.add(event);
        }
        
        return Array.from(events);
    }

    /**
     * 添加到事件历史
     * @param {Object} eventInfo - 事件信息
     */
    addToHistory(eventInfo) {
        this.eventHistory.push(eventInfo);
        
        // 限制历史记录大小
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }

    /**
     * 获取事件历史
     * @param {number} limit - 限制数量
     * @returns {Array} 事件历史
     */
    getEventHistory(limit = 100) {
        return this.eventHistory.slice(-limit);
    }

    /**
     * 清除事件历史
     */
    clearHistory() {
        this.eventHistory = [];
    }

    /**
     * 生成监听器ID
     * @returns {string} 监听器ID
     */
    generateListenerId() {
        return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 设置调试模式
     * @param {boolean} enabled - 是否启用
     */
    setDebugMode(enabled) {
        this.isDebugMode = enabled;
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalEvents: this.getEventNames().length,
            totalListeners: Array.from(this.listeners.values())
                .reduce((total, listeners) => total + listeners.length, 0),
            totalOnceListeners: Array.from(this.onceListeners.values())
                .reduce((total, listeners) => total + listeners.length, 0),
            historySize: this.eventHistory.length,
            maxHistorySize: this.maxHistorySize
        };
    }

    /**
     * 销毁事件总线
     */
    destroy() {
        this.removeAllListeners();
        this.clearHistory();
        
        if (this.isDebugMode) {
            console.log('EventBus: 已销毁');
        }
    }
}

// 创建全局事件总线实例
const eventBus = new EventBus();

// 导出EventBus类和实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EventBus, eventBus };
} else {
    window.EventBus = EventBus;
    window.eventBus = eventBus;
}
