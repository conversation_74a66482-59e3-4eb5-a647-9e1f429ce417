<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研究生复试系统 - 题库管理</title>
    <link rel="stylesheet" href="css/editor.css">
    <style>
        :root {
            --primary-color: #4a6da7;
            --secondary-color: #5a8ade;
            --background-color: #f4f6f9;
            --text-color: #333;
            --border-color: #ddd;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --modal-bg: #fff;
        }

        body.dark-theme {
            --primary-color: #375a8c;
            --secondary-color: #4a77be;
            --background-color: #2a2a2a;
            --text-color: #eee;
            --border-color: #444;
            --modal-bg: #333;
        }

        body.green-theme {
            --primary-color: #2e7d32;
            --secondary-color: #43a047;
            --background-color: #e8f5e9;
            --text-color: #1b5e20;
            --border-color: #a5d6a7;
            --modal-bg: #fff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--background-color);
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        h1, h2, h3 {
            color: var(--primary-color);
        }

        h1 {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary-color);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: var(--secondary-color);
        }

        .btn-success {
            background: var(--success-color);
        }

        .btn-success:hover {
            background: #27ae60;
        }

        .btn-warning {
            background: var(--warning-color);
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: var(--danger-color);
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            margin-right: 5px;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
        }

        .tab.active {
            background: white;
            border-color: var(--border-color);
            border-bottom: 1px solid white;
            margin-bottom: -1px;
            color: var(--primary-color);
            font-weight: bold;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .question-list {
            margin-bottom: 30px;
        }

        .question-item {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--border-color);
        }

        .question-title {
            display: flex;
            align-items: center;
            font-weight: bold;
            color: var(--primary-color);
        }

        .question-checkbox {
            margin-right: 8px;
        }

        .question-actions {
            display: flex;
            gap: 10px;
        }

        .question-content {
            margin-bottom: 10px;
        }

        .question-content p {
            margin-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"],
        textarea,
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: inherit;
            font-size: 14px;
        }

        textarea {
            min-height: 100px;
            resize: vertical;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
            background: var(--primary-color);
            color: white;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 22px;
            cursor: pointer;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 15px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .question-items {
            border: 1px solid var(--border-color);
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }

        .add-item-btn {
            margin-top: 10px;
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }

        .add-item-btn:hover {
            background-color: #0069d9;
        }

        .item-editor {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            border: 1px solid #e0e0e0;
        }

        .item-type {
            width: 100px;
            height: 36px;
            border-radius: 4px;
            border: 1px solid #ced4da;
            background-color: white;
        }

        .item-content {
            flex: 1;
            height: 36px;
            border-radius: 4px;
            border: 1px solid #ced4da;
            padding: 5px 10px;
        }

        .item-actions {
            display: flex;
            gap: 5px;
        }

        .item-actions button {
            border: none;
            background: #f8f9fa;
            color: #dc3545;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
        }

        .item-actions button:hover {
            background: #e2e6ea;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s;
            z-index: 1100;
        }

        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }

        .nav-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .search-box input {
            flex: 1;
        }

        .preview-image {
            max-width: 100%;
            max-height: 200px;
            margin-top: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .page-item {
            display: inline-block;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
        }

        .page-item.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .no-questions {
            text-align: center;
            padding: 30px;
            color: #777;
            background-color: #f9f9f9;
            border-radius: 5px;
            margin: 20px 0;
        }

        .no-questions p {
            margin: 5px 0;
        }

        .no-questions p:first-child {
            font-size: 18px;
            margin-bottom: 15px;
        }

        /* 批量操作相关样式 */
        .batch-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .select-all-container {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .bulk-action-select {
            min-width: 120px;
        }

        .checkbox-container {
            margin-right: 10px;
        }

        /* 标签样式 */
        .tag {
            display: inline-block;
            background: var(--secondary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .tag-container {
            margin-top: 5px;
            display: flex;
            flex-wrap: wrap;
        }

        .tag-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }

        .tag-item {
            cursor: pointer;
            padding: 5px 10px;
            background: #f0f0f0;
            border-radius: 15px;
            font-size: 12px;
            transition: all 0.2s;
            border: 1px solid #ddd;
        }

        .tag-item:hover {
            background: #e0e0e0;
        }

        .tag-item.selected {
            background: var(--primary-color);
            color: white;
        }

        /* 难度标记样式 */
        .difficulty {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }

        .difficulty-easy {
            background-color: #c8e6c9;
            color: #2e7d32;
        }

        .difficulty-medium {
            background-color: #fff9c4;
            color: #f57f17;
        }

        .difficulty-hard {
            background-color: #ffcdd2;
            color: #c62828;
        }

        /* 批量导入模态框样式 */
        .batch-import-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .batch-import-tab {
            padding: 8px 15px;
            cursor: pointer;
            margin-right: 5px;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
        }

        .batch-import-tab.active {
            background: white;
            border-color: var(--border-color);
            border-bottom: 1px solid white;
            margin-bottom: -1px;
            color: var(--primary-color);
            font-weight: bold;
        }

        .batch-import-content {
            display: none;
        }

        .batch-import-content.active {
            display: block;
        }

        .preview-area {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
            border: 1px solid var(--border-color);
            padding: 10px;
            border-radius: 5px;
        }

        .format-guide {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 13px;
        }

        .format-guide pre {
            margin: 10px 0;
            padding: 8px;
            background: #eee;
            border-radius: 3px;
            overflow-x: auto;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .item-editor {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .item-type, .item-content {
                width: 100%;
                margin-bottom: 5px;
            }
        }

        /* 设置相关样式 */
        .theme-selector {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }

        .theme-option {
            cursor: pointer;
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            transition: all 0.3s;
            width: 30%;
        }

        .theme-option:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .theme-option.selected {
            background-color: var(--primary-color);
            color: white;
        }

        .theme-preview {
            height: 60px;
            border-radius: 5px;
            margin-bottom: 5px;
            border: 1px solid var(--border-color);
        }

        .default-theme {
            background: linear-gradient(to bottom, #f4f6f9 50%, #4a6da7 50%);
        }

        .dark-theme {
            background: linear-gradient(to bottom, #2a2a2a 50%, #375a8c 50%);
        }

        .green-theme {
            background: linear-gradient(to bottom, #e8f5e9 50%, #2e7d32 50%);
        }

        /* 添加一些辅助样式，提高用户体验 */
        .form-loading {
            position: relative;
            min-height: 100px;
        }
        
        .form-loading::after {
            content: "处理中...";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: var(--primary-color);
        }
        
        /* 添加操作反馈提示 */
        .action-hint {
            background-color: #f8f9fa;
            border-left: 4px solid var(--primary-color);
            padding: 8px 12px;
            margin-bottom: 15px;
            font-size: 14px;
            display: none;
        }
        
        .action-hint.show {
            display: block;
        }
        
        /* 改进标签样式，增加删除按钮 */
        .tag {
            position: relative;
            padding-right: 20px;
        }
        
        .tag .remove-tag {
            position: absolute;
            right: 5px;
            top: 2px;
            font-size: 10px;
            cursor: pointer;
            opacity: 0.7;
        }
        
        .tag .remove-tag:hover {
            opacity: 1;
        }
        
        /* 预览区域的增强样式 */
        .preview-item-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            padding: 8px;
            margin-top: 10px;
        }
        
        /* 移动设备优化 */
        @media (max-width: 768px) {
            .batch-actions {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .select-all-container {
                margin-bottom: 10px;
            }
            
            .search-box {
                flex-wrap: wrap;
            }
            
            .search-box input,
            .search-box select,
            .search-box button {
                margin-bottom: 8px;
            }
        }

        /* 编辑/删除按钮样式优化 */
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        /* 加载指示器改进 */
        #loadingIndicator {
            display: none;
            opacity: 1;
            transform: translateY(0);
        }
        
        #loadingIndicator.show {
            display: block;
        }
        
        /* 题目序号样式 */
        .question-serial {
            display: inline-block;
            min-width: 24px;
            color: var(--primary-color);
            font-weight: bold;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-bar">
            <h1>研究生复试系统 - 题库管理</h1>
            <div>
                <a href="index.html" class="btn">返回主系统</a>
                <button id="settingsBtn" class="btn">设置</button>
                <button id="importBtn" class="btn btn-warning">导入题库</button>
                <button id="exportBtn" class="btn btn-success">导出题库</button>
            </div>
        </div>

        <div class="tabs">
            <div class="tab active" data-tab="translation">翻译题目管理</div>
            <div class="tab" data-tab="professional">专业题目管理</div>
        </div>

        <div class="tab-content active" id="translation-tab">
            <div class="header">
                <h2>翻译题库</h2>
                <div>
                    <button id="addTranslationBtn" class="btn btn-success">添加新题目</button>
                    <button id="batchAddTranslationBtn" class="btn btn-success">批量添加题目</button>
                </div>
            </div>
            
            <!-- 添加操作提示 -->
            <div id="translationActionHint" class="action-hint">
                操作提示将显示在这里
            </div>

            <div class="batch-actions">
                <div class="select-all-container">
                    <input type="checkbox" id="selectAllTranslation">
                    <label for="selectAllTranslation">全选</label>
                </div>
                <select class="bulk-action-select" id="bulkActionTranslation">
                    <option value="">批量操作</option>
                    <option value="delete">删除选中</option>
                    <option value="tag">添加标签</option>
                    <option value="difficulty">设置难度</option>
                    <option value="export">导出选中</option>
                </select>
                <button id="applyBulkActionTranslation" class="btn">应用</button>
            </div>

            <div class="search-box">
                <input type="text" id="searchTranslation" placeholder="搜索题目关键词...">
                <select id="tagFilterTranslation">
                    <option value="">所有标签</option>
                    <!-- 标签选项将动态加载 -->
                </select>
                <select id="difficultyFilterTranslation">
                    <option value="">所有难度</option>
                    <option value="easy">简单</option>
                    <option value="medium">中等</option>
                    <option value="hard">困难</option>
                </select>
                <button id="searchTranslationBtn" class="btn">搜索</button>
                <button id="resetTranslationBtn" class="btn btn-warning">重置</button>
            </div>

            <div id="translationList" class="question-list">
                <!-- 翻译题目将在这里动态加载 -->
                <div class="no-questions">
                    <p>暂无题目数据</p>
                    <p>您可以点击"添加新题目"或"批量添加题目"来创建题目</p>
                    <p>或者点击"初始化题库"载入示例题目</p>
                </div>
            </div>

            <!-- 修改分页部分 -->
            <div class="pagination-controls" style="display: flex; align-items: center; margin-top: 10px;">
                <label for="pageSizeTranslation" style="margin-right: 5px;">每页显示:</label>
                <select id="pageSizeTranslation" class="form-control" style="width: auto; display: inline-block; margin-right: 15px;">
                    <option value="10">10</option>
                    <option value="30">30</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <div id="translationPagination" class="pagination">
                    <!-- 分页控件将在这里动态生成 -->
                </div>
            </div>
        </div>

        <div class="tab-content" id="professional-tab">
            <div class="header">
                <h2>专业题库</h2>
                <div>
                    <button id="addProfessionalBtn" class="btn btn-success">添加新题目</button>
                    <button id="batchAddProfessionalBtn" class="btn btn-success">批量添加题目</button>
                </div>
            </div>
            
            <!-- 添加操作提示 -->
            <div id="professionalActionHint" class="action-hint">
                操作提示将显示在这里
            </div>

            <div class="batch-actions">
                <div class="select-all-container">
                    <input type="checkbox" id="selectAllProfessional">
                    <label for="selectAllProfessional">全选</label>
                </div>
                <select class="bulk-action-select" id="bulkActionProfessional">
                    <option value="">批量操作</option>
                    <option value="delete">删除选中</option>
                    <option value="tag">添加标签</option>
                    <option value="difficulty">设置难度</option>
                    <option value="export">导出选中</option>
                </select>
                <button id="applyBulkActionProfessional" class="btn">应用</button>
            </div>

            <div class="search-box">
                <input type="text" id="searchProfessional" placeholder="搜索题目关键词...">
                <select id="tagFilterProfessional">
                    <option value="">所有标签</option>
                    <!-- 标签选项将动态加载 -->
                </select>
                <select id="difficultyFilterProfessional">
                    <option value="">所有难度</option>
                    <option value="easy">简单</option>
                    <option value="medium">中等</option>
                    <option value="hard">困难</option>
                </select>
                <button id="searchProfessionalBtn" class="btn">搜索</button>
                <button id="resetProfessionalBtn" class="btn btn-warning">重置</button>
            </div>

            <div id="professionalList" class="question-list">
                <!-- 专业题目将在这里动态加载 -->
                <div class="no-questions">
                    <p>暂无题目数据</p>
                    <p>您可以点击"添加新题目"或"批量添加题目"来创建题目</p>
                    <p>或者点击"初始化题库"载入示例题目</p>
                </div>
            </div>

            <!-- 修改分页部分 -->
            <div class="pagination-controls" style="display: flex; align-items: center; margin-top: 10px;">
                <label for="pageSizeProfessional" style="margin-right: 5px;">每页显示:</label>
                <select id="pageSizeProfessional" class="form-control" style="width: auto; display: inline-block; margin-right: 15px;">
                    <option value="10">10</option>
                    <option value="30">30</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <div id="professionalPagination" class="pagination">
                    <!-- 分页控件将在这里动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 翻译题目编辑模态框 -->
    <div id="translationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="translationModalTitle">添加翻译题目</h3>
                <button class="close-btn" aria-label="关闭">&times;</button>
            </div>
            <div class="modal-body">
                <form id="translationForm">
                    <input type="hidden" id="translationId" value="">
                    <input type="hidden" id="translationIndex" value="">
                    
                    <div class="form-group">
                        <label for="translationTags">标签:</label>
                        <input type="text" id="translationTags" placeholder="输入标签，用逗号分隔">
                        <div class="tag-selector" id="commonTranslationTags">
                            <span class="tag-item" data-tag="英译汉">英译汉</span>
                            <span class="tag-item" data-tag="汉译英">汉译英</span>
                            <span class="tag-item" data-tag="专业术语">专业术语</span>
                            <span class="tag-item" data-tag="长句翻译">长句翻译</span>
                            <span class="tag-item" data-tag="文献摘要">文献摘要</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="translationDifficulty">难度:</label>
                        <select id="translationDifficulty">
                            <option value="easy">简单</option>
                            <option value="medium" selected>中等</option>
                            <option value="hard">困难</option>
                        </select>
                    </div>
                    
                    <div class="question-items" id="translationItems">
                        <!-- 题目项将在这里动态生成 -->
                    </div>
                    
                    <button type="button" id="addTranslationItemBtn" class="btn add-item-btn">添加题目项</button>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning cancel-btn">取消</button>
                <button type="button" id="saveTranslationBtn" class="btn btn-success">保存</button>
            </div>
        </div>
    </div>

    <!-- 专业题目编辑模态框 -->
    <div id="professionalModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="professionalModalTitle">添加专业题目</h3>
                <button class="close-btn" aria-label="关闭">&times;</button>
            </div>
            <div class="modal-body">
                <form id="professionalForm">
                    <input type="hidden" id="professionalId" value="">
                    <input type="hidden" id="professionalIndex" value="">
                    
                    <div class="form-group">
                        <label for="professionalTags">标签:</label>
                        <input type="text" id="professionalTags" placeholder="输入标签，用逗号分隔">
                        <div class="tag-selector" id="commonProfessionalTags">
                            <span class="tag-item" data-tag="算法">算法</span>
                            <span class="tag-item" data-tag="数据结构">数据结构</span>
                            <span class="tag-item" data-tag="操作系统">操作系统</span>
                            <span class="tag-item" data-tag="计算机网络">计算机网络</span>
                            <span class="tag-item" data-tag="数据库">数据库</span>
                            <span class="tag-item" data-tag="软件工程">软件工程</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="professionalDifficulty">难度:</label>
                        <select id="professionalDifficulty">
                            <option value="easy">简单</option>
                            <option value="medium" selected>中等</option>
                            <option value="hard">困难</option>
                        </select>
                    </div>
                    
                    <div class="question-items" id="professionalItems">
                        <!-- 题目项将在这里动态生成 -->
                    </div>
                    
                    <button type="button" id="addProfessionalItemBtn" class="btn add-item-btn">添加题目项</button>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning cancel-btn">取消</button>
                <button type="button" id="saveProfessionalBtn" class="btn btn-success">保存</button>
            </div>
        </div>
    </div>

    <!-- 批量添加题目模态框 -->
    <div id="batchAddModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="batchAddModalTitle">批量添加题目</h3>
                <button class="close-btn" aria-label="关闭">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="batchQuestionType">题目类型:</label>
                    <select id="batchQuestionType">
                        <option value="translation">翻译题目</option>
                        <option value="professional">专业题目</option>
                    </select>
                </div>
                
                <div class="batch-import-tabs">
                    <div class="batch-import-tab active" data-tab="text-input">文本输入</div>
                    <div class="batch-import-tab" data-tab="file-upload">文件上传</div>
                </div>
                
                <div class="batch-import-content active" id="text-input-content">
                    <div class="form-group">
                        <label for="batchTextInput">输入题目:</label>
                        <textarea id="batchTextInput" placeholder="请按照格式说明输入多个题目..." rows="10"></textarea>
                    </div>
                    
                    <div class="format-guide">
                        <h4>格式说明:</h4>
                        <p>每道题目使用三个短横线(---)分隔，题目内部结构如下：</p>
                        <pre>题目：问题内容
题目：问题内容
题目：问题内容
---
题目：下一个问题内容
...</pre>
                        <p>注：不需要的字段可以省略</p>
                        <button type="button" id="copyExampleBtn" class="btn">复制示例</button>
                    </div>
                </div>
                
                <div class="batch-import-content" id="file-upload-content">
                    <div class="form-group">
                        <label for="batchFileUpload">上传文件:</label>
                        <input type="file" id="batchFileUpload" accept=".csv,.xlsx,.txt">
                    </div>
                    
                    <div class="form-group">
                        <button type="button" id="downloadTemplateBtn" class="btn">下载模板</button>
                    </div>
                    
                    <div class="format-guide">
                        <h4>文件格式说明:</h4>
                        <p>支持Excel(.xlsx)和CSV(.csv)格式，文件第一行为表头，必须包含以下列：</p>
                        <p>题目、选项、答案、难度(可选)、标签(可选)</p>
                        <p>请下载模板文件查看具体格式要求</p>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="button" id="previewBatchBtn" class="btn">预览</button>
                </div>
                
                <div class="preview-area" id="batchPreviewArea" style="display: none;">
                    <h4>预览结果：<span id="previewCount"></span></h4>
                    <div id="batchPreviewContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning cancel-btn">取消</button>
                <button type="button" id="importBatchBtn" class="btn btn-success">导入</button>
            </div>
        </div>
    </div>

    <!-- 标签批量添加模态框 -->
    <div id="tagModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加标签</h3>
                <button class="close-btn" aria-label="关闭">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="bulkTags">输入标签:</label>
                    <input type="text" id="bulkTags" placeholder="输入标签，用逗号分隔">
                </div>
                <p>或选择常用标签：</p>
                <div class="tag-selector" id="commonBulkTags">
                    <!-- 常用标签将在这里动态生成 -->
                </div>
                <div id="selectedTagsPreview" class="tag-container" style="margin-top: 15px; display: none;">
                    <h4>已选标签：</h4>
                    <!-- 选中的标签将在这里显示 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning cancel-btn">取消</button>
                <button type="button" id="applyTagsBtn" class="btn btn-success">应用</button>
            </div>
        </div>
    </div>

    <!-- 难度批量设置模态框 -->
    <div id="difficultyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>设置难度</h3>
                <button class="close-btn" aria-label="关闭">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="bulkDifficulty">选择难度:</label>
                    <select id="bulkDifficulty">
                        <option value="easy">简单</option>
                        <option value="medium" selected>中等</option>
                        <option value="hard">困难</option>
                    </select>
                </div>
                <div class="difficulty-preview">
                    <p>预览：</p>
                    <div class="difficulty difficulty-easy">简单</div>
                    <div class="difficulty difficulty-medium">中等</div>
                    <div class="difficulty difficulty-hard">困难</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning cancel-btn">取消</button>
                <button type="button" id="applyDifficultyBtn" class="btn btn-success">应用</button>
            </div>
        </div>
    </div>

    <!-- 导入题库模态框 -->
    <div id="importModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>导入题库</h3>
                <button class="close-btn" aria-label="关闭">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="importFile">选择JSON文件:</label>
                    <input type="file" id="importFile" accept=".json">
                </div>
                <div class="form-group">
                    <label>题库类型:</label>
                    <div>
                        <input type="radio" id="importTranslation" name="importType" value="translation" checked>
                        <label for="importTranslation">翻译题库</label>
                    </div>
                    <div>
                        <input type="radio" id="importProfessional" name="importType" value="professional">
                        <label for="importProfessional">专业题库</label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="action-hint">
                        <p>导入将会添加新题目，不会覆盖现有题目。</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning cancel-btn">取消</button>
                <button type="button" id="confirmImportBtn" class="btn btn-success">导入</button>
            </div>
        </div>
    </div>

    <!-- 设置模态框 -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>系统设置</h3>
                <button class="close-btn" aria-label="关闭">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="pageSize">每页显示题目数量:</label>
                    <select id="pageSize">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="15">15</option>
                        <option value="20">20</option>
                        <option value="30">30</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>界面主题:</label>
                    <div class="theme-selector">
                        <div class="theme-option" data-theme="default">
                            <div class="theme-preview default-theme"></div>
                            <span>默认主题</span>
                        </div>
                        <div class="theme-option" data-theme="dark">
                            <div class="theme-preview dark-theme"></div>
                            <span>暗黑主题</span>
                        </div>
                        <div class="theme-option" data-theme="green">
                            <div class="theme-preview green-theme"></div>
                            <span>绿色主题</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning cancel-btn">取消</button>
                <button type="button" id="saveSettingsBtn" class="btn btn-success">保存设置</button>
            </div>
        </div>
    </div>

    <!-- Toast消息 -->
    <div id="toast" class="toast"></div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="toast" style="background-color: rgba(0,0,0,0.7);">处理中...</div>

    <script src="src/editor.js"></script>
    <script>
    // 模态框初始化和题目项管理
    document.addEventListener('DOMContentLoaded', function() {
        // 创建题目项编辑器
        function createItemEditor(type = '文本', content = '') {
            const itemEditor = document.createElement('div');
            itemEditor.className = 'item-editor';
            
            // 创建类型选择器
            const itemType = document.createElement('select');
            itemType.className = 'item-type';
            
            const types = ['文本', '题目', '解析', '答案'];
            types.forEach(function(t) {
                const option = document.createElement('option');
                option.value = t;
                option.textContent = t;
                if (t === type) {
                    option.selected = true;
                }
                itemType.appendChild(option);
            });
            
            // 创建内容输入框
            const itemContent = document.createElement('input');
            itemContent.type = 'text';
            itemContent.className = 'item-content';
            itemContent.placeholder = '输入题目内容...';
            itemContent.value = content;
            
            // 创建操作区域
            const itemActions = document.createElement('div');
            itemActions.className = 'item-actions';
            
            // 上移按钮
            const moveUpBtn = document.createElement('button');
            moveUpBtn.type = 'button';
            moveUpBtn.innerHTML = '↑';
            moveUpBtn.title = '上移';
            
            // 下移按钮
            const moveDownBtn = document.createElement('button');
            moveDownBtn.type = 'button';
            moveDownBtn.innerHTML = '↓';
            moveDownBtn.title = '下移';
            
            // 删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.innerHTML = '×';
            deleteBtn.title = '删除';
            
            // 添加按钮到操作区域
            itemActions.appendChild(moveUpBtn);
            itemActions.appendChild(moveDownBtn);
            itemActions.appendChild(deleteBtn);
            
            // 组装编辑器
            itemEditor.appendChild(itemType);
            itemEditor.appendChild(itemContent);
            itemEditor.appendChild(itemActions);
            
            return itemEditor;
        }
        
        // 初始化翻译题目项
        const translationItems = document.getElementById('translationItems');
        if (translationItems && translationItems.children.length === 0) {
            translationItems.appendChild(createItemEditor('文本', ''));
        }
        
        // 初始化专业题目项
        const professionalItems = document.getElementById('professionalItems');
        if (professionalItems && professionalItems.children.length === 0) {
            professionalItems.appendChild(createItemEditor('文本', ''));
        }
    });
    </script>
    <script>
    // 添加一些辅助功能脚本
    document.addEventListener('DOMContentLoaded', function() {
        // 复制格式示例到剪贴板
        document.getElementById('copyExampleBtn').addEventListener('click', function() {
            const example = `题目：批量添加的第一个题目
题目：批量添加的第二个题目
题目：批量添加的第三个题目
---
题目：批量添加的第四个题目
题目：批量添加的第五个题目
题目：批量添加的第六个题目
`;
            
            const textarea = document.getElementById('batchTextInput');
            textarea.value = example;
            textarea.focus();
            textarea.select();
            
            try {
                document.execCommand('copy');
                alert('示例已复制到文本框');
            } catch (err) {
                console.error('复制失败', err);
            }
        });
        
        // 标签选择时实时预览
        document.querySelectorAll('#commonBulkTags .tag-item').forEach(item => {
            item.addEventListener('click', function() {
                this.classList.toggle('selected');
                updateSelectedTagsPreview();
            });
        });
        
        document.getElementById('bulkTags').addEventListener('input', function() {
            updateSelectedTagsPreview();
        });
        
        function updateSelectedTagsPreview() {
            const tagsInput = document.getElementById('bulkTags').value;
            const inputTags = tagsInput ? tagsInput.split(',').map(t => t.trim()).filter(t => t) : [];
            
            const selectedTagItems = document.querySelectorAll('#commonBulkTags .tag-item.selected');
            const selectedTags = Array.from(selectedTagItems).map(el => el.textContent);
            
            const allTags = [...new Set([...inputTags, ...selectedTags])];
            
            const preview = document.getElementById('selectedTagsPreview');
            if (allTags.length > 0) {
                preview.style.display = 'block';
                preview.innerHTML = '<h4>已选标签：</h4>' + 
                    allTags.map(tag => `<span class="tag">${tag}</span>`).join('');
            } else {
                preview.style.display = 'none';
            }
        }
        
        // 模态框关闭按钮
        document.querySelectorAll('.modal .close-btn, .modal .cancel-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                modal.classList.remove('show');
            });
        });
    });
    </script>
</body>
</html>
