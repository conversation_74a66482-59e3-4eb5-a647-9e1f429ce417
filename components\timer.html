<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计时器组件</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/modern-ui.css">
    <style>
        /* 可拖拽计时器样式 */
        .draggable-timer {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            cursor: move;
            user-select: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-2xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all var(--transition-base);
        }
        
        .draggable-timer:hover {
            transform: scale(1.02);
            box-shadow: var(--shadow-3xl);
        }
        
        .draggable-timer.dragging {
            transform: scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            z-index: 1001;
        }
        
        .timer-drag-handle {
            padding: var(--space-2);
            text-align: center;
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            cursor: move;
            font-size: var(--font-size-xs);
            font-weight: 600;
        }
        
        .timer-content {
            padding: var(--space-4);
            text-align: center;
        }
        
        .timer-minimize-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            border: none;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 50%;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .timer-minimized {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
        }
        
        .timer-minimized .timer-drag-handle {
            display: none;
        }
        
        .timer-minimized .timer-content {
            padding: var(--space-2);
            font-size: var(--font-size-sm);
        }
        
        .timer-minimized .modern-timer-label,
        .timer-minimized .modern-timer-step {
            display: none;
        }
        
        .timer-minimized .modern-timer-value {
            font-size: var(--font-size-sm);
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- 可拖拽计时器组件 -->
    <div id="draggable-timer" class="draggable-timer">
        <div class="timer-drag-handle">
            <i class="bi bi-grip-horizontal"></i> 计时器
            <button class="timer-minimize-btn" id="timer-minimize">
                <i class="bi bi-dash"></i>
            </button>
        </div>
        <div class="timer-content">
            <div class="modern-timer">
                <div class="modern-timer-label">剩余时间</div>
                <div id="timer-display" class="modern-timer-value">00:00</div>
                <div id="current-step-name" class="modern-timer-step">准备开始</div>
            </div>
        </div>
    </div>

    <script>
        class DraggableTimer {
            constructor() {
                this.timer = document.getElementById('draggable-timer');
                this.dragHandle = this.timer.querySelector('.timer-drag-handle');
                this.minimizeBtn = document.getElementById('timer-minimize');
                this.isDragging = false;
                this.isMinimized = false;
                this.startX = 0;
                this.startY = 0;
                this.initialX = 0;
                this.initialY = 0;
                
                this.init();
            }
            
            init() {
                // 拖拽事件
                this.dragHandle.addEventListener('mousedown', this.startDrag.bind(this));
                document.addEventListener('mousemove', this.drag.bind(this));
                document.addEventListener('mouseup', this.endDrag.bind(this));
                
                // 最小化事件
                this.minimizeBtn.addEventListener('click', this.toggleMinimize.bind(this));
                
                // 双击展开/收起
                this.timer.addEventListener('dblclick', this.toggleMinimize.bind(this));
                
                // 触摸事件支持
                this.dragHandle.addEventListener('touchstart', this.startDrag.bind(this));
                document.addEventListener('touchmove', this.drag.bind(this));
                document.addEventListener('touchend', this.endDrag.bind(this));
                
                // 防止选中文本
                this.timer.addEventListener('selectstart', (e) => e.preventDefault());
            }
            
            startDrag(e) {
                this.isDragging = true;
                this.timer.classList.add('dragging');
                
                const clientX = e.clientX || e.touches[0].clientX;
                const clientY = e.clientY || e.touches[0].clientY;
                
                this.startX = clientX - this.timer.offsetLeft;
                this.startY = clientY - this.timer.offsetTop;
                
                e.preventDefault();
            }
            
            drag(e) {
                if (!this.isDragging) return;
                
                const clientX = e.clientX || e.touches[0].clientX;
                const clientY = e.clientY || e.touches[0].clientY;
                
                let newX = clientX - this.startX;
                let newY = clientY - this.startY;
                
                // 边界检测
                const maxX = window.innerWidth - this.timer.offsetWidth;
                const maxY = window.innerHeight - this.timer.offsetHeight;
                
                newX = Math.max(0, Math.min(newX, maxX));
                newY = Math.max(0, Math.min(newY, maxY));
                
                this.timer.style.left = newX + 'px';
                this.timer.style.top = newY + 'px';
                this.timer.style.right = 'auto';
                this.timer.style.bottom = 'auto';
                
                e.preventDefault();
            }
            
            endDrag() {
                if (!this.isDragging) return;
                
                this.isDragging = false;
                this.timer.classList.remove('dragging');
                
                // 磁性吸附到边缘
                this.snapToEdge();
            }
            
            snapToEdge() {
                const rect = this.timer.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 吸附到最近的边缘
                if (centerX < window.innerWidth / 2) {
                    // 吸附到左边
                    this.timer.style.left = '20px';
                } else {
                    // 吸附到右边
                    this.timer.style.left = 'auto';
                    this.timer.style.right = '20px';
                }
                
                // 确保不超出顶部和底部
                const currentTop = parseInt(this.timer.style.top) || 20;
                const maxTop = window.innerHeight - this.timer.offsetHeight - 20;
                this.timer.style.top = Math.max(20, Math.min(currentTop, maxTop)) + 'px';
            }
            
            toggleMinimize() {
                this.isMinimized = !this.isMinimized;
                
                if (this.isMinimized) {
                    this.timer.classList.add('timer-minimized');
                    this.minimizeBtn.innerHTML = '<i class="bi bi-plus"></i>';
                } else {
                    this.timer.classList.remove('timer-minimized');
                    this.minimizeBtn.innerHTML = '<i class="bi bi-dash"></i>';
                }
            }
            
            updateTime(timeString) {
                document.getElementById('timer-display').textContent = timeString;
            }
            
            updateStep(stepName) {
                document.getElementById('current-step-name').textContent = stepName;
            }
            
            show() {
                this.timer.style.display = 'block';
            }
            
            hide() {
                this.timer.style.display = 'none';
            }
        }
        
        // 初始化拖拽计时器
        const draggableTimer = new DraggableTimer();
        
        // 导出到全局作用域
        window.DraggableTimer = DraggableTimer;
        window.draggableTimer = draggableTimer;
    </script>
</body>
</html>
