<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="author" content="王文通">
    <meta name="copyright" content="版权所有 © 2025 王文通">
    <meta name="contact" content="<EMAIL>">
    <title>研究生复试流程控制系统 - 重构版</title>
    
    <!-- CSS样式 -->
    <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/progress.css">
    <link rel="stylesheet" href="assets/css/modern-ui.css">
    
    <style>
        /* 应用容器样式 */
        #app {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #f8fafc;
        }
        
        /* 加载样式 */
        .app-loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-content {
            text-align: center;
            padding: 2rem;
            background: #fff;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        .loading-message {
            color: #374151;
            font-size: 1rem;
            font-weight: 500;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 错误样式 */
        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
            padding: 2rem;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #ef4444;
            margin-bottom: 1rem;
        }
        
        .error-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 0.5rem;
        }
        
        .error-message {
            color: #6b7280;
            margin-bottom: 2rem;
            max-width: 500px;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
        }
        
        .btn-retry {
            background: #3b82f6;
            color: #fff;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-retry:hover {
            background: #2563eb;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .loading-content {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            .error-container {
                padding: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- 应用容器 -->
    <div id="app">
        <!-- 初始加载提示 -->
        <div id="initial-loading" class="app-loading">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-message">正在加载系统...</div>
            </div>
        </div>
    </div>

    <!-- JavaScript依赖 -->
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <!-- 数据文件 -->
    <script src="assets/js/data/questions.js"></script>
    
    <!-- 工具类 -->
    <script src="assets/js/utils/utils.js"></script>
    <script src="assets/js/utils/logger.js"></script>
    <script src="assets/js/utils/baseClass.js"></script>
    
    <!-- 核心架构 - 按依赖顺序加载 -->
    <!-- 1. 事件总线 -->
    <script src="assets/js/core/EventBus.js"></script>
    
    <!-- 2. 配置管理器 -->
    <script src="assets/js/core/ConfigManager.js"></script>
    
    <!-- 3. 数据模型 -->
    <script src="assets/js/models/BaseModel.js"></script>
    <script src="assets/js/models/StudentModel.js"></script>
    <script src="assets/js/models/QuestionModel.js"></script>
    <script src="assets/js/models/ExamRecordModel.js"></script>
    <script src="assets/js/models/SettingsModel.js"></script>
    
    <!-- 4. 服务层 -->
    <script src="assets/js/services/BaseService.js"></script>
    <script src="assets/js/services/StudentService.js"></script>
    <script src="assets/js/services/QuestionService.js"></script>
    <script src="assets/js/services/ExamService.js"></script>
    <script src="assets/js/services/TimerService.js"></script>
    <script src="assets/js/services/SettingsService.js"></script>
    <script src="assets/js/services/ServiceManager.js"></script>
    
    <!-- 5. 控制器层 -->
    <script src="assets/js/controllers/BaseController.js"></script>
    <script src="assets/js/controllers/ExamController.js"></script>
    <script src="assets/js/controllers/QuestionController.js"></script>
    <script src="assets/js/controllers/TimerController.js"></script>
    <script src="assets/js/controllers/ControllerManager.js"></script>
    
    <!-- 6. 视图层 -->
    <script src="assets/js/views/BaseView.js"></script>
    <script src="assets/js/views/ExamView.js"></script>
    <script src="assets/js/views/QuestionView.js"></script>
    <script src="assets/js/views/TimerView.js"></script>
    
    <!-- 7. 系统管理 -->
    <script src="assets/js/core/ViewManager.js"></script>
    <script src="assets/js/core/SystemManager.js"></script>
    <script src="assets/js/core/Application.js"></script>
    
    <!-- 8. 应用入口 -->
    <script src="assets/js/main.js"></script>
    
    <!-- 错误处理和兼容性检查 -->
    <script>
        // 检查浏览器兼容性
        function checkBrowserCompatibility() {
            const requiredFeatures = [
                'fetch',
                'Promise',
                'Map',
                'Set',
                'localStorage',
                'addEventListener'
            ];
            
            for (const feature of requiredFeatures) {
                if (!(feature in window)) {
                    return false;
                }
            }
            
            // 检查ES6支持
            try {
                eval('const test = () => {}');
                eval('class Test {}');
            } catch (e) {
                return false;
            }
            
            return true;
        }
        
        // 显示兼容性错误
        function showCompatibilityError() {
            document.getElementById('app').innerHTML = `
                <div class="error-container">
                    <div class="error-icon">⚠️</div>
                    <div class="error-title">浏览器不兼容</div>
                    <div class="error-message">
                        您的浏览器版本过低，无法运行此系统。<br>
                        请升级到最新版本的 Chrome、Firefox、Safari 或 Edge 浏览器。
                    </div>
                    <div class="error-actions">
                        <button class="btn-retry" onclick="location.reload()">重新加载</button>
                    </div>
                </div>
            `;
        }
        
        // 显示加载错误
        function showLoadingError(error) {
            document.getElementById('app').innerHTML = `
                <div class="error-container">
                    <div class="error-icon">❌</div>
                    <div class="error-title">系统加载失败</div>
                    <div class="error-message">
                        ${error.message || '未知错误'}<br>
                        请检查网络连接或联系系统管理员。
                    </div>
                    <div class="error-actions">
                        <button class="btn-retry" onclick="location.reload()">重新加载</button>
                    </div>
                </div>
            `;
        }
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
            
            // 如果是脚本加载错误，显示错误页面
            if (event.filename && event.filename.includes('.js')) {
                showLoadingError(new Error('脚本加载失败: ' + event.filename));
            }
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
        });
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 检查浏览器兼容性
            if (!checkBrowserCompatibility()) {
                showCompatibilityError();
                return;
            }
            
            // 隐藏初始加载提示
            setTimeout(() => {
                const initialLoading = document.getElementById('initial-loading');
                if (initialLoading) {
                    initialLoading.style.display = 'none';
                }
            }, 1000);
        });
        
        // 开发者工具
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('%c面试系统 - 重构版', 'color: #2563eb; font-size: 20px; font-weight: bold;');
            console.log('%c基于MVC架构重构，提供更好的代码组织和维护性', 'color: #059669; font-size: 14px;');
            
            // 暴露调试接口
            window.debug = {
                getApp: () => window.interviewApp,
                getSystemManager: () => window.systemManager,
                getEventBus: () => window.eventBus,
                restart: () => window.restartInterviewApplication(),
                stop: () => window.stopInterviewApplication()
            };
            
            console.log('%c调试接口已暴露到 window.debug', 'color: #7c3aed; font-size: 12px;');
        }
    </script>
</body>
</html>
