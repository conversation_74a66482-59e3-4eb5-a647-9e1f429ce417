<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="author" content="王文通">
    <meta name="copyright" content="版权所有 © 2025 王文通">
    <meta name="contact" content="<EMAIL>">
    <title>研究生复试流程控制系统</title>
    <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/progress.css">
    <link rel="stylesheet" href="assets/css/modern-ui.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f9f9f9;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #2196f3;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        textarea {
            min-height: 100px;
        }
        
        .submit-btn {
            background-color: #4caf50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto 0;
        }
        
        .submit-btn:hover {
            background-color: #388e3c;
        }
        
        .timer {
            text-align: center;
            font-size: 20px;
            margin: 20px 0;
            color: #f44336;
            font-weight: bold;
        }
        
        .question-area {
            margin: 20px 0;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 8px;
            border-left: 5px solid #2196f3;
        }
        
        .actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .actions button, .actions a {
            margin-left: 10px;
        }
        
        .student-info-container {
            background-color: #f0f7ff;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: right;
        }
        
        .student-info {
            font-weight: bold;
            color: #0056b3;
        }
        
        .step-controls {
            display: flex;
            justify-content: center;
            margin-top: 30px;
        }
        
        .step-controls button {
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            width: 100%;
        }
        
        .next-step {
            background-color: #2196f3;
            color: white;
            border: none;
            transition: all 0.3s ease;
        }

        .next-step:hover {
            background-color: #1976d2;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }
        
        .start-timer-btn {
            background-color: #4caf50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 10px;
            display: block;
            width: 100%;
            transition: all 0.3s ease;
        }

        .start-timer-btn:hover {
            background-color: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }
        
        /* draw-question-btn样式已移除，使用control-btn success类 */
        
        .question-display {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            position: relative;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            text-align: center;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-spinner {
            border: 6px solid #f3f3f3;
            border-top: 6px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        
        .english-section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px dashed #ccc;
        }
        
        .stepper-container {
            margin-bottom: 30px;
        }
        
        .step-indicators {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }
        
        .step-indicators::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 0;
        }
        
        .step-indicator {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            z-index: 1;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
        }
        
        .step-title {
            font-size: 14px;
            color: #6c757d;
            text-align: center;
        }
        
        .step-indicator.active .step-number {
            background-color: #0d6efd;
            color: white;
        }
        
        .step-indicator.active .step-title {
            color: #0d6efd;
            font-weight: bold;
        }
        
        .step-indicator.completed .step-number {
            background-color: #198754;
            color: white;
        }
        
        .step-pane {
            display: none;
        }
        
        .step-pane.active {
            display: block;
        }
        
        .timer-container {
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .step-title {
                display: none;
            }
            
            .timer-container {
                margin-bottom: 15px;
            }
        }
        
        /* 题目网格相关样式 */
        .question-grid-container {
            max-width: 100%;
            overflow-x: auto;
        }
        
        .question-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .question-number {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 50px;
            height: 50px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .question-number:hover {
            background-color: #e9ecef;
        }
        
        .question-number.selected {
            background-color: #0d6efd;
            color: white;
            transform: scale(1.1);
            box-shadow: 0 0 10px rgba(13, 110, 253, 0.5);
        }
        
        .question-number.used {
            background-color: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
            position: relative;
            cursor: not-allowed;
        }

        .question-number.used i {
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 12px;
            color: #28a745;
        }
        
        .question-number.highlight {
            animation: highlight-animation 0.5s ease-in-out infinite alternate;
        }
        
        @keyframes highlight-animation {
            0% {
                transform: scale(1);
                background-color: #f8f9fa;
            }
            100% {
                transform: scale(1.1);
                background-color: #0d6efd;
                color: white;
            }
        }
        
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        .question-content {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .question-content p {
            margin-bottom: 12px;
            line-height: 1.6;
        }
        
        .question-image {
            max-width: 100%;
            max-height: 250px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px;
        }
        
        .img-error {
            border: 1px solid #dc3545;
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            text-align: center;
        }
        
        .question-number.globally-used {
            background-color: #f8f9fa;
            color: #adb5bd;
            opacity: 0.6;
        }
        
        .question-status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border-left: 3px solid #6c757d;
        }
        
        .no-available-questions {
            padding: 15px;
            text-align: center;
            background-color: #fff3cd;
            color: #856404;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        /* 新的计时器样式 */
        .prominent-timer {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 3px solid #0d6efd;
            border-radius: 10px;
            padding: 10px 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            text-align: center;
            min-width: 200px;
        }
        
        .prominent-timer .timer-label {
            font-weight: bold;
            color: #0d6efd;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .prominent-timer .timer-value {
            font-size: 24px;
            font-weight: bold;
            color: #212529;
        }
        
        .prominent-timer .step-name {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
            white-space: nowrap;
        }
        
        .prominent-timer.warning .timer-value {
            color: #dc3545;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 可拖拽计时器样式 */
        .draggable-timer {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            z-index: 999; /* 降低z-index，避免阻挡按钮 */
            min-width: 200px;
            cursor: move;
            user-select: none;
            pointer-events: auto; /* 确保计时器本身可以接收事件 */
            /* 移除过渡效果以提高拖拽灵敏度 */
        }

        .draggable-timer:hover {
            transform: scale(1.02);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
            transition: transform 0.1s ease, box-shadow 0.1s ease;
        }

        .draggable-timer.dragging {
            transform: scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            z-index: 1001;
            cursor: grabbing !important;
            /* 拖拽时不使用过渡效果 */
            transition: none !important;
        }

        .timer-drag-handle {
            padding: 8px 12px;
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-weight: bold;
            font-size: 12px;
            border-radius: 12px 12px 0 0;
            cursor: grab;
            display: flex;
            justify-content: space-between;
            align-items: center;
            user-select: none;
            /* 提高拖拽响应性 */
            touch-action: none;
        }

        .timer-drag-handle:active {
            cursor: grabbing;
            background: rgba(255, 255, 255, 0.3);
        }

        .timer-minimize-btn {
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .timer-minimize-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .timer-content {
            padding: 16px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .timer-display .timer-value {
            font-size: 28px;
            font-weight: bold;
            color: white;
            margin-bottom: 4px;
            font-family: 'Courier New', monospace;
        }

        .timer-display .step-name {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 4px;
        }

        .timer-minimized {
            height: 40px;
            overflow: hidden;
        }

        .timer-minimized .timer-content {
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .timer-minimized .timer-value {
            font-size: 16px !important;
            margin: 0 !important;
        }

        .timer-minimized .step-name {
            display: none;
        }
    </style>
</head>
<body class="modern-page-layout">
    <!-- 页面头部 -->
    <header class="modern-page-header">
        <div class="header-content">
            <h1 class="h3 mb-0" style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">研究生复试考核系统</h1>
            <div class="d-flex align-items-center">
                <span class="badge" style="background: var(--gradient-primary); padding: 8px 12px; border-radius: 20px; margin-right: 8px;">考生:</span>
                <span class="fw-bold" id="studentNumber" style="color: var(--primary-600); font-size: 1.1rem;">2023XXXXX</span>
            </div>
        </div>
    </header>

    <!-- 主容器 -->
    <div class="modern-container">
        <div class="modern-main-layout">
            <!-- 头部区域 -->
            <div class="modern-header-section">
                <div class="modern-header-left">
                    <div class="modern-header-decoration">
                        <i class="bi bi-clock-history"></i>
                        <span>考试进行中</span>
                    </div>
                </div>

                <div class="modern-timer-area" style="display: none;">
                    <div class="modern-timer">
                        <div class="modern-timer-label">剩余时间</div>
                        <div id="timer" class="modern-timer-value">00:00</div>
                        <div id="current-step-name" class="modern-timer-step">中文自我介绍</div>
                    </div>
                </div>

                <!-- 可拖拽计时器 -->
                <div id="draggable-timer" class="draggable-timer">
                    <div class="timer-drag-handle">
                        <i class="bi bi-grip-horizontal"></i>
                        <span id="timer-handle-text">计时器</span>
                        <span id="timer-handle-time" style="display: none;">00:00</span>
                        <button class="timer-minimize-btn" id="timer-minimize">
                            <i class="bi bi-dash"></i>
                        </button>
                    </div>
                    <div class="timer-content">
                        <div class="timer-display">
                            <div id="draggable-timer-value" class="timer-value">00:00</div>
                            <div id="draggable-step-name" class="step-name">中文自我介绍</div>
                        </div>
                    </div>
                </div>

                <div class="modern-header-right">
                    <div class="modern-header-decoration">
                        <i class="bi bi-person-check"></i>
                        <span>考生就位</span>
                    </div>
                </div>
            </div>

            <!-- 三栏布局主体 -->
            <div class="three-column-layout">
                <!-- 左侧：流程节点 -->
                <div class="left-sidebar" id="leftSidebar">
                    <!-- 左侧栏控制按钮 -->
                    <div class="sidebar-controls">
                        <button class="sidebar-toggle-btn" id="leftToggleBtn" title="隐藏/显示左侧栏">
                            <i class="bi bi-chevron-left"></i>
                        </button>
                        <button class="sidebar-minimize-btn" id="leftMinimizeBtn" title="最小化左侧栏">
                            <i class="bi bi-dash-square"></i>
                        </button>
                    </div>
                    <div class="process-steps">
                        <div class="sidebar-header">
                            <h6 class="sidebar-title">
                                <i class="bi bi-list-check"></i>
                                考试流程
                            </h6>
                            <div class="progress-indicator">
                                <div class="progress-text">进度: 1/5</div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar" style="width: 20%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="step-list">
                            <div class="step-item active" data-step="1">
                                <div class="step-indicator">
                                    <div class="step-number">1</div>
                                    <div class="step-status">
                                        <i class="bi bi-circle pending-icon"></i>
                                        <i class="bi bi-play-circle-fill current-icon"></i>
                                        <i class="bi bi-check-circle-fill completed-icon"></i>
                                    </div>
                                </div>
                                <div class="step-content">
                                    <div class="step-title">中文自我介绍</div>
                                    <div class="step-duration">
                                        <i class="bi bi-clock"></i>
                                        <span>2分钟</span>
                                    </div>
                                    <div class="step-description">简短介绍个人情况</div>
                                </div>
                            </div>

                            <div class="step-item" data-step="2">
                                <div class="step-indicator">
                                    <div class="step-number">2</div>
                                    <div class="step-status">
                                        <i class="bi bi-circle pending-icon"></i>
                                        <i class="bi bi-play-circle-fill current-icon"></i>
                                        <i class="bi bi-check-circle-fill completed-icon"></i>
                                    </div>
                                </div>
                                <div class="step-content">
                                    <div class="step-title">英文自我介绍</div>
                                    <div class="step-duration">
                                        <i class="bi bi-clock"></i>
                                        <span>2分钟</span>
                                    </div>
                                    <div class="step-description">English self-introduction</div>
                                </div>
                            </div>

                            <div class="step-item" data-step="3">
                                <div class="step-indicator">
                                    <div class="step-number">3</div>
                                    <div class="step-status">
                                        <i class="bi bi-circle pending-icon"></i>
                                        <i class="bi bi-play-circle-fill current-icon"></i>
                                        <i class="bi bi-check-circle-fill completed-icon"></i>
                                    </div>
                                </div>
                                <div class="step-content">
                                    <div class="step-title">英文翻译</div>
                                    <div class="step-duration">
                                        <i class="bi bi-clock"></i>
                                        <span>5分钟</span>
                                    </div>
                                    <div class="step-description">随机抽取翻译题目</div>
                                </div>
                            </div>

                            <div class="step-item" data-step="4">
                                <div class="step-indicator">
                                    <div class="step-number">4</div>
                                    <div class="step-status">
                                        <i class="bi bi-circle pending-icon"></i>
                                        <i class="bi bi-play-circle-fill current-icon"></i>
                                        <i class="bi bi-check-circle-fill completed-icon"></i>
                                    </div>
                                </div>
                                <div class="step-content">
                                    <div class="step-title">专业课问答</div>
                                    <div class="step-duration">
                                        <i class="bi bi-clock"></i>
                                        <span>10分钟</span>
                                    </div>
                                    <div class="step-description">专业知识问答环节</div>
                                </div>
                            </div>

                            <div class="step-item" data-step="5">
                                <div class="step-indicator">
                                    <div class="step-number">5</div>
                                    <div class="step-status">
                                        <i class="bi bi-circle pending-icon"></i>
                                        <i class="bi bi-play-circle-fill current-icon"></i>
                                        <i class="bi bi-check-circle-fill completed-icon"></i>
                                    </div>
                                </div>
                                <div class="step-content">
                                    <div class="step-title">综合面试</div>
                                    <div class="step-duration">
                                        <i class="bi bi-clock"></i>
                                        <span>8分钟</span>
                                    </div>
                                    <div class="step-description">综合能力评估</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最小化状态的简略流程步骤 -->
                    <div class="minimized-process-steps">
                        <!-- 最小化进度指示器 -->
                        <div class="minimized-progress-indicator">
                            <div class="minimized-progress-text">4/5</div>
                            <div class="minimized-progress-bar">
                                <div class="minimized-progress-fill" style="width: 80%"></div>
                            </div>
                        </div>

                        <div class="minimized-step completed" data-step="1">
                            <div class="minimized-step-number">1</div>
                            <div class="minimized-step-time">2分钟</div>
                            <div class="minimized-step-indicator"></div>
                        </div>
                        <div class="minimized-step completed" data-step="2">
                            <div class="minimized-step-number">2</div>
                            <div class="minimized-step-time">3分钟</div>
                            <div class="minimized-step-indicator"></div>
                        </div>
                        <div class="minimized-step completed" data-step="3">
                            <div class="minimized-step-number">3</div>
                            <div class="minimized-step-time">5分钟</div>
                            <div class="minimized-step-indicator"></div>
                        </div>
                        <div class="minimized-step active" data-step="4">
                            <div class="minimized-step-number">4</div>
                            <div class="minimized-step-time">10分钟</div>
                            <div class="minimized-step-indicator"></div>
                        </div>
                        <div class="minimized-step" data-step="5">
                            <div class="minimized-step-number">5</div>
                            <div class="minimized-step-time">5分钟</div>
                        </div>
                    </div>
                </div>

                <!-- 中间：题目内容 -->
                <div class="center-content" id="centerContent">
                    <!-- 中间内容区域控制按钮 -->
                    <div class="content-controls">
                        <button class="content-control-btn" id="showLeftBtn" title="显示左侧栏" style="display: none;">
                            <i class="bi bi-layout-sidebar"></i>
                        </button>
                        <button class="content-control-btn" id="showRightBtn" title="显示右侧栏" style="display: none;">
                            <i class="bi bi-layout-sidebar-reverse"></i>
                        </button>
                        <button class="content-control-btn" id="fullscreenBtn" title="全屏模式">
                            <i class="bi bi-arrows-fullscreen"></i>
                        </button>
                    </div>

                    <div class="content-header">
                        <h5 class="content-title" id="current-step-title">
                            <i class="bi bi-person-circle"></i> 中文自我介绍（2分钟）
                        </h5>
                    </div>

                    <div class="content-body">
                        <!-- 步骤1：中文自我介绍 -->
                        <div class="step-content active" id="step1-content">
                            <div class="step-description">
                                <p>请用中文进行简短的自我介绍，包括个人基本情况、教育背景、研究经历、专业特长等。</p>
                            </div>
                        </div>

                        <!-- 步骤2：英文自我介绍 -->
                        <div class="step-content" id="step2-content">
                            <div class="step-description">
                                <p>Please introduce yourself in English, including your personal information, educational background, research experience, and professional specialties.</p>
                            </div>
                        </div>

                        <!-- 步骤3：英文翻译 -->
                        <div class="step-content" id="step3-content">
                            <div class="step-description">
                                <p>请点击"抽取题目"按钮随机抽取一道英文翻译题目。</p>
                            </div>

                            <!-- 题目状态网格 -->
                            <div id="translation-question-grid" class="question-grid-container" style="display: none;">
                                <div class="question-grid-header">
                                    <h6 class="question-grid-title">翻译题状态</h6>
                                    <div class="question-grid-legend">
                                        <div class="legend-item">
                                            <div class="legend-dot available"></div>
                                            <span>可抽取</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-dot used"></div>
                                            <span>已抽取</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-dot selected"></div>
                                            <span>当前选中</span>
                                        </div>
                                    </div>
                                </div>
                                <div id="translation-numbers-grid" class="question-numbers-grid"></div>
                                <div class="question-grid-stats">
                                    <span id="translation-total-count">总计: 20 题</span>
                                    <span id="translation-used-count">已抽取: 0 题</span>
                                    <span id="translation-available-count">可抽取: 20 题</span>
                                </div>
                            </div>

                            <!-- 题目显示区域 -->
                            <div id="translation-question-display" class="question-display">
                                <div class="question-placeholder">
                                    <div class="question-placeholder-icon">
                                        <i class="bi bi-file-text"></i>
                                    </div>
                                    <div class="question-placeholder-text">题目将在这里显示</div>
                                    <div class="question-placeholder-hint">请先抽取题目</div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤4：专业课问答 -->
                        <div class="step-content" id="step4-content">
                            <div class="step-description">
                                <p>请点击"抽取题目"按钮随机抽取一道专业课问答题目。</p>
                            </div>

                            <!-- 题目状态网格 -->
                            <div id="professional-question-grid" class="question-grid-container" style="display: none;">
                                <div class="question-grid-header">
                                    <h6 class="question-grid-title">专业题状态</h6>
                                    <div class="question-grid-legend">
                                        <div class="legend-item">
                                            <div class="legend-dot available"></div>
                                            <span>可抽取</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-dot used"></div>
                                            <span>已抽取</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-dot selected"></div>
                                            <span>当前选中</span>
                                        </div>
                                    </div>
                                </div>
                                <div id="professional-numbers-grid" class="question-numbers-grid"></div>
                                <div class="question-grid-stats">
                                    <span id="professional-total-count">总计: 20 题</span>
                                    <span id="professional-used-count">已抽取: 0 题</span>
                                    <span id="professional-available-count">可抽取: 20 题</span>
                                </div>
                            </div>

                            <!-- 题目显示区域 -->
                            <div id="professional-question-display" class="question-display">
                                <div class="question-placeholder">
                                    <div class="question-placeholder-icon">
                                        <i class="bi bi-file-text"></i>
                                    </div>
                                    <div class="question-placeholder-text">题目将在这里显示</div>
                                    <div class="question-placeholder-hint">请先抽取题目</div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤5：综合面试 -->
                        <div class="step-content" id="step5-content">
                            <div class="step-description">
                                <p>综合面试环节，考官将根据考生的整体表现进行综合评价。</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 中间内容区域结束 -->

                <!-- 右侧：操作面板 -->
                <div class="right-sidebar" id="rightSidebar">
                    <!-- 右侧栏控制按钮 -->
                    <div class="sidebar-controls right-controls">
                        <button class="sidebar-minimize-btn" id="rightMinimizeBtn" title="最小化右侧栏">
                            <i class="bi bi-dash-square"></i>
                        </button>
                        <button class="sidebar-toggle-btn" id="rightToggleBtn" title="隐藏/显示右侧栏">
                            <i class="bi bi-chevron-right"></i>
                        </button>
                    </div>

                    <div class="control-panel">
                        <div class="panel-header">
                            <h6 class="panel-title">
                                <i class="bi bi-sliders"></i>
                                控制面板
                            </h6>
                        </div>

                        <!-- 计时器控制 -->
                        <div class="control-section timer-section">
                            <div class="section-header">
                                <i class="bi bi-stopwatch"></i>
                                <span class="section-title">计时器</span>
                            </div>
                            <div class="control-buttons">
                                <button id="start-timer-btn" class="control-btn primary">
                                    <i class="bi bi-play-circle-fill"></i>
                                    <span>开始计时</span>
                                    <kbd>Space</kbd>
                                </button>
                                <button id="pause-timer-btn" class="control-btn secondary" style="display: none;">
                                    <i class="bi bi-pause-circle-fill"></i>
                                    <span>暂停计时</span>
                                    <kbd>Space</kbd>
                                </button>
                                <button id="reset-timer-btn" class="control-btn outline">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    <span>重置计时</span>
                                    <kbd>R</kbd>
                                </button>
                            </div>
                        </div>

                        <!-- 题目操作 -->
                        <div class="control-section question-section" id="question-controls" style="display: none;">
                            <div class="section-header">
                                <i class="bi bi-card-text"></i>
                                <span class="section-title">题目操作</span>
                            </div>
                            <div class="control-buttons">
                                <button id="draw-question-btn" class="control-btn">
                                    <i class="bi bi-shuffle"></i>
                                    <span>抽取题目</span>
                                    <kbd>D</kbd>
                                </button>
                                <button id="question-drawn-btn" class="control-btn disabled" style="display: none;" disabled>
                                    <i class="bi bi-check-circle-fill"></i>
                                    <span>已抽取</span>
                                </button>
                            </div>
                        </div>

                        <!-- 流程控制 -->
                        <div class="control-section navigation-section">
                            <div class="section-header">
                                <i class="bi bi-arrow-left-right"></i>
                                <span class="section-title">流程控制</span>
                            </div>
                            <div class="control-buttons navigation-buttons">
                                <button id="prev-step-btn" class="control-btn outline" disabled>
                                    <i class="bi bi-chevron-left"></i>
                                    <span>上一步</span>
                                </button>
                                <button id="next-step-btn" class="control-btn primary">
                                    <i class="bi bi-chevron-right"></i>
                                    <span>下一步</span>
                                </button>
                            </div>
                        </div>

                        <!-- 系统操作 -->
                        <div class="control-section system-section">
                            <div class="section-header">
                                <i class="bi bi-gear-wide-connected"></i>
                                <span class="section-title">系统操作</span>
                            </div>
                            <div class="control-buttons">
                                <button id="exam-records-btn" class="control-btn">
                                    <i class="bi bi-journal-text"></i>
                                    <span>考试记录</span>
                                </button>
                                <button id="next-student-btn" class="control-btn">
                                    <i class="bi bi-person-plus-fill"></i>
                                    <span>下一名考生</span>
                                </button>
                                <button id="settings-btn" class="control-btn secondary">
                                    <i class="bi bi-gear-fill"></i>
                                    <span>系统设置</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 最小化状态的图形化控制按钮 -->
                    <div class="minimized-control-panel">
                        <!-- 计时器控制 -->
                        <div class="minimized-control-group">
                            <button class="minimized-control-btn primary" id="minimizedStartTimer" title="开始计时">
                                <i class="bi bi-play-fill"></i>
                            </button>
                            <button class="minimized-control-btn" id="minimizedResetTimer" title="重置计时">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>

                        <div class="minimized-control-divider"></div>

                        <!-- 题目操作 -->
                        <div class="minimized-control-group">
                            <button class="minimized-control-btn" id="minimizedDrawQuestion" title="抽取题目">
                                <i class="bi bi-dice-3"></i>
                            </button>
                        </div>

                        <div class="minimized-control-divider"></div>

                        <!-- 流程控制 -->
                        <div class="minimized-control-group">
                            <button class="minimized-control-btn" id="minimizedPrevStep" title="上一步">
                                <i class="bi bi-chevron-left"></i>
                            </button>
                            <button class="minimized-control-btn" id="minimizedNextStep" title="下一步">
                                <i class="bi bi-chevron-right"></i>
                            </button>
                        </div>

                        <div class="minimized-control-divider"></div>

                        <!-- 系统操作 -->
                        <div class="minimized-control-group">
                            <button class="minimized-control-btn" id="minimizedExamRecord" title="考试记录">
                                <i class="bi bi-file-text"></i>
                            </button>
                            <button class="minimized-control-btn success" id="minimizedNextStudent" title="下一名考生">
                                <i class="bi bi-person-plus"></i>
                            </button>
                            <button class="minimized-control-btn" id="minimizedSettings" title="系统设置">
                                <i class="bi bi-gear"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 页面底部 -->
    <footer class="page-footer">
        <div class="footer-content">
            <div class="copyright-info">
                <div class="copyright-text">
                    <span>© 2024 研究生复试考核系统</span>
                    <span class="separator">|</span>
                    <span>版本 v2.0</span>
                    <span class="separator">|</span>
                    <span>技术支持</span>
                </div>
                <div class="system-info">
                    <small>基于现代化Web技术构建，为研究生复试提供专业的考核管理解决方案</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- 设置弹窗 -->
    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel">面试时间设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="chineseTime" class="form-label">中文自我介绍时间（分钟）</label>
                            <input type="number" class="form-control" id="chineseTime" value="2" min="0.1" step="0.1">
                        </div>
                        <div class="mb-3">
                            <label for="englishIntroTime" class="form-label">英文自我介绍时间（分钟）</label>
                            <input type="number" class="form-control" id="englishIntroTime" value="2" min="0.1" step="0.1">
                        </div>
                        <div class="mb-3">
                            <label for="translationTime" class="form-label">英文翻译时间（分钟）</label>
                            <input type="number" class="form-control" id="translationTime" value="5" min="0.1" step="0.1">
                        </div>
                        <div class="mb-3">
                            <label for="professionalTime" class="form-label">专业课问答时间（分钟）</label>
                            <input type="number" class="form-control" id="professionalTime" value="10" min="0.1" step="0.1">
                        </div>
                        <!-- 隐藏综合面试时间设置，因为综合面试不需要计时 -->
                        <div class="mb-3 d-none">
                            <label for="comprehensiveTime" class="form-label">综合面试时间（分钟）</label>
                            <input type="number" class="form-control" id="comprehensiveTime" value="5" min="0.1" step="0.1">
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> 注意：综合面试环节无需计时，由考官自由掌握时间。
                        </div>

                        <!-- 系统行为设置 -->
                        <div class="mb-3 mt-4 border-top pt-3">
                            <label class="form-label fw-bold">系统行为设置</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="autoNextStep" checked>
                                <label class="form-check-label" for="autoNextStep">
                                    时间到后自动进入下一环节
                                </label>
                                <div class="form-text">取消勾选后，时间到时只会提示，需要手动切换到下一环节</div>
                            </div>
                        </div>

                        <!-- 添加题库管理区域 -->
                        <div class="mb-3 mt-4 border-top pt-3">
                            <label class="form-label fw-bold">系统管理</label>
                            <div class="d-grid gap-2 mt-2">
                                <a href="editor.html" class="btn btn-primary">
                                    <i class="bi bi-database-gear"></i> 题库管理
                                </a>
                                <button type="button" class="btn btn-danger" id="resetExamBtn">
                                    <i class="bi bi-arrow-clockwise"></i> 重置考试状态
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveSettingsBtn">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 考试记录弹窗 -->
    <div class="modal fade" id="examRecordModal" tabindex="-1" aria-labelledby="examRecordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                <h5 class="modal-title" id="examRecordModalLabel"><i class="bi bi-list"></i> 考试记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                <div id="examRecordContent" class="p-2"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="exportRecordBtn">
                        <i class="bi bi-download"></i> 导出记录
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- 数据文件 -->
    <script src="assets/js/data/questions.js"></script>

    <!-- 工具类 -->
    <script src="assets/js/utils/utils.js"></script>
    <script src="assets/js/utils/logger.js"></script>
    <script src="assets/js/utils/baseClass.js"></script>

    <!-- 核心类 -->
    <script src="assets/js/core/database.js"></script>
    <script src="assets/js/core/timer.js"></script>
    <script src="assets/js/core/draggableTimer.js"></script>

    <!-- UI类 -->
    <script src="assets/js/ui/ui.js"></script>
    <script src="assets/js/ui/eventManager.js"></script>
    <script src="assets/js/ui/threeColumnLayout.js"></script>

    <!-- 管理器类 -->
    <script src="assets/js/buttonManager.js"></script>

    <!-- 业务逻辑类 -->
    <script src="assets/js/business/Student.js"></script>
    <script src="assets/js/business/ExamStep.js"></script>
    <script src="assets/js/business/Question.js"></script>
    <script src="assets/js/business/Settings.js"></script>

    <!-- 主控制器 -->
    <script src="assets/js/core/ExamSystem.js"></script>

    <!-- 应用入口 -->
    <script src="assets/js/app.js"></script>

    <!-- 高灵敏度拖拽脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const draggableTimer = document.getElementById('draggable-timer');
            const dragHandle = draggableTimer.querySelector('.timer-drag-handle');

            if (!draggableTimer || !dragHandle) {
                console.error('拖拽元素未找到!');
                return;
            }

            // 创建一个函数来同步缩小状态下的时间显示
            window.updateMinimizedTimer = function() {
                const timerHandleTime = document.getElementById('timer-handle-time');
                const draggableTimerValue = document.getElementById('draggable-timer-value');

                if (timerHandleTime && draggableTimerValue && timerHandleTime.style.display !== 'none') {
                    timerHandleTime.textContent = draggableTimerValue.textContent;
                }
            };

            let isDragging = false;
            let startX = 0;
            let startY = 0;

            // 高灵敏度拖拽实现
            function startDrag(e) {
                // 支持鼠标和触摸事件
                const clientX = e.clientX || (e.touches && e.touches[0].clientX);
                const clientY = e.clientY || (e.touches && e.touches[0].clientY);

                if (!clientX || !clientY) return;

                isDragging = true;

                // 添加拖拽样式
                draggableTimer.classList.add('dragging');

                // 确保计时器有正确的定位
                draggableTimer.style.setProperty('position', 'fixed', 'important');

                // 记录初始位置
                startX = clientX - draggableTimer.offsetLeft;
                startY = clientY - draggableTimer.offsetTop;

                // 阻止默认行为和事件冒泡
                e.preventDefault();
                e.stopPropagation();

                // 设置高优先级事件监听
                document.addEventListener('mousemove', drag, { passive: false });
                document.addEventListener('touchmove', drag, { passive: false });
                document.addEventListener('mouseup', endDrag, { once: true });
                document.addEventListener('touchend', endDrag, { once: true });
            }

            function drag(e) {
                if (!isDragging) return;

                // 支持鼠标和触摸事件
                const clientX = e.clientX || (e.touches && e.touches[0].clientX);
                const clientY = e.clientY || (e.touches && e.touches[0].clientY);

                if (!clientX || !clientY) return;

                // 计算新位置
                let newX = clientX - startX;
                let newY = clientY - startY;

                // 边界检测
                const maxX = window.innerWidth - draggableTimer.offsetWidth;
                const maxY = window.innerHeight - draggableTimer.offsetHeight;

                newX = Math.max(0, Math.min(newX, maxX));
                newY = Math.max(0, Math.min(newY, maxY));

                // 使用!important强制设置位置，覆盖CSS样式
                draggableTimer.style.setProperty('position', 'fixed', 'important');
                draggableTimer.style.setProperty('left', newX + 'px', 'important');
                draggableTimer.style.setProperty('top', newY + 'px', 'important');
                draggableTimer.style.setProperty('right', 'auto', 'important');
                draggableTimer.style.setProperty('bottom', 'auto', 'important');

                // 阻止默认行为
                e.preventDefault();
                e.stopPropagation();
            }

            function endDrag(e) {
                if (!isDragging) return;

                isDragging = false;
                draggableTimer.classList.remove('dragging');

                // 移除事件监听器
                document.removeEventListener('mousemove', drag);
                document.removeEventListener('touchmove', drag);

                e.preventDefault();
                e.stopPropagation();
            }

            // 绑定拖拽开始事件
            dragHandle.addEventListener('mousedown', startDrag, { passive: false });
            dragHandle.addEventListener('touchstart', startDrag, { passive: false });

            // 防止文本选择
            dragHandle.addEventListener('selectstart', e => e.preventDefault());
            draggableTimer.addEventListener('selectstart', e => e.preventDefault());

            // 最小化功能
            const minimizeBtn = document.getElementById('timer-minimize');
            const timerContent = draggableTimer.querySelector('.timer-content');
            const timerHandleText = document.getElementById('timer-handle-text');
            const timerHandleTime = document.getElementById('timer-handle-time');

            if (minimizeBtn && timerContent && timerHandleText && timerHandleTime) {
                minimizeBtn.addEventListener('click', function() {
                    if (timerContent.style.display === 'none') {
                        // 展开状态
                        timerContent.style.display = 'block';
                        minimizeBtn.innerHTML = '<i class="bi bi-dash"></i>';
                        timerHandleText.style.display = 'inline';
                        timerHandleTime.style.display = 'none';
                        draggableTimer.classList.remove('timer-minimized');
                    } else {
                        // 缩小状态
                        timerContent.style.display = 'none';
                        minimizeBtn.innerHTML = '<i class="bi bi-plus"></i>';
                        timerHandleText.style.display = 'none';
                        timerHandleTime.style.display = 'inline';
                        draggableTimer.classList.add('timer-minimized');

                        // 同步当前时间到缩小显示
                        const currentTime = document.getElementById('draggable-timer-value').textContent;
                        timerHandleTime.textContent = currentTime;
                    }
                });
            }
        });
    </script>

</div>
</body>
</html>