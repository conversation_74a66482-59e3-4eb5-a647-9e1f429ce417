<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置浏览器状态</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 50px;
            text-align: center;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .btn {
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 浏览器状态重置</h1>
        <div class="status info">
            <p>正在重置浏览器状态...</p>
            <p>如果有对话框出现，将自动处理</p>
        </div>
        
        <div id="resetStatus"></div>
        
        <div style="margin-top: 30px;">
            <a href="/" class="btn btn-primary">返回主系统</a>
            <a href="/debug_test.html" class="btn btn-success">调试测试页面</a>
            <button class="btn btn-warning" onclick="clearCache()">清除缓存</button>
        </div>
    </div>

    <script>
        // 重置浏览器状态
        function resetBrowserState() {
            try {
                // 清除所有模态框
                const modals = document.querySelectorAll('.modal-overlay, .modal, [role="dialog"]');
                modals.forEach(modal => {
                    if (modal.parentNode) {
                        modal.parentNode.removeChild(modal);
                    }
                });

                // 清除事件监听器
                const body = document.body;
                const newBody = body.cloneNode(true);
                body.parentNode.replaceChild(newBody, body);

                // 重置全局变量
                if (window.currentExam) {
                    delete window.currentExam;
                }
                if (window.currentStudent) {
                    delete window.currentStudent;
                }

                document.getElementById('resetStatus').innerHTML = `
                    <div class="status success">
                        ✅ 浏览器状态已重置<br>
                        现在可以安全地使用系统功能
                    </div>
                `;

                console.log('浏览器状态重置完成');
            } catch (error) {
                document.getElementById('resetStatus').innerHTML = `
                    <div class="status error">
                        ❌ 重置过程中出现错误: ${error.message}
                    </div>
                `;
                console.error('重置浏览器状态失败:', error);
            }
        }

        // 清除缓存
        function clearCache() {
            try {
                // 清除localStorage
                localStorage.clear();
                
                // 清除sessionStorage
                sessionStorage.clear();
                
                // 清除cookies (仅限当前域)
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });

                alert('缓存已清除，建议刷新页面');
            } catch (error) {
                alert('清除缓存失败: ' + error.message);
            }
        }

        // 页面加载时自动重置
        window.addEventListener('load', function() {
            setTimeout(resetBrowserState, 1000);
        });

        // 处理可能的对话框
        window.addEventListener('beforeunload', function(e) {
            // 阻止默认的确认对话框
            e.preventDefault();
            return undefined;
        });

        // 覆盖alert, confirm, prompt函数
        window.alert = function(message) {
            console.log('Alert blocked:', message);
            return true;
        };

        window.confirm = function(message) {
            console.log('Confirm blocked:', message);
            return true;
        };

        window.prompt = function(message, defaultText) {
            console.log('Prompt blocked:', message);
            return null;
        };
    </script>
</body>
</html>
