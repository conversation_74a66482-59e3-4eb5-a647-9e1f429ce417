/**
 * 研究生复试步骤管理模块
 * 用于控制考试步骤的显示和切换
 */

// 步骤管理状态
const stepsState = {
  currentStep: 0,
  steps: [],
  containerElement: null,
  onStepChange: null,
  allowNavigation: true,
  confirmOnChange: false,
  stepChanging: false
};

/**
 * 初始化步骤管理器
 * @param {String} containerId - 步骤容器的DOM ID
 * @param {Array} stepsConfig - 步骤配置数组
 * @param {Object} options - 配置选项
 */
function initSteps(containerId, stepsConfig, options = {}) {
  // 默认配置
  const defaultOptions = {
    startStep: 0,
    allowNavigation: true,
    confirmOnChange: false,
    onStepChange: null,
    showStepIndicator: true,
    showControls: true,
    prevBtnText: '上一步',
    nextBtnText: '下一步',
    enableKeyboardNav: true
  };

  const stepOptions = {...defaultOptions, ...options};
  
  // 设置容器元素
  stepsState.containerElement = document.getElementById(containerId);
  if (!stepsState.containerElement) {
    console.error(`步骤容器 [${containerId}] 不存在`);
    return false;
  }
  
  // 设置步骤配置
  stepsState.steps = stepsConfig.map((step, index) => {
    return {
      id: step.id || `step-${index}`,
      title: step.title || `步骤 ${index + 1}`,
      element: document.getElementById(step.id) || null,
      beforeEnter: step.beforeEnter || null,
      afterEnter: step.afterEnter || null,
      beforeLeave: step.beforeLeave || null,
      afterLeave: step.afterLeave || null,
      isValid: step.isValid || (() => true),
      isOptional: step.isOptional || false,
      isDisabled: step.isDisabled || false,
      data: step.data || {},
      canGoBack: typeof step.canGoBack !== 'undefined' ? step.canGoBack : true,
      canGoForward: typeof step.canGoForward !== 'undefined' ? step.canGoForward : true
    };
  });
  
  // 设置状态
  stepsState.currentStep = stepOptions.startStep >= 0 && 
                          stepOptions.startStep < stepsState.steps.length ? 
                          stepOptions.startStep : 0;
  stepsState.allowNavigation = stepOptions.allowNavigation;
  stepsState.confirmOnChange = stepOptions.confirmOnChange;
  stepsState.onStepChange = stepOptions.onStepChange;
  
  // 初始化UI
  _initStepsUI(stepOptions);
  
  // 显示初始步骤
  _showStep(stepsState.currentStep);
  
  // 添加键盘导航
  if (stepOptions.enableKeyboardNav) {
    document.addEventListener('keydown', _handleKeyNavigation);
  }
  
  console.log(`步骤管理器已初始化，共 ${stepsState.steps.length} 个步骤，当前步骤: ${stepsState.currentStep + 1}`);
  return true;
}

/**
 * 初始化步骤UI
 * @param {Object} options - 配置选项
 * @private
 */
function _initStepsUI(options) {
  const container = stepsState.containerElement;
  if (!container) return;
  
  // 清除容器现有内容
  // container.innerHTML = ''; // 不清除，因为步骤内容可能已经存在
  
  // 创建步骤指示器
  if (options.showStepIndicator) {
    const indicatorContainer = document.createElement('div');
    indicatorContainer.className = 'steps-indicator';
    
    stepsState.steps.forEach((step, index) => {
      const indicator = document.createElement('div');
      indicator.className = 'step-indicator';
      indicator.dataset.stepIndex = index;
      indicator.title = step.title;
      
      if (index === stepsState.currentStep) {
        indicator.classList.add('active');
      }
      
      if (step.isOptional) {
        indicator.classList.add('optional');
      }
      
      if (step.isDisabled) {
        indicator.classList.add('disabled');
      }
      
      // 可点击导航
      if (options.allowNavigation) {
        indicator.addEventListener('click', () => {
          goToStep(index);
        });
      }
      
      indicatorContainer.appendChild(indicator);
    });
    
    const existingIndicator = container.querySelector('.steps-indicator');
    if (existingIndicator) {
      existingIndicator.replaceWith(indicatorContainer);
    } else {
      container.prepend(indicatorContainer);
    }
  }
  
  // 创建控制按钮
  if (options.showControls) {
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'steps-controls';
    
    // 上一步按钮
    const prevBtn = document.createElement('button');
    prevBtn.className = 'step-btn prev-step-btn';
    prevBtn.textContent = options.prevBtnText;
    prevBtn.addEventListener('click', () => {
      goToPrevStep();
    });
    
    // 下一步按钮
    const nextBtn = document.createElement('button');
    nextBtn.className = 'step-btn next-step-btn';
    nextBtn.textContent = options.nextBtnText;
    nextBtn.addEventListener('click', () => {
      goToNextStep();
    });
    
    controlsContainer.appendChild(prevBtn);
    controlsContainer.appendChild(nextBtn);
    
    const existingControls = container.querySelector('.steps-controls');
    if (existingControls) {
      existingControls.replaceWith(controlsContainer);
    } else {
      container.appendChild(controlsContainer);
    }
    
    // 更新按钮状态
    updateControlsState();
  }
}

/**
 * 显示指定步骤
 * @param {Number} stepIndex - 步骤索引
 * @private
 */
function _showStep(stepIndex) {
  if (stepIndex < 0 || stepIndex >= stepsState.steps.length) {
    console.error(`步骤索引 [${stepIndex}] 超出范围`);
    return false;
  }
  
  // 隐藏所有步骤
  stepsState.steps.forEach((step, index) => {
    if (step.element) {
      step.element.classList.remove('active-step');
      step.element.classList.add('inactive-step');
    }
    
    // 更新指示器状态
    const indicator = stepsState.containerElement.querySelector(`.step-indicator[data-step-index="${index}"]`);
    if (indicator) {
      indicator.classList.remove('active');
      if (index < stepIndex) {
        indicator.classList.add('completed');
      } else {
        indicator.classList.remove('completed');
      }
    }
  });
  
  // 显示当前步骤
  const currentStep = stepsState.steps[stepIndex];
  if (currentStep && currentStep.element) {
    currentStep.element.classList.remove('inactive-step');
    currentStep.element.classList.add('active-step');
    
    // 更新当前指示器
    const indicator = stepsState.containerElement.querySelector(`.step-indicator[data-step-index="${stepIndex}"]`);
    if (indicator) {
      indicator.classList.add('active');
    }
  }
  
  // 更新按钮状态
  updateControlsState();
  
  return true;
}

/**
 * 更新控制按钮状态
 */
function updateControlsState() {
  const prevBtn = stepsState.containerElement.querySelector('.prev-step-btn');
  const nextBtn = stepsState.containerElement.querySelector('.next-step-btn');
  
  if (!prevBtn || !nextBtn) return;
  
  const currentStep = stepsState.steps[stepsState.currentStep];
  
  // 是否可以返回上一步
  const canGoBack = stepsState.currentStep > 0 && 
                   (currentStep ? currentStep.canGoBack : true);
  prevBtn.disabled = !canGoBack || stepsState.stepChanging;
  
  // 是否可以前进下一步
  const canGoForward = stepsState.currentStep < stepsState.steps.length - 1 && 
                      (currentStep ? currentStep.canGoForward : true);
  nextBtn.disabled = !canGoForward || stepsState.stepChanging;
  
  // 最后一步时改变下一步按钮文本
  if (stepsState.currentStep === stepsState.steps.length - 1) {
    nextBtn.textContent = '完成';
  } else {
    nextBtn.textContent = '下一步';
  }
}

/**
 * 转到下一步
 * @returns {Boolean} 是否成功转到下一步
 */
function goToNextStep() {
  const nextStep = stepsState.currentStep + 1;
  if (nextStep >= stepsState.steps.length) {
    console.log('已经是最后一步');
    return false;
  }
  
  return goToStep(nextStep);
}

/**
 * 转到上一步
 * @returns {Boolean} 是否成功转到上一步
 */
function goToPrevStep() {
  const prevStep = stepsState.currentStep - 1;
  if (prevStep < 0) {
    console.log('已经是第一步');
    return false;
  }
  
  return goToStep(prevStep);
}

/**
 * 转到指定步骤
 * @param {Number} stepIndex - 步骤索引
 * @returns {Boolean} 是否成功转到指定步骤
 */
async function goToStep(stepIndex) {
  if (stepsState.stepChanging) {
    console.log('步骤切换中，请稍候');
    return false;
  }
  
  if (stepIndex < 0 || stepIndex >= stepsState.steps.length) {
    console.error(`步骤索引 [${stepIndex}] 超出范围`);
    return false;
  }
  
  if (stepIndex === stepsState.currentStep) {
    console.log('已经在当前步骤');
    return true;
  }
  
  // 检查是否允许导航
  if (!stepsState.allowNavigation) {
    console.log('当前禁止步骤导航');
    return false;
  }
  
  stepsState.stepChanging = true;
  updateControlsState();
  
  // 获取当前步骤和目标步骤
  const currentStep = stepsState.steps[stepsState.currentStep];
  const targetStep = stepsState.steps[stepIndex];
  
  if (currentStep.isDisabled || targetStep.isDisabled) {
    console.log('目标步骤已禁用');
    stepsState.stepChanging = false;
    updateControlsState();
    return false;
  }
  
  // 如果是前进，验证当前步骤是否有效
  if (stepIndex > stepsState.currentStep && !currentStep.isOptional) {
    const isValid = typeof currentStep.isValid === 'function' ? 
                    await Promise.resolve(currentStep.isValid()) : true;
    
    if (!isValid) {
      console.log('当前步骤验证失败，无法前进');
      stepsState.stepChanging = false;
      updateControlsState();
      return false;
    }
  }
  
  // 如果配置了确认变更，询问用户
  if (stepsState.confirmOnChange) {
    const confirmed = confirm(`确定要${stepIndex > stepsState.currentStep ? '前进到' : '返回到'}${targetStep.title}吗？`);
    if (!confirmed) {
      stepsState.stepChanging = false;
      updateControlsState();
      return false;
    }
  }
  
  // 离开当前步骤
  if (typeof currentStep.beforeLeave === 'function') {
    const canLeave = await Promise.resolve(currentStep.beforeLeave(stepIndex));
    if (canLeave === false) {
      console.log('当前步骤阻止了离开');
      stepsState.stepChanging = false;
      updateControlsState();
      return false;
    }
  }
  
  // 进入目标步骤
  if (typeof targetStep.beforeEnter === 'function') {
    const canEnter = await Promise.resolve(targetStep.beforeEnter(stepsState.currentStep));
    if (canEnter === false) {
      console.log('目标步骤阻止了进入');
      stepsState.stepChanging = false;
      updateControlsState();
      return false;
    }
  }
  
  // 更新当前步骤并显示
  const oldStep = stepsState.currentStep;
  stepsState.currentStep = stepIndex;
  _showStep(stepIndex);
  
  console.log(`从步骤 ${oldStep + 1} 转到步骤 ${stepIndex + 1}`);
  
  // 调用步骤变更回调
  if (typeof stepsState.onStepChange === 'function') {
    stepsState.onStepChange(oldStep, stepIndex);
  }
  
  // 离开后回调
  if (typeof currentStep.afterLeave === 'function') {
    await Promise.resolve(currentStep.afterLeave(stepIndex));
  }
  
  // 进入后回调
  if (typeof targetStep.afterEnter === 'function') {
    await Promise.resolve(targetStep.afterEnter(oldStep));
  }
  
  stepsState.stepChanging = false;
  updateControlsState();

  // 更新最小化状态
  if (typeof window.updateMinimizedSteps === 'function') {
    window.updateMinimizedSteps();
  }
  if (typeof window.updateMinimizedButtons === 'function') {
    window.updateMinimizedButtons();
  }

  // 更新展开状态的进度指示器
  console.log(`[Steps] 准备更新进度指示器，步骤索引: ${stepIndex}`);
  if (typeof window.updateExpandedProgress === 'function') {
    const totalSteps = window.stepsModule ? window.stepsModule.getTotalSteps() : 5;
    console.log(`[Steps] 调用进度更新函数: ${stepIndex + 1}/${totalSteps}`);
    window.updateExpandedProgress(stepIndex + 1, totalSteps);
  } else {
    console.error('[Steps] updateExpandedProgress 函数不存在');
  }

  return true;
}

/**
 * 处理键盘导航
 * @param {KeyboardEvent} event - 键盘事件
 * @private
 */
function _handleKeyNavigation(event) {
  // 仅在允许导航且未处于步骤切换状态时响应键盘导航
  if (!stepsState.allowNavigation || stepsState.stepChanging) {
    return;
  }
  
  // 判断是否在步骤容器内有元素被聚焦
  if (document.activeElement && !stepsState.containerElement.contains(document.activeElement)) {
    return;
  }
  
  switch (event.key) {
    case 'ArrowLeft':
      goToPrevStep();
      event.preventDefault();
      break;
    case 'ArrowRight':
      goToNextStep();
      event.preventDefault();
      break;
  }
}

/**
 * 启用或禁用步骤
 * @param {Number} stepIndex - 步骤索引
 * @param {Boolean} disabled - 是否禁用
 */
function setStepDisabled(stepIndex, disabled) {
  if (stepIndex < 0 || stepIndex >= stepsState.steps.length) {
    console.error(`步骤索引 [${stepIndex}] 超出范围`);
    return false;
  }
  
  stepsState.steps[stepIndex].isDisabled = !!disabled;
  
  // 更新UI
  const indicator = stepsState.containerElement.querySelector(`.step-indicator[data-step-index="${stepIndex}"]`);
  if (indicator) {
    if (disabled) {
      indicator.classList.add('disabled');
    } else {
      indicator.classList.remove('disabled');
    }
  }
  
  updateControlsState();
  return true;
}

/**
 * 获取当前步骤索引
 * @returns {Number} 当前步骤索引
 */
function getCurrentStepIndex() {
  return stepsState.currentStep;
}

/**
 * 获取当前步骤对象
 * @returns {Object} 当前步骤对象
 */
function getCurrentStep() {
  return stepsState.steps[stepsState.currentStep];
}

/**
 * 获取总步骤数
 * @returns {Number} 总步骤数
 */
function getTotalSteps() {
  return stepsState.steps.length;
}

/**
 * 获取步骤状态
 * @returns {Object} 步骤状态对象
 */
function getStepsState() {
  return { ...stepsState };
}

/**
 * 设置步骤数据
 * @param {Number} stepIndex - 步骤索引
 * @param {Object} data - 数据对象
 */
function setStepData(stepIndex, data) {
  if (stepIndex < 0 || stepIndex >= stepsState.steps.length) {
    console.error(`步骤索引 [${stepIndex}] 超出范围`);
    return false;
  }
  
  stepsState.steps[stepIndex].data = { ...stepsState.steps[stepIndex].data, ...data };
  return true;
}

// 导出函数
window.stepsModule = {
  initSteps,
  goToNextStep,
  goToPrevStep,
  goToStep,
  updateControlsState,
  setStepDisabled,
  getCurrentStepIndex,
  getCurrentStep,
  getTotalSteps,
  getStepsState,
  setStepData
}; 