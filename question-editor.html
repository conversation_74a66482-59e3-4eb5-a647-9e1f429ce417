<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库编辑器 - 研究生复试系统</title>
    <link rel="icon" href="assets/images/favicon.ico" type="image/x-icon">
    
    <!-- 基础样式 -->
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    
    <!-- 字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* 题库编辑器专用样式 */
        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #f8f9fa;
        }

        .app-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 30px;
            background: white;
            border-bottom: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo-link {
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
            color: #333;
        }

        .logo {
            height: 32px;
            width: auto;
        }

        .app-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }

        .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: #666;
            text-decoration: none;
        }

        .breadcrumb-item:hover {
            color: #007bff;
        }

        .breadcrumb-item.active {
            color: #333;
            font-weight: bold;
        }

        .breadcrumb-separator {
            color: #999;
        }

        .header-right {
            display: flex;
            gap: 10px;
        }

        .main-content {
            flex: 1;
            overflow: hidden;
        }

        .app-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 30px;
            background: white;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #666;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }

        .status-dot.error {
            background: #dc3545;
        }

        .status-dot.warning {
            background: #ffc107;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            text-align: center;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #666;
            font-size: 16px;
        }

        .error-page {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            text-align: center;
            padding: 40px;
        }

        .error-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .error-message {
            color: #666;
            margin: 20px 0;
            font-size: 16px;
        }

        .error-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
        }

        .toast {
            background: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 10px;
            min-width: 300px;
            animation: slideIn 0.3s ease;
        }

        .toast-success {
            border-left: 4px solid #28a745;
        }

        .toast-error {
            border-left: 4px solid #dc3545;
        }

        .toast-warning {
            border-left: 4px solid #ffc107;
        }

        .toast-info {
            border-left: 4px solid #007bff;
        }

        .toast-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
        }

        .toast-message {
            flex: 1;
            color: #333;
        }

        .toast-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #999;
            cursor: pointer;
            padding: 0;
            margin-left: 15px;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .modal-dialog {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            padding: 0;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 20px;
            border-top: 1px solid #dee2e6;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: white;
            color: #333;
            text-decoration: none;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .btn-primary {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background: #0056b3;
            border-color: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62;
            border-color: #545b62;
        }

        .btn-outline {
            background: transparent;
            color: #007bff;
            border-color: #007bff;
        }

        .btn-outline:hover {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <!-- 页面加载指示器 -->
    <div id="loadingIndicator" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">正在加载题库编辑器...</div>
        </div>
    </div>

    <!-- 主容器 -->
    <div id="app" class="app-container">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-left">
                <a href="/" class="logo-link">
                    <span class="app-title">📚 研究生复试系统</span>
                </a>
                <nav class="nav-breadcrumb">
                    <a href="/" class="breadcrumb-item">首页</a>
                    <span class="breadcrumb-separator">></span>
                    <span class="breadcrumb-item active">题库编辑器</span>
                </nav>
            </div>
            <div class="header-right">
                <button class="btn btn-outline" onclick="window.open('docs/editor.md', '_blank')">
                    <i class="fas fa-question-circle"></i>
                    帮助文档
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='/'">
                    <i class="fas fa-arrow-left"></i>
                    返回主系统
                </button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main id="mainContent" class="main-content">
            <!-- QuestionEditorView 将在这里渲染 -->
        </main>

        <!-- 底部状态栏 -->
        <footer class="app-footer">
            <div class="footer-left">
                <span class="status-indicator">
                    <span class="status-dot" id="connectionStatus"></span>
                    <span id="statusText">系统就绪</span>
                </span>
            </div>
            <div class="footer-center">
                <span id="operationStatus"></span>
            </div>
            <div class="footer-right">
                <span class="version-info">题库编辑器 v2.0.0</span>
            </div>
        </footer>
    </div>

    <!-- 通知容器 -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- 确认对话框模板 -->
    <div id="confirmDialog" class="modal-overlay" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 id="confirmTitle">确认操作</h3>
                <button class="modal-close" onclick="closeConfirmDialog()">&times;</button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeConfirmDialog()">取消</button>
                <button class="btn btn-primary" id="confirmButton" onclick="confirmAction()">确认</button>
            </div>
        </div>
    </div>

    <!-- 核心框架脚本 -->
    <script src="assets/js/core/EventBus.js"></script>
    <script src="assets/js/core/Logger.js"></script>
    <script src="assets/js/core/ConfigManager.js"></script>
    <script src="assets/js/core/Database.js"></script>
    <script src="assets/js/core/ServiceManager.js"></script>
    <script src="assets/js/core/ControllerManager.js"></script>
    <script src="assets/js/core/ViewManager.js"></script>
    <script src="assets/js/core/SystemManager.js"></script>
    <script src="assets/js/core/Application.js"></script>

    <!-- 数据模型 -->
    <script src="assets/js/models/BaseModel.js"></script>
    <script src="assets/js/models/QuestionModel.js"></script>
    <script src="assets/js/models/StudentModel.js"></script>
    <script src="assets/js/models/SettingsModel.js"></script>

    <!-- 基础类 -->
    <script src="assets/js/base/BaseService.js"></script>
    <script src="assets/js/base/BaseController.js"></script>
    <script src="assets/js/base/BaseView.js"></script>

    <!-- 服务层 -->
    <script src="assets/js/services/DatabaseService.js"></script>
    <script src="assets/js/services/QuestionService.js"></script>
    <script src="assets/js/services/StudentService.js"></script>
    <script src="assets/js/services/SettingsService.js"></script>

    <!-- 控制器层 -->
    <script src="assets/js/controllers/QuestionEditorController.js"></script>

    <!-- 视图层 -->
    <script src="assets/js/views/QuestionEditorView.js"></script>

    <!-- 工具函数 -->
    <script src="assets/js/utils/helpers.js"></script>
    <script src="assets/js/utils/validators.js"></script>
    <script src="assets/js/utils/formatters.js"></script>

    <!-- 题库编辑器主脚本 -->
    <script>
        // 全局变量
        let editorApp = null;
        let confirmCallback = null;

        /**
         * 启动题库编辑器应用
         */
        async function startEditorApplication() {
            try {
                console.log('🚀 启动题库编辑器应用...');

                // 创建应用实例
                editorApp = new Application({
                    name: 'QuestionEditor',
                    version: '2.0.0',
                    container: document.getElementById('mainContent')
                });

                // 注册组件
                editorApp.registerController('QuestionEditorController', QuestionEditorController);
                editorApp.registerView('QuestionEditorView', QuestionEditorView);

                // 初始化应用
                await editorApp.initialize();

                // 启动应用
                await editorApp.start();

                // 隐藏加载指示器
                document.getElementById('loadingIndicator').style.display = 'none';

                // 更新状态
                updateStatus('题库编辑器已就绪', 'success');

                console.log('✅ 题库编辑器启动成功');

            } catch (error) {
                console.error('❌ 题库编辑器启动失败:', error);

                // 显示错误信息
                showErrorPage(error);

                // 更新状态
                updateStatus('启动失败', 'error');
            }
        }

        /**
         * 显示错误页面
         */
        function showErrorPage(error) {
            const mainContent = document.getElementById('mainContent');
            mainContent.innerHTML = `
                <div class="error-page">
                    <div class="error-icon">⚠️</div>
                    <h2>应用启动失败</h2>
                    <p class="error-message">${error.message}</p>
                    <div class="error-actions">
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-redo"></i>
                            重新加载
                        </button>
                        <button class="btn btn-secondary" onclick="window.location.href='/'">
                            <i class="fas fa-home"></i>
                            返回首页
                        </button>
                    </div>
                </div>
            `;

            document.getElementById('loadingIndicator').style.display = 'none';
        }

        /**
         * 更新状态显示
         */
        function updateStatus(message, type = 'info') {
            const statusText = document.getElementById('statusText');
            const statusDot = document.getElementById('connectionStatus');
            const operationStatus = document.getElementById('operationStatus');

            if (statusText) {
                statusText.textContent = message;
            }

            if (statusDot) {
                statusDot.className = `status-dot ${type}`;
            }

            if (operationStatus) {
                operationStatus.textContent = message;
                setTimeout(() => {
                    operationStatus.textContent = '';
                }, 3000);
            }
        }

        /**
         * 显示确认对话框
         */
        function showConfirmDialog(title, message, callback) {
            const dialog = document.getElementById('confirmDialog');
            const titleElement = document.getElementById('confirmTitle');
            const messageElement = document.getElementById('confirmMessage');

            titleElement.textContent = title;
            messageElement.textContent = message;
            confirmCallback = callback;

            dialog.style.display = 'flex';
        }

        /**
         * 关闭确认对话框
         */
        function closeConfirmDialog() {
            const dialog = document.getElementById('confirmDialog');
            dialog.style.display = 'none';
            confirmCallback = null;
        }

        /**
         * 确认操作
         */
        function confirmAction() {
            if (confirmCallback) {
                confirmCallback();
            }
            closeConfirmDialog();
        }

        /**
         * 显示Toast通知
         */
        function showToast(message, type = 'info', duration = 3000) {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <span class="toast-message">${message}</span>
                    <button class="toast-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
                </div>
            `;

            container.appendChild(toast);

            // 自动移除
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, duration);
        }

        // 页面加载完成后启动应用
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 页面加载完成，准备启动题库编辑器...');

            // 延迟启动，确保所有脚本都已加载
            setTimeout(startEditorApplication, 100);
        });

        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
            updateStatus('发生错误', 'error');
        });

        // 全局未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
            updateStatus('发生错误', 'error');
        });

        // 暴露全局函数供调试使用
        window.editorDebug = {
            app: () => editorApp,
            status: updateStatus,
            toast: showToast,
            confirm: showConfirmDialog
        };
    </script>
</body>
</html>
