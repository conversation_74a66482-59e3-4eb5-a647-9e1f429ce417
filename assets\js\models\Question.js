/**
 * Question Model - 题目数据模型
 * 负责题目相关的数据结构定义和基础操作
 */

class Question {
    /**
     * 构造函数
     * @param {Object} data - 题目数据
     */
    constructor(data = {}) {
        this.id = data.id || '';
        this.type = data.type || '';
        this.index = data.index || data.question_index || data.questionIndex || 0;
        this.title = data.title || '';
        this.content = data.content || data.question_data || {};
        this.isUsed = data.isUsed || data.is_used || false;
        this.usedBy = data.usedBy || data.used_by || null;
        this.usedAt = data.usedAt || data.used_at || null;
        this.isActive = data.isActive || data.is_active !== false;
        this.createdAt = data.createdAt || data.created_at || null;
        this.updatedAt = data.updatedAt || data.updated_at || null;

        // 确保content是对象格式
        if (typeof this.content === 'string') {
            try {
                this.content = JSON.parse(this.content);
            } catch (e) {
                this.content = { text: this.content };
            }
        }

        // 生成默认ID（如果没有）
        if (!this.id && this.type && this.index) {
            this.id = `${this.type}_${this.index}`;
        }

        // 生成默认标题（如果没有）
        if (!this.title && this.type && this.index) {
            const typeNames = {
                'translation': '翻译题',
                'professional': '专业题'
            };
            this.title = `${typeNames[this.type] || this.type}${this.index}`;
        }
    }

    /**
     * 获取题目显示文本
     * @returns {string} 题目显示文本
     */
    getDisplayText() {
        if (this.type === 'translation') {
            return this.content.english || this.content.text || '翻译题目';
        } else if (this.type === 'professional') {
            return this.content.question || this.content.text || '专业题目';
        }
        return this.content.text || this.title || '题目';
    }

    /**
     * 获取题目答案或参考内容
     * @returns {string} 答案或参考内容
     */
    getAnswerText() {
        if (this.type === 'translation') {
            return this.content.chinese || this.content.answer || '';
        } else if (this.type === 'professional') {
            return this.content.answer || this.content.reference || '';
        }
        return this.content.answer || '';
    }

    /**
     * 获取题目难度
     * @returns {string} 题目难度
     */
    getDifficulty() {
        return this.content.difficulty || 'medium';
    }

    /**
     * 获取题目标签
     * @returns {Array} 题目标签数组
     */
    getTags() {
        return this.content.tags || [];
    }

    /**
     * 获取题目分值
     * @returns {number} 题目分值
     */
    getScore() {
        return this.content.score || 0;
    }

    /**
     * 获取题目时长限制
     * @returns {number} 时长限制（秒）
     */
    getTimeLimit() {
        return this.content.timeLimit || 0;
    }

    /**
     * 标记题目为已使用
     * @param {string} studentNumber - 使用的学生编号
     */
    markAsUsed(studentNumber) {
        this.isUsed = true;
        this.usedBy = studentNumber;
        this.usedAt = new Date().toISOString();
    }

    /**
     * 取消题目使用标记
     */
    unmarkAsUsed() {
        this.isUsed = false;
        this.usedBy = null;
        this.usedAt = null;
    }

    /**
     * 检查题目是否可用
     * @returns {boolean} 题目是否可用
     */
    isAvailable() {
        return this.isActive && !this.isUsed;
    }

    /**
     * 检查题目是否被指定学生使用
     * @param {string} studentNumber - 学生编号
     * @returns {boolean} 是否被指定学生使用
     */
    isUsedByStudent(studentNumber) {
        return this.isUsed && this.usedBy === studentNumber;
    }

    /**
     * 获取题目状态
     * @returns {string} 题目状态
     */
    getStatus() {
        if (!this.isActive) return 'inactive';
        if (this.isUsed) return 'used';
        return 'available';
    }

    /**
     * 获取题目状态显示文本
     * @returns {string} 状态显示文本
     */
    getStatusText() {
        const statusMap = {
            'available': '可抽取',
            'used': '已抽取',
            'inactive': '已禁用'
        };
        return statusMap[this.getStatus()] || '未知';
    }

    /**
     * 获取题目状态CSS类
     * @returns {string} CSS类名
     */
    getStatusClass() {
        const classMap = {
            'available': 'question-available',
            'used': 'question-used',
            'inactive': 'question-inactive'
        };
        return classMap[this.getStatus()] || 'question-unknown';
    }

    /**
     * 转换为数据库格式
     * @returns {Object} 数据库格式的数据
     */
    toDatabase() {
        return {
            id: this.id,
            type: this.type,
            question_index: this.index,
            title: this.title,
            question_data: JSON.stringify(this.content),
            is_active: this.isActive,
            updated_at: new Date().toISOString()
        };
    }

    /**
     * 转换为API格式
     * @returns {Object} API格式的数据
     */
    toAPI() {
        return {
            id: this.id,
            type: this.type,
            index: this.index,
            questionIndex: this.index, // 兼容字段
            title: this.title,
            content: this.content,
            isUsed: this.isUsed,
            usedBy: this.usedBy,
            usedAt: this.usedAt,
            isActive: this.isActive,
            status: this.getStatus(),
            statusText: this.getStatusText(),
            statusClass: this.getStatusClass(),
            displayText: this.getDisplayText(),
            answerText: this.getAnswerText(),
            difficulty: this.getDifficulty(),
            tags: this.getTags(),
            score: this.getScore(),
            timeLimit: this.getTimeLimit(),
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    /**
     * 转换为题目网格显示格式
     * @returns {Object} 网格显示格式
     */
    toGridFormat() {
        return {
            number: this.index,
            id: this.id,
            status: this.getStatus(),
            statusText: this.getStatusText(),
            statusClass: this.getStatusClass(),
            title: this.title,
            isAvailable: this.isAvailable(),
            usedBy: this.usedBy,
            usedAt: this.usedAt
        };
    }

    /**
     * 验证题目数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];

        if (!this.id || this.id.trim() === '') {
            errors.push('题目ID不能为空');
        }

        if (!this.type || !['translation', 'professional'].includes(this.type)) {
            errors.push('题目类型必须是translation或professional');
        }

        if (!this.index || this.index < 1) {
            errors.push('题目编号必须大于0');
        }

        if (!this.content || Object.keys(this.content).length === 0) {
            errors.push('题目内容不能为空');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 克隆题目对象
     * @returns {Question} 克隆的题目对象
     */
    clone() {
        return new Question(this.toAPI());
    }

    /**
     * 从数据库数据创建Question实例
     * @param {Object} dbData - 数据库数据
     * @returns {Question} Question实例
     */
    static fromDatabase(dbData) {
        return new Question(dbData);
    }

    /**
     * 从API数据创建Question实例
     * @param {Object} apiData - API数据
     * @returns {Question} Question实例
     */
    static fromAPI(apiData) {
        return new Question(apiData);
    }

    /**
     * 创建翻译题
     * @param {number} index - 题目编号
     * @param {string} english - 英文内容
     * @param {string} chinese - 中文内容
     * @returns {Question} 翻译题实例
     */
    static createTranslationQuestion(index, english, chinese = '') {
        return new Question({
            type: 'translation',
            index,
            content: {
                english,
                chinese,
                difficulty: 'medium'
            }
        });
    }

    /**
     * 创建专业题
     * @param {number} index - 题目编号
     * @param {string} question - 题目内容
     * @param {string} answer - 参考答案
     * @returns {Question} 专业题实例
     */
    static createProfessionalQuestion(index, question, answer = '') {
        return new Question({
            type: 'professional',
            index,
            content: {
                question,
                answer,
                difficulty: 'medium'
            }
        });
    }

    /**
     * 比较两个题目是否相同
     * @param {Question} other - 另一个题目
     * @returns {boolean} 是否相同
     */
    equals(other) {
        return other instanceof Question && 
               this.id === other.id && 
               this.type === other.type && 
               this.index === other.index;
    }
}

// 导出Question类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Question;
} else {
    window.Question = Question;
}
