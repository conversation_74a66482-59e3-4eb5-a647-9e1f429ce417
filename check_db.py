#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库检查工具 - 查看题库数据结构
"""

import sqlite3
import json

DB_PATH = 'assets/data/interview_system.db'

def check_database():
    """检查数据库结构和数据"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("=== 数据库检查报告 ===\n")
        
        # 检查表结构
        print("1. 检查表结构:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            table_name = table['name']
            print(f"   - {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            for col in columns:
                print(f"     * {col['name']} ({col['type']})")
        
        print("\n2. 检查翻译题数据:")
        cursor.execute("SELECT * FROM translation_questions LIMIT 3")
        translation_questions = cursor.fetchall()
        
        if translation_questions:
            for i, question in enumerate(translation_questions):
                print(f"   题目 {i+1}:")
                print(f"     ID: {question['id']}")
                print(f"     编号: {question['question_index']}")
                print(f"     数据: {question['question_data']}")
                
                # 尝试解析JSON数据
                try:
                    data = json.loads(question['question_data'])
                    print(f"     解析后的数据: {data}")
                except:
                    print(f"     JSON解析失败")
                print()
        else:
            print("   没有翻译题数据")
        
        print("\n3. 检查专业题数据:")
        cursor.execute("SELECT * FROM professional_questions LIMIT 3")
        professional_questions = cursor.fetchall()
        
        if professional_questions:
            for i, question in enumerate(professional_questions):
                print(f"   题目 {i+1}:")
                print(f"     ID: {question['id']}")
                print(f"     编号: {question['question_index']}")
                print(f"     数据: {question['question_data']}")
                
                # 尝试解析JSON数据
                try:
                    data = json.loads(question['question_data'])
                    print(f"     解析后的数据: {data}")
                except:
                    print(f"     JSON解析失败")
                print()
        else:
            print("   没有专业题数据")
        
        print("\n4. 统计信息:")
        cursor.execute("SELECT COUNT(*) as count FROM translation_questions")
        translation_count = cursor.fetchone()['count']
        print(f"   翻译题总数: {translation_count}")
        
        cursor.execute("SELECT COUNT(*) as count FROM professional_questions")
        professional_count = cursor.fetchone()['count']
        print(f"   专业题总数: {professional_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")

if __name__ == "__main__":
    check_database()
