/**
 * BaseController - 控制器基类
 * 提供所有控制器的通用功能和接口
 */

class BaseController {
    constructor() {
        this.eventListeners = new Map();
        this.state = {};
        this.isInitialized = false;
        this.eventBus = null;
    }

    /**
     * 初始化控制器
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }

        try {
            await this.onInitialize();
            this.isInitialized = true;
            this.emit('initialized', this);
        } catch (error) {
            console.error(`${this.constructor.name} 初始化失败:`, error);
            throw error;
        }
    }

    /**
     * 子类重写此方法进行具体初始化
     * @returns {Promise<void>}
     */
    async onInitialize() {
        // 子类实现
    }

    /**
     * 设置事件总线
     * @param {EventBus} eventBus - 事件总线实例
     */
    setEventBus(eventBus) {
        this.eventBus = eventBus;
    }

    /**
     * 更新控制器状态
     * @param {Object} newState - 新状态
     */
    updateState(newState) {
        const oldState = { ...this.state };
        this.state = { ...this.state, ...newState };
        
        this.emit('stateChanged', {
            controller: this.constructor.name,
            oldState,
            newState: this.state,
            changes: newState
        });
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getState() {
        return { ...this.state };
    }

    /**
     * 重置状态
     */
    resetState() {
        const oldState = { ...this.state };
        this.state = {};
        this.emit('stateReset', { controller: this.constructor.name, oldState });
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     */
    emit(event, data) {
        // 触发本地监听器
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理器错误 (${event}):`, error);
                }
            });
        }

        // 触发全局事件总线
        if (this.eventBus) {
            this.eventBus.emit(`${this.constructor.name}.${event}`, data);
        }
    }

    /**
     * 监听全局事件
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    listenGlobal(event, callback) {
        if (this.eventBus) {
            this.eventBus.on(event, callback);
        }
    }

    /**
     * 执行异步操作并处理错误
     * @param {Function} operation - 异步操作函数
     * @param {string} context - 操作上下文
     * @returns {Promise<any>} 操作结果
     */
    async executeOperation(operation, context = 'unknown') {
        try {
            this.emit('operationStarted', { context });
            const result = await operation();
            this.emit('operationCompleted', { context, result });
            return result;
        } catch (error) {
            console.error(`${this.constructor.name} 操作失败 [${context}]:`, error);
            this.emit('operationFailed', { context, error });
            throw error;
        }
    }

    /**
     * 验证操作前置条件
     * @param {Array<Function>} conditions - 条件检查函数数组
     * @param {string} operation - 操作名称
     * @returns {boolean} 是否满足条件
     */
    validateConditions(conditions, operation = 'unknown') {
        for (let i = 0; i < conditions.length; i++) {
            const condition = conditions[i];
            try {
                if (!condition()) {
                    this.emit('conditionFailed', { 
                        operation, 
                        conditionIndex: i,
                        message: `条件 ${i + 1} 不满足`
                    });
                    return false;
                }
            } catch (error) {
                console.error(`条件检查失败 [${operation}]:`, error);
                this.emit('conditionError', { operation, conditionIndex: i, error });
                return false;
            }
        }
        return true;
    }

    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handleError(error, context = 'unknown') {
        const errorInfo = {
            controller: this.constructor.name,
            context,
            error: {
                message: error.message,
                code: error.code || 'UNKNOWN_ERROR',
                stack: error.stack
            },
            timestamp: new Date().toISOString()
        };

        console.error(`${this.constructor.name} 错误 [${context}]:`, error);
        this.emit('error', errorInfo);

        // 根据错误类型进行不同处理
        switch (error.code) {
            case 'VALIDATION_ERROR':
                this.handleValidationError(error, context);
                break;
            case 'NETWORK_ERROR':
                this.handleNetworkError(error, context);
                break;
            case 'PERMISSION_ERROR':
                this.handlePermissionError(error, context);
                break;
            default:
                this.handleGenericError(error, context);
        }
    }

    /**
     * 处理验证错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handleValidationError(error, context) {
        this.emit('validationError', { error, context });
    }

    /**
     * 处理网络错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handleNetworkError(error, context) {
        this.emit('networkError', { error, context });
    }

    /**
     * 处理权限错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handlePermissionError(error, context) {
        this.emit('permissionError', { error, context });
    }

    /**
     * 处理通用错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handleGenericError(error, context) {
        this.emit('genericError', { error, context });
    }

    /**
     * 记录日志
     * @param {string} level - 日志级别
     * @param {string} message - 日志消息
     * @param {any} data - 附加数据
     */
    log(level, message, data = null) {
        const logEntry = {
            controller: this.constructor.name,
            level,
            message,
            data,
            timestamp: new Date().toISOString()
        };

        console[level] ? console[level](message, data) : console.log(message, data);
        this.emit('log', logEntry);
    }

    /**
     * 销毁控制器
     */
    destroy() {
        try {
            this.onDestroy();
            this.eventListeners.clear();
            this.state = {};
            this.isInitialized = false;
            this.emit('destroyed', this);
        } catch (error) {
            console.error(`${this.constructor.name} 销毁失败:`, error);
        }
    }

    /**
     * 子类重写此方法进行清理工作
     */
    onDestroy() {
        // 子类实现
    }

    /**
     * 获取控制器信息
     * @returns {Object} 控制器信息
     */
    getInfo() {
        return {
            name: this.constructor.name,
            isInitialized: this.isInitialized,
            state: this.getState(),
            eventListenerCount: Array.from(this.eventListeners.values())
                .reduce((total, listeners) => total + listeners.length, 0)
        };
    }

    /**
     * 检查控制器是否健康
     * @returns {boolean} 是否健康
     */
    isHealthy() {
        return this.isInitialized && !this.hasErrors();
    }

    /**
     * 检查是否有错误
     * @returns {boolean} 是否有错误
     */
    hasErrors() {
        return this.state.hasErrors || false;
    }

    /**
     * 清除错误状态
     */
    clearErrors() {
        this.updateState({ hasErrors: false, lastError: null });
    }
}

// 导出BaseController类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BaseController;
} else {
    window.BaseController = BaseController;
}
