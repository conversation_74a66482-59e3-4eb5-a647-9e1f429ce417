/**
 * ExamView - 考试主界面组件
 * 负责考试整体布局管理、步骤导航显示、学生信息展示
 */

class ExamView extends BaseView {
    constructor(container) {
        super(container);

        // 视图状态
        this.state = {
            currentStudent: null,
            currentStep: 1,
            totalSteps: 5,
            examStatus: 'idle',
            stepStatus: 'pending',
            leftPanelExpanded: true,
            rightPanelExpanded: true
        };

        // 步骤定义
        this.stepDefinitions = {
            1: { name: '中文自我介绍', icon: '🗣️', time: '2分钟' },
            2: { name: '英文自我介绍', icon: '🌍', time: '2分钟' },
            3: { name: '英文翻译', icon: '📝', time: '5分钟' },
            4: { name: '专业课问答', icon: '🎓', time: '10分钟' },
            5: { name: '综合面试', icon: '💼', time: '8分钟' }
        };

        this.template = this.getExamTemplate();
        this.styles = this.getExamStyles();
    }

    /**
     * 获取考试界面模板
     * @returns {string} HTML模板
     */
    getExamTemplate() {
        return `
            <div class="exam-container">
                <!-- 头部信息栏 -->
                <header class="exam-header">
                    <div class="student-info">
                        <span class="student-number" data-element="studentNumber">考生编号: --</span>
                        <span class="exam-status" data-element="examStatus">未开始</span>
                    </div>
                    <div class="system-controls">
                        <button class="settings-btn" data-action="showSettings">
                            <i class="icon-settings"></i>
                            <span>系统设置</span>
                        </button>
                        <button class="switch-student-btn" data-action="switchStudent">
                            <i class="icon-user"></i>
                            <span>切换考生</span>
                        </button>
                    </div>
                </header>

                <!-- 三栏布局 -->
                <main class="exam-main">
                    <!-- 左侧进度面板 -->
                    <aside class="progress-panel" data-element="progressPanel">
                        <div class="panel-header">
                            <h3>考试进度</h3>
                            <button class="panel-toggle" data-action="toggleLeftPanel">
                                <i class="icon-chevron-left" data-element="leftPanelIcon"></i>
                            </button>
                        </div>
                        <div class="panel-content">
                            <div class="step-list" data-element="stepList">
                                <!-- 步骤列表将动态生成 -->
                            </div>
                            <div class="progress-summary" data-element="progressSummary">
                                <div class="progress-bar">
                                    <div class="progress-fill" data-element="progressFill" style="width: 0%"></div>
                                </div>
                                <div class="progress-text">
                                    <span data-element="progressText">第1步 / 共5步</span>
                                </div>
                            </div>
                        </div>
                    </aside>

                    <!-- 中央内容区 -->
                    <section class="content-area" data-element="contentArea">
                        <div class="content-header">
                            <h2 class="current-step-title" data-element="currentStepTitle">
                                <i class="step-icon" data-element="stepIcon">🗣️</i>
                                <span data-element="stepName">中文自我介绍</span>
                            </h2>
                            <div class="step-info" data-element="stepInfo">
                                <span class="step-time" data-element="stepTime">2分钟</span>
                                <span class="step-status" data-element="stepStatusText">准备中</span>
                            </div>
                        </div>
                        <div class="content-body" data-element="contentBody">
                            <!-- 动态内容区域 -->
                            <div id="step-content" data-element="stepContent">
                                <div class="welcome-message">
                                    <h3>欢迎参加研究生复试</h3>
                                    <p>请选择考生编号开始考试</p>
                                </div>
                            </div>
                        </div>
                        <div class="content-footer">
                            <div class="navigation-controls" data-element="navigationControls">
                                <button class="nav-btn prev-step-btn" data-action="previousStep" data-element="prevStepBtn">
                                    <i class="icon-chevron-left"></i>
                                    <span>上一步</span>
                                </button>
                                <button class="nav-btn next-step-btn" data-action="nextStep" data-element="nextStepBtn">
                                    <span>下一步</span>
                                    <i class="icon-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </section>

                    <!-- 右侧控制面板 -->
                    <aside class="control-panel" data-element="controlPanel">
                        <div class="panel-header">
                            <h3>操作控制</h3>
                            <button class="panel-toggle" data-action="toggleRightPanel">
                                <i class="icon-chevron-right" data-element="rightPanelIcon"></i>
                            </button>
                        </div>
                        <div class="panel-content">
                            <div class="timer-section" data-element="timerSection">
                                <!-- 计时器组件将在这里渲染 -->
                            </div>
                            <div class="action-section" data-element="actionSection">
                                <button class="action-btn draw-question-btn" data-action="drawQuestion" data-element="drawQuestionBtn">
                                    <i class="icon-dice"></i>
                                    <span>抽取题目</span>
                                </button>
                                <button class="action-btn complete-step-btn" data-action="completeStep" data-element="completeStepBtn">
                                    <i class="icon-check"></i>
                                    <span>完成步骤</span>
                                </button>
                                <button class="action-btn start-exam-btn" data-action="startExam" data-element="startExamBtn">
                                    <i class="icon-play"></i>
                                    <span>开始考试</span>
                                </button>
                            </div>
                        </div>
                    </aside>
                </main>

                <!-- 底部状态栏 -->
                <footer class="exam-footer">
                    <div class="status-info">
                        <span class="current-step" data-element="currentStepFooter">第1步</span>
                        <span class="total-time" data-element="totalTime">总用时: 00:00</span>
                        <span class="system-status" data-element="systemStatus">系统正常</span>
                    </div>
                </footer>
            </div>
        `;
    }

    /**
     * 获取考试界面样式
     * @returns {string} CSS样式
     */
    getExamStyles() {
        return `
            .exam-container {
                display: flex;
                flex-direction: column;
                height: 100vh;
                background: #f5f5f5;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            /* 头部样式 */
            .exam-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem 2rem;
                background: #fff;
                border-bottom: 1px solid #e0e0e0;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .student-info {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .student-number {
                font-weight: 600;
                color: #333;
            }

            .exam-status {
                padding: 0.25rem 0.75rem;
                border-radius: 1rem;
                font-size: 0.875rem;
                font-weight: 500;
            }

            .exam-status.idle { background: #f3f4f6; color: #6b7280; }
            .exam-status.active { background: #dbeafe; color: #1d4ed8; }
            .exam-status.completed { background: #d1fae5; color: #065f46; }
            .exam-status.paused { background: #fef3c7; color: #92400e; }

            .system-controls {
                display: flex;
                gap: 0.5rem;
            }

            .settings-btn, .switch-student-btn {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1rem;
                border: 1px solid #d1d5db;
                border-radius: 0.5rem;
                background: #fff;
                color: #374151;
                cursor: pointer;
                transition: all 0.2s;
            }

            .settings-btn:hover, .switch-student-btn:hover {
                background: #f9fafb;
                border-color: #9ca3af;
            }

            /* 主体三栏布局 */
            .exam-main {
                display: grid;
                grid-template-columns: 300px 1fr 280px;
                flex: 1;
                gap: 1px;
                background: #e5e7eb;
            }

            .exam-main.left-collapsed {
                grid-template-columns: 60px 1fr 280px;
            }

            .exam-main.right-collapsed {
                grid-template-columns: 300px 1fr 60px;
            }

            .exam-main.both-collapsed {
                grid-template-columns: 60px 1fr 60px;
            }

            /* 侧边面板样式 */
            .progress-panel, .control-panel {
                background: #fff;
                display: flex;
                flex-direction: column;
                transition: all 0.3s ease;
            }

            .panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem;
                border-bottom: 1px solid #e5e7eb;
                background: #f9fafb;
            }

            .panel-header h3 {
                margin: 0;
                font-size: 1rem;
                font-weight: 600;
                color: #374151;
            }

            .panel-toggle {
                padding: 0.25rem;
                border: none;
                background: none;
                cursor: pointer;
                border-radius: 0.25rem;
                transition: background 0.2s;
            }

            .panel-toggle:hover {
                background: #e5e7eb;
            }

            .panel-content {
                flex: 1;
                padding: 1rem;
                overflow-y: auto;
            }

            /* 步骤列表样式 */
            .step-list {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
                margin-bottom: 1rem;
            }

            .step-item {
                display: flex;
                align-items: center;
                padding: 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid #e5e7eb;
                background: #fff;
                cursor: pointer;
                transition: all 0.2s;
            }

            .step-item:hover {
                border-color: #d1d5db;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .step-item.current {
                border-color: #3b82f6;
                background: #eff6ff;
            }

            .step-item.completed {
                border-color: #10b981;
                background: #ecfdf5;
            }

            .step-icon {
                font-size: 1.25rem;
                margin-right: 0.75rem;
            }

            .step-info {
                flex: 1;
            }

            .step-title {
                font-weight: 500;
                color: #374151;
                margin-bottom: 0.25rem;
            }

            .step-time {
                font-size: 0.875rem;
                color: #6b7280;
            }

            .step-status {
                font-size: 0.75rem;
                padding: 0.125rem 0.5rem;
                border-radius: 0.75rem;
                margin-left: 0.5rem;
            }

            .step-status.pending { background: #f3f4f6; color: #6b7280; }
            .step-status.active { background: #dbeafe; color: #1d4ed8; }
            .step-status.completed { background: #d1fae5; color: #065f46; }

            /* 进度条样式 */
            .progress-bar {
                width: 100%;
                height: 0.5rem;
                background: #e5e7eb;
                border-radius: 0.25rem;
                overflow: hidden;
                margin-bottom: 0.5rem;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #3b82f6, #1d4ed8);
                transition: width 0.5s ease;
            }

            .progress-text {
                text-align: center;
                font-size: 0.875rem;
                color: #6b7280;
            }

            /* 中央内容区样式 */
            .content-area {
                background: #fff;
                display: flex;
                flex-direction: column;
            }

            .content-header {
                padding: 1.5rem 2rem;
                border-bottom: 1px solid #e5e7eb;
                background: #fff;
            }

            .current-step-title {
                display: flex;
                align-items: center;
                margin: 0 0 0.5rem 0;
                font-size: 1.5rem;
                font-weight: 600;
                color: #111827;
            }

            .current-step-title .step-icon {
                margin-right: 0.75rem;
                font-size: 1.75rem;
            }

            .step-info {
                display: flex;
                align-items: center;
                gap: 1rem;
                font-size: 0.875rem;
                color: #6b7280;
            }

            .content-body {
                flex: 1;
                padding: 2rem;
                overflow-y: auto;
            }

            .welcome-message {
                text-align: center;
                padding: 3rem 1rem;
                color: #6b7280;
            }

            .welcome-message h3 {
                margin: 0 0 1rem 0;
                font-size: 1.5rem;
                color: #374151;
            }

            .content-footer {
                padding: 1rem 2rem;
                border-top: 1px solid #e5e7eb;
                background: #f9fafb;
            }

            .navigation-controls {
                display: flex;
                justify-content: space-between;
            }

            .nav-btn {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.75rem 1.5rem;
                border: 1px solid #d1d5db;
                border-radius: 0.5rem;
                background: #fff;
                color: #374151;
                cursor: pointer;
                font-weight: 500;
                transition: all 0.2s;
            }

            .nav-btn:hover {
                background: #f9fafb;
                border-color: #9ca3af;
            }

            .nav-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            /* 操作按钮样式 */
            .action-section {
                display: flex;
                flex-direction: column;
                gap: 0.75rem;
            }

            .action-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                padding: 0.75rem 1rem;
                border: none;
                border-radius: 0.5rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
            }

            .draw-question-btn {
                background: #3b82f6;
                color: #fff;
            }

            .draw-question-btn:hover {
                background: #2563eb;
            }

            .complete-step-btn {
                background: #10b981;
                color: #fff;
            }

            .complete-step-btn:hover {
                background: #059669;
            }

            .start-exam-btn {
                background: #f59e0b;
                color: #fff;
            }

            .start-exam-btn:hover {
                background: #d97706;
            }

            .action-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            /* 底部状态栏样式 */
            .exam-footer {
                padding: 0.75rem 2rem;
                background: #374151;
                color: #fff;
                border-top: 1px solid #4b5563;
            }

            .status-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 0.875rem;
            }

            /* 响应式设计 */
            @media (max-width: 1200px) {
                .exam-main {
                    grid-template-columns: 250px 1fr 250px;
                }
            }

            @media (max-width: 992px) {
                .exam-main {
                    grid-template-columns: 1fr;
                    grid-template-rows: auto 1fr auto;
                }

                .progress-panel,
                .control-panel {
                    order: 3;
                }

                .content-area {
                    order: 2;
                }
            }

            @media (max-width: 768px) {
                .exam-header {
                    padding: 0.75rem 1rem;
                }

                .system-controls {
                    flex-direction: column;
                    gap: 0.25rem;
                }

                .content-header {
                    padding: 1rem;
                }

                .content-body {
                    padding: 1rem;
                }

                .current-step-title {
                    font-size: 1.25rem;
                }
            }
        `;
    }

    /**
     * 初始化视图
     */
    async onInitialize() {
        // 生成步骤列表
        this.generateStepList();

        // 设置初始状态
        this.updateExamState({
            currentStep: 1,
            examStatus: 'idle'
        });

        // 监听全局事件
        this.setupGlobalListeners();
    }

    /**
     * 设置全局监听器
     */
    setupGlobalListeners() {
        if (this.eventBus) {
            // 监听考试控制器事件
            this.eventBus.on('ExamController.examInitialized', (data) => {
                this.handleExamInitialized(data);
            });

            this.eventBus.on('ExamController.examStarted', (data) => {
                this.handleExamStarted(data);
            });

            this.eventBus.on('ExamController.stepChanged', (data) => {
                this.handleStepChanged(data);
            });

            this.eventBus.on('ExamController.examCompleted', (data) => {
                this.handleExamCompleted(data);
            });

            // 监听计时器事件
            this.eventBus.on('TimerController.tick', (data) => {
                this.updateTotalTime(data);
            });
        }
    }

    /**
     * 缓存特定元素
     */
    onCacheElements() {
        // 缓存步骤相关元素
        this.stepElements = {};
        for (let i = 1; i <= this.state.totalSteps; i++) {
            this.stepElements[i] = this.container.querySelector(`[data-step="${i}"]`);
        }
    }

    /**
     * 绑定特定事件
     */
    onBindEvents() {
        // 监听面板切换
        this.container.addEventListener('click', (event) => {
            if (event.target.closest('[data-step]')) {
                const stepElement = event.target.closest('[data-step]');
                const stepNumber = parseInt(stepElement.getAttribute('data-step'));
                this.handleStepClick(stepNumber);
            }
        });
    }

    /**
     * 处理动作
     * @param {string} action - 动作名称
     * @param {Element} target - 目标元素
     * @param {Event} event - 原始事件
     */
    handleAction(action, target, event) {
        switch (action) {
            case 'startExam':
                this.handleStartExamAction();
                break;
            case 'switchStudent':
                this.handleSwitchStudentAction();
                break;
            case 'drawQuestion':
                this.handleDrawQuestionAction();
                break;
            case 'completeStep':
                this.handleCompleteStepAction();
                break;
            case 'nextStep':
                this.handleNextStepAction();
                break;
            case 'previousStep':
                this.handlePreviousStepAction();
                break;
            case 'toggleLeftPanel':
                this.handleToggleLeftPanelAction();
                break;
            case 'toggleRightPanel':
                this.handleToggleRightPanelAction();
                break;
            case 'openSettings':
                this.handleOpenSettingsAction();
                break;
            default:
                // 调用父类方法
                super.handleAction(action, target, event);
        }
    }

    // ==================== 动作处理方法 ====================

    /**
     * 处理显示设置动作
     */
    handleShowSettingsAction() {
        this.emit('settingsRequested');
    }

    /**
     * 处理切换学生动作
     */
    handleSwitchStudentAction() {
        this.showStudentSwitchDialog();
    }

    /**
     * 处理上一步动作
     */
    handlePreviousStepAction() {
        this.emit('previousStepRequested');
    }

    /**
     * 处理下一步动作
     */
    handleNextStepAction() {
        this.emit('nextStepRequested');
    }

    /**
     * 处理抽取题目动作
     */
    handleDrawQuestionAction() {
        const stepInfo = this.stepDefinitions[this.state.currentStep];
        if (stepInfo && this.hasQuestions(this.state.currentStep)) {
            this.emit('drawQuestionRequested', {
                type: this.getQuestionType(this.state.currentStep),
                method: 'random'
            });
        }
    }

    /**
     * 处理完成步骤动作
     */
    handleCompleteStepAction() {
        this.emit('completeStepRequested', {
            stepNumber: this.state.currentStep
        });
    }

    /**
     * 处理开始考试动作
     */
    handleStartExamAction() {
        this.showStudentNumberDialog();
    }

    /**
     * 处理切换学生动作
     */
    handleSwitchStudentAction() {
        this.showStudentSwitchDialog();
    }

    /**
     * 处理抽取题目动作
     */
    handleDrawQuestionAction() {
        this.emit('drawQuestionRequested', {
            stepNumber: this.state.currentStep
        });
    }

    /**
     * 处理完成步骤动作
     */
    handleCompleteStepAction() {
        this.emit('completeStepRequested', {
            stepNumber: this.state.currentStep
        });
    }

    /**
     * 处理下一步动作
     */
    handleNextStepAction() {
        this.emit('nextStepRequested', {
            currentStep: this.state.currentStep
        });
    }

    /**
     * 处理上一步动作
     */
    handlePreviousStepAction() {
        this.emit('previousStepRequested', {
            currentStep: this.state.currentStep
        });
    }

    /**
     * 处理切换左侧面板动作
     */
    handleToggleLeftPanelAction() {
        this.toggleLeftPanel();
    }

    /**
     * 处理切换右侧面板动作
     */
    handleToggleRightPanelAction() {
        this.toggleRightPanel();
    }

    /**
     * 处理打开设置动作
     */
    handleOpenSettingsAction() {
        this.emit('openSettingsRequested');
    }

    /**
     * 处理切换左侧面板动作
     */
    handleToggleLeftPanelAction() {
        this.toggleLeftPanel();
    }

    /**
     * 处理切换右侧面板动作
     */
    handleToggleRightPanelAction() {
        this.toggleRightPanel();
    }

    // ==================== 界面更新方法 ====================

    /**
     * 更新考试状态
     * @param {Object} stateUpdate - 状态更新
     */
    updateExamState(stateUpdate) {
        this.state = { ...this.state, ...stateUpdate };

        // 更新学生信息
        if (stateUpdate.currentStudent) {
            this.updateStudentInfo(stateUpdate.currentStudent);
        }

        // 更新当前步骤
        if (stateUpdate.currentStep) {
            this.updateCurrentStep(stateUpdate.currentStep);
        }

        // 更新考试状态
        if (stateUpdate.examStatus) {
            this.updateExamStatus(stateUpdate.examStatus);
        }

        // 更新进度
        this.updateProgress();
    }

    /**
     * 更新学生信息
     * @param {Object} student - 学生对象
     */
    updateStudentInfo(student) {
        if (this.elements.studentNumber) {
            this.elements.studentNumber.textContent = `考生编号: ${student.studentNumber || '--'}`;
        }
    }

    /**
     * 更新当前步骤
     * @param {number} stepNumber - 步骤编号
     */
    updateCurrentStep(stepNumber) {
        const stepInfo = this.stepDefinitions[stepNumber];
        if (!stepInfo) return;

        // 更新步骤标题
        if (this.elements.stepIcon) {
            this.elements.stepIcon.textContent = stepInfo.icon;
        }
        if (this.elements.stepName) {
            this.elements.stepName.textContent = stepInfo.name;
        }
        if (this.elements.stepTime) {
            this.elements.stepTime.textContent = stepInfo.time;
        }

        // 更新步骤列表状态
        this.updateStepListStatus(stepNumber);

        // 更新导航按钮状态
        this.updateNavigationButtons(stepNumber);

        // 更新操作按钮状态
        this.updateActionButtons(stepNumber);

        // 更新底部状态
        if (this.elements.currentStepFooter) {
            this.elements.currentStepFooter.textContent = `第${stepNumber}步`;
        }
    }

    /**
     * 更新考试状态
     * @param {string} status - 考试状态
     */
    updateExamStatus(status) {
        if (this.elements.examStatus) {
            const statusText = this.getStatusText(status);
            this.elements.examStatus.textContent = statusText;
            this.elements.examStatus.className = `exam-status ${status}`;
        }
    }

    /**
     * 更新进度
     */
    updateProgress() {
        const progressPercentage = ((this.state.currentStep - 1) / (this.state.totalSteps - 1)) * 100;

        if (this.elements.progressFill) {
            this.elements.progressFill.style.width = `${progressPercentage}%`;
        }

        if (this.elements.progressText) {
            this.elements.progressText.textContent = `第${this.state.currentStep}步 / 共${this.state.totalSteps}步`;
        }
    }

    /**
     * 生成步骤列表
     */
    generateStepList() {
        if (!this.elements.stepList) return;

        const stepListHTML = Object.entries(this.stepDefinitions).map(([stepNum, stepInfo]) => {
            return `
                <div class="step-item" data-step="${stepNum}">
                    <div class="step-icon">${stepInfo.icon}</div>
                    <div class="step-info">
                        <div class="step-title">${stepInfo.name}</div>
                        <div class="step-time">${stepInfo.time}</div>
                    </div>
                    <div class="step-status pending" data-step-status="${stepNum}">准备中</div>
                </div>
            `;
        }).join('');

        this.elements.stepList.innerHTML = stepListHTML;
    }

    /**
     * 更新步骤列表状态
     * @param {number} currentStep - 当前步骤
     */
    updateStepListStatus(currentStep) {
        for (let i = 1; i <= this.state.totalSteps; i++) {
            const stepElement = this.container.querySelector(`[data-step="${i}"]`);
            const statusElement = this.container.querySelector(`[data-step-status="${i}"]`);

            if (stepElement && statusElement) {
                stepElement.classList.remove('current', 'completed', 'pending');
                statusElement.classList.remove('current', 'completed', 'pending');

                if (i < currentStep) {
                    stepElement.classList.add('completed');
                    statusElement.classList.add('completed');
                    statusElement.textContent = '已完成';
                } else if (i === currentStep) {
                    stepElement.classList.add('current');
                    statusElement.classList.add('current');
                    statusElement.textContent = '进行中';
                } else {
                    stepElement.classList.add('pending');
                    statusElement.classList.add('pending');
                    statusElement.textContent = '准备中';
                }
            }
        }
    }

    /**
     * 更新导航按钮状态
     * @param {number} currentStep - 当前步骤
     */
    updateNavigationButtons(currentStep) {
        if (this.elements.prevStepBtn) {
            this.elements.prevStepBtn.disabled = currentStep <= 1;
        }

        if (this.elements.nextStepBtn) {
            this.elements.nextStepBtn.disabled = currentStep >= this.state.totalSteps;
        }
    }

    /**
     * 更新操作按钮状态
     * @param {number} currentStep - 当前步骤
     */
    updateActionButtons(currentStep) {
        const hasQuestions = this.hasQuestions(currentStep);

        if (this.elements.drawQuestionBtn) {
            this.elements.drawQuestionBtn.style.display = hasQuestions ? 'flex' : 'none';
        }

        if (this.elements.startExamBtn) {
            this.elements.startExamBtn.style.display = this.state.examStatus === 'idle' ? 'flex' : 'none';
        }
    }

    // ==================== 面板管理 ====================

    /**
     * 切换左侧面板
     */
    toggleLeftPanel() {
        this.state.leftPanelExpanded = !this.state.leftPanelExpanded;
        this.updatePanelLayout();

        if (this.elements.leftPanelIcon) {
            this.elements.leftPanelIcon.className = this.state.leftPanelExpanded
                ? 'icon-chevron-left'
                : 'icon-chevron-right';
        }
    }

    /**
     * 切换右侧面板
     */
    toggleRightPanel() {
        this.state.rightPanelExpanded = !this.state.rightPanelExpanded;
        this.updatePanelLayout();

        if (this.elements.rightPanelIcon) {
            this.elements.rightPanelIcon.className = this.state.rightPanelExpanded
                ? 'icon-chevron-right'
                : 'icon-chevron-left';
        }
    }

    /**
     * 更新面板布局
     */
    updatePanelLayout() {
        const examMain = this.container.querySelector('.exam-main');
        if (!examMain) return;

        examMain.classList.remove('left-collapsed', 'right-collapsed', 'both-collapsed');

        if (!this.state.leftPanelExpanded && !this.state.rightPanelExpanded) {
            examMain.classList.add('both-collapsed');
        } else if (!this.state.leftPanelExpanded) {
            examMain.classList.add('left-collapsed');
        } else if (!this.state.rightPanelExpanded) {
            examMain.classList.add('right-collapsed');
        }
    }

    // ==================== 对话框管理 ====================

    /**
     * 显示学生编号输入对话框
     */
    showStudentNumberDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay';
        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>输入考生编号</h3>
                    <button class="modal-close" data-action="closeDialog">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="studentNumberInput">考生编号:</label>
                        <input type="text" id="studentNumberInput" class="form-input"
                               placeholder="请输入考生编号" data-field="studentNumber">
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" data-action="closeDialog">取消</button>
                    <button class="btn btn-primary" data-action="confirmStudentNumber">确认</button>
                </div>
            </div>
        `;

        // 添加对话框样式
        this.addDialogStyles();

        // 绑定对话框事件
        dialog.addEventListener('click', (event) => {
            if (event.target.classList.contains('modal-overlay')) {
                this.closeDialog(dialog);
            }

            const action = event.target.getAttribute('data-action');
            if (action === 'closeDialog') {
                this.closeDialog(dialog);
            } else if (action === 'confirmStudentNumber') {
                this.handleStudentNumberConfirm(dialog);
            }
        });

        // 监听回车键
        const input = dialog.querySelector('#studentNumberInput');
        input.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                this.handleStudentNumberConfirm(dialog);
            }
        });

        document.body.appendChild(dialog);
        input.focus();
    }

    /**
     * 显示学生切换对话框
     */
    showStudentSwitchDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay';
        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>切换考生</h3>
                    <button class="modal-close" data-action="closeDialog">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="newStudentNumberInput">新考生编号:</label>
                        <input type="text" id="newStudentNumberInput" class="form-input"
                               placeholder="请输入新考生编号" data-field="studentNumber">
                    </div>
                    <div class="warning-message">
                        <p>⚠️ 切换考生将重置当前考试进度</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" data-action="closeDialog">取消</button>
                    <button class="btn btn-primary" data-action="confirmStudentSwitch">确认切换</button>
                </div>
            </div>
        `;

        // 添加对话框样式
        this.addDialogStyles();

        // 绑定对话框事件
        dialog.addEventListener('click', (event) => {
            if (event.target.classList.contains('modal-overlay')) {
                this.closeDialog(dialog);
            }

            const action = event.target.getAttribute('data-action');
            if (action === 'closeDialog') {
                this.closeDialog(dialog);
            } else if (action === 'confirmStudentSwitch') {
                this.handleStudentSwitchConfirm(dialog);
            }
        });

        // 监听回车键
        const input = dialog.querySelector('#newStudentNumberInput');
        input.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                this.handleStudentSwitchConfirm(dialog);
            }
        });

        document.body.appendChild(dialog);
        input.focus();
    }

    /**
     * 显示学生切换对话框
     */
    showStudentSwitchDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay';
        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>切换考生</h3>
                    <button class="modal-close" data-action="closeDialog">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="newStudentNumberInput">新考生编号:</label>
                        <input type="text" id="newStudentNumberInput" class="form-input"
                               placeholder="请输入新考生编号" data-field="newStudentNumber">
                    </div>
                    <div class="warning-message">
                        <i class="icon-warning"></i>
                        <span>切换考生将保存当前考试进度</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" data-action="closeDialog">取消</button>
                    <button class="btn btn-primary" data-action="confirmStudentSwitch">确认切换</button>
                </div>
            </div>
        `;

        // 绑定对话框事件
        dialog.addEventListener('click', (event) => {
            if (event.target.classList.contains('modal-overlay')) {
                this.closeDialog(dialog);
            }

            const action = event.target.getAttribute('data-action');
            if (action === 'closeDialog') {
                this.closeDialog(dialog);
            } else if (action === 'confirmStudentSwitch') {
                this.handleStudentSwitchConfirm(dialog);
            }
        });

        document.body.appendChild(dialog);
        dialog.querySelector('#newStudentNumberInput').focus();
    }

    /**
     * 处理学生编号确认
     * @param {Element} dialog - 对话框元素
     */
    handleStudentNumberConfirm(dialog) {
        const input = dialog.querySelector('#studentNumberInput');
        const studentNumber = input.value.trim();

        if (!studentNumber) {
            this.showToast('请输入考生编号', 'error');
            return;
        }

        this.emit('startExamRequested', { studentNumber });
        this.closeDialog(dialog);
    }

    /**
     * 处理学生切换确认
     * @param {Element} dialog - 对话框元素
     */
    handleStudentSwitchConfirm(dialog) {
        const input = dialog.querySelector('#newStudentNumberInput');
        const studentNumber = input.value.trim();

        if (!studentNumber) {
            this.showToast('请输入新考生编号', 'error');
            return;
        }

        this.emit('studentSwitchRequested', { studentNumber });
        this.closeDialog(dialog);
    }

    /**
     * 关闭对话框
     * @param {Element} dialog - 对话框元素
     */
    closeDialog(dialog) {
        if (dialog && dialog.parentNode) {
            dialog.parentNode.removeChild(dialog);
        }
    }

    /**
     * 添加对话框样式
     */
    addDialogStyles() {
        if (document.getElementById('exam-dialog-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'exam-dialog-styles';
        styles.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }

            .modal-dialog {
                background: #fff;
                border-radius: 0.5rem;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                width: 90%;
                max-width: 400px;
                max-height: 90vh;
                overflow: hidden;
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem 1.5rem;
                border-bottom: 1px solid #e5e7eb;
                background: #f9fafb;
            }

            .modal-header h3 {
                margin: 0;
                font-size: 1.125rem;
                font-weight: 600;
                color: #111827;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #6b7280;
                padding: 0;
                width: 2rem;
                height: 2rem;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 0.25rem;
            }

            .modal-close:hover {
                background: #e5e7eb;
                color: #374151;
            }

            .modal-body {
                padding: 1.5rem;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .form-group label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 500;
                color: #374151;
            }

            .form-input {
                width: 100%;
                padding: 0.75rem;
                border: 1px solid #d1d5db;
                border-radius: 0.375rem;
                font-size: 1rem;
                transition: border-color 0.2s;
            }

            .form-input:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .warning-message {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.75rem;
                background: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 0.375rem;
                color: #92400e;
                font-size: 0.875rem;
            }

            .modal-footer {
                display: flex;
                justify-content: flex-end;
                gap: 0.75rem;
                padding: 1rem 1.5rem;
                border-top: 1px solid #e5e7eb;
                background: #f9fafb;
            }

            .btn {
                padding: 0.5rem 1rem;
                border-radius: 0.375rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
                border: 1px solid transparent;
            }

            .btn-secondary {
                background: #fff;
                color: #374151;
                border-color: #d1d5db;
            }

            .btn-secondary:hover {
                background: #f9fafb;
                border-color: #9ca3af;
            }

            .btn-primary {
                background: #3b82f6;
                color: #fff;
            }

            .btn-primary:hover {
                background: #2563eb;
            }
        `;

        document.head.appendChild(styles);
    }

    // ==================== 事件处理 ====================

    /**
     * 处理步骤点击
     * @param {number} stepNumber - 步骤编号
     */
    handleStepClick(stepNumber) {
        if (stepNumber !== this.state.currentStep) {
            this.emit('stepChangeRequested', { stepNumber });
        }
    }

    /**
     * 处理考试初始化
     * @param {Object} data - 初始化数据
     */
    handleExamInitialized(data) {
        this.updateExamState({
            currentStudent: data.student,
            currentStep: data.currentStep,
            examStatus: 'initialized'
        });
    }

    /**
     * 处理考试开始
     * @param {Object} data - 考试开始数据
     */
    handleExamStarted(data) {
        this.updateExamState({
            currentStudent: data.student,
            currentStep: data.currentStep,
            examStatus: 'active'
        });

        this.showToast('考试已开始', 'success');
    }

    /**
     * 处理步骤变更
     * @param {Object} data - 步骤变更数据
     */
    handleStepChanged(data) {
        this.updateExamState({
            currentStep: data.currentStep,
            currentStudent: data.student
        });

        this.showToast(`已进入${this.stepDefinitions[data.currentStep].name}`, 'info');
    }

    /**
     * 处理考试完成
     * @param {Object} data - 考试完成数据
     */
    handleExamCompleted(data) {
        this.updateExamState({
            examStatus: 'completed'
        });

        this.showToast('考试已完成', 'success');
    }

    /**
     * 更新总用时
     * @param {Object} timerData - 计时器数据
     */
    updateTotalTime(timerData) {
        if (this.elements.totalTime) {
            const totalSeconds = timerData.elapsedTime || 0;
            const formattedTime = this.formatTime(totalSeconds);
            this.elements.totalTime.textContent = `总用时: ${formattedTime}`;
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 获取状态文本
     * @param {string} status - 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'idle': '未开始',
            'active': '进行中',
            'paused': '已暂停',
            'completed': '已完成'
        };
        return statusMap[status] || '未知';
    }

    /**
     * 检查步骤是否有题目
     * @param {number} stepNumber - 步骤编号
     * @returns {boolean} 是否有题目
     */
    hasQuestions(stepNumber) {
        return stepNumber === 3 || stepNumber === 4;
    }

    /**
     * 获取题目类型
     * @param {number} stepNumber - 步骤编号
     * @returns {string} 题目类型
     */
    getQuestionType(stepNumber) {
        const typeMap = {
            3: 'translation',
            4: 'professional'
        };
        return typeMap[stepNumber] || null;
    }

    /**
     * 格式化时间
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间
     */
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }

    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showToast(message, type = 'info') {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // 添加到页面
        document.body.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);

        // 添加提示样式（如果不存在）
        this.addToastStyles();
    }

    /**
     * 添加提示样式
     */
    addToastStyles() {
        if (document.getElementById('toast-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'toast-styles';
        styles.textContent = `
            .toast {
                position: fixed;
                top: 2rem;
                right: 2rem;
                padding: 0.75rem 1rem;
                border-radius: 0.375rem;
                color: #fff;
                font-weight: 500;
                z-index: 1001;
                animation: slideIn 0.3s ease;
            }

            .toast-info { background: #3b82f6; }
            .toast-success { background: #10b981; }
            .toast-warning { background: #f59e0b; }
            .toast-error { background: #ef4444; }

            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * 销毁视图
     */
    onDestroy() {
        // 移除对话框样式
        const dialogStyles = document.getElementById('exam-dialog-styles');
        if (dialogStyles) {
            dialogStyles.remove();
        }

        // 移除提示样式
        const toastStyles = document.getElementById('toast-styles');
        if (toastStyles) {
            toastStyles.remove();
        }
    }
}

// 导出ExamView类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExamView;
} else {
    window.ExamView = ExamView;
}