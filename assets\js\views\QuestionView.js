/**
 * QuestionView - 题目显示组件
 * 负责题目内容渲染、抽题动画展示、题目网格显示
 */

class QuestionView extends BaseView {
    constructor(container) {
        super(container);
        
        // 视图状态
        this.state = {
            currentQuestionType: null,
            selectedQuestion: null,
            isAnimating: false,
            gridVisible: false,
            questionStats: {}
        };

        // 动画配置
        this.animationConfig = {
            duration: 3000,
            spinInterval: 100,
            highlightDuration: 2000
        };

        this.template = this.getQuestionTemplate();
        this.styles = this.getQuestionStyles();
    }

    /**
     * 获取题目界面模板
     * @returns {string} HTML模板
     */
    getQuestionTemplate() {
        return `
            <div class="question-container">
                <!-- 题目网格 -->
                <div class="question-grid" data-element="questionGrid" style="display: none;">
                    <div class="grid-header">
                        <h3 class="grid-title" data-element="gridTitle">题目网格</h3>
                        <div class="grid-stats" data-element="gridStats">
                            <span class="stat-item">
                                <span class="stat-label">总计:</span>
                                <span class="stat-value" data-element="totalCount">0</span>
                                <span class="stat-unit">题</span>
                            </span>
                            <span class="stat-item">
                                <span class="stat-label">已抽取:</span>
                                <span class="stat-value" data-element="usedCount">0</span>
                                <span class="stat-unit">题</span>
                            </span>
                            <span class="stat-item">
                                <span class="stat-label">可抽取:</span>
                                <span class="stat-value" data-element="availableCount">0</span>
                                <span class="stat-unit">题</span>
                            </span>
                        </div>
                        <button class="grid-close-btn" data-action="hideQuestionGrid">
                            <i class="icon-close"></i>
                        </button>
                    </div>
                    <div class="grid-body">
                        <div class="question-numbers" data-element="questionNumbers">
                            <!-- 题号网格将动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 抽题动画区域 -->
                <div class="draw-animation" data-element="drawAnimation" style="display: none;">
                    <div class="animation-container">
                        <div class="animation-title">正在抽取题目...</div>
                        <div class="spinning-container">
                            <div class="spinning-numbers" data-element="spinningNumbers">1</div>
                        </div>
                        <div class="selected-container" data-element="selectedContainer" style="display: none;">
                            <div class="selected-label">抽中题目</div>
                            <div class="selected-number" data-element="selectedNumber">1</div>
                        </div>
                    </div>
                </div>

                <!-- 题目内容显示 -->
                <div class="question-content" data-element="questionContent" style="display: none;">
                    <div class="question-header">
                        <h3 class="question-title" data-element="questionTitle">题目标题</h3>
                        <div class="question-meta">
                            <span class="question-type" data-element="questionType">题目类型</span>
                            <span class="question-number" data-element="questionNumber">第1题</span>
                        </div>
                        <button class="question-close-btn" data-action="hideQuestion">
                            <i class="icon-close"></i>
                        </button>
                    </div>
                    <div class="question-body" data-element="questionBody">
                        <!-- 题目内容将动态生成 -->
                    </div>
                </div>

                <!-- 控制按钮 -->
                <div class="question-controls" data-element="questionControls">
                    <button class="control-btn draw-btn" data-action="drawQuestion">
                        <i class="icon-dice"></i>
                        <span>抽取题目</span>
                    </button>
                    <button class="control-btn grid-btn" data-action="showQuestionGrid">
                        <i class="icon-grid"></i>
                        <span>显示题目网格</span>
                    </button>
                    <button class="control-btn hide-btn" data-action="hideQuestion" style="display: none;">
                        <i class="icon-eye-off"></i>
                        <span>隐藏题目</span>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 获取题目界面样式
     * @returns {string} CSS样式
     */
    getQuestionStyles() {
        return `
            .question-container {
                position: relative;
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            /* 题目网格样式 */
            .question-grid {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: #fff;
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                z-index: 100;
                display: flex;
                flex-direction: column;
            }

            .grid-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem 1.5rem;
                border-bottom: 1px solid #e5e7eb;
                background: #f9fafb;
                border-radius: 0.5rem 0.5rem 0 0;
            }

            .grid-title {
                margin: 0;
                font-size: 1.125rem;
                font-weight: 600;
                color: #111827;
            }

            .grid-stats {
                display: flex;
                gap: 1rem;
                font-size: 0.875rem;
            }

            .stat-item {
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .stat-label {
                color: #6b7280;
            }

            .stat-value {
                font-weight: 600;
                color: #111827;
            }

            .stat-unit {
                color: #6b7280;
            }

            .grid-close-btn {
                padding: 0.5rem;
                border: none;
                background: none;
                cursor: pointer;
                border-radius: 0.25rem;
                color: #6b7280;
                transition: all 0.2s;
            }

            .grid-close-btn:hover {
                background: #e5e7eb;
                color: #374151;
            }

            .grid-body {
                flex: 1;
                padding: 1.5rem;
                overflow-y: auto;
            }

            .question-numbers {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
                gap: 0.75rem;
            }

            .question-number {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 60px;
                height: 60px;
                border-radius: 0.5rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s;
                position: relative;
            }

            .question-number.available {
                background: #dbeafe;
                color: #1d4ed8;
                border: 2px solid #3b82f6;
            }

            .question-number.available:hover {
                background: #bfdbfe;
                transform: scale(1.05);
            }

            .question-number.used {
                background: #fee2e2;
                color: #dc2626;
                border: 2px solid #ef4444;
                cursor: not-allowed;
            }

            .question-number.missing {
                background: #f3f4f6;
                color: #9ca3af;
                border: 2px solid #d1d5db;
                cursor: not-allowed;
            }

            .question-number.highlighted {
                animation: highlight 1s ease-in-out;
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
            }

            @keyframes highlight {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.2); }
            }

            /* 抽题动画样式 */
            .draw-animation {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 200;
            }

            .animation-container {
                text-align: center;
                color: #fff;
            }

            .animation-title {
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 2rem;
            }

            .spinning-container {
                margin-bottom: 2rem;
            }

            .spinning-numbers {
                display: inline-block;
                font-size: 4rem;
                font-weight: 700;
                color: #fbbf24;
                text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
                animation: spin 0.1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotateY(0deg); }
                100% { transform: rotateY(360deg); }
            }

            .selected-container {
                animation: fadeIn 0.5s ease-in-out;
            }

            .selected-label {
                font-size: 1.25rem;
                margin-bottom: 1rem;
                color: #d1fae5;
            }

            .selected-number {
                font-size: 5rem;
                font-weight: 700;
                color: #10b981;
                text-shadow: 0 0 30px rgba(16, 185, 129, 0.5);
                animation: pulse 1s ease-in-out infinite;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: scale(0.8); }
                to { opacity: 1; transform: scale(1); }
            }

            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.1); }
            }

            /* 题目内容样式 */
            .question-content {
                background: #fff;
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            .question-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                padding: 1.5rem;
                border-bottom: 1px solid #e5e7eb;
                background: #f9fafb;
            }

            .question-title {
                margin: 0;
                font-size: 1.25rem;
                font-weight: 600;
                color: #111827;
            }

            .question-meta {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 0.25rem;
                font-size: 0.875rem;
            }

            .question-type {
                padding: 0.25rem 0.75rem;
                background: #dbeafe;
                color: #1d4ed8;
                border-radius: 1rem;
                font-weight: 500;
            }

            .question-number {
                color: #6b7280;
                font-weight: 500;
            }

            .question-close-btn {
                padding: 0.5rem;
                border: none;
                background: none;
                cursor: pointer;
                border-radius: 0.25rem;
                color: #6b7280;
                transition: all 0.2s;
            }

            .question-close-btn:hover {
                background: #e5e7eb;
                color: #374151;
            }

            .question-body {
                flex: 1;
                padding: 2rem;
                overflow-y: auto;
            }

            /* 翻译题样式 */
            .translation-question {
                display: flex;
                flex-direction: column;
                gap: 2rem;
            }

            .english-text {
                background: #f0f9ff;
                padding: 1.5rem;
                border-radius: 0.5rem;
                border-left: 4px solid #0ea5e9;
            }

            .english-text h4 {
                margin: 0 0 1rem 0;
                color: #0c4a6e;
                font-size: 1rem;
                font-weight: 600;
            }

            .english-content {
                font-size: 1.125rem;
                line-height: 1.6;
                color: #374151;
                margin: 0;
            }

            .translation-area {
                background: #f0fdf4;
                padding: 1.5rem;
                border-radius: 0.5rem;
                border-left: 4px solid #22c55e;
            }

            .translation-area h4 {
                margin: 0 0 1rem 0;
                color: #14532d;
                font-size: 1rem;
                font-weight: 600;
            }

            .translation-input {
                width: 100%;
                min-height: 120px;
                padding: 1rem;
                border: 1px solid #d1d5db;
                border-radius: 0.375rem;
                font-size: 1rem;
                line-height: 1.5;
                resize: vertical;
                font-family: inherit;
            }

            .translation-input:focus {
                outline: none;
                border-color: #22c55e;
                box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
            }

            /* 专业题样式 */
            .professional-question {
                display: flex;
                flex-direction: column;
                gap: 2rem;
            }

            .question-prompt {
                background: #fef3c7;
                padding: 1.5rem;
                border-radius: 0.5rem;
                border-left: 4px solid #f59e0b;
            }

            .question-prompt h4 {
                margin: 0 0 1rem 0;
                color: #92400e;
                font-size: 1rem;
                font-weight: 600;
            }

            .question-content {
                font-size: 1.125rem;
                line-height: 1.6;
                color: #374151;
                margin: 0;
            }

            .answer-area {
                background: #f3f4f6;
                padding: 1.5rem;
                border-radius: 0.5rem;
                border-left: 4px solid #6b7280;
            }

            .answer-area h4 {
                margin: 0 0 1rem 0;
                color: #374151;
                font-size: 1rem;
                font-weight: 600;
            }

            .answer-input {
                width: 100%;
                min-height: 150px;
                padding: 1rem;
                border: 1px solid #d1d5db;
                border-radius: 0.375rem;
                font-size: 1rem;
                line-height: 1.5;
                resize: vertical;
                font-family: inherit;
            }

            .answer-input:focus {
                outline: none;
                border-color: #6b7280;
                box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.1);
            }

            /* 控制按钮样式 */
            .question-controls {
                display: flex;
                justify-content: center;
                gap: 1rem;
                padding: 1rem;
                background: #f9fafb;
                border-top: 1px solid #e5e7eb;
            }

            .control-btn {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.75rem 1.5rem;
                border: none;
                border-radius: 0.5rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
            }

            .draw-btn {
                background: #3b82f6;
                color: #fff;
            }

            .draw-btn:hover {
                background: #2563eb;
            }

            .grid-btn {
                background: #10b981;
                color: #fff;
            }

            .grid-btn:hover {
                background: #059669;
            }

            .hide-btn {
                background: #6b7280;
                color: #fff;
            }

            .hide-btn:hover {
                background: #4b5563;
            }

            .control-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .question-numbers {
                    grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
                    gap: 0.5rem;
                }

                .question-number {
                    width: 50px;
                    height: 50px;
                    font-size: 0.875rem;
                }

                .grid-header {
                    padding: 1rem;
                }

                .grid-stats {
                    flex-direction: column;
                    gap: 0.5rem;
                    font-size: 0.75rem;
                }

                .question-header {
                    padding: 1rem;
                }

                .question-body {
                    padding: 1rem;
                }

                .question-controls {
                    flex-direction: column;
                    align-items: stretch;
                }

                .control-btn {
                    justify-content: center;
                }
            }
        `;
    }

    /**
     * 初始化视图
     */
    async onInitialize() {
        // 监听全局事件
        this.setupGlobalListeners();
    }

    /**
     * 设置全局监听器
     */
    setupGlobalListeners() {
        if (this.eventBus) {
            // 监听题目控制器事件
            this.eventBus.on('QuestionController.showQuestionGrid', (data) => {
                this.showQuestionGrid(data);
            });

            this.eventBus.on('QuestionController.hideQuestionGrid', () => {
                this.hideQuestionGrid();
            });

            this.eventBus.on('QuestionController.startDrawAnimation', (data) => {
                this.startDrawAnimation(data);
            });

            this.eventBus.on('QuestionController.updateSpinningNumber', (data) => {
                this.updateSpinningNumber(data.number);
            });

            this.eventBus.on('QuestionController.showSelectedNumber', (data) => {
                this.showSelectedNumber(data.number);
            });

            this.eventBus.on('QuestionController.highlightSelectedNumber', (data) => {
                this.highlightSelectedNumber(data.number);
            });

            this.eventBus.on('QuestionController.displayQuestion', (data) => {
                this.displayQuestion(data);
            });

            this.eventBus.on('QuestionController.hideQuestion', () => {
                this.hideQuestion();
            });

            this.eventBus.on('QuestionController.updateQuestionGrid', (data) => {
                this.updateQuestionGrid(data);
            });

            this.eventBus.on('QuestionController.prepareQuestionUI', (data) => {
                this.prepareQuestionUI(data);
            });
        }
    }

    /**
     * 绑定特定事件
     */
    onBindEvents() {
        // 监听题号点击
        this.container.addEventListener('click', (event) => {
            if (event.target.classList.contains('question-number') &&
                event.target.classList.contains('available')) {
                const number = parseInt(event.target.textContent);
                this.handleQuestionNumberClick(number);
            }
        });
    }

    // ==================== 动作处理方法 ====================

    /**
     * 处理抽取题目动作
     */
    handleDrawQuestionAction() {
        this.emit('drawQuestionRequested', {
            type: this.state.currentQuestionType,
            method: 'random'
        });
    }

    /**
     * 处理显示题目网格动作
     */
    handleShowQuestionGridAction() {
        this.emit('showQuestionGridRequested', {
            type: this.state.currentQuestionType
        });
    }

    /**
     * 处理隐藏题目网格动作
     */
    handleHideQuestionGridAction() {
        this.hideQuestionGrid();
    }

    /**
     * 处理隐藏题目动作
     */
    handleHideQuestionAction() {
        this.hideQuestion();
    }

    // ==================== 题目网格管理 ====================

    /**
     * 显示题目网格
     * @param {Object} data - 网格数据
     */
    showQuestionGrid(data) {
        const { type, gridData, stats } = data;

        // 更新状态
        this.state.gridVisible = true;
        this.state.currentQuestionType = type;
        this.state.questionStats = stats;

        // 更新网格标题
        const typeNames = {
            'translation': '翻译题',
            'professional': '专业题'
        };
        if (this.elements.gridTitle) {
            this.elements.gridTitle.textContent = `${typeNames[type] || type}网格`;
        }

        // 更新统计信息
        this.updateGridStats(stats);

        // 生成题号网格
        this.generateQuestionNumbers(gridData);

        // 显示网格
        if (this.elements.questionGrid) {
            this.elements.questionGrid.style.display = 'flex';
        }

        this.emit('questionGridShown', { type, stats });
    }

    /**
     * 隐藏题目网格
     */
    hideQuestionGrid() {
        this.state.gridVisible = false;

        if (this.elements.questionGrid) {
            this.elements.questionGrid.style.display = 'none';
        }

        this.emit('questionGridHidden');
    }

    /**
     * 更新题目网格
     * @param {Object} data - 更新数据
     */
    updateQuestionGrid(data) {
        const { gridData, stats, updatedNumber, newStatus } = data;

        // 更新统计信息
        this.updateGridStats(stats);

        // 重新生成网格（如果需要）
        if (gridData) {
            this.generateQuestionNumbers(gridData);
        }

        // 高亮更新的题号
        if (updatedNumber) {
            this.highlightQuestionNumber(updatedNumber, newStatus);
        }
    }

    /**
     * 更新网格统计信息
     * @param {Object} stats - 统计数据
     */
    updateGridStats(stats) {
        if (this.elements.totalCount) {
            this.elements.totalCount.textContent = stats.total || 0;
        }
        if (this.elements.usedCount) {
            this.elements.usedCount.textContent = stats.used || 0;
        }
        if (this.elements.availableCount) {
            this.elements.availableCount.textContent = stats.available || 0;
        }
    }

    /**
     * 生成题号网格
     * @param {Array} gridData - 网格数据
     */
    generateQuestionNumbers(gridData) {
        if (!this.elements.questionNumbers) return;

        const numbersHTML = gridData.map(item => {
            const statusClass = item.statusClass || item.status;
            const title = item.statusText || this.getStatusText(item.status);

            return `
                <div class="question-number ${statusClass}"
                     data-number="${item.number}"
                     title="${title}">
                    ${item.number}
                </div>
            `;
        }).join('');

        this.elements.questionNumbers.innerHTML = numbersHTML;
    }

    /**
     * 高亮题号
     * @param {number} number - 题号
     * @param {string} status - 状态（可选）
     */
    highlightQuestionNumber(number, status = null) {
        const numberElement = this.container.querySelector(`[data-number="${number}"]`);
        if (numberElement) {
            // 移除之前的高亮
            this.container.querySelectorAll('.question-number.highlighted').forEach(el => {
                el.classList.remove('highlighted');
            });

            // 添加高亮
            numberElement.classList.add('highlighted');

            // 更新状态（如果提供）
            if (status) {
                numberElement.className = `question-number ${status} highlighted`;
            }

            // 滚动到可见区域
            numberElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // 自动移除高亮
            setTimeout(() => {
                numberElement.classList.remove('highlighted');
            }, this.animationConfig.highlightDuration);
        }
    }

    // ==================== 抽题动画 ====================

    /**
     * 开始抽题动画
     * @param {Object} data - 动画数据
     */
    startDrawAnimation(data) {
        const { availableNumbers, duration } = data;

        this.state.isAnimating = true;

        // 显示动画容器
        if (this.elements.drawAnimation) {
            this.elements.drawAnimation.style.display = 'flex';
        }

        // 隐藏选中结果容器
        if (this.elements.selectedContainer) {
            this.elements.selectedContainer.style.display = 'none';
        }

        this.emit('drawAnimationStarted', { availableNumbers, duration });
    }

    /**
     * 更新旋转数字
     * @param {number} number - 当前数字
     */
    updateSpinningNumber(number) {
        if (this.elements.spinningNumbers) {
            this.elements.spinningNumbers.textContent = number;
        }
    }

    /**
     * 显示选中数字
     * @param {number} number - 选中的数字
     */
    showSelectedNumber(number) {
        // 隐藏旋转数字
        if (this.elements.spinningNumbers) {
            this.elements.spinningNumbers.style.display = 'none';
        }

        // 显示选中结果
        if (this.elements.selectedContainer) {
            this.elements.selectedContainer.style.display = 'block';
        }

        if (this.elements.selectedNumber) {
            this.elements.selectedNumber.textContent = number;
        }

        // 延迟隐藏动画
        setTimeout(() => {
            this.hideDrawAnimation();
        }, 2000);

        this.emit('numberSelected', { number });
    }

    /**
     * 隐藏抽题动画
     */
    hideDrawAnimation() {
        this.state.isAnimating = false;

        if (this.elements.drawAnimation) {
            this.elements.drawAnimation.style.display = 'none';
        }

        // 重置动画状态
        if (this.elements.spinningNumbers) {
            this.elements.spinningNumbers.style.display = 'block';
        }

        if (this.elements.selectedContainer) {
            this.elements.selectedContainer.style.display = 'none';
        }
    }

    /**
     * 高亮选中的题号
     * @param {number} number - 题号
     */
    highlightSelectedNumber(number) {
        this.highlightQuestionNumber(number, 'used');
    }

    // ==================== 题目显示 ====================

    /**
     * 显示题目内容
     * @param {Object} data - 题目数据
     */
    displayQuestion(data) {
        const { question, type, index, title, content } = data;

        // 更新状态
        this.state.selectedQuestion = question;

        // 更新题目头部信息
        if (this.elements.questionTitle) {
            this.elements.questionTitle.textContent = title || `${type}题目`;
        }

        if (this.elements.questionType) {
            const typeNames = {
                'translation': '翻译题',
                'professional': '专业题'
            };
            this.elements.questionType.textContent = typeNames[type] || type;
        }

        if (this.elements.questionNumber) {
            this.elements.questionNumber.textContent = `第${index}题`;
        }

        // 渲染题目内容
        this.renderQuestionContent(type, content);

        // 显示题目容器
        if (this.elements.questionContent) {
            this.elements.questionContent.style.display = 'flex';
        }

        // 更新控制按钮
        this.updateControlButtons(true);

        this.emit('questionDisplayed', { question: data });
    }

    /**
     * 渲染题目内容
     * @param {string} type - 题目类型
     * @param {Object} content - 题目内容
     */
    renderQuestionContent(type, content) {
        if (!this.elements.questionBody) return;

        if (type === 'translation') {
            this.renderTranslationQuestion(content);
        } else if (type === 'professional') {
            this.renderProfessionalQuestion(content);
        } else {
            this.renderGenericQuestion(content);
        }
    }

    /**
     * 渲染翻译题
     * @param {Object} content - 题目内容
     */
    renderTranslationQuestion(content) {
        const html = `
            <div class="translation-question">
                <div class="english-text">
                    <h4>请翻译以下英文：</h4>
                    <p class="english-content">${content.english || content.question || ''}</p>
                </div>
                <div class="translation-area">
                    <h4>中文翻译：</h4>
                    <textarea class="translation-input"
                              placeholder="请在此输入中文翻译..."
                              data-field="translation"></textarea>
                </div>
            </div>
        `;
        this.elements.questionBody.innerHTML = html;
    }

    /**
     * 渲染专业题
     * @param {Object} content - 题目内容
     */
    renderProfessionalQuestion(content) {
        const html = `
            <div class="professional-question">
                <div class="question-prompt">
                    <h4>专业问题：</h4>
                    <p class="question-content">${content.question || content.text || ''}</p>
                </div>
                <div class="answer-area">
                    <h4>请回答：</h4>
                    <textarea class="answer-input"
                              placeholder="请在此输入您的回答..."
                              data-field="answer"></textarea>
                </div>
            </div>
        `;
        this.elements.questionBody.innerHTML = html;
    }

    /**
     * 渲染通用题目
     * @param {Object} content - 题目内容
     */
    renderGenericQuestion(content) {
        const html = `
            <div class="generic-question">
                <div class="question-text">
                    <p>${content.text || content.question || '题目内容'}</p>
                </div>
            </div>
        `;
        this.elements.questionBody.innerHTML = html;
    }

    /**
     * 隐藏题目
     */
    hideQuestion() {
        this.state.selectedQuestion = null;

        if (this.elements.questionContent) {
            this.elements.questionContent.style.display = 'none';
        }

        // 更新控制按钮
        this.updateControlButtons(false);

        this.emit('questionHidden');
    }

    // ==================== 界面控制 ====================

    /**
     * 准备题目UI
     * @param {Object} data - 准备数据
     */
    prepareQuestionUI(data) {
        const { questionType, stepNumber, canDraw } = data;

        // 更新状态
        this.state.currentQuestionType = questionType;

        // 更新控制按钮状态
        this.updateControlButtons(false, canDraw);

        // 隐藏之前的内容
        this.hideQuestion();
        this.hideQuestionGrid();

        this.emit('questionUIPrepared', { questionType, stepNumber });
    }

    /**
     * 更新控制按钮
     * @param {boolean} questionVisible - 题目是否可见
     * @param {boolean} canDraw - 是否可以抽题
     */
    updateControlButtons(questionVisible = false, canDraw = true) {
        // 抽题按钮
        const drawBtn = this.container.querySelector('.draw-btn');
        if (drawBtn) {
            drawBtn.disabled = !canDraw || this.state.isAnimating;
        }

        // 网格按钮
        const gridBtn = this.container.querySelector('.grid-btn');
        if (gridBtn) {
            gridBtn.disabled = !this.state.currentQuestionType;
        }

        // 隐藏按钮
        const hideBtn = this.container.querySelector('.hide-btn');
        if (hideBtn) {
            hideBtn.style.display = questionVisible ? 'flex' : 'none';
        }
    }

    // ==================== 事件处理 ====================

    /**
     * 处理题号点击
     * @param {number} number - 题号
     */
    handleQuestionNumberClick(number) {
        this.emit('questionNumberSelected', {
            type: this.state.currentQuestionType,
            number
        });
    }

    // ==================== 工具方法 ====================

    /**
     * 获取状态文本
     * @param {string} status - 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'available': '可抽取',
            'used': '已抽取',
            'missing': '不存在'
        };
        return statusMap[status] || '未知';
    }

    /**
     * 获取题目类型名称
     * @param {string} type - 题目类型
     * @returns {string} 类型名称
     */
    getQuestionTypeName(type) {
        const typeNames = {
            'translation': '翻译题',
            'professional': '专业题'
        };
        return typeNames[type] || type;
    }

    /**
     * 检查是否可以抽题
     * @returns {boolean} 是否可以抽题
     */
    canDrawQuestion() {
        return !this.state.isAnimating &&
               this.state.currentQuestionType &&
               !this.state.selectedQuestion;
    }

    /**
     * 获取用户输入
     * @returns {Object} 用户输入数据
     */
    getUserInput() {
        const input = {};

        // 获取翻译输入
        const translationInput = this.container.querySelector('.translation-input');
        if (translationInput) {
            input.translation = translationInput.value.trim();
        }

        // 获取回答输入
        const answerInput = this.container.querySelector('.answer-input');
        if (answerInput) {
            input.answer = answerInput.value.trim();
        }

        return input;
    }

    /**
     * 清空用户输入
     */
    clearUserInput() {
        const translationInput = this.container.querySelector('.translation-input');
        if (translationInput) {
            translationInput.value = '';
        }

        const answerInput = this.container.querySelector('.answer-input');
        if (answerInput) {
            answerInput.value = '';
        }
    }

    /**
     * 设置用户输入
     * @param {Object} input - 输入数据
     */
    setUserInput(input) {
        if (input.translation) {
            const translationInput = this.container.querySelector('.translation-input');
            if (translationInput) {
                translationInput.value = input.translation;
            }
        }

        if (input.answer) {
            const answerInput = this.container.querySelector('.answer-input');
            if (answerInput) {
                answerInput.value = input.answer;
            }
        }
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getCurrentState() {
        return {
            currentQuestionType: this.state.currentQuestionType,
            selectedQuestion: this.state.selectedQuestion,
            isAnimating: this.state.isAnimating,
            gridVisible: this.state.gridVisible,
            questionStats: this.state.questionStats,
            userInput: this.getUserInput()
        };
    }

    /**
     * 重置视图状态
     */
    resetState() {
        this.state = {
            currentQuestionType: null,
            selectedQuestion: null,
            isAnimating: false,
            gridVisible: false,
            questionStats: {}
        };

        // 隐藏所有内容
        this.hideQuestion();
        this.hideQuestionGrid();
        this.hideDrawAnimation();

        // 清空用户输入
        this.clearUserInput();

        // 重置控制按钮
        this.updateControlButtons(false, false);
    }

    /**
     * 显示加载状态
     * @param {string} message - 加载消息
     */
    showLoading(message = '加载中...') {
        const loadingHTML = `
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <div class="loading-message">${message}</div>
            </div>
        `;

        if (this.elements.questionBody) {
            this.elements.questionBody.innerHTML = loadingHTML;
        }

        // 添加加载样式
        this.addLoadingStyles();
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingContainer = this.container.querySelector('.loading-container');
        if (loadingContainer) {
            loadingContainer.remove();
        }
    }

    /**
     * 添加加载样式
     */
    addLoadingStyles() {
        if (document.getElementById('question-loading-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'question-loading-styles';
        styles.textContent = `
            .loading-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 3rem;
                text-align: center;
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #e5e7eb;
                border-top: 4px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 1rem;
            }

            .loading-message {
                color: #6b7280;
                font-size: 1rem;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const errorHTML = `
            <div class="error-container">
                <div class="error-icon">⚠️</div>
                <div class="error-message">${message}</div>
                <button class="error-retry-btn" data-action="retryOperation">重试</button>
            </div>
        `;

        if (this.elements.questionBody) {
            this.elements.questionBody.innerHTML = errorHTML;
        }

        // 添加错误样式
        this.addErrorStyles();
    }

    /**
     * 添加错误样式
     */
    addErrorStyles() {
        if (document.getElementById('question-error-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'question-error-styles';
        styles.textContent = `
            .error-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 3rem;
                text-align: center;
            }

            .error-icon {
                font-size: 3rem;
                margin-bottom: 1rem;
            }

            .error-message {
                color: #dc2626;
                font-size: 1rem;
                margin-bottom: 1.5rem;
                max-width: 400px;
                line-height: 1.5;
            }

            .error-retry-btn {
                padding: 0.75rem 1.5rem;
                background: #3b82f6;
                color: #fff;
                border: none;
                border-radius: 0.375rem;
                font-weight: 500;
                cursor: pointer;
                transition: background 0.2s;
            }

            .error-retry-btn:hover {
                background: #2563eb;
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * 处理重试操作
     */
    handleRetryOperationAction() {
        this.emit('retryRequested');
    }

    /**
     * 销毁视图
     */
    onDestroy() {
        // 移除动态添加的样式
        const loadingStyles = document.getElementById('question-loading-styles');
        if (loadingStyles) {
            loadingStyles.remove();
        }

        const errorStyles = document.getElementById('question-error-styles');
        if (errorStyles) {
            errorStyles.remove();
        }

        // 重置状态
        this.resetState();
    }
}

// 导出QuestionView类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QuestionView;
} else {
    window.QuestionView = QuestionView;
}
