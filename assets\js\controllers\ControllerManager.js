/**
 * ControllerManager - 控制器管理器
 * 负责控制器的创建、初始化、协调和生命周期管理
 */

class ControllerManager {
    constructor() {
        this.controllers = new Map();
        this.eventBus = null;
        this.isInitialized = false;
        this.initializationOrder = [
            'TimerController',
            'QuestionController', 
            'ExamController'
        ];
    }

    /**
     * 初始化控制器管理器
     * @param {EventBus} eventBus - 事件总线实例
     * @returns {Promise<void>}
     */
    async initialize(eventBus) {
        if (this.isInitialized) {
            return;
        }

        try {
            this.eventBus = eventBus;

            // 创建控制器实例
            await this.createControllers();

            // 设置事件总线
            this.setupEventBus();

            // 按顺序初始化控制器
            await this.initializeControllers();

            // 设置控制器间协调
            this.setupControllerCoordination();

            this.isInitialized = true;
            console.log('ControllerManager 初始化完成');

        } catch (error) {
            console.error('ControllerManager 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建控制器实例
     * @returns {Promise<void>}
     */
    async createControllers() {
        // 创建ExamController
        if (window.ExamController) {
            const examController = new ExamController();
            this.controllers.set('ExamController', examController);
        }

        // 创建QuestionController
        if (window.QuestionController) {
            const questionController = new QuestionController();
            this.controllers.set('QuestionController', questionController);
        }

        // 创建TimerController
        if (window.TimerController) {
            const timerController = new TimerController();
            this.controllers.set('TimerController', timerController);
        }

        console.log(`创建了 ${this.controllers.size} 个控制器`);
    }

    /**
     * 设置事件总线
     */
    setupEventBus() {
        this.controllers.forEach(controller => {
            controller.setEventBus(this.eventBus);
        });
    }

    /**
     * 按顺序初始化控制器
     * @returns {Promise<void>}
     */
    async initializeControllers() {
        for (const controllerName of this.initializationOrder) {
            const controller = this.controllers.get(controllerName);
            if (controller) {
                try {
                    await controller.initialize();
                    console.log(`${controllerName} 初始化完成`);
                } catch (error) {
                    console.error(`${controllerName} 初始化失败:`, error);
                    throw error;
                }
            }
        }

        // 初始化其他控制器
        for (const [name, controller] of this.controllers) {
            if (!this.initializationOrder.includes(name)) {
                try {
                    await controller.initialize();
                    console.log(`${name} 初始化完成`);
                } catch (error) {
                    console.error(`${name} 初始化失败:`, error);
                }
            }
        }
    }

    /**
     * 设置控制器间协调
     */
    setupControllerCoordination() {
        // 监听全局协调事件
        this.eventBus.on('coordinator.startExam', async (data) => {
            await this.coordinateStartExam(data);
        });

        this.eventBus.on('coordinator.drawQuestion', async (data) => {
            await this.coordinateDrawQuestion(data);
        });

        this.eventBus.on('coordinator.nextStep', async (data) => {
            await this.coordinateNextStep(data);
        });

        this.eventBus.on('coordinator.completeExam', async (data) => {
            await this.coordinateCompleteExam(data);
        });

        // 监听控制器错误
        this.eventBus.on('*.error', (data) => {
            this.handleControllerError(data);
        });

        // 监听控制器状态变更
        this.eventBus.on('*.stateChanged', (data) => {
            this.handleControllerStateChange(data);
        });
    }

    // ==================== 协调方法 ====================

    /**
     * 协调开始考试流程
     * @param {Object} data - 考试数据
     * @returns {Promise<void>}
     */
    async coordinateStartExam(data) {
        try {
            const { studentNumber } = data;

            // 1. 初始化考试
            const examController = this.getController('ExamController');
            await examController.initializeExam(studentNumber);

            // 2. 开始考试
            await examController.startExam(studentNumber);

            // 3. 重置计时器
            const timerController = this.getController('TimerController');
            const stepInfo = examController.getStepInfo(1);
            await timerController.resetTimer(stepInfo.timeLimit * 60, 1);

            console.log(`协调开始考试: ${studentNumber}`);

        } catch (error) {
            console.error('协调开始考试失败:', error);
            this.eventBus.emit('coordinator.error', {
                action: 'startExam',
                error,
                data
            });
        }
    }

    /**
     * 协调抽题流程
     * @param {Object} data - 抽题数据
     * @returns {Promise<void>}
     */
    async coordinateDrawQuestion(data) {
        try {
            const { type, method = 'random', specificNumber } = data;

            // 1. 抽取题目
            const questionController = this.getController('QuestionController');
            const question = await questionController.drawQuestion(type, method, specificNumber);

            // 2. 自动开始计时器
            const timerController = this.getController('TimerController');
            if (timerController.getTimerState().remainingTime > 0) {
                await timerController.startTimer();
            }

            console.log(`协调抽题完成: ${type}_${question.index}`);

        } catch (error) {
            console.error('协调抽题失败:', error);
            this.eventBus.emit('coordinator.error', {
                action: 'drawQuestion',
                error,
                data
            });
        }
    }

    /**
     * 协调下一步流程
     * @param {Object} data - 步骤数据
     * @returns {Promise<void>}
     */
    async coordinateNextStep(data) {
        try {
            // 1. 切换到下一步
            const examController = this.getController('ExamController');
            const result = await examController.nextStep();

            // 2. 重置计时器
            const timerController = this.getController('TimerController');
            const stepInfo = examController.getStepInfo(result.currentStep);
            await timerController.resetTimer(stepInfo.timeLimit * 60, result.currentStep);

            // 3. 如果新步骤有题目，准备题目界面
            if (stepInfo.hasQuestions) {
                const questionController = this.getController('QuestionController');
                await questionController.prepareQuestionInterface({
                    questionType: stepInfo.questionType,
                    stepNumber: result.currentStep
                });
            }

            console.log(`协调下一步完成: ${result.previousStep} -> ${result.currentStep}`);

        } catch (error) {
            console.error('协调下一步失败:', error);
            this.eventBus.emit('coordinator.error', {
                action: 'nextStep',
                error,
                data
            });
        }
    }

    /**
     * 协调完成考试流程
     * @param {Object} data - 完成数据
     * @returns {Promise<void>}
     */
    async coordinateCompleteExam(data) {
        try {
            // 1. 停止计时器
            const timerController = this.getController('TimerController');
            await timerController.stopTimer();

            // 2. 完成考试
            const examController = this.getController('ExamController');
            const student = await examController.completeExam();

            // 3. 清理题目状态
            const questionController = this.getController('QuestionController');
            questionController.resetQuestionState();

            console.log(`协调完成考试: ${student.studentNumber}`);

        } catch (error) {
            console.error('协调完成考试失败:', error);
            this.eventBus.emit('coordinator.error', {
                action: 'completeExam',
                error,
                data
            });
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 获取控制器实例
     * @param {string} name - 控制器名称
     * @returns {BaseController} 控制器实例
     */
    getController(name) {
        const controller = this.controllers.get(name);
        if (!controller) {
            throw new Error(`控制器 ${name} 不存在`);
        }
        return controller;
    }

    /**
     * 检查控制器是否存在
     * @param {string} name - 控制器名称
     * @returns {boolean} 是否存在
     */
    hasController(name) {
        return this.controllers.has(name);
    }

    /**
     * 获取所有控制器名称
     * @returns {Array} 控制器名称数组
     */
    getControllerNames() {
        return Array.from(this.controllers.keys());
    }

    /**
     * 获取控制器状态
     * @param {string} name - 控制器名称（可选）
     * @returns {Object} 控制器状态
     */
    getControllerState(name = null) {
        if (name) {
            const controller = this.getController(name);
            return controller.getState();
        }

        const states = {};
        this.controllers.forEach((controller, controllerName) => {
            states[controllerName] = controller.getState();
        });
        return states;
    }

    /**
     * 获取系统健康状态
     * @returns {Object} 健康状态
     */
    getHealthStatus() {
        const status = {
            isHealthy: true,
            controllers: {},
            errors: []
        };

        this.controllers.forEach((controller, name) => {
            const isHealthy = controller.isHealthy();
            status.controllers[name] = {
                isHealthy,
                isInitialized: controller.isInitialized,
                hasErrors: controller.hasErrors()
            };

            if (!isHealthy) {
                status.isHealthy = false;
                status.errors.push(`${name} 不健康`);
            }
        });

        return status;
    }

    /**
     * 销毁控制器管理器
     */
    destroy() {
        try {
            // 销毁所有控制器
            this.controllers.forEach((controller, name) => {
                try {
                    controller.destroy();
                    console.log(`控制器 ${name} 已销毁`);
                } catch (error) {
                    console.error(`销毁控制器 ${name} 失败:`, error);
                }
            });

            // 清理资源
            this.controllers.clear();
            this.eventBus = null;
            this.isInitialized = false;

            console.log('ControllerManager 已销毁');
        } catch (error) {
            console.error('ControllerManager 销毁失败:', error);
        }
    }
}

// 创建全局控制器管理器实例
const controllerManager = new ControllerManager();

// 导出ControllerManager类和实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ControllerManager, controllerManager };
} else {
    window.ControllerManager = ControllerManager;
    window.controllerManager = controllerManager;
}
