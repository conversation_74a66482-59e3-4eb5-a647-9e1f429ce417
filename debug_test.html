<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 系统调试测试页面</h1>
        <p>用于测试修复后的系统功能是否正常工作</p>

        <!-- API测试 -->
        <div class="test-section">
            <h3>📡 API接口测试</h3>
            <button class="btn btn-primary" onclick="testHealthAPI()">测试健康检查</button>
            <button class="btn btn-primary" onclick="testSettingsAPI()">测试设置接口</button>
            <button class="btn btn-primary" onclick="testStudentsAPI()">测试学生接口</button>
            <div id="apiResults"></div>
        </div>

        <!-- 模型测试 -->
        <div class="test-section">
            <h3>🏗️ 数据模型测试</h3>
            <button class="btn btn-success" onclick="testModels()">测试所有模型</button>
            <div id="modelResults"></div>
        </div>

        <!-- 服务测试 -->
        <div class="test-section">
            <h3>⚙️ 服务功能测试</h3>
            <button class="btn btn-warning" onclick="testServices()">测试服务功能</button>
            <div id="serviceResults"></div>
        </div>

        <!-- 考试流程测试 -->
        <div class="test-section">
            <h3>🎓 考试流程测试</h3>
            <button class="btn btn-primary" onclick="testExamFlow()">开始流程测试</button>
            <div id="examResults"></div>
        </div>

        <!-- 日志显示 -->
        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div id="testLog" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            container.innerHTML += `<div class="result ${className}">${message}</div>`;
        }

        // API测试
        async function testHealthAPI() {
            log('开始测试健康检查API...');
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                if (data.success) {
                    showResult('apiResults', '✅ 健康检查API正常: ' + JSON.stringify(data), 'success');
                    log('健康检查API测试成功', 'success');
                } else {
                    showResult('apiResults', '❌ 健康检查API失败: ' + JSON.stringify(data), 'error');
                    log('健康检查API测试失败', 'error');
                }
            } catch (error) {
                showResult('apiResults', '❌ 健康检查API错误: ' + error.message, 'error');
                log('健康检查API测试错误: ' + error.message, 'error');
            }
        }

        async function testSettingsAPI() {
            log('开始测试设置API...');
            try {
                const response = await fetch('/api/settings');
                const data = await response.json();
                if (data.success) {
                    showResult('apiResults', '✅ 设置API正常，获取到设置数据', 'success');
                    log('设置API测试成功', 'success');
                } else {
                    showResult('apiResults', '❌ 设置API失败: ' + JSON.stringify(data), 'error');
                    log('设置API测试失败', 'error');
                }
            } catch (error) {
                showResult('apiResults', '❌ 设置API错误: ' + error.message, 'error');
                log('设置API测试错误: ' + error.message, 'error');
            }
        }

        async function testStudentsAPI() {
            log('开始测试学生API...');
            try {
                const response = await fetch('/api/students');
                const data = await response.json();
                if (data.success) {
                    showResult('apiResults', `✅ 学生API正常，获取到${data.students.length}个学生`, 'success');
                    log('学生API测试成功', 'success');
                } else {
                    showResult('apiResults', '❌ 学生API失败: ' + JSON.stringify(data), 'error');
                    log('学生API测试失败', 'error');
                }
            } catch (error) {
                showResult('apiResults', '❌ 学生API错误: ' + error.message, 'error');
                log('学生API测试错误: ' + error.message, 'error');
            }
        }

        // 模型测试
        function testModels() {
            log('开始测试数据模型...');
            try {
                // 测试BaseModel
                if (typeof BaseModel !== 'undefined') {
                    const baseModel = new BaseModel({ id: 1, name: 'test' });
                    showResult('modelResults', '✅ BaseModel加载正常', 'success');
                } else {
                    showResult('modelResults', '❌ BaseModel未加载', 'error');
                }

                // 测试StudentModel
                if (typeof StudentModel !== 'undefined') {
                    const student = new StudentModel({ studentNumber: '001', name: '测试学生' });
                    showResult('modelResults', '✅ StudentModel加载正常', 'success');
                } else {
                    showResult('modelResults', '❌ StudentModel未加载', 'error');
                }

                // 测试QuestionModel
                if (typeof QuestionModel !== 'undefined') {
                    const question = new QuestionModel({ title: '测试题目', content: '这是一个测试题目' });
                    showResult('modelResults', '✅ QuestionModel加载正常', 'success');
                } else {
                    showResult('modelResults', '❌ QuestionModel未加载', 'error');
                }

                log('数据模型测试完成', 'success');
            } catch (error) {
                showResult('modelResults', '❌ 模型测试错误: ' + error.message, 'error');
                log('数据模型测试错误: ' + error.message, 'error');
            }
        }

        // 服务测试
        function testServices() {
            log('开始测试服务功能...');
            try {
                // 检查服务是否存在
                const services = ['studentService', 'questionService', 'examService', 'timerService', 'settingsService'];
                let successCount = 0;

                services.forEach(serviceName => {
                    if (window[serviceName]) {
                        showResult('serviceResults', `✅ ${serviceName}服务已加载`, 'success');
                        successCount++;
                    } else {
                        showResult('serviceResults', `❌ ${serviceName}服务未加载`, 'error');
                    }
                });

                if (successCount === services.length) {
                    log('所有服务测试成功', 'success');
                } else {
                    log(`服务测试部分成功: ${successCount}/${services.length}`, 'error');
                }
            } catch (error) {
                showResult('serviceResults', '❌ 服务测试错误: ' + error.message, 'error');
                log('服务测试错误: ' + error.message, 'error');
            }
        }

        // 考试流程测试
        function testExamFlow() {
            log('开始测试考试流程...');
            try {
                // 模拟考试流程
                showResult('examResults', '🎯 开始模拟考试流程...', 'info');
                
                // 步骤1: 创建学生
                showResult('examResults', '📝 步骤1: 创建测试学生', 'info');
                
                // 步骤2: 开始考试
                showResult('examResults', '🚀 步骤2: 开始考试流程', 'info');
                
                // 步骤3: 测试各个考试步骤
                const examSteps = [
                    '中文自我介绍 (2分钟)',
                    '英文自我介绍 (2分钟)',
                    '英文翻译 (5分钟)',
                    '专业题目 (10分钟)',
                    '综合问答 (8分钟)'
                ];
                
                examSteps.forEach((step, index) => {
                    showResult('examResults', `📋 步骤${index + 3}: ${step}`, 'info');
                });
                
                showResult('examResults', '✅ 考试流程模拟完成', 'success');
                log('考试流程测试完成', 'success');
            } catch (error) {
                showResult('examResults', '❌ 考试流程测试错误: ' + error.message, 'error');
                log('考试流程测试错误: ' + error.message, 'error');
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('调试测试页面已加载');
            log('点击上方按钮开始测试各项功能');
        });
    </script>
</body>
</html>
