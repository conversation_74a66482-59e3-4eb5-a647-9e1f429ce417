/**
 * TimerService - 计时器服务
 * 负责考试计时的业务逻辑和状态管理
 */

class TimerService extends BaseService {
    constructor() {
        super();
        this.timers = new Map(); // 支持多个计时器
        this.currentTimer = null;
        this.defaultSettings = {
            chineseTime: 2,      // 中文自我介绍 2分钟
            englishTime: 2,      // 英文自我介绍 2分钟
            translationTime: 5,  // 英文翻译 5分钟
            professionalTime: 10, // 专业问题 10分钟
            comprehensiveTime: 8  // 综合问答 8分钟
        };
    }

    /**
     * 初始化服务
     */
    async onInitialize() {
        // 初始化逻辑
    }

    /**
     * 启动服务
     */
    async onStart() {
        // 启动逻辑
    }

    // ==================== 计时器管理 ====================

    /**
     * 创建计时器
     * @param {string} timerId - 计时器ID
     * @param {number} duration - 持续时间（秒）
     * @param {number} stepNumber - 步骤编号
     * @returns {Object} 计时器状态
     */
    createTimer(timerId, duration, stepNumber = 1) {
        const timer = {
            id: timerId,
            stepNumber,
            duration,
            remainingTime: duration,
            startTime: null,
            endTime: null,
            isRunning: false,
            isPaused: false,
            intervalId: null,
            callbacks: {
                onTick: [],
                onComplete: [],
                onStart: [],
                onPause: [],
                onStop: []
            }
        };

        this.timers.set(timerId, timer);
        return this.getTimerState(timerId);
    }

    /**
     * 启动计时器
     * @param {string} timerId - 计时器ID
     * @returns {Object} 计时器状态
     */
    startTimer(timerId = 'default') {
        try {
            let timer = this.timers.get(timerId);
            if (!timer) {
                // 如果计时器不存在，创建默认计时器
                timer = this.createTimer(timerId, this.getDefaultDuration(1), 1);
            }

            if (timer.isRunning) {
                return this.getTimerState(timerId);
            }

            timer.isRunning = true;
            timer.isPaused = false;
            timer.startTime = new Date().toISOString();

            // 设置定时器
            timer.intervalId = setInterval(() => {
                this.tick(timerId);
            }, 1000);

            this.currentTimer = timer;

            // 触发开始事件
            this.triggerCallbacks(timer, 'onStart');
            this.emit('timerStarted', {
                timerId,
                stepNumber: timer.stepNumber,
                duration: timer.duration,
                remainingTime: timer.remainingTime
            });

            return this.getTimerState(timerId);
        } catch (error) {
            console.error('启动计时器失败:', error);
            throw error;
        }
    }

    /**
     * 暂停计时器
     * @param {string} timerId - 计时器ID
     * @returns {Object} 计时器状态
     */
    pauseTimer(timerId = 'default') {
        try {
            const timer = this.timers.get(timerId);
            if (!timer || !timer.isRunning) {
                throw new Error('计时器未运行');
            }

            timer.isPaused = true;
            timer.isRunning = false;

            if (timer.intervalId) {
                clearInterval(timer.intervalId);
                timer.intervalId = null;
            }

            // 触发暂停事件
            this.triggerCallbacks(timer, 'onPause');
            this.emit('timerPaused', {
                timerId,
                stepNumber: timer.stepNumber,
                remainingTime: timer.remainingTime
            });

            return this.getTimerState(timerId);
        } catch (error) {
            console.error('暂停计时器失败:', error);
            throw error;
        }
    }

    /**
     * 停止计时器
     * @param {string} timerId - 计时器ID
     * @returns {Object} 计时器状态
     */
    stopTimer(timerId = 'default') {
        try {
            const timer = this.timers.get(timerId);
            if (!timer) {
                return null;
            }

            timer.isRunning = false;
            timer.isPaused = false;
            timer.endTime = new Date().toISOString();

            if (timer.intervalId) {
                clearInterval(timer.intervalId);
                timer.intervalId = null;
            }

            // 触发停止事件
            this.triggerCallbacks(timer, 'onStop');
            this.emit('timerStopped', {
                timerId,
                stepNumber: timer.stepNumber,
                remainingTime: timer.remainingTime,
                totalTime: timer.duration - timer.remainingTime
            });

            return this.getTimerState(timerId);
        } catch (error) {
            console.error('停止计时器失败:', error);
            throw error;
        }
    }

    /**
     * 重置计时器
     * @param {string} timerId - 计时器ID
     * @param {number} duration - 新的持续时间（可选）
     * @param {number} stepNumber - 步骤编号（可选）
     * @returns {Object} 计时器状态
     */
    resetTimer(timerId = 'default', duration = null, stepNumber = null) {
        try {
            // 先停止现有计时器
            this.stopTimer(timerId);

            const timer = this.timers.get(timerId);
            if (timer) {
                // 重置计时器状态
                timer.remainingTime = duration || timer.duration;
                timer.duration = duration || timer.duration;
                timer.stepNumber = stepNumber || timer.stepNumber;
                timer.startTime = null;
                timer.endTime = null;
                timer.isRunning = false;
                timer.isPaused = false;
            } else if (duration) {
                // 创建新计时器
                this.createTimer(timerId, duration, stepNumber || 1);
            }

            this.emit('timerReset', {
                timerId,
                stepNumber: stepNumber || (timer ? timer.stepNumber : 1),
                duration: duration || (timer ? timer.duration : this.getDefaultDuration(1))
            });

            return this.getTimerState(timerId);
        } catch (error) {
            console.error('重置计时器失败:', error);
            throw error;
        }
    }

    /**
     * 计时器滴答
     * @param {string} timerId - 计时器ID
     */
    tick(timerId) {
        const timer = this.timers.get(timerId);
        if (!timer || !timer.isRunning) {
            return;
        }

        timer.remainingTime--;

        // 触发滴答事件
        this.triggerCallbacks(timer, 'onTick');
        this.emit('timerTick', {
            timerId,
            stepNumber: timer.stepNumber,
            remainingTime: timer.remainingTime,
            duration: timer.duration
        });

        // 检查是否完成
        if (timer.remainingTime <= 0) {
            this.completeTimer(timerId);
        }
    }

    /**
     * 完成计时器
     * @param {string} timerId - 计时器ID
     */
    completeTimer(timerId) {
        const timer = this.timers.get(timerId);
        if (!timer) {
            return;
        }

        timer.isRunning = false;
        timer.remainingTime = 0;
        timer.endTime = new Date().toISOString();

        if (timer.intervalId) {
            clearInterval(timer.intervalId);
            timer.intervalId = null;
        }

        // 触发完成事件
        this.triggerCallbacks(timer, 'onComplete');
        this.emit('timerCompleted', {
            timerId,
            stepNumber: timer.stepNumber,
            duration: timer.duration
        });
    }

    // ==================== 状态查询 ====================

    /**
     * 获取计时器状态
     * @param {string} timerId - 计时器ID
     * @returns {Object} 计时器状态
     */
    getTimerState(timerId = 'default') {
        const timer = this.timers.get(timerId);
        if (!timer) {
            return {
                exists: false,
                isRunning: false,
                isPaused: false,
                duration: 0,
                remainingTime: 0,
                startTime: null,
                endTime: null,
                stepNumber: 1
            };
        }

        return {
            exists: true,
            id: timer.id,
            stepNumber: timer.stepNumber,
            isRunning: timer.isRunning,
            isPaused: timer.isPaused,
            duration: timer.duration,
            remainingTime: timer.remainingTime,
            startTime: timer.startTime,
            endTime: timer.endTime,
            elapsedTime: timer.duration - timer.remainingTime,
            progressPercentage: ((timer.duration - timer.remainingTime) / timer.duration) * 100
        };
    }

    /**
     * 获取当前计时器状态
     * @returns {Object} 当前计时器状态
     */
    getCurrentTimerState() {
        if (!this.currentTimer) {
            return this.getTimerState('default');
        }
        return this.getTimerState(this.currentTimer.id);
    }

    /**
     * 获取所有计时器状态
     * @returns {Object} 所有计时器状态
     */
    getAllTimerStates() {
        const states = {};
        for (const [timerId, timer] of this.timers) {
            states[timerId] = this.getTimerState(timerId);
        }
        return states;
    }

    // ==================== 设置管理 ====================

    /**
     * 设置计时器持续时间
     * @param {string} timerId - 计时器ID
     * @param {number} duration - 持续时间（秒）
     * @returns {Object} 计时器状态
     */
    setTimerDuration(timerId, duration) {
        try {
            let timer = this.timers.get(timerId);
            if (!timer) {
                timer = this.createTimer(timerId, duration);
            } else {
                timer.duration = duration;
                timer.remainingTime = duration;
            }

            this.emit('timerDurationChanged', {
                timerId,
                duration,
                stepNumber: timer.stepNumber
            });

            return this.getTimerState(timerId);
        } catch (error) {
            console.error('设置计时器持续时间失败:', error);
            throw error;
        }
    }

    /**
     * 获取默认持续时间
     * @param {number} stepNumber - 步骤编号
     * @returns {number} 持续时间（秒）
     */
    getDefaultDuration(stepNumber) {
        const stepDurations = {
            1: this.defaultSettings.chineseTime * 60,
            2: this.defaultSettings.englishTime * 60,
            3: this.defaultSettings.translationTime * 60,
            4: this.defaultSettings.professionalTime * 60,
            5: this.defaultSettings.comprehensiveTime * 60
        };

        return stepDurations[stepNumber] || 300; // 默认5分钟
    }

    /**
     * 更新默认设置
     * @param {Object} settings - 新设置
     */
    updateDefaultSettings(settings) {
        this.defaultSettings = { ...this.defaultSettings, ...settings };
        this.emit('settingsUpdated', this.defaultSettings);
    }

    // ==================== 事件回调管理 ====================

    /**
     * 添加计时器回调
     * @param {string} timerId - 计时器ID
     * @param {string} event - 事件类型
     * @param {Function} callback - 回调函数
     */
    addTimerCallback(timerId, event, callback) {
        const timer = this.timers.get(timerId);
        if (timer && timer.callbacks[event]) {
            timer.callbacks[event].push(callback);
        }
    }

    /**
     * 移除计时器回调
     * @param {string} timerId - 计时器ID
     * @param {string} event - 事件类型
     * @param {Function} callback - 回调函数
     */
    removeTimerCallback(timerId, event, callback) {
        const timer = this.timers.get(timerId);
        if (timer && timer.callbacks[event]) {
            const index = timer.callbacks[event].indexOf(callback);
            if (index > -1) {
                timer.callbacks[event].splice(index, 1);
            }
        }
    }

    /**
     * 触发回调
     * @param {Object} timer - 计时器对象
     * @param {string} event - 事件类型
     */
    triggerCallbacks(timer, event) {
        if (timer.callbacks[event]) {
            timer.callbacks[event].forEach(callback => {
                try {
                    callback(this.getTimerState(timer.id));
                } catch (error) {
                    console.error(`计时器回调错误 (${event}):`, error);
                }
            });
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 格式化时间
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    /**
     * 清理所有计时器
     */
    clearAllTimers() {
        for (const [timerId, timer] of this.timers) {
            this.stopTimer(timerId);
        }
        this.timers.clear();
        this.currentTimer = null;
        this.emit('allTimersCleared');
    }

    /**
     * 删除计时器
     * @param {string} timerId - 计时器ID
     */
    deleteTimer(timerId) {
        this.stopTimer(timerId);
        this.timers.delete(timerId);
        
        if (this.currentTimer && this.currentTimer.id === timerId) {
            this.currentTimer = null;
        }

        this.emit('timerDeleted', { timerId });
    }
}

// 创建全局实例
const timerService = new TimerService();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TimerService, timerService };
} else {
    window.TimerService = TimerService;
    window.timerService = timerService;
}
