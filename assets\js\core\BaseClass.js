/**
 * 基础类 - 为所有类提供公共功能
 * 遵循面向对象设计原则，提供统一的错误处理和日志记录
 */
class BaseClass {
    constructor(name = 'BaseClass') {
        this.className = name;
        this.initialized = false;
        this.eventListeners = new Map();
        this.logger = window.logger || console;
    }

    /**
     * 初始化方法 - 子类必须实现
     */
    async initialize() {
        throw new Error(`${this.className} must implement initialize() method`);
    }

    /**
     * 验证方法 - 子类可以重写
     */
    validate() {
        return true;
    }

    /**
     * 销毁方法 - 清理资源
     */
    destroy() {
        // 移除所有事件监听器
        this.eventListeners.forEach((listener, element) => {
            if (element && typeof element.removeEventListener === 'function') {
                element.removeEventListener(listener.event, listener.handler);
            }
        });
        this.eventListeners.clear();
        
        this.initialized = false;
        this.logger.info(`${this.className} destroyed`);
    }

    /**
     * 添加事件监听器并记录
     */
    addEventListener(element, event, handler, options = {}) {
        if (!element || typeof element.addEventListener !== 'function') {
            this.logger.error(`Invalid element for addEventListener in ${this.className}`);
            return;
        }

        element.addEventListener(event, handler, options);
        
        // 记录监听器以便后续清理
        const key = `${element.tagName || 'unknown'}_${event}_${Date.now()}`;
        this.eventListeners.set(key, {
            element,
            event,
            handler
        });
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(element, event, handler) {
        if (!element || typeof element.removeEventListener !== 'function') {
            this.logger.error(`Invalid element for removeEventListener in ${this.className}`);
            return;
        }

        element.removeEventListener(event, handler);
        
        // 从记录中移除
        for (let [key, listener] of this.eventListeners.entries()) {
            if (listener.element === element && listener.event === event && listener.handler === handler) {
                this.eventListeners.delete(key);
                break;
            }
        }
    }

    /**
     * 错误处理
     */
    handleError(error, context = '') {
        const errorMessage = `Error in ${this.className}${context ? ` (${context})` : ''}: ${error.message}`;
        this.logger.error(errorMessage, error);
        
        // 可以在这里添加错误上报逻辑
        if (window.errorReporter) {
            window.errorReporter.report(error, {
                className: this.className,
                context
            });
        }
    }

    /**
     * 发出事件
     */
    emit(eventName, data = null) {
        const event = new CustomEvent(`${this.className.toLowerCase()}:${eventName}`, {
            detail: data
        });
        document.dispatchEvent(event);
        this.logger.debug(`${this.className} emitted event: ${eventName}`, data);
    }

    /**
     * 监听事件
     */
    on(eventName, handler) {
        const fullEventName = `${this.className.toLowerCase()}:${eventName}`;
        this.addEventListener(document, fullEventName, handler);
    }

    /**
     * 移除事件监听
     */
    off(eventName, handler) {
        const fullEventName = `${this.className.toLowerCase()}:${eventName}`;
        this.removeEventListener(document, fullEventName, handler);
    }

    /**
     * 获取类信息
     */
    getInfo() {
        return {
            className: this.className,
            initialized: this.initialized,
            eventListeners: this.eventListeners.size
        };
    }

    /**
     * 设置初始化状态
     */
    setInitialized(status = true) {
        this.initialized = status;
        this.logger.info(`${this.className} initialization status: ${status}`);
    }

    /**
     * 检查是否已初始化
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 安全执行方法
     */
    async safeExecute(method, ...args) {
        try {
            if (typeof method === 'function') {
                return await method.apply(this, args);
            } else if (typeof this[method] === 'function') {
                return await this[method].apply(this, args);
            } else {
                throw new Error(`Method ${method} not found in ${this.className}`);
            }
        } catch (error) {
            this.handleError(error, `safeExecute(${method})`);
            return null;
        }
    }

    /**
     * 延迟执行
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 重试机制
     */
    async retry(fn, maxAttempts = 3, delayMs = 1000) {
        let lastError;
        
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;
                this.logger.warn(`${this.className} retry attempt ${attempt}/${maxAttempts} failed:`, error.message);
                
                if (attempt < maxAttempts) {
                    await this.delay(delayMs * attempt);
                }
            }
        }
        
        throw lastError;
    }

    /**
     * 深拷贝对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const cloned = {};
            for (let key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }
        
        return obj;
    }

    /**
     * 格式化日期
     */
    formatDate(date = new Date(), format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    /**
     * 生成唯一ID
     */
    generateId(prefix = '') {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `${prefix}${prefix ? '_' : ''}${timestamp}_${random}`;
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func.apply(this, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}
