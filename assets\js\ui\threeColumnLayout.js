/**
 * 三栏布局管理器 - 专门处理新的三栏布局交互
 * 负责左侧流程节点、中间内容区域、右侧操作按钮的协调
 */
class ThreeColumnLayoutManager {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 5;
        // 初始化默认步骤名称，稍后会从ExamSystem获取动态名称
        this.stepNames = {
            1: '中文自我介绍（2分钟）',
            2: '英文自我介绍（2分钟）',
            3: '英文翻译（5分钟）',
            4: '专业课问答（10分钟）',
            5: '综合面试（8分钟）'
        };

        this.init();
    }
    
    init() {
        this.bindEvents();
        console.log('ThreeColumnLayoutManager initialized');

        // 监听考试系统的步骤变化事件，保持同步
        document.addEventListener('examStepChanged', (e) => {
            const { stepNumber } = e.detail;
            this.syncCurrentStep(stepNumber);
        });

        // 延迟初始化布局，等待ExamSystem完成状态恢复
        setTimeout(() => {
            // 从ExamSystem获取当前步骤，如果没有则默认为1
            const currentStep = window.examSystem ? window.examSystem.currentStepNumber : 1;
            this.updateLayout(currentStep);
            console.log(`[Layout] 延迟初始化布局，当前步骤: ${currentStep}`);
        }, 1500); // 等待ExamSystem完成初始化
    }

    /**
     * 同步当前步骤（从考试系统）
     * @param {number} stepNumber - 步骤编号
     */
    syncCurrentStep(stepNumber) {
        console.log(`[Layout] 同步当前步骤: ${this.currentStep} -> ${stepNumber}`);
        this.currentStep = stepNumber;
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 监听步骤点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.step-item')) {
                const stepItem = e.target.closest('.step-item');
                const stepNumber = parseInt(stepItem.dataset.step);
                if (stepNumber) {
                    this.switchToStep(stepNumber);
                }
            }
        });
        
        // 监听右侧按钮点击
        this.bindActionButtons();
        
        // 监听自定义步骤切换事件
        document.addEventListener('stepChange', (e) => {
            const { stepNumber } = e.detail;
            this.switchToStep(stepNumber);
        });
        
        // 监听键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey) {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    this.previousStep();
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    this.nextStep();
                }
            }
        });
    }
    
    /**
     * 绑定右侧操作按钮事件
     */
    bindActionButtons() {
        // 计时器控制按钮由ButtonManager处理，这里不重复绑定
        // 避免事件冲突
        
        // 题目操作按钮
        const drawQuestionBtn = document.getElementById('draw-question-btn');
        if (drawQuestionBtn) {
            drawQuestionBtn.addEventListener('click', () => this.handleDrawQuestion());
        }
        
        // 流程控制按钮
        const prevStepBtn = document.getElementById('prev-step-btn');
        const nextStepBtn = document.getElementById('next-step-btn');
        
        if (prevStepBtn) {
            prevStepBtn.addEventListener('click', () => this.previousStep());
        }
        if (nextStepBtn) {
            nextStepBtn.addEventListener('click', () => this.nextStep());
        }
        
        // 系统操作按钮
        const examRecordsBtn = document.getElementById('exam-records-btn');
        const nextStudentBtn = document.getElementById('next-student-btn');
        const settingsBtn = document.getElementById('settings-btn');
        
        if (examRecordsBtn) {
            examRecordsBtn.addEventListener('click', () => this.handleExamRecords());
        }
        if (nextStudentBtn) {
            nextStudentBtn.addEventListener('click', () => this.handleNextStudent());
        }
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.handleSettings());
        }
    }
    
    /**
     * 切换到指定步骤
     * @param {number} stepNumber - 步骤编号
     */
    switchToStep(stepNumber) {
        if (stepNumber < 1 || stepNumber > this.totalSteps) {
            console.warn(`Invalid step number: ${stepNumber}`);
            return;
        }
        
        if (stepNumber === this.currentStep) {
            return; // 已经是当前步骤
        }
        
        // 添加切换动画
        this.animateStepTransition(() => {
            this.currentStep = stepNumber;
            this.updateLayout(stepNumber);
            
            // 触发步骤变更事件
            const event = new CustomEvent('stepChanged', {
                detail: { 
                    stepNumber,
                    stepName: this.stepNames[stepNumber]
                }
            });
            document.dispatchEvent(event);
        });
    }
    
    /**
     * 更新整个布局
     * @param {number} stepNumber - 步骤编号
     */
    updateLayout(stepNumber) {
        this.updateProcessSteps(stepNumber);
        this.updateCenterContent(stepNumber);
        this.updateActionButtons(stepNumber);

        // 更新进度指示器
        this.updateProgressIndicators(stepNumber);

        console.log(`Layout updated to step ${stepNumber}: ${this.getDynamicStepName(stepNumber)}`);
    }

    /**
     * 更新进度指示器
     * @param {number} stepNumber - 步骤编号
     */
    updateProgressIndicators(stepNumber) {
        console.log(`[Layout] 更新进度指示器: ${stepNumber}/${this.totalSteps}`);

        // 更新展开状态的进度指示器
        if (typeof window.updateExpandedProgress === 'function') {
            window.updateExpandedProgress(stepNumber, this.totalSteps);
        }

        // 更新最小化状态的进度指示器
        if (typeof window.updateMinimizedSteps === 'function') {
            window.updateMinimizedSteps();
        }

        if (typeof window.updateMinimizedButtons === 'function') {
            window.updateMinimizedButtons();
        }
    }
    
    /**
     * 更新左侧流程步骤
     * @param {number} currentStep - 当前步骤
     */
    updateProcessSteps(currentStep) {
        const stepItems = document.querySelectorAll('.step-item');
        stepItems.forEach((item, index) => {
            const stepNumber = index + 1;
            item.classList.remove('active', 'completed');
            
            if (stepNumber === currentStep) {
                item.classList.add('active');
                // 添加激活动画
                item.style.animation = 'stepActivate 0.3s ease-out';
                setTimeout(() => {
                    item.style.animation = '';
                }, 300);
            } else if (stepNumber < currentStep) {
                item.classList.add('completed');
            }
        });
    }
    
    /**
     * 获取动态步骤名称
     * @param {number} stepNumber - 步骤编号
     * @returns {string} 步骤名称
     */
    getDynamicStepName(stepNumber) {
        // 尝试从ExamSystem获取动态步骤名称
        if (window.examSystem && window.examSystem.settings) {
            const timeSettings = window.examSystem.settings.get('timeSettings');
            if (timeSettings) {
                const stepTimes = {
                    1: timeSettings.chineseTime || 2,
                    2: timeSettings.englishTime || 2,
                    3: timeSettings.translationTime || 5,
                    4: timeSettings.professionalTime || 5,
                    5: timeSettings.comprehensiveTime || 8
                };

                const stepNames = {
                    1: `中文自我介绍（${stepTimes[1]}分钟）`,
                    2: `英文自我介绍（${stepTimes[2]}分钟）`,
                    3: `英文翻译（${stepTimes[3]}分钟）`,
                    4: `专业课问答（${stepTimes[4]}分钟）`,
                    5: `综合面试（${stepTimes[5]}分钟）`
                };

                return stepNames[stepNumber] || this.stepNames[stepNumber];
            }
        }

        // 如果无法获取动态名称，使用默认名称
        return this.stepNames[stepNumber];
    }

    /**
     * 更新中间内容区域
     * @param {number} stepNumber - 步骤编号
     */
    updateCenterContent(stepNumber) {
        // 更新标题
        const contentTitle = document.getElementById('current-step-title');
        if (contentTitle) {
            const icons = {
                1: 'bi-person-circle',
                2: 'bi-translate',
                3: 'bi-journal-text',
                4: 'bi-mortarboard',
                5: 'bi-chat-dots'
            };

            // 使用动态步骤名称
            const stepName = this.getDynamicStepName(stepNumber);
            contentTitle.innerHTML = `<i class="${icons[stepNumber]}"></i> ${stepName}`;
        }
        
        // 显示对应的步骤内容
        const stepContents = document.querySelectorAll('.step-content');
        stepContents.forEach((content, index) => {
            content.classList.remove('active');
            if (index + 1 === stepNumber) {
                content.classList.add('active');
                // 添加内容切换动画
                content.style.animation = 'slideInRight 0.4s ease-out';
                setTimeout(() => {
                    content.style.animation = '';
                }, 400);
            }
        });
    }
    
    /**
     * 更新右侧操作按钮
     * @param {number} stepNumber - 步骤编号
     */
    updateActionButtons(stepNumber) {
        // 更新导航按钮状态
        const prevBtn = document.getElementById('prev-step-btn');
        const nextBtn = document.getElementById('next-step-btn');
        
        if (prevBtn) {
            prevBtn.disabled = stepNumber <= 1;
            prevBtn.classList.toggle('disabled', stepNumber <= 1);
        }
        
        if (nextBtn) {
            nextBtn.disabled = stepNumber >= this.totalSteps;
            nextBtn.classList.toggle('disabled', stepNumber >= this.totalSteps);
        }
        
        // 显示/隐藏题目操作按钮
        const questionControls = document.getElementById('question-controls');
        if (questionControls) {
            if (stepNumber === 3 || stepNumber === 4) {
                questionControls.style.display = 'block';
                this.updateQuestionButton(stepNumber);
            } else {
                questionControls.style.display = 'none';
            }
        }
    }
    
    /**
     * 更新抽题按钮状态
     * @param {number} stepNumber - 步骤编号
     */
    updateQuestionButton(stepNumber) {
        const drawBtn = document.getElementById('draw-question-btn');
        const drawnBtn = document.getElementById('question-drawn-btn');
        
        // 这里可以检查是否已经抽过题
        const hasQuestion = this.checkIfQuestionDrawn(stepNumber);
        
        if (hasQuestion) {
            if (drawBtn) drawBtn.style.display = 'none';
            if (drawnBtn) drawnBtn.style.display = 'block';
        } else {
            if (drawBtn) drawBtn.style.display = 'block';
            if (drawnBtn) drawnBtn.style.display = 'none';
        }
    }
    
    /**
     * 检查是否已抽题
     * @param {number} stepNumber - 步骤编号
     * @returns {boolean}
     */
    checkIfQuestionDrawn(stepNumber) {
        // 这里应该检查实际的题目状态
        // 暂时返回false，实际实现时需要连接到题目管理系统
        return false;
    }
    
    /**
     * 添加步骤切换动画
     * @param {Function} callback - 动画完成后的回调
     */
    animateStepTransition(callback) {
        const centerContent = document.querySelector('.center-content');
        if (centerContent) {
            centerContent.classList.add('step-transition');
            setTimeout(() => {
                callback();
                centerContent.classList.remove('step-transition');
            }, 150);
        } else {
            callback();
        }
    }
    
    /**
     * 上一步
     */
    previousStep() {
        if (this.currentStep > 1) {
            this.switchToStep(this.currentStep - 1);
        }
    }
    
    /**
     * 下一步
     */
    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.switchToStep(this.currentStep + 1);
        }
    }
    
    // 按钮处理方法
    handleStartTimer() {
        console.log('Start timer clicked');
        // 这里连接到计时器系统
    }
    
    handlePauseTimer() {
        console.log('Pause timer clicked');
        // 这里连接到计时器系统
    }
    
    handleResetTimer() {
        console.log('Reset timer clicked');
        // 这里连接到计时器系统
    }
    
    handleDrawQuestion() {
        console.log('Draw question clicked for step', this.currentStep);

        // 根据当前步骤确定按钮ID
        let buttonId = '';
        if (this.currentStep === 3) {
            buttonId = 'draw-translation-question';
        } else if (this.currentStep === 4) {
            buttonId = 'draw-professional-question';
        }

        // 调用ExamSystem的抽题方法
        if (buttonId && window.examSystem && window.examSystem.drawQuestion) {
            window.examSystem.drawQuestion(buttonId);
        } else {
            console.warn('无法抽取题目：当前步骤不支持抽题或ExamSystem不可用');
        }
    }
    
    handleExamRecords() {
        console.log('Exam records clicked');

        // 调用ExamSystem的显示考试记录方法
        if (window.examSystem && window.examSystem.showExamRecords) {
            window.examSystem.showExamRecords();
        } else {
            console.warn('ExamSystem不可用或showExamRecords方法不存在');
        }
    }

    handleNextStudent() {
        console.log('Next student clicked');

        // 调用ExamSystem的下一名考生方法
        if (window.examSystem && window.examSystem.nextStudent) {
            window.examSystem.nextStudent();
        } else {
            console.warn('ExamSystem不可用或nextStudent方法不存在');
        }
    }

    handleSettings() {
        console.log('Settings clicked');

        // 调用ExamSystem的显示设置方法
        if (window.examSystem && window.examSystem.showSettings) {
            window.examSystem.showSettings();
        } else {
            console.warn('ExamSystem不可用或showSettings方法不存在');
        }
    }
}

// 初始化三栏布局管理器
document.addEventListener('DOMContentLoaded', () => {
    window.threeColumnLayout = new ThreeColumnLayoutManager();
});
