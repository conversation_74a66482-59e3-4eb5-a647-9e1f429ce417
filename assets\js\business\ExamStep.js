/**
 * 考试步骤类 - 管理考试步骤的逻辑和状态
 * 遵循单一职责原则，专门负责考试步骤相关的业务逻辑
 */
class ExamStep extends BaseClass {
    constructor(stepNumber, stepName, duration) {
        super('ExamStep');
        this.stepNumber = stepNumber;
        this.stepName = stepName;
        this.duration = duration; // 步骤时长（秒）
        this.isActive = false;
        this.isCompleted = false;
        this.startTime = null;
        this.endTime = null;
        this.actualDuration = 0;
        this.questions = [];
        this.requiredActions = [];
        this.completedActions = new Set();
    }

    /**
     * 初始化考试步骤
     * @returns {Promise<boolean>} 初始化结果
     */
    async initialize() {
        try {
            this.setupStepConfiguration();
            this.log(`考试步骤 ${this.stepNumber} - ${this.stepName} 初始化成功`);
            this.initialized = true;
            return true;
        } catch (error) {
            this.handleError(error, '考试步骤初始化');
            return false;
        }
    }

    /**
     * 设置步骤配置
     */
    setupStepConfiguration() {
        const stepConfigs = {
            1: {
                name: '中文自我介绍',
                requiredActions: ['start_timer', 'show_instructions'],
                hasQuestions: false
            },
            2: {
                name: '英文自我介绍',
                requiredActions: ['start_timer', 'show_instructions'],
                hasQuestions: false
            },
            3: {
                name: '翻译题',
                requiredActions: ['start_timer', 'load_questions', 'display_questions'],
                hasQuestions: true
            },
            4: {
                name: '专业题',
                requiredActions: ['start_timer', 'load_questions', 'display_questions'],
                hasQuestions: true
            },
            5: {
                name: '综合题',
                requiredActions: ['start_timer', 'show_instructions'],
                hasQuestions: false
            }
        };

        const config = stepConfigs[this.stepNumber];
        if (config) {
            this.stepName = config.name;
            this.requiredActions = config.requiredActions;
            this.hasQuestions = config.hasQuestions;
        }
    }

    /**
     * 开始步骤
     * @returns {boolean} 开始结果
     */
    start() {
        if (this.isActive) {
            this.log(`步骤 ${this.stepNumber} 已经在进行中`, 'warn');
            return false;
        }

        this.isActive = true;
        this.isCompleted = false;
        this.startTime = new Date();
        this.completedActions.clear();

        this.log(`步骤 ${this.stepNumber} - ${this.stepName} 开始`);
        return true;
    }

    /**
     * 结束步骤
     * @returns {boolean} 结束结果
     */
    end() {
        if (!this.isActive) {
            this.log(`步骤 ${this.stepNumber} 未在进行中`, 'warn');
            return false;
        }

        this.isActive = false;
        this.isCompleted = true;
        this.endTime = new Date();
        this.actualDuration = Math.floor((this.endTime - this.startTime) / 1000);

        this.log(`步骤 ${this.stepNumber} - ${this.stepName} 结束，用时 ${this.actualDuration} 秒`);
        return true;
    }

    /**
     * 暂停步骤
     * @returns {boolean} 暂停结果
     */
    pause() {
        if (!this.isActive) {
            this.log(`步骤 ${this.stepNumber} 未在进行中，无法暂停`, 'warn');
            return false;
        }

        this.isPaused = true;
        this.pauseTime = new Date();
        this.log(`步骤 ${this.stepNumber} - ${this.stepName} 已暂停`);
        return true;
    }

    /**
     * 恢复步骤
     * @returns {boolean} 恢复结果
     */
    resume() {
        if (!this.isPaused) {
            this.log(`步骤 ${this.stepNumber} 未暂停，无法恢复`, 'warn');
            return false;
        }

        this.isPaused = false;
        
        // 调整开始时间以排除暂停时间
        if (this.pauseTime && this.startTime) {
            const pauseDuration = new Date() - this.pauseTime;
            this.startTime = new Date(this.startTime.getTime() + pauseDuration);
        }
        
        this.pauseTime = null;
        this.log(`步骤 ${this.stepNumber} - ${this.stepName} 已恢复`);
        return true;
    }

    /**
     * 完成动作
     * @param {string} action - 动作名称
     * @returns {boolean} 完成结果
     */
    completeAction(action) {
        if (!this.requiredActions.includes(action)) {
            this.log(`动作 ${action} 不在必需动作列表中`, 'warn');
            return false;
        }

        this.completedActions.add(action);
        this.log(`步骤 ${this.stepNumber} 完成动作: ${action}`);
        
        // 检查是否所有必需动作都已完成
        if (this.areAllActionsCompleted()) {
            this.log(`步骤 ${this.stepNumber} 所有必需动作已完成`);
        }
        
        return true;
    }

    /**
     * 检查是否所有必需动作都已完成
     * @returns {boolean} 检查结果
     */
    areAllActionsCompleted() {
        return this.requiredActions.every(action => this.completedActions.has(action));
    }

    /**
     * 获取剩余时间
     * @returns {number} 剩余时间（秒）
     */
    getRemainingTime() {
        if (!this.isActive || !this.startTime) {
            return this.duration;
        }

        const elapsed = Math.floor((new Date() - this.startTime) / 1000);
        return Math.max(0, this.duration - elapsed);
    }

    /**
     * 获取已用时间
     * @returns {number} 已用时间（秒）
     */
    getElapsedTime() {
        if (!this.startTime) {
            return 0;
        }

        const endTime = this.endTime || new Date();
        return Math.floor((endTime - this.startTime) / 1000);
    }

    /**
     * 获取进度百分比
     * @returns {number} 进度百分比
     */
    getProgress() {
        if (this.duration === 0) return 100;
        
        const elapsed = this.getElapsedTime();
        return Math.min(100, (elapsed / this.duration) * 100);
    }

    /**
     * 是否超时
     * @returns {boolean} 是否超时
     */
    isOvertime() {
        return this.getElapsedTime() > this.duration;
    }

    /**
     * 设置题目
     * @param {Array} questions - 题目数组
     */
    setQuestions(questions) {
        this.questions = questions || [];
        this.log(`步骤 ${this.stepNumber} 设置了 ${this.questions.length} 道题目`);
    }

    /**
     * 获取题目
     * @returns {Array} 题目数组
     */
    getQuestions() {
        return [...this.questions];
    }

    /**
     * 添加题目
     * @param {Object} question - 题目对象
     */
    addQuestion(question) {
        this.questions.push(question);
        this.log(`步骤 ${this.stepNumber} 添加了题目: ${question.id}`);
    }

    /**
     * 移除题目
     * @param {string} questionId - 题目ID
     * @returns {boolean} 移除结果
     */
    removeQuestion(questionId) {
        const index = this.questions.findIndex(q => q.id === questionId);
        if (index > -1) {
            this.questions.splice(index, 1);
            this.log(`步骤 ${this.stepNumber} 移除了题目: ${questionId}`);
            return true;
        }
        return false;
    }

    /**
     * 获取步骤状态
     * @returns {Object} 步骤状态对象
     */
    getStatus() {
        return {
            stepNumber: this.stepNumber,
            stepName: this.stepName,
            duration: this.duration,
            isActive: this.isActive,
            isCompleted: this.isCompleted,
            isPaused: this.isPaused || false,
            startTime: this.startTime,
            endTime: this.endTime,
            elapsedTime: this.getElapsedTime(),
            remainingTime: this.getRemainingTime(),
            progress: this.getProgress(),
            isOvertime: this.isOvertime(),
            questionsCount: this.questions.length,
            completedActions: Array.from(this.completedActions),
            allActionsCompleted: this.areAllActionsCompleted()
        };
    }

    /**
     * 获取步骤摘要
     * @returns {Object} 步骤摘要对象
     */
    getSummary() {
        return {
            stepNumber: this.stepNumber,
            stepName: this.stepName,
            duration: this.duration,
            actualDuration: this.actualDuration,
            isCompleted: this.isCompleted,
            questionsCount: this.questions.length,
            completionRate: this.getProgress()
        };
    }

    /**
     * 验证步骤数据
     * @returns {boolean} 验证结果
     */
    validate() {
        if (!Utils.validateInput(this.stepNumber, 'number', { min: 1, max: 10 })) {
            this.log('步骤编号无效', 'error');
            return false;
        }

        if (!Utils.validateInput(this.stepName, 'string', { minLength: 1 })) {
            this.log('步骤名称无效', 'error');
            return false;
        }

        if (!Utils.validateInput(this.duration, 'number', { min: 1 })) {
            this.log('步骤时长无效', 'error');
            return false;
        }

        return true;
    }

    /**
     * 重置步骤状态
     */
    reset() {
        this.isActive = false;
        this.isCompleted = false;
        this.isPaused = false;
        this.startTime = null;
        this.endTime = null;
        this.pauseTime = null;
        this.actualDuration = 0;
        this.completedActions.clear();
        
        this.log(`步骤 ${this.stepNumber} - ${this.stepName} 状态已重置`);
    }

    /**
     * 导出步骤数据
     * @returns {Object} 步骤数据对象
     */
    export() {
        return {
            stepNumber: this.stepNumber,
            stepName: this.stepName,
            duration: this.duration,
            isActive: this.isActive,
            isCompleted: this.isCompleted,
            isPaused: this.isPaused || false,
            startTime: this.startTime,
            endTime: this.endTime,
            actualDuration: this.actualDuration,
            questions: [...this.questions],
            requiredActions: [...this.requiredActions],
            completedActions: Array.from(this.completedActions)
        };
    }

    /**
     * 导入步骤数据
     * @param {Object} data - 步骤数据对象
     * @returns {boolean} 导入结果
     */
    import(data) {
        try {
            this.stepNumber = data.stepNumber;
            this.stepName = data.stepName;
            this.duration = data.duration;
            this.isActive = data.isActive || false;
            this.isCompleted = data.isCompleted || false;
            this.isPaused = data.isPaused || false;
            this.startTime = data.startTime ? new Date(data.startTime) : null;
            this.endTime = data.endTime ? new Date(data.endTime) : null;
            this.actualDuration = data.actualDuration || 0;
            this.questions = data.questions || [];
            this.requiredActions = data.requiredActions || [];
            this.completedActions = new Set(data.completedActions || []);
            
            this.log(`步骤 ${this.stepNumber} - ${this.stepName} 数据导入成功`);
            return true;
        } catch (error) {
            this.handleError(error, '导入步骤数据');
            return false;
        }
    }
}

// 导出到全局作用域
window.ExamStep = ExamStep;
