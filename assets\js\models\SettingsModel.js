/**
 * SettingsModel - 系统设置数据模型
 * 管理系统设置相关的数据结构和验证
 */

class SettingsModel extends BaseModel {
    constructor(data = {}) {
        super(data);
        
        // 基本设置
        this.systemName = data.systemName || '研究生复试流程控制系统';
        this.version = data.version || '2.0.0';
        this.language = data.language || 'zh-CN';
        this.theme = data.theme || 'default';
        
        // 考试设置
        this.examSettings = data.examSettings || {
            totalSteps: 5,
            autoSave: true,
            autoSaveInterval: 30, // 秒
            allowPause: true,
            allowSkip: false,
            showProgress: true,
            showTimer: true
        };
        
        // 时间设置
        this.timeSettings = data.timeSettings || {
            step1Time: 120, // 中文自我介绍 2分钟
            step2Time: 120, // 英文自我介绍 2分钟
            step3Time: 300, // 英文翻译 5分钟
            step4Time: 600, // 专业课问答 10分钟
            step5Time: 480, // 综合面试 8分钟
            warningTime: 30, // 剩余30秒警告
            overtimeAllowed: false,
            overtimeLimit: 60 // 超时限制（秒）
        };
        
        // 评分设置
        this.scoringSettings = data.scoringSettings || {
            maxScore: 100,
            passingScore: 60,
            stepWeights: {
                step1: 0.15, // 中文自我介绍 15%
                step2: 0.15, // 英文自我介绍 15%
                step3: 0.20, // 英文翻译 20%
                step4: 0.30, // 专业课问答 30%
                step5: 0.20  // 综合面试 20%
            },
            autoCalculate: true,
            showScores: false
        };
        
        // 界面设置
        this.uiSettings = data.uiSettings || {
            showLeftPanel: true,
            showRightPanel: true,
            leftPanelWidth: 20, // 百分比
            rightPanelWidth: 20, // 百分比
            fontSize: 'medium',
            compactMode: false,
            animations: true,
            sounds: false
        };
        
        // 数据设置
        this.dataSettings = data.dataSettings || {
            autoBackup: true,
            backupInterval: 3600, // 1小时
            maxBackups: 10,
            exportFormat: 'json',
            dataRetention: 365, // 天数
            anonymizeData: false
        };
        
        // 安全设置
        this.securitySettings = data.securitySettings || {
            requireAuth: false,
            sessionTimeout: 7200, // 2小时
            maxLoginAttempts: 3,
            passwordPolicy: {
                minLength: 6,
                requireNumbers: false,
                requireSymbols: false,
                requireUppercase: false
            }
        };
        
        // 通知设置
        this.notificationSettings = data.notificationSettings || {
            enableNotifications: true,
            soundNotifications: false,
            desktopNotifications: false,
            emailNotifications: false,
            notificationTypes: {
                examStart: true,
                examComplete: true,
                timeWarning: true,
                systemError: true
            }
        };
        
        // 高级设置
        this.advancedSettings = data.advancedSettings || {
            debugMode: false,
            logLevel: 'info',
            apiTimeout: 30000,
            maxConcurrentUsers: 50,
            cacheEnabled: true,
            cacheTimeout: 300
        };
    }

    /**
     * 验证属性是否有效
     * @param {string} property - 属性名
     * @returns {boolean} 是否有效
     */
    isValidProperty(property) {
        const validProperties = [
            'systemName', 'version', 'language', 'theme',
            'examSettings', 'timeSettings', 'scoringSettings',
            'uiSettings', 'dataSettings', 'securitySettings',
            'notificationSettings', 'advancedSettings'
        ];
        return validProperties.includes(property);
    }

    /**
     * 自定义验证
     * @returns {Object} 验证结果
     */
    customValidate() {
        const errors = [];

        // 系统名称验证
        if (!this.systemName || this.systemName.trim() === '') {
            errors.push('系统名称不能为空');
        }

        // 语言验证
        const validLanguages = ['zh-CN', 'en-US', 'zh-TW'];
        if (!validLanguages.includes(this.language)) {
            errors.push('不支持的语言设置');
        }

        // 时间设置验证
        Object.keys(this.timeSettings).forEach(key => {
            if (key.endsWith('Time') && this.timeSettings[key] <= 0) {
                errors.push(`${key}时间设置必须大于0`);
            }
        });

        // 评分权重验证
        const totalWeight = Object.values(this.scoringSettings.stepWeights).reduce((sum, weight) => sum + weight, 0);
        if (Math.abs(totalWeight - 1.0) > 0.01) {
            errors.push('步骤权重总和必须等于1.0');
        }

        // 界面设置验证
        if (this.uiSettings.leftPanelWidth + this.uiSettings.rightPanelWidth >= 100) {
            errors.push('左右面板宽度总和不能超过100%');
        }

        return { errors };
    }

    /**
     * 获取步骤时间
     * @param {number} stepNumber - 步骤编号
     * @returns {number} 时间（秒）
     */
    getStepTime(stepNumber) {
        const timeKey = `step${stepNumber}Time`;
        return this.timeSettings[timeKey] || 300; // 默认5分钟
    }

    /**
     * 设置步骤时间
     * @param {number} stepNumber - 步骤编号
     * @param {number} time - 时间（秒）
     */
    setStepTime(stepNumber, time) {
        if (time > 0) {
            const timeKey = `step${stepNumber}Time`;
            this.timeSettings[timeKey] = time;
            this.touch();
        }
    }

    /**
     * 获取步骤权重
     * @param {number} stepNumber - 步骤编号
     * @returns {number} 权重
     */
    getStepWeight(stepNumber) {
        const weightKey = `step${stepNumber}`;
        return this.scoringSettings.stepWeights[weightKey] || 0.2; // 默认20%
    }

    /**
     * 设置步骤权重
     * @param {number} stepNumber - 步骤编号
     * @param {number} weight - 权重
     */
    setStepWeight(stepNumber, weight) {
        if (weight >= 0 && weight <= 1) {
            const weightKey = `step${stepNumber}`;
            this.scoringSettings.stepWeights[weightKey] = weight;
            this.touch();
        }
    }

    /**
     * 重置为默认设置
     */
    resetToDefaults() {
        const defaultSettings = new SettingsModel();
        Object.keys(defaultSettings.getData()).forEach(key => {
            if (key !== 'id' && key !== 'createdAt') {
                this[key] = defaultSettings[key];
            }
        });
        this.touch();
    }

    /**
     * 重置特定类别的设置
     * @param {string} category - 设置类别
     */
    resetCategory(category) {
        const defaultSettings = new SettingsModel();
        if (defaultSettings[category]) {
            this[category] = { ...defaultSettings[category] };
            this.touch();
        }
    }

    /**
     * 更新考试设置
     * @param {Object} settings - 新的考试设置
     */
    updateExamSettings(settings) {
        this.examSettings = { ...this.examSettings, ...settings };
        this.touch();
    }

    /**
     * 更新时间设置
     * @param {Object} settings - 新的时间设置
     */
    updateTimeSettings(settings) {
        this.timeSettings = { ...this.timeSettings, ...settings };
        this.touch();
    }

    /**
     * 更新评分设置
     * @param {Object} settings - 新的评分设置
     */
    updateScoringSettings(settings) {
        this.scoringSettings = { ...this.scoringSettings, ...settings };
        this.touch();
    }

    /**
     * 更新界面设置
     * @param {Object} settings - 新的界面设置
     */
    updateUISettings(settings) {
        this.uiSettings = { ...this.uiSettings, ...settings };
        this.touch();
    }

    /**
     * 启用/禁用功能
     * @param {string} feature - 功能名称
     * @param {boolean} enabled - 是否启用
     */
    setFeatureEnabled(feature, enabled) {
        const featurePaths = {
            'autoSave': 'examSettings.autoSave',
            'showProgress': 'examSettings.showProgress',
            'showTimer': 'examSettings.showTimer',
            'animations': 'uiSettings.animations',
            'sounds': 'uiSettings.sounds',
            'notifications': 'notificationSettings.enableNotifications',
            'debugMode': 'advancedSettings.debugMode'
        };

        const path = featurePaths[feature];
        if (path) {
            const keys = path.split('.');
            let obj = this;
            for (let i = 0; i < keys.length - 1; i++) {
                obj = obj[keys[i]];
            }
            obj[keys[keys.length - 1]] = enabled;
            this.touch();
        }
    }

    /**
     * 检查功能是否启用
     * @param {string} feature - 功能名称
     * @returns {boolean} 是否启用
     */
    isFeatureEnabled(feature) {
        const featurePaths = {
            'autoSave': 'examSettings.autoSave',
            'showProgress': 'examSettings.showProgress',
            'showTimer': 'examSettings.showTimer',
            'animations': 'uiSettings.animations',
            'sounds': 'uiSettings.sounds',
            'notifications': 'notificationSettings.enableNotifications',
            'debugMode': 'advancedSettings.debugMode'
        };

        const path = featurePaths[feature];
        if (path) {
            const keys = path.split('.');
            let obj = this;
            for (const key of keys) {
                obj = obj[key];
                if (obj === undefined) return false;
            }
            return !!obj;
        }
        return false;
    }

    /**
     * 获取主题配置
     * @returns {Object} 主题配置
     */
    getThemeConfig() {
        const themes = {
            'default': {
                primaryColor: '#007bff',
                secondaryColor: '#6c757d',
                successColor: '#28a745',
                warningColor: '#ffc107',
                errorColor: '#dc3545'
            },
            'dark': {
                primaryColor: '#0d6efd',
                secondaryColor: '#6c757d',
                successColor: '#198754',
                warningColor: '#ffc107',
                errorColor: '#dc3545'
            },
            'light': {
                primaryColor: '#0066cc',
                secondaryColor: '#666666',
                successColor: '#009900',
                warningColor: '#ff9900',
                errorColor: '#cc0000'
            }
        };
        return themes[this.theme] || themes['default'];
    }

    /**
     * 导出设置
     * @returns {Object} 设置数据
     */
    exportSettings() {
        return {
            ...this.getData(),
            exportedAt: new Date().toISOString(),
            version: this.version
        };
    }

    /**
     * 导入设置
     * @param {Object} settings - 设置数据
     */
    importSettings(settings) {
        // 验证版本兼容性
        if (settings.version && settings.version !== this.version) {
            console.warn('设置版本不匹配，可能存在兼容性问题');
        }

        // 导入设置
        Object.keys(settings).forEach(key => {
            if (this.isValidProperty(key) && key !== 'id' && key !== 'createdAt') {
                this[key] = settings[key];
            }
        });

        this.touch();
    }
}

// 导出SettingsModel类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SettingsModel;
} else {
    window.SettingsModel = SettingsModel;
}
