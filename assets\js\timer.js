/**
 * 计时器模块 - 简化版本
 * 提供基本的计时功能
 */

// 全局计时器状态
window.timerState = {
    isRunning: false,
    startTime: 0,
    elapsedTime: 0,
    intervalId: null
};

/**
 * 开始计时
 */
function startTimer() {
    if (window.timerState.isRunning) {
        return;
    }
    
    window.timerState.startTime = Date.now();
    window.timerState.isRunning = true;
    
    window.timerState.intervalId = setInterval(() => {
        window.timerState.elapsedTime = Date.now() - window.timerState.startTime;
        updateTimerDisplay();
    }, 100);

    // 更新最小化按钮状态
    if (typeof window.updateMinimizedButtons === 'function') {
        window.updateMinimizedButtons();
    }
}

/**
 * 停止计时
 */
function stopTimer() {
    if (!window.timerState.isRunning) {
        return;
    }
    
    clearInterval(window.timerState.intervalId);
    window.timerState.isRunning = false;

    // 更新最小化按钮状态
    if (typeof window.updateMinimizedButtons === 'function') {
        window.updateMinimizedButtons();
    }
}

/**
 * 重置计时器
 */
function resetTimer() {
    stopTimer();
    window.timerState.elapsedTime = 0;
    updateTimerDisplay();
}

/**
 * 更新计时器显示
 */
function updateTimerDisplay() {
    const timerElement = document.getElementById('timer-display');
    if (timerElement) {
        timerElement.textContent = formatTime(window.timerState.elapsedTime);
    }
}

/**
 * 格式化时间
 */
function formatTime(milliseconds) {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * 获取当前时间
 */
function getCurrentTime() {
    return window.timerState.elapsedTime;
}

// 导出函数到全局
window.startTimer = startTimer;
window.stopTimer = stopTimer;
window.resetTimer = resetTimer;
window.formatTime = formatTime;
window.getCurrentTime = getCurrentTime;
