/**
 * 考试系统主控制器 - 协调所有其他类的工作
 * 遵循依赖倒置原则，通过依赖注入管理各个组件
 */
class ExamSystem extends BaseClass {
    constructor() {
        super('ExamSystem');
        
        // 核心组件
        this.database = null;
        this.ui = null;
        this.eventManager = null;
        this.settings = null;
        this.questionBank = null;
        
        // 业务组件
        this.currentStudent = null;
        this.examSteps = new Map();
        this.timer = null;
        this.draggableTimer = null;
        
        // 系统状态
        this.isInitialized = false;
        this.currentStepNumber = 1;
        this.isExamInProgress = false;
        this.isPaused = false;
        
        // 配置
        this.config = {
            totalSteps: 5,
            stepNames: [
                '中文自我介绍',
                '英文自我介绍', 
                '翻译题',
                '专业题',
                '综合题'
            ]
        };
    }

    /**
     * 初始化考试系统
     * @returns {Promise<boolean>} 初始化结果
     */
    async initialize() {
        try {
            this.log('开始初始化考试系统...');
            
            // 1. 初始化核心组件
            await this.initializeCoreComponents();
            
            // 2. 初始化业务组件
            await this.initializeBusinessComponents();
            
            // 3. 设置事件绑定
            this.setupEventBindings();
            
            // 4. 初始化界面
            this.initializeUI();
            
            // 5. 创建第一个学生
            await this.createNewStudent();
            
            this.isInitialized = true;
            this.initialized = true;
            this.log('考试系统初始化完成');
            
            return true;
        } catch (error) {
            this.handleError(error, '考试系统初始化');
            return false;
        }
    }

    /**
     * 初始化核心组件
     */
    async initializeCoreComponents() {
        // 初始化数据库
        this.database = new Database();
        await this.database.initialize();
        
        // 初始化设置管理器
        this.settings = new Settings(this.database);
        await this.settings.initialize();
        
        // 初始化UI管理器
        this.ui = new UI();
        await this.ui.initialize();
        
        // 初始化事件管理器
        this.eventManager = new EventManager();
        await this.eventManager.initialize();
        
        // 初始化题库
        this.questionBank = new QuestionBank();
        await this.questionBank.initialize();
        
        this.log('核心组件初始化完成');
    }

    /**
     * 初始化业务组件
     */
    async initializeBusinessComponents() {
        // 初始化计时器
        this.timer = new Timer();
        await this.timer.initialize();
        
        // 初始化可拖拽计时器
        this.draggableTimer = new DraggableTimer(this.timer);
        await this.draggableTimer.initialize();
        
        // 初始化考试步骤
        await this.initializeExamSteps();
        
        this.log('业务组件初始化完成');
    }

    /**
     * 初始化考试步骤
     */
    async initializeExamSteps() {
        const timeSettings = this.settings.get('timeSettings');
        const stepDurations = [
            timeSettings.chineseTime * 60,
            timeSettings.englishTime * 60,
            timeSettings.translationTime * 60,
            timeSettings.professionalTime * 60,
            timeSettings.professionalTime * 60  // 综合面试使用专业题时间
        ];

        for (let i = 1; i <= this.config.totalSteps; i++) {
            const step = new ExamStep(
                i,
                this.config.stepNames[i - 1],
                stepDurations[i - 1]
            );
            await step.initialize();
            this.examSteps.set(i, step);
        }

        this.log('考试步骤初始化完成');
    }

    /**
     * 设置事件绑定
     */
    setupEventBindings() {
        // 监听按钮点击事件
        this.eventManager.listen('button:click', (event) => {
            this.handleButtonClick(event.detail);
        });

        // 监听表单提交事件
        this.eventManager.listen('form:submit', (event) => {
            this.handleFormSubmit(event.detail);
        });

        // 监听输入变化事件
        this.eventManager.listen('input:change', (event) => {
            this.handleInputChange(event.detail);
        });

        // 监听快捷键事件
        this.eventManager.listen('shortcut:escape', () => {
            this.handleEscapeKey();
        });

        // 添加键盘快捷键监听
        this.setupKeyboardShortcuts();

        // 监听计时器事件
        this.timer.on('onTick', (data) => {
            this.handleTimerTick(data);
        });

        this.timer.on('onComplete', () => {
            this.handleTimerComplete();
        });

        // 监听设置变更事件
        this.settings.addListener('timeSettings', '*', () => {
            this.updateStepDurations();
            this.updateStepTitles();
            // 同时更新当前步骤的content-title
            this.updateCenterContent(this.currentStepNumber);
        });

        // 监听题目恢复事件
        this.bindQuestionRestoreListener();

        this.log('事件绑定设置完成');
    }

    /**
     * 绑定题目恢复事件监听器
     */
    bindQuestionRestoreListener() {
        document.addEventListener('questionsRestored', (event) => {
            this.handleQuestionsRestored(event.detail);
        });
    }

    /**
     * 处理题目恢复事件
     * @param {Object} detail - 题目恢复详情
     */
    handleQuestionsRestored(detail) {
        this.log(`恢复学生 ${detail.studentNumber} 的题目内容`);

        // 恢复当前步骤
        if (detail.currentStep && detail.currentStep !== this.currentStepNumber) {
            this.currentStepNumber = detail.currentStep;
            this.ui.updateStep(this.currentStepNumber, this.config.stepNames[this.currentStepNumber - 1]);
        }

        // 根据当前步骤恢复对应的题目显示
        if (detail.currentStep === 3 && detail.translationQuestionContent) {
            // 恢复翻译题显示
            this.restoreTranslationQuestion(detail.translationQuestionContent);
        } else if (detail.currentStep === 4 && detail.professionalQuestionContent) {
            // 恢复专业题显示
            this.restoreProfessionalQuestion(detail.professionalQuestionContent);
        }
    }

    /**
     * 恢复翻译题显示
     * @param {string} questionContent - 题目内容JSON字符串
     */
    restoreTranslationQuestion(questionContent) {
        try {
            const questions = JSON.parse(questionContent);
            if (questions && questions.length > 0) {
                // 直接显示恢复的题目
                this.displayTranslationQuestions(questions);
                this.log('翻译题目恢复成功');
            }
        } catch (error) {
            this.log('恢复翻译题目失败: ' + error.message, 'error');
        }
    }

    /**
     * 恢复专业题显示
     * @param {string} questionContent - 题目内容JSON字符串
     */
    restoreProfessionalQuestion(questionContent) {
        try {
            const questions = JSON.parse(questionContent);
            if (questions && questions.length > 0) {
                // 直接显示恢复的题目
                this.displayProfessionalQuestions(questions);
                this.log('专业题目恢复成功');
            }
        } catch (error) {
            this.log('恢复专业题目失败: ' + error.message, 'error');
        }
    }

    /**
     * 初始化界面
     */
    initializeUI() {
        // 更新学生编号显示
        if (this.currentStudent) {
            this.ui.updateStudentNumber(this.currentStudent.studentNumber);
        }

        // 更新步骤显示
        this.ui.updateStep(this.currentStepNumber, this.config.stepNames[0]);

        // 初始化计时器显示
        const currentStep = this.examSteps.get(this.currentStepNumber);
        if (currentStep) {
            this.ui.updateTimer(currentStep.duration, currentStep.stepName);
        }

        this.log('界面初始化完成');

        // 加载设置到界面
        this.loadSettingsToUI();
    }

    /**
     * 创建新学生
     */
    async createNewStudent() {
        this.currentStudent = new Student();
        await this.currentStudent.initialize();

        if (this.ui) {
            this.ui.updateStudentNumber(this.currentStudent.studentNumber);
        }

        // 如果恢复了现有考试，需要更新界面到正确的步骤
        if (this.currentStudent.currentStep > 1) {
            this.currentStepNumber = this.currentStudent.currentStep;
            this.ui.updateStep(this.currentStepNumber, this.config.stepNames[this.currentStepNumber - 1]);

            // 设置计时器为当前步骤的时长
            const currentStep = this.examSteps.get(this.currentStepNumber);
            if (currentStep) {
                this.timer.setDuration(currentStep.duration);
                this.ui.updateTimer(currentStep.duration, currentStep.stepName);
            }

            // 加载当前步骤的内容
            this.loadStepContent(this.currentStepNumber);

            this.log(`恢复学生 ${this.currentStudent.studentNumber} 到步骤 ${this.currentStepNumber}`);
        } else {
            this.log(`创建新学生: ${this.currentStudent.studentNumber}`);
        }
    }

    /**
     * 开始考试
     */
    startExam() {
        if (this.isExamInProgress) {
            this.ui.showNotification('考试已在进行中', 'warning');
            return false;
        }

        try {
            // 开始学生考试
            this.currentStudent.startExam();

            // 进入第一步但不开始计时
            this.startStep(1);

            this.isExamInProgress = true;
            this.ui.showNotification('考试开始，请点击"开始计时"按钮开始第一环节', 'success');
            this.log('考试开始，等待手动开始第一步计时');

            return true;
        } catch (error) {
            this.handleError(error, '开始考试');
            return false;
        }
    }

    /**
     * 开始计时器 - 根据按钮ID启动相应的步骤
     * @param {string} buttonId - 按钮ID
     */
    startTimer(buttonId) {
        console.log('ExamSystem.startTimer called with buttonId:', buttonId);

        // 根据按钮ID确定要启动的步骤
        let stepNumber = 1;
        switch (buttonId) {
            case 'start-intro-timer':
            case 'start-chinese-intro-timer':
                stepNumber = 1; // 中文自我介绍
                break;
            case 'start-english-intro-timer':
                stepNumber = 2; // 英文自我介绍
                break;
            case 'start-translation-timer':
                stepNumber = 3; // 翻译题
                break;
            case 'start-professional-timer':
                stepNumber = 4; // 专业题
                break;
            case 'start-comprehensive-timer':
                stepNumber = 5; // 综合题
                break;
            default:
                this.log(`未知的计时器按钮ID: ${buttonId}`, 'warn');
                return false;
        }

        // 如果考试还没开始，先开始考试
        if (!this.isExamInProgress) {
            this.startExam();
        } else {
            // 如果考试已经开始，确保在正确的步骤
            if (this.currentStepNumber !== stepNumber) {
                this.startStep(stepNumber);
            }
        }

        // 确保计时器完全重置后再开始
        const currentStep = this.examSteps.get(stepNumber);
        if (currentStep) {
            this.timer.stop();  // 先停止
            this.timer.setDuration(currentStep.duration);  // 重新设置时长
            this.timer.start();  // 从头开始计时

            this.log(`开始计时: 步骤 ${stepNumber} - ${this.config.stepNames[stepNumber - 1]} (${currentStep.duration}秒)`);
            this.ui.showNotification(`开始计时: ${this.config.stepNames[stepNumber - 1]}`, 'info');
        } else {
            this.log(`步骤 ${stepNumber} 不存在，无法开始计时`, 'error');
        }

        return true;
    }

    /**
     * 开始当前步骤的计时器（不切换步骤）
     */
    startCurrentStepTimer() {
        const currentStep = this.examSteps.get(this.currentStepNumber);
        if (currentStep) {
            this.timer.stop();  // 先停止
            this.timer.setDuration(currentStep.duration);  // 重新设置时长
            this.timer.start();  // 从头开始计时

            this.log(`开始计时: 步骤 ${this.currentStepNumber} - ${this.config.stepNames[this.currentStepNumber - 1]} (${currentStep.duration}秒)`);
            this.ui.showNotification(`开始计时: ${this.config.stepNames[this.currentStepNumber - 1]}`, 'info');
            return true;
        } else {
            this.log(`当前步骤 ${this.currentStepNumber} 不存在，无法开始计时`, 'error');
            return false;
        }
    }

    /**
     * 重置当前步骤的计时器
     */
    resetCurrentStepTimer() {
        const currentStep = this.examSteps.get(this.currentStepNumber);
        if (currentStep) {
            this.timer.stop();  // 停止并重置计时器
            this.timer.setDuration(currentStep.duration);  // 重新设置时长

            // 更新UI显示
            this.ui.updateTimer(currentStep.duration, currentStep.stepName);

            this.log(`计时器重置: 步骤 ${this.currentStepNumber} - ${this.config.stepNames[this.currentStepNumber - 1]} (${currentStep.duration}秒)`);
            this.ui.showNotification(`计时器已重置: ${this.config.stepNames[this.currentStepNumber - 1]}`, 'info');
            return true;
        } else {
            this.log(`当前步骤 ${this.currentStepNumber} 不存在，无法重置计时器`, 'error');
            return false;
        }
    }

    /**
     * 开始步骤
     * @param {number} stepNumber - 步骤编号
     */
    async startStep(stepNumber) {
        const step = this.examSteps.get(stepNumber);
        if (!step) {
            this.log(`步骤 ${stepNumber} 不存在`, 'error');
            return false;
        }

        try {
            // 结束当前步骤
            if (this.currentStepNumber !== stepNumber) {
                const currentStep = this.examSteps.get(this.currentStepNumber);
                if (currentStep && currentStep.isActive) {
                    currentStep.end();
                }
            }

            // 开始新步骤
            step.start();
            this.currentStepNumber = stepNumber;

            // 初始化步骤特定的显示状态
            await this.initializeStepDisplay(stepNumber);

            // 更新学生状态
            await this.currentStudent.nextStep(stepNumber);

            // 保存当前状态到数据库
            const saveResult = await this.currentStudent.saveToDatabase();
            if (!saveResult) {
                this.log(`警告：学生 ${this.currentStudent.studentNumber} 状态保存失败`, 'warn');
                // 继续执行，不阻止步骤切换
            }

            // 停止当前计时器并重置为新步骤的时长
            this.timer.stop();  // 完全停止当前计时器
            this.timer.setDuration(step.duration);  // 设置新步骤的时长
            // 不自动开始计时 - 需要手动点击按钮开始

            console.log(`步骤 ${stepNumber}: 计时器已停止并重置为 ${step.duration} 秒`);

            // 更新界面
            this.ui.updateStep(stepNumber, step.stepName);
            this.ui.updateTimer(step.duration, step.stepName);

            // 调试信息
            console.log(`步骤切换到: ${stepNumber} - ${step.stepName}`);

            // 触发步骤变化事件，通知布局管理器同步
            const event = new CustomEvent('examStepChanged', {
                detail: { stepNumber, stepName: step.stepName }
            });
            document.dispatchEvent(event);

            // 直接更新进度指示器
            if (typeof window.updateExpandedProgress === 'function') {
                console.log(`[ExamSystem] 更新进度指示器: ${stepNumber}/5`);
                window.updateExpandedProgress(stepNumber, 5);
            }

            if (typeof window.updateMinimizedSteps === 'function') {
                window.updateMinimizedSteps();
            }

            // 隐藏可拖拽计时器，只使用主计时器
            this.draggableTimer.hide();

            // 加载步骤内容
            this.loadStepContent(stepNumber);

            this.log(`进入步骤 ${stepNumber}: ${step.stepName} (等待手动开始计时)`);
            return true;
        } catch (error) {
            this.handleError(error, `开始步骤 ${stepNumber}`);
            return false;
        }
    }

    /**
     * 加载步骤内容
     * @param {number} stepNumber - 步骤编号
     */
    loadStepContent(stepNumber) {
        // 更新中间内容区域显示
        this.updateCenterContent(stepNumber);

        // 对于翻译题和专业题步骤，隐藏开始计时按钮
        if (stepNumber === 3) {
            this.ui.hideStartTimerButton('translation');
        } else if (stepNumber === 4) {
            this.ui.hideStartTimerButton('professional');
        }

        // 检查是否有已保存的题目内容需要恢复
        const shouldRestore = this.checkSavedQuestions(stepNumber);

        if (shouldRestore) {
            // 如果恢复了题目，说明已经抽取过，需要禁用抽取按钮
            if (stepNumber === 3) {
                this.ui.disableDrawButton('translation');
            } else if (stepNumber === 4) {
                this.ui.disableDrawButton('professional');
            }
        } else {
            // 如果没有保存的题目，不自动加载题目，保持空白状态
            // 用户需要手动点击"抽取题目"按钮
            this.log(`步骤${stepNumber}没有保存的题目，等待用户手动抽取`);
        }
    }

    /**
     * 检查是否有已保存的题目需要恢复
     * @param {number} stepNumber - 步骤编号
     * @returns {boolean} 是否已恢复题目
     */
    checkSavedQuestions(stepNumber) {
        if (!this.currentStudent) return false;

        if (stepNumber === 3 && this.currentStudent.translationQuestionContent) {
            // 恢复翻译题
            this.restoreTranslationQuestion(this.currentStudent.translationQuestionContent);
            return true;
        } else if (stepNumber === 4 && this.currentStudent.professionalQuestionContent) {
            // 恢复专业题
            this.restoreProfessionalQuestion(this.currentStudent.professionalQuestionContent);
            return true;
        }

        return false;
    }

    /**
     * 加载翻译题（随机选择）
     */
    async loadTranslationQuestions() {
        const questions = this.questionBank.selectRandomQuestions('translation', 1);
        if (questions.length > 0) {
            this.displayTranslationQuestions(questions);

            // 保存题目到步骤
            const step = this.examSteps.get(3);
            step.setQuestions(questions);

            // 保存题目内容到数据库
            if (this.currentStudent) {
                // 构造包含题目编号的题目信息
                const questionInfo = {
                    question_index: questions[0].question_index || questions[0].questionIndex || 1,
                    id: questions[0].id,
                    title: questions[0].title || '翻译题',
                    content: questions[0]
                };

                await this.currentStudent.saveQuestionsToDatabase({
                    translationQuestion: JSON.stringify(questionInfo),
                    translationQuestionContent: JSON.stringify(questions)
                });
            }
        }
    }

    /**
     * 根据题号加载翻译题
     * @param {number} questionNumber - 题号
     */
    async loadTranslationQuestionByNumber(questionNumber) {
        try {
            // 从数据库获取指定题号的题目
            const question = await this.questionBank.getQuestionByNumber('translation', questionNumber);

            if (question) {
                this.displayTranslationQuestions([question]);

                // 保存题目到步骤
                const step = this.examSteps.get(3);
                step.setQuestions([question]);

                // 保存题目内容到数据库
                if (this.currentStudent) {
                    const questionInfo = {
                        question_index: questionNumber,
                        id: question.id || `translation_${questionNumber}`,
                        title: question.title || '翻译题',
                        content: question
                    };

                    await this.currentStudent.saveQuestionsToDatabase({
                        translationQuestion: `题目${questionNumber}`,
                        translationQuestionContent: JSON.stringify([question])
                    });
                }

                this.log(`加载翻译题${questionNumber}号成功`);
            } else {
                throw new Error(`翻译题${questionNumber}号不存在`);
            }
        } catch (error) {
            this.handleError(error, `加载翻译题${questionNumber}号`);
        }
    }

    /**
     * 加载专业题（随机选择）
     */
    async loadProfessionalQuestions() {
        const questions = this.questionBank.selectRandomQuestions('professional', 1);
        if (questions.length > 0) {
            this.displayProfessionalQuestions(questions);

            // 保存题目到步骤
            const step = this.examSteps.get(4);
            step.setQuestions(questions);

            // 保存题目内容到数据库
            if (this.currentStudent) {
                // 构造包含题目编号的题目信息
                const questionInfo = {
                    question_index: questions[0].question_index || questions[0].questionIndex || 1,
                    id: questions[0].id,
                    title: questions[0].title || '专业题',
                    content: questions[0]
                };

                await this.currentStudent.saveQuestionsToDatabase({
                    professionalQuestion: JSON.stringify(questionInfo),
                    professionalQuestionContent: JSON.stringify(questions)
                });
            }
        }
    }

    /**
     * 根据题号加载专业题
     * @param {number} questionNumber - 题号
     */
    async loadProfessionalQuestionByNumber(questionNumber) {
        try {
            // 从数据库获取指定题号的题目
            const question = await this.questionBank.getQuestionByNumber('professional', questionNumber);

            if (question) {
                this.displayProfessionalQuestions([question]);

                // 保存题目到步骤
                const step = this.examSteps.get(4);
                step.setQuestions([question]);

                // 保存题目内容到数据库
                if (this.currentStudent) {
                    const questionInfo = {
                        question_index: questionNumber,
                        id: question.id || `professional_${questionNumber}`,
                        title: question.title || '专业题',
                        content: question
                    };

                    await this.currentStudent.saveQuestionsToDatabase({
                        professionalQuestion: `题目${questionNumber}`,
                        professionalQuestionContent: JSON.stringify([question])
                    });
                }

                this.log(`加载专业题${questionNumber}号成功`);
            } else {
                throw new Error(`专业题${questionNumber}号不存在`);
            }
        } catch (error) {
            this.handleError(error, `加载专业题${questionNumber}号`);
        }
    }

    /**
     * 显示翻译题目
     * @param {Array} questions - 题目数组
     */
    displayTranslationQuestions(questions) {
        if (questions && questions.length > 0) {
            this.ui.displayQuestion('translation', questions[0].content);
        }
    }

    /**
     * 显示专业题目
     * @param {Array} questions - 题目数组
     */
    displayProfessionalQuestions(questions) {
        if (questions && questions.length > 0) {
            this.ui.displayQuestion('professional', questions[0].content);
        }
    }

    /**
     * 抽取题目（由按钮调用）
     * @param {string} buttonId - 按钮ID
     */
    async drawQuestion(buttonId) {
        try {
            if (buttonId === 'draw-translation-question') {
                await this.drawQuestionWithAnimation('translation');
                // 抽取题目后自动开始计时（直接启动当前步骤的计时器）
                this.startCurrentStepTimer();
                // 禁用抽取按钮，隐藏开始计时按钮
                this.ui.disableDrawButton('translation');
                this.ui.hideStartTimerButton('translation');
                this.log('手动抽取翻译题目并自动开始计时');
            } else if (buttonId === 'draw-professional-question') {
                await this.drawQuestionWithAnimation('professional');
                // 抽取题目后自动开始计时（直接启动当前步骤的计时器）
                this.startCurrentStepTimer();
                // 禁用抽取按钮，隐藏开始计时按钮
                this.ui.disableDrawButton('professional');
                this.ui.hideStartTimerButton('professional');
                this.log('手动抽取专业题目并自动开始计时');
            }
        } catch (error) {
            this.handleError(error, '抽取题目');
        }
    }

    /**
     * 带动画的抽题流程
     * @param {string} type - 题目类型
     */
    async drawQuestionWithAnimation(type) {
        try {
            // 1. 获取已使用的题目列表
            const usedQuestions = await this.questionBank.getUsedQuestions(type);

            // 2. 获取实际可用的题目数量
            const actualQuestions = type === 'translation' ? this.questionBank.translationQuestions : this.questionBank.professionalQuestions;
            const maxQuestions = actualQuestions.length;

            // 3. 生成题号网格（显示所有题目状态）
            this.ui.generateQuestionGrid(type, maxQuestions, usedQuestions, true);
            this.ui.showQuestionGrid(type);

            // 4. 从实际存在的题目中选择可用的题号
            const availableNumbers = [];
            for (let i = 1; i <= maxQuestions; i++) {
                if (!usedQuestions.includes(i)) {
                    availableNumbers.push(i);
                }
            }

            if (availableNumbers.length === 0) {
                throw new Error(`所有${type}题目都已被使用`);
            }

            const selectedNumber = availableNumbers[Math.floor(Math.random() * availableNumbers.length)];

            // 4. 显示选择动画
            await this.ui.showQuestionSelection(type, selectedNumber);

            // 5. 等待一下让用户看到选中的题号
            await new Promise(resolve => setTimeout(resolve, 1500));

            // 6. 根据选中的题号加载并显示题目内容
            if (type === 'translation') {
                await this.loadTranslationQuestionByNumber(selectedNumber);
            } else if (type === 'professional') {
                await this.loadProfessionalQuestionByNumber(selectedNumber);
            }

            // 7. 更新题目状态（将选中的题号标记为已使用）
            this.updateQuestionStatus(type, selectedNumber);

            // 8. 保持题号网格显示，让用户看到更新后的状态
            this.log(`完成${type}题目抽取，选中题号: ${selectedNumber}`);
        } catch (error) {
            this.handleError(error, '抽题动画流程');
        }
    }

    /**
     * 更新题目状态
     * @param {string} type - 题目类型
     * @param {number} selectedNumber - 选中的题号
     */
    async updateQuestionStatus(type, selectedNumber) {
        try {
            // 记录题目使用状态到数据库
            await this.markQuestionAsUsed(type, selectedNumber);

            // 获取当前已使用的题目列表
            const usedQuestions = await this.questionBank.getUsedQuestions(type);

            // 添加新选中的题号（如果不在列表中）
            if (!usedQuestions.includes(selectedNumber)) {
                usedQuestions.push(selectedNumber);
            }

            // 获取实际题目数量并重新生成题号网格
            const actualQuestions = type === 'translation' ? this.questionBank.translationQuestions : this.questionBank.professionalQuestions;
            const maxQuestions = actualQuestions.length;
            this.ui.generateQuestionGrid(type, maxQuestions, usedQuestions, true);

            this.log(`更新${type}题目状态，题号${selectedNumber}已标记为已使用`);
        } catch (error) {
            this.log(`更新题目状态失败: ${error.message}`, 'error');
        }
    }

    /**
     * 标记题目为已使用
     * @param {string} type - 题目类型
     * @param {number} questionNumber - 题号
     */
    async markQuestionAsUsed(type, questionNumber) {
        try {
            const response = await fetch('/api/questions/mark-used', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    questionType: type,
                    questionNumber: questionNumber,
                    studentNumber: this.currentStudent.studentNumber
                })
            });

            const data = await response.json();
            if (data.success) {
                this.log(`题目${type}_${questionNumber}已标记为已使用`);
            } else {
                this.log(`标记题目失败: ${data.error}`, 'warn');
            }
        } catch (error) {
            this.log(`标记题目失败: ${error.message}`, 'error');
        }
    }

    /**
     * 初始化步骤显示状态
     * @param {number} stepNumber - 步骤编号
     */
    async initializeStepDisplay(stepNumber) {
        try {
            switch (stepNumber) {
                case 3: // 翻译题步骤
                    // 清空题目显示，显示空白状态
                    this.ui.clearQuestionDisplay('translation');
                    // 重置抽题按钮状态
                    this.ui.enableDrawButton('translation');
                    // 从数据库获取已使用的题目状态
                    const translationUsedQuestions = await this.questionBank.getUsedQuestions('translation');
                    this.ui.generateQuestionGrid('translation', 20, translationUsedQuestions, true);
                    this.ui.showQuestionGrid('translation');
                    this.log('初始化翻译题步骤显示 - 显示空白状态和题目网格');
                    break;

                case 4: // 专业题步骤
                    // 清空题目显示，显示空白状态
                    this.ui.clearQuestionDisplay('professional');
                    // 重置抽题按钮状态
                    this.ui.enableDrawButton('professional');
                    // 从数据库获取已使用的题目状态
                    const professionalUsedQuestions = await this.questionBank.getUsedQuestions('professional');
                    this.ui.generateQuestionGrid('professional', 20, professionalUsedQuestions, true);
                    this.ui.showQuestionGrid('professional');
                    this.log('初始化专业题步骤显示 - 显示空白状态和题目网格');
                    break;

                default:
                    // 其他步骤不需要特殊处理
                    break;
            }
        } catch (error) {
            this.log(`初始化步骤显示失败: ${error.message}`, 'error');
        }
    }

    /**
     * 下一步
     */
    async nextStep() {
        if (this.currentStepNumber >= this.config.totalSteps) {
            this.endExam();
            return;
        }

        const nextStepNumber = this.currentStepNumber + 1;
        await this.startStep(nextStepNumber);
    }

    /**
     * 上一步
     */
    previousStep() {
        if (this.currentStepNumber <= 1) {
            this.log('已经是第一步，无法回退', 'warn');
            return;
        }

        const prevStepNumber = this.currentStepNumber - 1;
        this.startStep(prevStepNumber);
        this.log(`回退到步骤 ${prevStepNumber}`);
    }

    /**
     * 跳转到指定步骤（由ButtonManager调用）
     * @param {number} stepNumber - 步骤编号
     */
    async goToStep(stepNumber) {
        if (stepNumber < 1 || stepNumber > this.config.totalSteps) {
            this.log(`无效的步骤号: ${stepNumber}`, 'error');
            return false;
        }

        // 调用startStep方法
        const result = await this.startStep(stepNumber);

        // 更新中间内容区域
        this.updateCenterContent(stepNumber);

        return result;
    }

    /**
     * 更新中间内容区域
     * @param {number} stepNumber - 步骤编号
     */
    updateCenterContent(stepNumber) {
        // 更新内容标题
        const contentTitle = document.getElementById('current-step-title');
        if (contentTitle) {
            // 从数据库设置中获取时间
            const timeSettings = this.settings.get('timeSettings');
            const stepTimes = {
                1: timeSettings?.chineseTime || 2,
                2: timeSettings?.englishTime || 2,
                3: timeSettings?.translationTime || 5,
                4: timeSettings?.professionalTime || 5,
                5: timeSettings?.comprehensiveTime || 8
            };

            const stepNames = {
                1: `中文自我介绍（${stepTimes[1]}分钟）`,
                2: `英文自我介绍（${stepTimes[2]}分钟）`,
                3: `英文翻译（${stepTimes[3]}分钟）`,
                4: `专业课问答（${stepTimes[4]}分钟）`,
                5: `综合面试（${stepTimes[5]}分钟）`
            };

            const icons = {
                1: 'bi-person-circle',
                2: 'bi-translate',
                3: 'bi-journal-text',
                4: 'bi-mortarboard',
                5: 'bi-chat-dots'
            };

            const stepName = stepNames[stepNumber] || `步骤${stepNumber}`;
            const iconClass = icons[stepNumber] || 'bi-circle';

            contentTitle.innerHTML = `<i class="${iconClass}"></i> ${stepName}`;
        }

        // 显示对应的步骤内容
        const stepContents = document.querySelectorAll('.content-body .step-content');
        this.log(`找到 ${stepContents.length} 个step-content元素`);

        stepContents.forEach((content, index) => {
            content.classList.remove('active');
            if (index + 1 === stepNumber) {
                content.classList.add('active');
                this.log(`设置step-content ${index + 1} 为active`);
            }
        });

        this.log(`中间内容区域已更新到步骤: ${stepNumber}`);
    }

    /**
     * 结束考试
     */
    async endExam() {
        try {
            // 停止计时器
            this.timer.stop();

            // 结束当前步骤
            const currentStep = this.examSteps.get(this.currentStepNumber);
            if (currentStep && currentStep.isActive) {
                currentStep.end();
            }

            // 结束学生考试
            await this.currentStudent.endExam();

            // 保存考试记录
            await this.saveExamRecord();

            // 隐藏可拖拽计时器
            this.draggableTimer.hide();

            this.isExamInProgress = false;
            this.ui.showNotification('考试结束', 'success');
            this.log('考试结束');

        } catch (error) {
            this.handleError(error, '结束考试');
        }
    }

    /**
     * 保存考试记录
     */
    async saveExamRecord() {
        try {
            const examRecord = this.currentStudent.getExamRecord();
            await this.database.saveExamRecord(examRecord);
            this.log('考试记录保存成功');
        } catch (error) {
            this.handleError(error, '保存考试记录');
        }
    }

    /**
     * 处理按钮点击
     * @param {Object} data - 事件数据
     */
    async handleButtonClick(data) {
        const { buttonId, action } = data;

        switch (buttonId) {
            case 'next-step':
                await this.nextStep();
                break;
            case 'next-student':
                await this.nextStudent();
                break;
            case 'exam-records':
                this.showExamRecords();
                break;
            case 'settings-btn':
                this.showSettings();
                break;
            default:
                if (action) {
                    this.handleAction(action, data);
                }
                break;
        }
    }

    /**
     * 处理表单提交
     * @param {Object} data - 事件数据
     */
    handleFormSubmit(data) {
        const { formId } = data;
        
        switch (formId) {
            case 'settings-form':
                this.handleSettingsSubmit(data);
                break;
            default:
                this.log(`未处理的表单提交: ${formId}`, 'warn');
                break;
        }
    }

    /**
     * 处理输入变化
     * @param {Object} data - 事件数据
     */
    handleInputChange(data) {
        // 可以在这里处理输入验证、自动保存等
        this.log(`输入变化: ${data.inputId} = ${data.value}`);
    }

    /**
     * 处理计时器滴答
     * @param {Object} data - 计时器数据
     */
    handleTimerTick(data) {
        const currentStep = this.examSteps.get(this.currentStepNumber);
        if (currentStep) {
            this.ui.updateTimer(data.remaining, currentStep.stepName);
            // 只更新主计时器，不更新可拖拽计时器
        }
    }

    /**
     * 处理计时器完成
     */
    handleTimerComplete() {
        const currentStepName = this.config.stepNames[this.currentStepNumber - 1];

        // 显示时间到的弹窗提示
        this.showTimeUpDialog(currentStepName);

        // 根据设置决定是否自动进入下一环节
        const autoNextStep = this.settings.get('examSettings', 'autoNextStep');
        if (autoNextStep) {
            // 延迟一点时间让用户看到提示，然后自动切换
            setTimeout(async () => {
                await this.nextStep();
            }, 2000);  // 2秒后自动切换
        }

        this.log(`步骤 ${this.currentStepNumber} 时间到，自动切换: ${autoNextStep}`);
    }

    /**
     * 显示时间到的弹窗提示
     * @param {string} stepName - 当前步骤名称
     */
    showTimeUpDialog(stepName) {
        // 创建自定义弹窗
        const modal = document.createElement('div');
        modal.className = 'modal fade time-up-modal';
        modal.id = 'timeUpModal';
        modal.setAttribute('tabindex', '-1');
        modal.setAttribute('aria-hidden', 'true');

        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="bi bi-clock-fill"></i> 时间到！
                        </h5>
                    </div>
                    <div class="modal-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-alarm text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <h6>${stepName} 时间已到</h6>
                        <p class="text-muted mb-0">
                            ${this.settings.get('examSettings', 'autoNextStep')
                                ? '系统将在2秒后自动进入下一环节'
                                : '请手动切换到下一环节'}
                        </p>
                    </div>
                    <div class="modal-footer justify-content-center">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                            <i class="bi bi-check-lg"></i> 知道了
                        </button>
                        ${!this.settings.get('examSettings', 'autoNextStep')
                            ? '<button type="button" class="btn btn-success" onclick="(async () => { await window.examSystem.nextStep(); bootstrap.Modal.getInstance(document.getElementById(\'timeUpModal\')).hide(); })();"><i class="bi bi-arrow-right"></i> 下一环节</button>'
                            : ''}
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(modal);

        // 显示弹窗
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // 弹窗关闭后移除元素
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

        // 播放提示音（如果浏览器支持）
        this.playNotificationSound();
    }

    /**
     * 播放提示音
     */
    playNotificationSound() {
        try {
            // 创建音频上下文播放提示音
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            // 如果音频播放失败，静默处理
            this.log('无法播放提示音', 'warn');
        }
    }

    /**
     * 处理ESC键
     */
    handleEscapeKey() {
        // 可以用于暂停考试或显示帮助
        if (this.isExamInProgress) {
            this.pauseExam();
        }
    }

    /**
     * 设置键盘快捷键
     */
    setupKeyboardShortcuts() {
        console.log('Setting up keyboard shortcuts...'); // 调试信息

        document.addEventListener('keydown', (event) => {
            console.log('Key pressed:', event.key, 'Ctrl:', event.ctrlKey); // 调试所有按键

            // 只在按下 Ctrl 键时处理快捷键
            if (event.ctrlKey) {
                console.log('Ctrl key pressed with:', event.key); // 调试信息
                switch (event.key) {
                    case 'ArrowLeft':
                        console.log('Ctrl+ArrowLeft detected, calling handlePrevStep'); // 调试信息
                        event.preventDefault();
                        this.handlePrevStep();
                        break;
                    case 'ArrowRight':
                        console.log('Ctrl+ArrowRight detected, calling handleNextStep'); // 调试信息
                        event.preventDefault();
                        this.handleNextStep();
                        break;
                }
            }
        });

        this.log('键盘快捷键设置完成');
        console.log('Keyboard shortcuts setup completed'); // 调试信息
    }

    /**
     * 处理上一步快捷键
     */
    handlePrevStep() {
        if (this.currentStepNumber > 1) {
            this.previousStep();
            this.log('快捷键: 上一步');
        }
    }

    /**
     * 处理下一步快捷键
     */
    async handleNextStep() {
        const totalSteps = this.examSteps.size;
        if (this.currentStepNumber < totalSteps) {
            await this.nextStep();
            this.log('快捷键: 下一步');
        }
    }

    /**
     * 暂停考试
     */
    pauseExam() {
        if (this.isPaused) {
            this.resumeExam();
        } else {
            this.timer.pause();
            this.isPaused = true;
            this.ui.showNotification('考试已暂停', 'info');
            this.log('考试暂停');
        }
    }

    /**
     * 恢复考试
     */
    resumeExam() {
        this.timer.resume();
        this.isPaused = false;
        this.ui.showNotification('考试已恢复', 'info');
        this.log('考试恢复');
    }

    /**
     * 下一个学生
     */
    async nextStudent() {
        // 显示确认对话框
        const confirmed = await this.ui.showConfirmDialog(
            '切换考生确认',
            '确定要切换到下一名考生吗？当前考生的考试将被结束。',
            'warning'
        );

        if (!confirmed) {
            this.log('用户取消切换到下一位学生');
            return;
        }

        // 强制结束当前考试（无论isExamInProgress状态如何）
        if (this.currentStudent) {
            await this.endExam();
        }

        // 重置题库状态（清空已使用题目记录）
        if (this.questionBank) {
            this.questionBank.resetUsedQuestions();
        }

        await this.createNewStudent();
        this.currentStepNumber = 1;

        // 重置界面
        this.ui.updateStep(1, this.config.stepNames[0]);
        const firstStep = this.examSteps.get(1);
        if (firstStep) {
            this.ui.updateTimer(firstStep.duration, firstStep.stepName);
        }

        // 清空所有题目显示并重置按钮状态
        this.ui.clearQuestionDisplay('translation');
        this.ui.clearQuestionDisplay('professional');
        this.ui.enableDrawButton('translation');
        this.ui.enableDrawButton('professional');

        this.ui.showNotification('准备下一位学生', 'info');
        this.log('切换到下一位学生');
    }

    /**
     * 重置考试状态
     */
    async resetExam() {
        try {
            // 显示确认对话框
            const confirmed = await this.ui.showConfirmDialog(
                '重置考试状态',
                '确定要重置所有考试状态吗？这将清空所有考试记录和学生数据，此操作不可撤销！',
                'danger'
            );

            if (!confirmed) {
                return;
            }

            this.log('开始重置考试状态...');

            // 1. 停止当前考试
            if (this.isExamInProgress) {
                this.timer.stop();
                this.isExamInProgress = false;
            }

            // 2. 重置所有考试步骤
            this.examSteps.forEach(step => {
                if (step.isActive) {
                    step.end();
                }
                step.reset();
            });

            // 3. 清空数据库中的考试记录（使用现有的API）
            const response = await fetch('/api/exams/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || '清空考试记录失败');
            }

            // 5. 重置当前学生
            this.currentStudent = null;

            // 6. 重置系统状态
            this.currentStepNumber = 1;
            this.isExamInProgress = false;
            this.isPaused = false;

            // 7. 重置界面
            this.ui.updateStep(1, this.config.stepNames[0]);
            const firstStep = this.examSteps.get(1);
            if (firstStep) {
                this.timer.setDuration(firstStep.duration);
                this.ui.updateTimer(firstStep.duration, firstStep.stepName);
            }

            // 8. 清空题目显示
            this.ui.clearQuestionDisplay('translation');
            this.ui.clearQuestionDisplay('professional');

            // 9. 重置题目选择状态
            if (this.questionBank) {
                this.questionBank.resetAllQuestionStates();
                this.log('已重置所有题目状态');
            }

            // 10. 清空本地存储的题目状态
            localStorage.removeItem('examSystem_questionBank');
            localStorage.removeItem('examSystem_usedQuestions');
            localStorage.removeItem('examSystem_selectedQuestions');

            // 11. 隐藏可拖拽计时器
            this.draggableTimer.hide();

            // 12. 重新创建新学生（确保从1号开始）
            await this.createNewStudent();

            this.ui.showNotification('考试状态已重置，考生序号从1号开始', 'success');
            this.log('考试状态重置完成，考生序号从1号开始');

        } catch (error) {
            this.handleError(error, '重置考试状态');
            this.ui.showNotification('重置失败，请重试', 'error');
        }
    }

    /**
     * 显示考试记录
     */
    async showExamRecords() {
        try {
            // 显示加载状态
            this.ui.showNotification('正在加载考试记录...', 'info');

            // 从数据库获取考试记录
            const records = await this.database.getExamRecords();

            this.log(`获取到 ${records.length} 条考试记录`);

            // 更新记录内容到模态框
            this.updateExamRecordsContent(records);

            // 直接使用Bootstrap模态框显示
            const modalElement = document.getElementById('examRecordModal');
            if (modalElement && window.bootstrap) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
                this.log('显示考试记录');
            } else {
                this.log('考试记录模态框不存在或Bootstrap未加载', 'error');
            }
        } catch (error) {
            this.handleError(error, '显示考试记录');
            this.ui.showNotification('加载考试记录失败', 'error');
        }
    }

    /**
     * 更新考试记录内容
     * @param {Array} records - 考试记录数组
     */
    updateExamRecordsContent(records) {
        const contentElement = document.getElementById('examRecordContent');
        const statsElement = document.querySelector('.modal-body .bg-light');

        if (!contentElement) {
            this.log('考试记录内容容器不存在', 'warn');
            return;
        }

        if (!records || records.length === 0) {
            contentElement.innerHTML = '<p class="text-muted text-center py-4">暂无考试记录</p>';
            this.updateRecordStats(0, 0, 0, 0);
            return;
        }

        // 计算统计信息
        const totalStudents = records.length;
        const completedStudents = records.filter(r => r.status === '已完成').length;
        const inProgressStudents = records.filter(r => r.status === '进行中' || r.status === '考试中').length;

        // 计算平均用时（只计算已完成的）
        const completedRecords = records.filter(r => r.status === '已完成' && r.examDate);
        let averageTime = 0;
        if (completedRecords.length > 0) {
            // 这里简化处理，假设每个考试大约30分钟
            averageTime = 30; // 可以根据实际需要计算
        }

        // 更新统计信息
        this.updateRecordStats(totalStudents, completedStudents, inProgressStudents, averageTime);

        // 生成记录列表HTML
        const recordsHtml = records.map(record => {
            const isCompleted = record.status === '已完成';
            const statusClass = isCompleted ? 'bg-success' : 'bg-warning';
            const statusText = record.status || '未知';
            const examDate = record.examDate ? new Date(record.examDate).toLocaleString() : '未知';
            const currentStep = record.currentStep || 1;

            return `
                <div class="card mb-3 border-0 shadow-sm">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-1 d-flex align-items-center">
                                    <i class="bi bi-person-circle me-2 text-primary"></i>
                                    学生编号: ${record.studentNumber || '未知'}
                                </h6>
                                <p class="card-text small text-muted mb-1">
                                    <i class="bi bi-calendar3 me-1"></i>
                                    考试时间: ${examDate}
                                </p>
                                <p class="card-text small text-muted mb-1">
                                    <i class="bi bi-list-ol me-1"></i>
                                    当前步骤: 第${currentStep}步
                                </p>
                                ${record.translationQuestion ? `
                                    <p class="card-text small text-muted mb-1">
                                        <i class="bi bi-translate me-1"></i>
                                        翻译题: ${record.translationQuestion}
                                    </p>
                                ` : ''}
                                ${record.professionalQuestion ? `
                                    <p class="card-text small text-muted mb-0">
                                        <i class="bi bi-mortarboard me-1"></i>
                                        专业题: ${record.professionalQuestion}
                                    </p>
                                ` : ''}
                            </div>
                            <span class="badge ${statusClass} px-3 py-2">
                                ${statusText}
                            </span>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        contentElement.innerHTML = recordsHtml;
    }

    /**
     * 更新考试记录统计信息
     */
    updateRecordStats(total, completed, inProgress, averageTime) {
        // 更新总考生数
        const totalElement = document.querySelector('.modal-body .bg-light .col-md-3:nth-child(1) .h4');
        if (totalElement) totalElement.textContent = total;

        // 更新已完成数
        const completedElement = document.querySelector('.modal-body .bg-light .col-md-3:nth-child(2) .h4');
        if (completedElement) completedElement.textContent = completed;

        // 更新进行中数
        const inProgressElement = document.querySelector('.modal-body .bg-light .col-md-3:nth-child(3) .h4');
        if (inProgressElement) inProgressElement.textContent = inProgress;

        // 更新平均用时
        const averageElement = document.querySelector('.modal-body .bg-light .col-md-3:nth-child(4) .h4');
        if (averageElement) averageElement.textContent = `${averageTime}分钟`;
    }

    /**
     * 显示设置
     */
    showSettings() {
        this.ui.showModal('settingsModal');
        this.log('显示设置');
    }

    /**
     * 更新步骤时长
     */
    async updateStepDurations() {
        const timeSettings = this.settings.get('timeSettings');
        const stepDurations = [
            (timeSettings.chineseTime || timeSettings.chineseIntro || 2) * 60,
            (timeSettings.englishTime || timeSettings.englishIntro || 2) * 60,
            (timeSettings.translationTime || timeSettings.translation || 5) * 60,
            (timeSettings.professionalTime || timeSettings.professional || 5) * 60,
            (timeSettings.comprehensiveTime || timeSettings.comprehensive || 8) * 60
        ];

        this.examSteps.forEach((step, stepNumber) => {
            step.duration = stepDurations[stepNumber - 1];
        });

        // 如果当前步骤的计时器还没有开始运行，更新计时器时长
        if (this.timer && !this.timer.isRunning) {
            const currentStep = this.examSteps.get(this.currentStepNumber);
            if (currentStep) {
                this.timer.setDuration(currentStep.duration);

                // 更新界面显示
                if (this.ui) {
                    this.ui.updateTimer(currentStep.duration, currentStep.stepName);
                }

                this.log(`当前步骤计时器已更新为 ${currentStep.duration} 秒`);
            }
        }

        this.log('步骤时长已更新');
    }

    /**
     * 获取系统状态
     * @returns {Object} 系统状态对象
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isExamInProgress: this.isExamInProgress,
            isPaused: this.isPaused,
            currentStepNumber: this.currentStepNumber,
            currentStudent: this.currentStudent ? this.currentStudent.getStatus() : null,
            timer: this.timer ? this.timer.getStatus() : null
        };
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            // 获取时间设置
            const timeSettings = {
                chineseTime: parseFloat(document.getElementById('chineseTime')?.value) || 1,
                englishTime: parseFloat(document.getElementById('englishIntroTime')?.value) || 1,
                translationTime: parseFloat(document.getElementById('translationTime')?.value) || 4,
                professionalTime: parseFloat(document.getElementById('professionalTime')?.value) || 5
            };

            // 获取系统行为设置
            const examSettings = {
                autoNextStep: document.getElementById('autoNextStep')?.checked ?? true
            };

            // 保存到设置管理器
            await this.settings.setMultiple('timeSettings', timeSettings);
            await this.settings.setMultiple('examSettings', examSettings);

            // 更新步骤时长
            this.updateStepDurations();

            // 更新步骤标题
            this.updateStepTitles();

            // 关闭设置模态框
            const settingsModal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
            if (settingsModal) {
                settingsModal.hide();
            }

            // 显示成功提示
            this.ui.showNotification('设置保存成功', 'success');
            this.log('设置保存成功', 'info');

        } catch (error) {
            this.handleError(error, '保存设置');
            this.ui.showNotification('设置保存失败', 'error');
        }
    }

    /**
     * 更新步骤标题中的时间显示
     */
    updateStepTitles() {
        try {
            const timeSettings = this.settings.get('timeSettings');
            if (!timeSettings) return;

            // 更新各步骤标题中的时间显示
            const titleUpdates = [
                { id: 'step1-title', icon: 'bi-person-circle', name: '中文自我介绍', time: timeSettings.chineseTime },
                { id: 'step2-title', icon: 'bi-translate', name: '英文自我介绍', time: timeSettings.englishTime },
                { id: 'step3-title', icon: 'bi-journal-text', name: '英文翻译', time: timeSettings.translationTime },
                { id: 'step4-title', icon: 'bi-mortarboard', name: '专业课问答', time: timeSettings.professionalTime }
            ];

            titleUpdates.forEach(update => {
                const titleElement = document.getElementById(update.id);
                if (titleElement) {
                    // 格式化时间显示（如果是整数则不显示小数点）
                    const timeText = update.time % 1 === 0 ? update.time.toString() : update.time.toString();
                    titleElement.innerHTML = `<i class="bi ${update.icon}"></i> ${update.name}（${timeText}分钟）`;
                }
            });

            // 同时更新step-item中的时间显示
            this.updateStepItemTimes();

            this.log('步骤标题已更新');
        } catch (error) {
            this.handleError(error, '更新步骤标题');
        }
    }

    /**
     * 更新step-item中的时间显示
     */
    updateStepItemTimes() {
        try {
            const timeSettings = this.settings.get('timeSettings');
            if (!timeSettings) return;

            // 定义步骤时间映射
            const stepTimes = [
                timeSettings.chineseTime || 2,
                timeSettings.englishTime || 2,
                timeSettings.translationTime || 5,
                timeSettings.professionalTime || 5,
                timeSettings.comprehensiveTime || 8
            ];

            // 更新每个step-item的时间显示
            for (let i = 1; i <= 5; i++) {
                const stepItem = document.querySelector(`.step-item[data-step="${i}"]`);
                if (stepItem) {
                    const durationSpan = stepItem.querySelector('.step-duration span');
                    if (durationSpan) {
                        const timeText = stepTimes[i - 1] % 1 === 0 ? stepTimes[i - 1].toString() : stepTimes[i - 1].toString();
                        durationSpan.textContent = `${timeText}分钟`;
                    }
                }

                // 同时更新最小化状态的时间显示
                const minimizedStep = document.querySelector(`.minimized-step[data-step="${i}"]`);
                if (minimizedStep) {
                    const timeElement = minimizedStep.querySelector('.minimized-step-time');
                    if (timeElement) {
                        const timeText = stepTimes[i - 1] % 1 === 0 ? stepTimes[i - 1].toString() : stepTimes[i - 1].toString();
                        timeElement.textContent = `${timeText}分钟`;
                    }
                }
            }

            this.log('Step-item时间显示已更新');
        } catch (error) {
            this.handleError(error, '更新Step-item时间');
        }
    }

    /**
     * 加载设置到界面
     */
    loadSettingsToUI() {
        try {
            // 加载时间设置
            const timeSettings = this.settings.get('timeSettings');
            if (timeSettings) {
                document.getElementById('chineseTime').value = timeSettings.chineseTime || 1;
                document.getElementById('englishIntroTime').value = timeSettings.englishTime || 1;
                document.getElementById('translationTime').value = timeSettings.translationTime || 4;
                document.getElementById('professionalTime').value = timeSettings.professionalTime || 5;

                // 更新步骤标题
                this.updateStepTitles();
            }

            // 加载系统行为设置
            const examSettings = this.settings.get('examSettings');
            if (examSettings) {
                const autoNextStepCheckbox = document.getElementById('autoNextStep');
                if (autoNextStepCheckbox) {
                    autoNextStepCheckbox.checked = examSettings.autoNextStep ?? true;
                }
            }

            this.log('设置已加载到界面');
        } catch (error) {
            this.handleError(error, '加载设置到界面');
        }
    }

    /**
     * 销毁考试系统
     */
    destroy() {
        // 停止考试
        if (this.isExamInProgress) {
            this.endExam();
        }

        // 销毁所有组件
        if (this.draggableTimer) this.draggableTimer.destroy();
        if (this.timer) this.timer.destroy();
        if (this.eventManager) this.eventManager.destroy();
        if (this.ui) this.ui.destroy();
        if (this.settings) this.settings.destroy();
        // Database 类没有 destroy 方法，跳过
        // if (this.database) this.database.destroy();

        // 清理步骤
        this.examSteps.forEach(step => step.destroy());
        this.examSteps.clear();

        super.destroy();
        this.log('考试系统已销毁');
    }
}

// 导出到全局作用域
window.ExamSystem = ExamSystem;
