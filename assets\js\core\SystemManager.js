/**
 * SystemManager - 系统集成管理器
 * 负责协调所有组件的初始化、生命周期管理和系统级操作
 */

class SystemManager {
    constructor() {
        this.isInitialized = false;
        this.isStarted = false;
        this.components = new Map();
        this.managers = new Map();
        this.eventBus = null;
        this.config = null;
        
        // 初始化顺序
        this.initializationOrder = [
            'ConfigManager',
            'EventBus',
            'ServiceManager', 
            'ControllerManager',
            'ViewManager'
        ];
        
        // 组件状态
        this.componentStates = new Map();
        
        // 错误收集
        this.errors = [];
        this.warnings = [];
    }

    /**
     * 初始化系统
     * @param {Object} config - 系统配置
     * @returns {Promise<void>}
     */
    async initialize(config = {}) {
        if (this.isInitialized) {
            console.warn('SystemManager 已经初始化');
            return;
        }

        try {
            console.log('🚀 开始初始化系统...');
            
            // 1. 初始化配置管理器
            await this.initializeConfigManager(config);
            
            // 2. 初始化事件总线
            await this.initializeEventBus();
            
            // 3. 按顺序初始化各个管理器
            await this.initializeManagers();
            
            // 4. 设置全局错误处理
            this.setupGlobalErrorHandling();
            
            // 5. 设置系统监控
            this.setupSystemMonitoring();
            
            this.isInitialized = true;
            console.log('✅ 系统初始化完成');
            
            // 触发初始化完成事件
            this.eventBus.emit('system.initialized', {
                timestamp: new Date().toISOString(),
                components: Array.from(this.components.keys()),
                managers: Array.from(this.managers.keys())
            });
            
        } catch (error) {
            console.error('❌ 系统初始化失败:', error);
            this.errors.push({
                type: 'initialization',
                error,
                timestamp: new Date().toISOString()
            });
            throw error;
        }
    }

    /**
     * 启动系统
     * @returns {Promise<void>}
     */
    async start() {
        if (!this.isInitialized) {
            throw new Error('系统未初始化，请先调用 initialize()');
        }

        if (this.isStarted) {
            console.warn('系统已经启动');
            return;
        }

        try {
            console.log('🎯 启动系统...');
            
            // 1. 启动所有管理器
            await this.startManagers();
            
            // 2. 执行系统自检
            await this.performSystemCheck();
            
            // 3. 加载用户设置
            await this.loadUserSettings();
            
            // 4. 恢复系统状态
            await this.restoreSystemState();
            
            this.isStarted = true;
            console.log('✅ 系统启动完成');
            
            // 触发启动完成事件
            this.eventBus.emit('system.started', {
                timestamp: new Date().toISOString(),
                healthStatus: this.getHealthStatus()
            });
            
        } catch (error) {
            console.error('❌ 系统启动失败:', error);
            this.errors.push({
                type: 'startup',
                error,
                timestamp: new Date().toISOString()
            });
            throw error;
        }
    }

    /**
     * 停止系统
     * @returns {Promise<void>}
     */
    async stop() {
        if (!this.isStarted) {
            return;
        }

        try {
            console.log('🛑 停止系统...');
            
            // 1. 保存系统状态
            await this.saveSystemState();
            
            // 2. 停止所有管理器
            await this.stopManagers();
            
            // 3. 清理资源
            this.cleanup();
            
            this.isStarted = false;
            console.log('✅ 系统已停止');
            
        } catch (error) {
            console.error('❌ 系统停止失败:', error);
            this.errors.push({
                type: 'shutdown',
                error,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 重启系统
     * @returns {Promise<void>}
     */
    async restart() {
        console.log('🔄 重启系统...');
        await this.stop();
        await this.start();
    }

    // ==================== 管理器初始化 ====================

    /**
     * 初始化配置管理器
     * @param {Object} config - 配置对象
     */
    async initializeConfigManager(config) {
        if (window.ConfigManager) {
            this.config = new ConfigManager(config);
            await this.config.initialize();
            this.managers.set('ConfigManager', this.config);
            console.log('✅ ConfigManager 初始化完成');
        } else {
            console.warn('⚠️ ConfigManager 未找到，使用默认配置');
            this.config = { get: (key, defaultValue) => defaultValue };
        }
    }

    /**
     * 初始化事件总线
     */
    async initializeEventBus() {
        if (window.eventBus) {
            this.eventBus = window.eventBus;
            this.managers.set('EventBus', this.eventBus);
            console.log('✅ EventBus 初始化完成');
        } else {
            throw new Error('EventBus 未找到');
        }
    }

    /**
     * 初始化各个管理器
     */
    async initializeManagers() {
        // 初始化服务管理器
        if (window.serviceManager) {
            await window.serviceManager.initialize(this.eventBus);
            this.managers.set('ServiceManager', window.serviceManager);
            console.log('✅ ServiceManager 初始化完成');
        }

        // 初始化控制器管理器
        if (window.controllerManager) {
            await window.controllerManager.initialize(this.eventBus);
            this.managers.set('ControllerManager', window.controllerManager);
            console.log('✅ ControllerManager 初始化完成');
        }

        // 初始化视图管理器
        if (window.ViewManager) {
            const viewManager = new ViewManager();
            await viewManager.initialize(this.eventBus);
            this.managers.set('ViewManager', viewManager);
            window.viewManager = viewManager;
            console.log('✅ ViewManager 初始化完成');
        }
    }

    /**
     * 启动所有管理器
     */
    async startManagers() {
        for (const [name, manager] of this.managers) {
            if (manager && typeof manager.start === 'function') {
                try {
                    await manager.start();
                    this.componentStates.set(name, 'running');
                    console.log(`✅ ${name} 启动完成`);
                } catch (error) {
                    console.error(`❌ ${name} 启动失败:`, error);
                    this.componentStates.set(name, 'error');
                    this.errors.push({
                        type: 'manager_start',
                        manager: name,
                        error,
                        timestamp: new Date().toISOString()
                    });
                }
            } else {
                this.componentStates.set(name, 'initialized');
            }
        }
    }

    /**
     * 停止所有管理器
     */
    async stopManagers() {
        // 按相反顺序停止
        const managerNames = Array.from(this.managers.keys()).reverse();
        
        for (const name of managerNames) {
            const manager = this.managers.get(name);
            if (manager && typeof manager.stop === 'function') {
                try {
                    await manager.stop();
                    this.componentStates.set(name, 'stopped');
                    console.log(`✅ ${name} 停止完成`);
                } catch (error) {
                    console.error(`❌ ${name} 停止失败:`, error);
                    this.componentStates.set(name, 'error');
                }
            }
        }
    }

    // ==================== 系统监控 ====================

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // 监听未捕获的错误
        window.addEventListener('error', (event) => {
            this.handleGlobalError('javascript', event.error, {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });

        // 监听未捕获的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleGlobalError('promise', event.reason);
        });

        // 监听系统级错误事件
        this.eventBus.on('*.error', (data) => {
            this.handleSystemError(data);
        });
    }

    /**
     * 设置系统监控
     */
    setupSystemMonitoring() {
        // 定期检查系统健康状态
        setInterval(() => {
            this.performHealthCheck();
        }, 30000); // 30秒检查一次

        // 监听性能指标
        if (window.performance && window.performance.observer) {
            this.setupPerformanceMonitoring();
        }
    }

    /**
     * 执行系统自检
     */
    async performSystemCheck() {
        const checks = [
            this.checkManagersHealth(),
            this.checkEventBusHealth(),
            this.checkDatabaseConnection(),
            this.checkAPIConnection()
        ];

        const results = await Promise.allSettled(checks);
        
        results.forEach((result, index) => {
            if (result.status === 'rejected') {
                this.warnings.push({
                    type: 'system_check',
                    check: ['managers', 'eventbus', 'database', 'api'][index],
                    error: result.reason,
                    timestamp: new Date().toISOString()
                });
            }
        });
    }

    /**
     * 执行健康检查
     */
    performHealthCheck() {
        const healthStatus = this.getHealthStatus();
        
        if (!healthStatus.isHealthy) {
            console.warn('⚠️ 系统健康检查发现问题:', healthStatus.issues);
            
            this.eventBus.emit('system.healthWarning', {
                status: healthStatus,
                timestamp: new Date().toISOString()
            });
        }
    }

    // ==================== 状态管理 ====================

    /**
     * 加载用户设置
     */
    async loadUserSettings() {
        try {
            const settingsService = this.getService('SettingsService');
            if (settingsService) {
                const settings = await settingsService.getSettings();
                this.eventBus.emit('system.settingsLoaded', settings);
            }
        } catch (error) {
            console.warn('⚠️ 加载用户设置失败:', error);
        }
    }

    /**
     * 恢复系统状态
     */
    async restoreSystemState() {
        try {
            const savedState = localStorage.getItem('interview_system_state');
            if (savedState) {
                const state = JSON.parse(savedState);
                this.eventBus.emit('system.stateRestored', state);
            }
        } catch (error) {
            console.warn('⚠️ 恢复系统状态失败:', error);
        }
    }

    /**
     * 保存系统状态
     */
    async saveSystemState() {
        try {
            const state = {
                timestamp: new Date().toISOString(),
                managers: Object.fromEntries(this.componentStates),
                errors: this.errors.slice(-10), // 保存最近10个错误
                warnings: this.warnings.slice(-10)
            };
            
            localStorage.setItem('interview_system_state', JSON.stringify(state));
        } catch (error) {
            console.warn('⚠️ 保存系统状态失败:', error);
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 获取管理器
     * @param {string} name - 管理器名称
     * @returns {Object} 管理器实例
     */
    getManager(name) {
        return this.managers.get(name);
    }

    /**
     * 获取服务
     * @param {string} name - 服务名称
     * @returns {Object} 服务实例
     */
    getService(name) {
        const serviceManager = this.getManager('ServiceManager');
        return serviceManager ? serviceManager.getService(name) : null;
    }

    /**
     * 获取控制器
     * @param {string} name - 控制器名称
     * @returns {Object} 控制器实例
     */
    getController(name) {
        const controllerManager = this.getManager('ControllerManager');
        return controllerManager ? controllerManager.getController(name) : null;
    }

    /**
     * 获取视图
     * @param {string} name - 视图名称
     * @returns {Object} 视图实例
     */
    getView(name) {
        const viewManager = this.getManager('ViewManager');
        return viewManager ? viewManager.getView(name) : null;
    }

    /**
     * 获取健康状态
     * @returns {Object} 健康状态
     */
    getHealthStatus() {
        const issues = [];
        let isHealthy = true;

        // 检查管理器状态
        for (const [name, state] of this.componentStates) {
            if (state === 'error') {
                issues.push(`${name} 处于错误状态`);
                isHealthy = false;
            }
        }

        // 检查错误数量
        const recentErrors = this.errors.filter(error => {
            const errorTime = new Date(error.timestamp);
            const now = new Date();
            return (now - errorTime) < 300000; // 5分钟内的错误
        });

        if (recentErrors.length > 5) {
            issues.push(`最近5分钟内发生了${recentErrors.length}个错误`);
            isHealthy = false;
        }

        return {
            isHealthy,
            issues,
            componentStates: Object.fromEntries(this.componentStates),
            errorCount: this.errors.length,
            warningCount: this.warnings.length,
            uptime: this.isStarted ? Date.now() - this.startTime : 0
        };
    }

    /**
     * 处理全局错误
     */
    handleGlobalError(type, error, context = {}) {
        console.error(`🚨 全局${type}错误:`, error);
        
        this.errors.push({
            type: `global_${type}`,
            error: error.toString(),
            context,
            timestamp: new Date().toISOString()
        });

        this.eventBus.emit('system.globalError', {
            type,
            error,
            context,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 处理系统错误
     */
    handleSystemError(data) {
        this.errors.push({
            type: 'system',
            ...data,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 检查管理器健康状态
     */
    async checkManagersHealth() {
        for (const [name, manager] of this.managers) {
            if (manager && typeof manager.isHealthy === 'function') {
                if (!manager.isHealthy()) {
                    throw new Error(`${name} 不健康`);
                }
            }
        }
    }

    /**
     * 检查事件总线健康状态
     */
    async checkEventBusHealth() {
        if (!this.eventBus || typeof this.eventBus.emit !== 'function') {
            throw new Error('EventBus 不可用');
        }
    }

    /**
     * 检查数据库连接
     */
    async checkDatabaseConnection() {
        // 这里可以添加数据库连接检查逻辑
        return true;
    }

    /**
     * 检查API连接
     */
    async checkAPIConnection() {
        try {
            const response = await fetch('/api/health');
            if (!response.ok) {
                throw new Error(`API健康检查失败: ${response.status}`);
            }
        } catch (error) {
            throw new Error(`API连接失败: ${error.message}`);
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.components.clear();
        this.componentStates.clear();
        this.errors = [];
        this.warnings = [];
    }

    /**
     * 销毁系统管理器
     */
    destroy() {
        this.stop().then(() => {
            this.cleanup();
            this.isInitialized = false;
            console.log('✅ SystemManager 已销毁');
        });
    }
}

// 创建全局系统管理器实例
const systemManager = new SystemManager();

// 导出SystemManager类和实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SystemManager, systemManager };
} else {
    window.SystemManager = SystemManager;
    window.systemManager = systemManager;
}
