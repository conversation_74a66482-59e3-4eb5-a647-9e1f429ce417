/**
 * BaseModel - 数据模型基类
 * 提供所有数据模型的通用功能和接口
 */

class BaseModel {
    constructor(data = {}) {
        this.id = data.id || null;
        this.createdAt = data.createdAt || new Date().toISOString();
        this.updatedAt = data.updatedAt || new Date().toISOString();
        
        // 设置模型数据
        this.setData(data);
    }

    /**
     * 设置模型数据
     * @param {Object} data - 数据对象
     */
    setData(data) {
        Object.keys(data).forEach(key => {
            if (this.hasOwnProperty(key) || this.isValidProperty(key)) {
                this[key] = data[key];
            }
        });
    }

    /**
     * 获取模型数据
     * @returns {Object} 数据对象
     */
    getData() {
        const data = {};
        Object.keys(this).forEach(key => {
            if (!key.startsWith('_') && typeof this[key] !== 'function') {
                data[key] = this[key];
            }
        });
        return data;
    }

    /**
     * 验证属性是否有效
     * @param {string} property - 属性名
     * @returns {boolean} 是否有效
     */
    isValidProperty(property) {
        // 子类可以重写此方法
        return true;
    }

    /**
     * 验证模型数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];
        
        // 基础验证
        if (this.id && typeof this.id !== 'string' && typeof this.id !== 'number') {
            errors.push('ID必须是字符串或数字');
        }

        // 子类可以重写此方法添加更多验证
        const customValidation = this.customValidate();
        if (customValidation.errors.length > 0) {
            errors.push(...customValidation.errors);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 自定义验证（子类重写）
     * @returns {Object} 验证结果
     */
    customValidate() {
        return { errors: [] };
    }

    /**
     * 转换为JSON
     * @returns {string} JSON字符串
     */
    toJSON() {
        return JSON.stringify(this.getData());
    }

    /**
     * 从JSON创建模型实例
     * @param {string} json - JSON字符串
     * @returns {BaseModel} 模型实例
     */
    static fromJSON(json) {
        try {
            const data = JSON.parse(json);
            return new this(data);
        } catch (error) {
            throw new Error('无效的JSON数据');
        }
    }

    /**
     * 克隆模型
     * @returns {BaseModel} 克隆的模型实例
     */
    clone() {
        return new this.constructor(this.getData());
    }

    /**
     * 更新时间戳
     */
    touch() {
        this.updatedAt = new Date().toISOString();
    }

    /**
     * 比较两个模型是否相等
     * @param {BaseModel} other - 另一个模型
     * @returns {boolean} 是否相等
     */
    equals(other) {
        if (!other || !(other instanceof BaseModel)) {
            return false;
        }

        const thisData = this.getData();
        const otherData = other.getData();

        return JSON.stringify(thisData) === JSON.stringify(otherData);
    }

    /**
     * 获取模型的字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return `${this.constructor.name}(${this.id || 'new'})`;
    }
}

// 导出BaseModel类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BaseModel;
} else {
    window.BaseModel = BaseModel;
}
