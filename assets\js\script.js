/*
 * 研究生复试流程控制系统
 * 版权所有© 2025 王文通
 * 联系方式：<EMAIL>
 */

// 全局设置和状态管理
const settings = {
    chineseTime: 1,
    englishTime: 1,
    translationTime: 4,
    professionalTime: 5
};

// 状态管理
const state = {
    currentStudent: 1,
    timer: null,
    isTimerRunning: false,
    currentStep: 1
};

// 题目记录管理
const studentQuestionRecords = {
    translations: new Map(),
    professionals: new Map()
};

// 全局状态
let examState = {
    currentStep: 1,
    remainingTime: 0,
    selectedMinutes: 15,
    questionCount: 3,
    hasStarted: false,
    currentPage: 'index'
};

// 学生记录
let studentRecords = [];

// 模态框实例
let settingsModal = null;
let recordModal = null;

// 初始化页面
document.addEventListener('DOMContentLoaded', () => {
    console.log('页面已加载，初始化应用...');
    
    // 确保startTranslation函数存在
    ensureStartTranslationExists();
    
    // 初始化模态框
    initModals();
    
    // 初始化设置
    initSettings();
    
    // 初始化计时器
    initTimer();
    
    // 绑定事件
    bindEvents();
    
    // 加载本地存储数据
    loadFromLocalStorage();
    
    // 确定当前页面并执行相应初始化
    determineCurrentPage();
    
    // 确保进度条和步骤导航系统已初始化
    setTimeout(() => {
        initSteps();
        // 初始化step-item状态
        updateStepItems();
        console.log('步骤导航系统已初始化');
    }, 100);
});

// 确定当前页面
function determineCurrentPage() {
    const path = window.location.pathname;
    
    if (path.includes('editor.html')) {
        examState.currentPage = 'editor';
        initEditorPage();
            } else {
        examState.currentPage = 'index';
        initIndexPage();
    }
    
    console.log(`当前页面: ${examState.currentPage}`);
}

// 初始化主页
function initIndexPage() {
    // 检查是否有设置按钮和考试记录按钮
    const settingsBtn = document.getElementById('settingsBtn');
    const recordBtn = document.getElementById('recordBtn');
    const startBtn = document.getElementById('startBtn');
    const translationBtn = document.getElementById('translationBtn');
    
    if (settingsBtn) {
        console.log('找到设置按钮');
    }
    
    if (recordBtn) {
        console.log('找到记录按钮');
    }
    
    if (startBtn) {
        startBtn.addEventListener('click', function() {
            window.location.href = 'editor.html';
        });
    }
    
    if (translationBtn) {
        translationBtn.addEventListener('click', function() {
            drawTranslationQuestion();
        });
    }
    
    // 初始化步骤
    initSteps();
}

// 初始化编辑器页面
function initEditorPage() {
    // 此处为编辑器页面的初始化代码
    console.log('初始化编辑器页面');
}

// 初始化模态框
function initModals() {
    // 获取模态框元素
    const settingsModalEl = document.getElementById('settingsModal');
    const recordModalEl = document.getElementById('examRecordModal'); // 修正模态框ID
    
    // 初始化Bootstrap模态框
    if (settingsModalEl) {
        try {
            settingsModal = new bootstrap.Modal(settingsModalEl);
            console.log('设置模态框已初始化');
        } catch (error) {
            console.error('初始化设置模态框时出错:', error);
        }
    } else {
        console.warn('未找到设置模态框元素');
    }
    
    if (recordModalEl) {
        try {
            recordModal = new bootstrap.Modal(recordModalEl);
            console.log('记录模态框已初始化');
        } catch (error) {
            console.error('初始化记录模态框时出错:', error);
        }
    } else {
        console.warn('未找到记录模态框元素');
    }
}

// 初始化设置
function initSettings() {
    const minutesInput = document.getElementById('examMinutes');
    const questionsInput = document.getElementById('questionCount');
    
    if (minutesInput) {
        minutesInput.value = examState.selectedMinutes;
    }
    
    if (questionsInput) {
        questionsInput.value = examState.questionCount;
    }
}

// 初始化计时器
function initTimer() {
    examState.remainingTime = examState.selectedMinutes * 60;
    updateTimerDisplay();
}

// 更新计时器显示
function updateTimerDisplay() {
    const timerElement = document.getElementById('timer');
    if (timerElement) {
        const minutes = Math.floor(examState.remainingTime / 60);
        const seconds = examState.remainingTime % 60;
        timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
}

// 绑定事件
function bindEvents() {
    console.log('绑定事件处理程序...');
    
    // 设置按钮事件
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', showSettings);
        console.log('已绑定设置按钮事件');
    }
    
    // 考试记录按钮事件
    const recordBtn = document.getElementById('recordBtn');
    if (recordBtn) {
        recordBtn.addEventListener('click', showExamRecord);
        console.log('已绑定记录按钮事件');
    }
    
    // 保存设置按钮事件
    const saveSettingsBtn = document.getElementById('saveSettingsBtn');
    if (saveSettingsBtn) {
        saveSettingsBtn.addEventListener('click', saveSettings);
        console.log('已绑定保存设置按钮事件');
    }
    
    // 重置考试按钮事件 - 由 buttonManager.js 处理
    // const resetExamBtn = document.getElementById('resetExamBtn');
    // if (resetExamBtn) {
    //     resetExamBtn.addEventListener('click', resetExam);
    //     console.log('已绑定重置考试按钮事件');
    // }
    
    // 导出记录按钮事件
    const exportRecordBtn = document.getElementById('exportRecordBtn');
    if (exportRecordBtn) {
        exportRecordBtn.addEventListener('click', exportExamRecord);
        console.log('已绑定导出记录按钮事件');
    }
    
    // 绑定开始计时按钮
    const startTimerBtns = document.querySelectorAll('.start-timer-btn');
    startTimerBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            startTimer(settings.chineseTime);
            console.log('开始中文自我介绍计时');
        });
    });
    console.log(`已绑定${startTimerBtns.length}个开始计时按钮事件`);
    
    // 绑定英文自我介绍按钮
    const englishIntroBtns = document.querySelectorAll('.english-intro-btn');
    englishIntroBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            startTimer(settings.englishTime);
            console.log('开始英文自我介绍计时');
        });
    });
    console.log(`已绑定${englishIntroBtns.length}个英文自我介绍按钮事件`);
    
    // 绑定翻译按钮
    const translationBtns = document.querySelectorAll('.translation-btn');
    translationBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            startTranslation();
            console.log('开始翻译考核');
        });
    });
    console.log(`已绑定${translationBtns.length}个翻译按钮事件`);
    
    // 绑定专业课考核按钮
    const professionalBtns = document.querySelectorAll('.professional-btn');
    professionalBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            startProfessional();
            console.log('开始专业课考核');
        });
    });
    console.log(`已绑定${professionalBtns.length}个专业课考核按钮事件`);
    
    // 绑定所有下一步按钮
    const nextStepBtns = document.querySelectorAll('.next-step-btn');
    nextStepBtns.forEach(btn => {
        btn.addEventListener('click', goToNextStep);
    });
    console.log(`已绑定${nextStepBtns.length}个下一步按钮事件`);
    
    // 绑定所有上一步按钮
    const prevStepBtns = document.querySelectorAll('.prev-step-btn');
    prevStepBtns.forEach(btn => {
        btn.addEventListener('click', goToPrevStep);
    });
    console.log(`已绑定${prevStepBtns.length}个上一步按钮事件`);

    // 绑定左侧步骤点击事件
    const stepItems = document.querySelectorAll('.step-item');
    stepItems.forEach(item => {
        item.addEventListener('click', function() {
            const stepNumber = parseInt(this.getAttribute('data-step'));
            if (stepNumber) {
                startStep(stepNumber);
            }
        });
    });
    console.log(`已绑定${stepItems.length}个步骤项点击事件`);
    
    // 绑定结束面试按钮
    const endExamBtns = document.querySelectorAll('.end-exam-btn');
    endExamBtns.forEach(btn => {
        btn.addEventListener('click', endExam);
    });
    console.log(`已绑定${endExamBtns.length}个结束面试按钮事件`);
    
    // 绑定下一位考生按钮
    const nextStudentBtns = document.querySelectorAll('.next-student-btn');
    nextStudentBtns.forEach(btn => {
        btn.addEventListener('click', nextStudent);
    });
    console.log(`已绑定${nextStudentBtns.length}个下一位考生按钮事件`);
    
    // 设置进度指示器初始状态
    updateProgressIndicator();
    
    // 绑定步骤点点击事件
    bindStepPointsEvents();
    
    console.log('所有事件处理程序绑定完成');
}

// 初始化步骤
function initSteps() {
    // 添加步骤指示器
    addStepIndicator();
    
    // 初始化步骤显示
    updateStepDisplay();
    
    // 绑定所有步骤按钮事件
    bindStepButtons();
}

// 更新步骤显示
function updateStepDisplay() {
    // 获取所有步骤元素
    const allSteps = [
        document.getElementById('step1'),
        document.getElementById('step2'),
        document.getElementById('step3'),
        document.getElementById('step4')
    ];
    
    // 更新步骤状态
    allSteps.forEach((step, index) => {
        if (!step) return;
        
        // 当前步骤索引（从0开始）
        const stepIndex = index + 1;
        
        if (examState.currentStep === stepIndex) {
            // 当前步骤
            step.classList.add('active');
            step.classList.remove('completed');
            step.classList.remove('inactive');
        } else if (examState.currentStep > stepIndex) {
            // 已完成的步骤
            step.classList.remove('active');
            step.classList.add('completed');
            step.classList.remove('inactive');
    } else {
            // 未到达的步骤
            step.classList.remove('active');
            step.classList.remove('completed');
            step.classList.add('inactive');
        }
    });
    
    // 更新进度指示器
    updateProgressIndicator();
}

// 更新进度指示器
function updateProgressIndicator() {
    const progressIndicator = document.getElementById('progress-indicator');
    if (progressIndicator) {
        // 计算进度百分比（总共4个步骤）
        const totalSteps = 4;
        const progress = ((examState.currentStep - 1) / (totalSteps - 1)) * 100;
        progressIndicator.style.width = `${progress}%`;
        console.log(`进度指示器更新为 ${progress}%`);
        
        // 同时更新步骤点的状态
        updateStepPoints();
    } else {
        console.warn('未找到进度指示器元素');
    }
}

// 更新步骤点状态
function updateStepPoints() {
    // 获取所有步骤点
    const stepPoints = document.querySelectorAll('.step-point');

    // 更新每个步骤点的状态
    stepPoints.forEach(point => {
        const stepNumber = parseInt(point.getAttribute('data-step'));

        // 移除所有状态类
        point.classList.remove('active', 'completed');

        if (stepNumber === examState.currentStep) {
            // 当前步骤
            point.classList.add('active');
        } else if (stepNumber < examState.currentStep) {
            // 已完成步骤
            point.classList.add('completed');
        }
        // 未达到的步骤保持默认状态
    });

    console.log(`步骤点状态已更新，当前步骤: ${examState.currentStep}`);
}

// 更新左侧step-item状态
function updateStepItems() {
    // 获取所有step-item
    const stepItems = document.querySelectorAll('.step-item');

    // 更新每个step-item的状态
    stepItems.forEach(item => {
        const stepNumber = parseInt(item.getAttribute('data-step'));

        // 移除所有状态类
        item.classList.remove('active', 'completed');

        if (stepNumber === examState.currentStep) {
            // 当前步骤
            item.classList.add('active');
        } else if (stepNumber < examState.currentStep) {
            // 已完成步骤
            item.classList.add('completed');
        }
        // 未达到的步骤保持默认状态
    });

    // 更新中间内容区域的step-content
    updateCenterContent(examState.currentStep);

    console.log(`左侧step-item状态已更新，当前步骤: ${examState.currentStep}`);
}

// 更新中间内容区域
function updateCenterContent(stepNumber) {
    // 更新内容标题
    const contentTitle = document.getElementById('current-step-title');
    if (contentTitle) {
        const stepNames = {
            1: '中文自我介绍（2分钟）',
            2: '英文自我介绍（2分钟）',
            3: '英文翻译（5分钟）',
            4: '专业课问答（5分钟）',
            5: '综合面试（8分钟）'
        };

        const icons = {
            1: 'bi-person-circle',
            2: 'bi-translate',
            3: 'bi-journal-text',
            4: 'bi-mortarboard',
            5: 'bi-chat-dots'
        };

        const stepName = stepNames[stepNumber] || `步骤${stepNumber}`;
        const iconClass = icons[stepNumber] || 'bi-circle';

        contentTitle.innerHTML = `<i class="${iconClass}"></i> ${stepName}`;
    }

    // 显示对应的步骤内容
    const stepContents = document.querySelectorAll('.content-body .step-content');
    console.log(`找到 ${stepContents.length} 个step-content元素`);

    stepContents.forEach((content, index) => {
        content.classList.remove('active');
        if (index + 1 === stepNumber) {
            content.classList.add('active');
            console.log(`设置step-content ${index + 1} 为active`);
        }
    });

    console.log(`中间内容区域已更新到步骤: ${stepNumber}`);
}

// 转到下一步
function goToNextStep() {
    console.log('尝试转到下一步');
    
    // 停止当前计时器
    stopTimer();
    
    // 获取所有步骤元素
    const steps = document.querySelectorAll('.step');
    
    // 找到当前活动步骤
    let currentStepIndex = -1;
    for (let i = 0; i < steps.length; i++) {
        if (steps[i].classList.contains('active')) {
            currentStepIndex = i;
            break;
        }
    }
    
    if (currentStepIndex === -1) {
        console.error('未找到当前活动步骤');
        return;
    }
    
    // 计算下一步索引
    const nextStepIndex = currentStepIndex + 1;
    
    // 确保下一步存在
    if (nextStepIndex < steps.length) {
        // 移除当前步骤的active类
        steps[currentStepIndex].classList.remove('active');
        
        // 添加active类到下一步
        steps[nextStepIndex].classList.add('active');
        
        // 更新当前步骤状态
        examState.currentStep = nextStepIndex + 1; // 步骤索引从0开始，但步骤编号从1开始
        
        // 滚动到新步骤
        steps[nextStepIndex].scrollIntoView({ behavior: 'smooth' });
        
        console.log(`已切换到步骤 ${examState.currentStep}`);
        
        // 可选：触发步骤变化事件
        const event = new CustomEvent('stepChanged', { 
            detail: { 
                previousStep: currentStepIndex + 1,
                currentStep: examState.currentStep
            } 
        });
        document.dispatchEvent(event);
    } else {
        console.log('已经是最后一步');
        alert('已经是最后一步');
    }
}

// 转到上一步
function goToPrevStep() {
    console.log('尝试转到上一步');
    
    // 停止当前计时器
    stopTimer();
    
    // 获取所有步骤元素
    const steps = document.querySelectorAll('.step');
    
    // 找到当前活动步骤
    let currentStepIndex = -1;
    for (let i = 0; i < steps.length; i++) {
        if (steps[i].classList.contains('active')) {
            currentStepIndex = i;
            break;
        }
    }
    
    if (currentStepIndex === -1) {
        console.error('未找到当前活动步骤');
        return;
    }
    
    // 计算上一步索引
    const prevStepIndex = currentStepIndex - 1;
    
    // 确保上一步存在
    if (prevStepIndex >= 0) {
        // 移除当前步骤的active类
        steps[currentStepIndex].classList.remove('active');
        
        // 添加active类到上一步
        steps[prevStepIndex].classList.add('active');
        
        // 更新当前步骤状态
        examState.currentStep = prevStepIndex + 1; // 步骤索引从0开始，但步骤编号从1开始
        
        // 滚动到新步骤
        steps[prevStepIndex].scrollIntoView({ behavior: 'smooth' });
        
        console.log(`已切换到步骤 ${examState.currentStep}`);
        
        // 可选：触发步骤变化事件
        const event = new CustomEvent('stepChanged', { 
            detail: { 
                previousStep: currentStepIndex + 1,
                currentStep: examState.currentStep
            } 
        });
        document.dispatchEvent(event);
    } else {
        console.log('已经是第一步');
        alert('已经是第一步');
    }
}

// 监听步骤变化事件
document.addEventListener('stepChanged', function(e) {
    console.log(`步骤已从 ${e.detail.previousStep} 变为 ${e.detail.currentStep}`);
    
    // 可以在这里添加其他步骤变化相关的逻辑
    // 例如更新进度指示器等
    updateProgressIndicator();
});

// 保存当前步骤状态
function saveCurrentStepState() {
    // 根据当前步骤保存相关数据
    switch (examState.currentStep) {
        case 1:
            // 保存中文自我介绍相关数据
            break;
        case 2:
            // 保存英语考核相关数据
            break;
        case 3:
            // 保存专业课考核相关数据
            break;
        case 4:
            // 保存综合面试相关数据
            break;
    }
}

// 设置特定步骤
function startStep(stepNumber) {
    // 确保步骤号有效
    if (stepNumber < 1 || stepNumber > 5) {
        console.error(`无效的步骤号: ${stepNumber}`);
            return;
        }
        
    // 保存当前步骤的状态
    saveCurrentStepState();
    
    // 先获取当前激活的步骤
    const currentStep = document.querySelector('.step.active');
    const nextStep = document.getElementById(`step${stepNumber}`);
    
    if (!nextStep) {
        console.error(`找不到步骤元素: step${stepNumber}`);
        return;
    }
    
    if (currentStep === nextStep) {
        // 如果点击的是当前步骤，不执行任何操作
        return;
    }
    
    // 记录当前步骤
    examState.currentStep = stepNumber;
    
    // 添加淡出动画
    if (currentStep) {
        currentStep.classList.add('fade-out');
        
        // 监听动画结束
        currentStep.addEventListener('animationend', function handler() {
            // 更新所有步骤状态
            updateStepDisplay();

            // 更新左侧step-item状态
            updateStepItems();

            // 更新中间内容区域
            updateCenterContent(stepNumber);

            // 添加淡入动画
            nextStep.classList.add('fade-in');
            
            // 移除监听器和淡出类
            currentStep.removeEventListener('animationend', handler);
            currentStep.classList.remove('fade-out');
            
            // 监听淡入动画结束
            nextStep.addEventListener('animationend', function inHandler() {
                nextStep.removeEventListener('animationend', inHandler);
                nextStep.classList.remove('fade-in');
            }, { once: true });
        }, { once: true });
    } else {
        // 如果没有当前步骤（初始化），直接更新显示
        updateStepDisplay();

        // 更新左侧step-item状态
        updateStepItems();

        // 更新中间内容区域
        updateCenterContent(stepNumber);
    }
    
    // 停止当前计时器
    stopTimer();
    
    // 保存状态
    saveToLocalStorage();
    
    console.log(`已切换到第${stepNumber}步`);
}

// 添加步骤指示器
function addStepIndicator() {
    // 检查是否已存在进度条
    if (document.getElementById('steps-progress')) {
        return;
    }

    // 创建进度条容器
    const progressContainer = document.createElement('div');
    progressContainer.id = 'steps-progress';
    progressContainer.className = 'steps-progress';
    
    // 创建进度指示器
    const progressIndicator = document.createElement('div');
    progressIndicator.id = 'progress-indicator';
    progressIndicator.className = 'progress-indicator';
    
    // 创建步骤点
    const steps = [1, 2, 3, 4];
    const stepPointsContainer = document.createElement('div');
    stepPointsContainer.className = 'step-points';
    
    steps.forEach(step => {
        const stepPoint = document.createElement('div');
        stepPoint.className = 'step-point';
        stepPoint.dataset.step = step;
        stepPoint.title = `第${step}步`;
        
        // 点击步骤点可以直接跳转
        stepPoint.addEventListener('click', () => {
            startStep(step);
        });
        
        stepPointsContainer.appendChild(stepPoint);
    });
    
    // 组装进度条
    progressContainer.appendChild(progressIndicator);
    progressContainer.appendChild(stepPointsContainer);
    
    // 添加到页面
    const container = document.querySelector('.container');
    
        if (container) {
        container.insertBefore(progressContainer, container.firstChild);
    }
}

// 绑定步骤按钮事件
function bindStepButtons() {
    // 绑定所有"下一步"按钮
    const nextButtons = document.querySelectorAll('.step .button-group .success-btn');
    nextButtons.forEach(button => {
        // 清除可能已有的点击事件
        button.replaceWith(button.cloneNode(true));
        
        // 获取新的按钮引用
        const newButton = button.parentNode.lastChild;
        
        // 添加点击事件
        newButton.addEventListener('click', function() {
            goToNextStep();
        });
    });
    
    // 如果有"上一步"按钮，也绑定
    const prevButtons = document.querySelectorAll('.step .button-group .back-btn');
    prevButtons.forEach(button => {
        // 清除可能已有的点击事件
        button.replaceWith(button.cloneNode(true));
        
        // 获取新的按钮引用
        const newButton = button.parentNode.lastChild;
        
        // 添加点击事件
        newButton.addEventListener('click', function() {
            goToPrevStep();
        });
    });
}

// 显示设置模态框
function showSettings() {
    console.log('尝试显示设置模态框');
    
    // 尝试直接通过DOM操作显示模态框
    if (!settingsModal) {
        const settingsModalEl = document.getElementById('settingsModal');
        if (settingsModalEl) {
            try {
                settingsModal = new bootstrap.Modal(settingsModalEl);
            } catch (error) {
                console.error('初始化设置模态框失败:', error);
                // 备用方法：直接修改模态框样式
                settingsModalEl.classList.add('show');
                settingsModalEl.style.display = 'block';
                document.body.classList.add('modal-open');
                return;
            }
        }
    }
    
    if (settingsModal) {
        try {
            settingsModal.show();
            console.log('设置模态框已显示');
        } catch (error) {
            console.error('显示设置模态框时出错:', error);
        }
    } else {
        console.error('无法获取设置模态框实例');
    }
}

function closeSettings() {
    console.log('关闭设置模态框');
        const modalElement = document.getElementById('settingsModal');
    
        if (!modalElement) {
        console.error('未找到设置模态框元素');
            return;
        }
        
    const modal = bootstrap.Modal.getInstance(modalElement);
    if (modal) {
        modal.hide();
    }
}

function saveSettings() {
    console.log('保存设置');
    
    // 从输入框获取值
    const chineseTime = parseFloat(document.getElementById('chineseTime').value) || 1;
    const englishTime = parseFloat(document.getElementById('englishTime').value) || 1;
    const translationTime = parseFloat(document.getElementById('translationTime').value) || 4;
    const professionalTime = parseFloat(document.getElementById('professionalTime').value) || 5;
    
    // 更新设置
    settings.chineseTime = chineseTime;
    settings.englishTime = englishTime;
    settings.translationTime = translationTime;
    settings.professionalTime = professionalTime;
    
    // 更新UI显示
    document.getElementById('chineseTimeDisplay').textContent = chineseTime;
    document.getElementById('englishTimeDisplay').textContent = englishTime;
    document.getElementById('translationTimeDisplay').textContent = translationTime;
    document.getElementById('professionalTimeDisplay').textContent = professionalTime;
    
    // 保存到本地存储
    localStorage.setItem('examSettings', JSON.stringify(settings));
    
    // 尝试关闭模态框
    if (settingsModal) {
        try {
            settingsModal.hide();
        } catch (error) {
            console.error('关闭设置模态框时出错:', error);
            // 备用方法
            const settingsModalEl = document.getElementById('settingsModal');
            if (settingsModalEl) {
                settingsModalEl.classList.remove('show');
                settingsModalEl.style.display = 'none';
                document.body.classList.remove('modal-open');
            }
        }
    }
    
    // 显示成功消息
    alert('设置已保存');
}

function resetExam() {
    if (!confirm('确定要重置考试吗？这将清除所有记录并恢复默认设置')) {
        return;
    }
    
    // 显示加载提示
    const loadingToast = showLoadingToast('正在重置考试数据...');
    
    // 向服务器发送重置请求，重置数据库中的题目选择状态
    fetch('/api/reset', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('重置服务器数据失败');
        }
        return response.json();
    })
    .then(data => {
        // 重置本地设置
        settings.chineseTime = 1;
        settings.englishTime = 1;
        settings.translationTime = 4;
        settings.professionalTime = 5;

        // 重置状态 - 确保从1号考生开始
        state.currentStudent = 1;
        state.currentStep = 1;
        stopTimer();

        // 重置考试状态
        examState.currentStep = 1;
        examState.remainingTime = 0;
        examState.hasStarted = false;

        // 清空题目记录
        studentQuestionRecords.translations.clear();
        studentQuestionRecords.professionals.clear();

        // 清空题目选择状态
        if (window.questionBank) {
            window.questionBank.resetUsedQuestions();
        }

        // 清空全局题目状态
        if (window.questionsData) {
            // 重置题目使用计数
            if (window.questionsData.translation) {
                window.questionsData.translation.forEach(q => {
                    q.usageCount = 0;
                    q.lastUsed = null;
                });
            }
            if (window.questionsData.professional) {
                window.questionsData.professional.forEach(q => {
                    q.usageCount = 0;
                    q.lastUsed = null;
                });
            }
        }

        // 清除本地存储
        localStorage.removeItem('XueShuoSanPi');
        localStorage.removeItem('examSystem_questionBank');
        localStorage.removeItem('examSystem_usedQuestions');

        // 更新UI
        updateUI();

        // 关闭设置弹窗
        closeSettings();

        // 显示成功消息
        dismissToast(loadingToast);
        showSuccessToast('考试已重置，考生序号从1号开始');
    })
    .catch(error => {
        console.error('重置考试时出错:', error);
        dismissToast(loadingToast);
        showErrorToast('重置考试失败: ' + error.message);
    });
}

/**
 * 增强的Toast提示系统 - 使用Bootstrap的Toast
 */
function toast(message, type = 'info') {
    // 获取或创建toast容器
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // 创建toast元素ID
    const toastId = 'toast-' + Date.now();
    
    // 设置图标和背景色
    let iconClass = 'bi-info-circle-fill';
    let bgClass = 'bg-primary';
    
    switch(type) {
        case 'success':
            iconClass = 'bi-check-circle-fill';
            bgClass = 'bg-success';
            break;
        case 'error':
            iconClass = 'bi-exclamation-triangle-fill';
            bgClass = 'bg-danger';
            break;
        case 'warning':
            iconClass = 'bi-exclamation-circle-fill';
            bgClass = 'bg-warning';
            break;
    }
    
    // 创建Toast HTML
    const toastHtml = `
    <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header ${bgClass} text-white">
            <i class="bi ${iconClass} me-2"></i>
            <strong class="me-auto">提示</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    </div>`;
    
    // 添加toast到容
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // 获取新创建的toast元素
    const toastElement = document.getElementById(toastId);
    
    // 创建Bootstrap的Toast实例
    const toast = new bootstrap.Toast(toastElement, {
        animation: true,
        autohide: true,
        delay: 3000
    });
    
    // 显示toast
    toast.show();
    
    // 监听toast隐藏事件，移除DOM元素
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
    
    return toastElement;
}

function showSuccessToast(message) {
    return toast(message, 'success');
}

function showErrorToast(message) {
    return toast(message, 'error');
}

function showWarningToast(message) {
    return toast(message, 'warning');
}

function showLoadingToast(message) {
    // 获取或创建toast容器
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // 创建toast元素ID
    const toastId = 'loading-toast-' + Date.now();
    
    // 创建Loading Toast HTML
    const toastHtml = `
    <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="false">
        <div class="toast-header bg-secondary text-white">
            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            <strong class="me-auto">处理/strong>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    </div>`;
    
    // 添加toast到容
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // 获取新创建的toast元素
    const toastElement = document.getElementById(toastId);
    
    // 创建Bootstrap的Toast实例
    const toast = new bootstrap.Toast(toastElement, {
        animation: true,
        autohide: false
    });
    
    // 显示toast
    toast.show();
    
    return toastElement;
}

function removeLoadingToast(toastElement) {
    if (toastElement) {
        const toast = bootstrap.Toast.getInstance(toastElement);
        if (toast) {
            toast.hide();
            // Bootstrap toast会在隐藏后自动从DOM中移除元素
        }
    }
}

/**
 * 辅助函数
 */
function showExamRecord() {
    console.log('尝试显示考试记录模态框');
    
    // 更新考试记录内容
    updateExamRecordContent();
    
    // 类似于showSettings函数，添加备用方法
    if (!recordModal) {
        const recordModalEl = document.getElementById('examRecordModal');
        if (recordModalEl) {
            try {
                recordModal = new bootstrap.Modal(recordModalEl);
            } catch (error) {
                console.error('初始化记录模态框失败:', error);
                // 备用方法：直接修改模态框样式
                recordModalEl.classList.add('show');
                recordModalEl.style.display = 'block';
                document.body.classList.add('modal-open');
                return;
            }
        }
    }
    
    if (recordModal) {
        try {
            recordModal.show();
            console.log('考试记录模态框已显示');
        } catch (error) {
            console.error('显示考试记录模态框时出错:', error);
        }
    } else {
        console.error('无法获取考试记录模态框实例');
    }
}

// 更新考试记录内容
function updateExamRecordContent() {
    const recordContent = document.getElementById('examRecordContent');
    if (recordContent) {
        // 这里可以实现记录内容的生成逻辑
        recordContent.innerHTML = '<p>暂无考试记录</p>';
    }
}

function closeExamRecord() {
    console.log('关闭考试记录模态框');
    const modalElement = document.getElementById('examRecordModal');
    
    if (!modalElement) {
        console.error('未找到考试记录模态框元素');
        return;
    }
    
    const modal = bootstrap.Modal.getInstance(modalElement);
    if (modal) {
        modal.hide();
    }
}

// 添加导出考试记录功能
function exportExamRecord() {
    try {
        // 获取表格数据
        const table = document.getElementById('records-table');
        if (!table) {
            showErrorToast('无法找到记录表格');
            return;
        }

        // 构建CSV内容
        let csvContent = "考生姓名,复试时间,外语得分,专业得分,综合得分,总分\n";
        
        // 获取表格中的所有行（跳过表头）
        const rows = table.querySelectorAll('tbody tr');
        
        if (rows.length === 0) {
            showWarningToast('没有可导出的记录');
            return;
        }
        
        // 遍历行并添加到CSV
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 7) { // 跳过操作
                const rowData = Array.from(cells)
                    .slice(0, 7) // 排除最后一列（操作列）
                    .map(cell => `"${cell.textContent.trim()}"`)
                    .join(',');
                csvContent += rowData + '\n';
            }
        });
        
        // 创建下载链接
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
        a.href = url;
        a.download = `复试记录_${timestamp}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showSuccessToast('记录导出成功');
    } catch (error) {
        console.error('导出记录失败:', error);
        showErrorToast('导出记录失败');
    }
}

/**
 * 清空复试记录
 */
function clearRecords() {
    if (!confirm('确定要清空所有复试记录吗？此操作不可恢复')) {
            return;
        }
        
    try {
        // 清空表格
        const tbody = document.getElementById('records-body');
        if (tbody) {
            tbody.innerHTML = '';
        }
        
        // 也可以在这里添加与后端的通信，清空数据库中的记录
        // 例如
        /*
        fetch('/api/clear_records', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => {
            if (!response.ok) throw new Error('服务器清空记录失);
            return response.json();
        })
        .then(data => {
            showSuccessToast('记录已全部清);
        })
        .catch(error => {
            console.error('清空记录失败:', error);
            showErrorToast('清空记录失败: ' + error.message);
        });
        */
        
        // 简单版本，仅清空本地显
        showSuccessToast('记录已全部清');
    } catch (error) {
        console.error('清空记录失败:', error);
        showErrorToast('清空记录失败');
    }
}

// 移除开头重复定义的函数，保留一个统一的初始化函数
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM已加载，初始化应用...');
    
    // 初始化设置
        loadState();
        
    // 更新UI显示
            updateUI();
    
    // 绑定事件处理函数到按钮
    bindEventHandlers();
    
    console.log('应用初始化完成');
});

// 绑定所有事件处理程序
function bindEventHandlers() {
    // 设置按钮
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
        settingsBtn.removeEventListener('click', showSettings);
        settingsBtn.addEventListener('click', showSettings);
    }
    
    // 记录按钮
    const recordBtn = document.getElementById('recordBtn');
    if (recordBtn) {
        recordBtn.removeEventListener('click', showExamRecord);
        recordBtn.addEventListener('click', showExamRecord);
    }
    
    // 将内联onclick函数暴露到全局作用域
    window.startTimer = startTimer;
    window.startStep = startStep;
    window.startEnglishIntro = startEnglishIntro;
    window.startTranslation = startTranslation;
    window.startProfessional = startProfessional;
    window.endExam = endExam;
    window.nextStudent = nextStudent;
    window.closeSettings = closeSettings;
    window.saveSettings = saveSettings;
    window.resetExam = resetExam;
    window.closeExamRecord = closeExamRecord;
    window.exportExamRecord = exportExamRecord;
}

// 增强选题动画效果
async function animateQuestionSelection(prefix, index) {
    // 获取所有相关题目元素
    const allQuestions = document.querySelectorAll(`[id^="${prefix}-"]`);
    
    // 创建闪烁效果的函
    const flashElement = async (element, duration = 100) => {
        element.classList.add('highlight-pulse');
        await new Promise(resolve => setTimeout(resolve, duration));
        element.classList.remove('highlight-pulse');
    };
    
    // 随机闪烁效果动画
    for (let i = 0; i < 15; i++) {
        // 随机高亮一个题
        const randomIndex = Math.floor(Math.random() * allQuestions.length);
        
        // 随着循环进行，减慢动画速度，增强最终选择的期待感
        const duration = 80 + Math.floor(i * 10);
        
        await flashElement(allQuestions[randomIndex], duration);
    }
    
    // 高亮最终选中的题
    const selectedElement = document.getElementById(`${prefix}-${index}`);
    if (selectedElement) {
        // 执行最终选中的动
        selectedElement.classList.add('selected');
        selectedElement.classList.add('final-selected');
        
        // 等待动画完成
        await new Promise(resolve => setTimeout(resolve, 500));
        selectedElement.classList.remove('final-selected');
    }
}

// 添加showRecords函数来显示recordsModal
function showRecords() {
    console.log('尝试显示记录模态框');
    try {
        // 首先检查元素是否存在
        const modalElement = document.getElementById('recordsModal');
        if (!modalElement) {
            console.error('未找到模态框元素: recordsModal');
            showErrorToast('记录模态框不存在');
            
            // 备选方案：尝试打开examRecordModal
            console.log('尝试显示备选记录模态框');
            try {
                showExamRecord();
                return;
            } catch (fallbackError) {
                console.error('备选记录模态框也失', fallbackError);
                showErrorToast('所有记录查看功能不可用');
                return;
            }
        }
        
        console.log('找到模态框元素:', modalElement);
        
        // 确保bootstrap已定
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap未定义，可能未正确加');
            showErrorToast('Bootstrap库未正确加载，请刷新页面');
            
            // 回退方法：直接修改显示样
            modalElement.style.display = 'block';
            modalElement.classList.add('show');
            document.body.classList.add('modal-open');
            
            // 添加背景遮罩
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
            
            return;
        }
        
        // 使用Bootstrap的模态框API
        const recordsModal = new bootstrap.Modal(modalElement);
        console.log('创建Bootstrap模态框实例:', recordsModal);
        
        // 可以在这里添加加载记录的逻辑
        // 例如：loadExamRecords();
        
        // 显示模态框
        recordsModal.show();
        console.log('已调用show()方法');
    } catch (error) {
        console.error('显示记录时出错', error);
        showErrorToast('无法打开考试记录: ' + error.message);
        
        // 如果recordsModal不存在，尝试使用examRecordModal作为备
        try {
            showExamRecord();
        } catch (fallbackError) {
            console.error('备选记录模态框也失', fallbackError);
        }
    }
}

// 添加基本的startTranslation函数，如果尚未定义
function ensureStartTranslationExists() {
    if (typeof window.startTranslation !== 'function') {
        console.log('添加基本的startTranslation函数');
        
        window.startTranslation = function() {
            console.log('开始抽题 - 基本实现');
            
            // 获取或创建翻译问题区域
            let translationArea = document.getElementById('translation-question');
            
            if (!translationArea) {
                console.error('未找到translation-question区域，尝试创建一个');
                const step2 = document.getElementById('step2');
                if (step2) {
                    const newArea = document.createElement('div');
                    newArea.id = 'translation-question';
                    newArea.className = 'mt-4';
                    step2.appendChild(newArea);
                    translationArea = newArea;
                } else {
                    console.error('未找到step2区域，无法创建翻译问题区域');
            return;
        }
            }
            
            // 模拟翻译题目数据
            const questions = [
                { english: "Please introduce yourself briefly.", chinese: "请简单介绍一下自己。" },
                { english: "What is your research interest?", chinese: "你的研究兴趣是什么？" },
                { english: "Why did you choose this major?", chinese: "你为什么选择这个专业？" },
                { english: "What are your career plans after graduation?", chinese: "毕业后你的职业规划是什么？" }
            ];
            
            // 随机选择一个问题
            const randomIndex = Math.floor(Math.random() * questions.length);
            const selectedQuestion = questions[randomIndex];
            
            // 设置问题内容
            translationArea.innerHTML = `
                <div class="card mb-3">
                    <div class="card-header bg-primary text-white">翻译问题 #${randomIndex + 1}</div>
                    <div class="card-body">
                        <p class="card-text"><strong>英语:</strong> ${selectedQuestion.english}</p>
                        <p class="card-text"><strong>中文:</strong> ${selectedQuestion.chinese}</p>
                    </div>
                </div>
            `;
            
            // 确保区域可见
            translationArea.style.display = 'block';
            
            // 滚动到问题区域
            translationArea.scrollIntoView({ behavior: 'smooth' });
            
            console.log('已显示翻译问题');
        };
    }
}

// 在文档加载完成后运行
if (document.readyState === 'complete') {
    ensureStartTranslationExists();
} else {
    window.addEventListener('load', ensureStartTranslationExists);
}

// 结束面试
function endExam() {
    console.log('结束本次面试');
    if (confirm('确定要结束本次面试吗？')) {
        alert('面试已结束');
        // 这里可以添加更多逻辑，例如保存面试结果等
    }
}

// 下一位考生
function nextStudent() {
    console.log('下一位考生');
    if (confirm('确定要转到下一位考生吗？')) {
        // 增加考生序号
        state.currentStudent++;
        document.getElementById('studentNumber').textContent = state.currentStudent;
        
        // 重置步骤
        examState.currentStep = 1;
        
        // 更新步骤显示
        const allSteps = document.querySelectorAll('.step');
        allSteps.forEach((step, index) => {
            if (index === 0) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });
        
        // 隐藏所有计时器
        const allTimers = document.querySelectorAll('.timer-display');
        allTimers.forEach(timer => {
            timer.classList.add('hidden');
        });
        
        alert('已切换到下一位考生');
    }
}

// 启动翻译题测试
function startTranslation() {
    console.log('开始翻译题测试');
    
    // 开始计时
    startTimer(settings.translationTime);
    
    // 随机选择一个翻译题目
    // 这里可以实现更复杂的逻辑，例如从服务器获取题目等
    const translationQuestion = {
        id: 1,
        text: '翻译题示例：Based on the development of cloud computing, the Internet of Things, and big data technology, smart cities aim to realize comprehensive, integrated, and sustainable development.'
    };
    
    // 显示翻译题目
    const questionElement = document.getElementById('translation-question');
    if (questionElement) {
        questionElement.innerHTML = `
            <div class="card mt-3">
                <div class="card-body">
                    <h5 class="card-title">翻译题 #${translationQuestion.id}</h5>
                    <p class="card-text">${translationQuestion.text}</p>
                </div>
            </div>
        `;
    }
    
    // 更新剩余题目显示
    const remainingElement = document.getElementById('remaining-translations');
    if (remainingElement) {
        remainingElement.innerHTML = '<p>已选择当前题目</p>';
    }
}

// 启动专业课测试
function startProfessional() {
    console.log('开始专业课测试');
    
    // 开始计时
    startTimer(settings.professionalTime);
    
    // 随机选择专业课题目
    // 这里可以实现更复杂的逻辑，例如从服务器获取题目等
    const professionalQuestions = [
        { id: 1, text: '请解释什么是机器学习，并举例说明其在实际中的应用。' },
        { id: 2, text: '简述深度学习与传统机器学习的区别。' },
        { id: 3, text: '解释卷积神经网络(CNN)的基本原理及其在图像识别中的应用。' }
    ];
    
    // 显示专业课题目
    const questionsElement = document.getElementById('professional-questions');
    if (questionsElement) {
        let html = '<div class="card mt-3"><div class="card-body"><h5 class="card-title">专业课题目</h5>';
        
        professionalQuestions.forEach(q => {
            html += `<div class="mb-3"><strong>${q.id}. </strong>${q.text}</div>`;
        });
        
        html += '</div></div>';
        questionsElement.innerHTML = html;
    }
    
    // 更新剩余题目显示
    const remainingElement = document.getElementById('remaining-professionals');
    if (remainingElement) {
        remainingElement.innerHTML = '<p>已抽取所有题目</p>';
    }
}

// 启动英文自我介绍
function startEnglishIntro() {
    console.log('开始英文自我介绍');
    startTimer(settings.englishTime);
}

// 添加计时器函数
function startTimer(minutes) {
    console.log(`开始计时：${minutes}分钟`);
    
    // 清除可能存在的计时器
    if (state.timer) {
        clearInterval(state.timer);
    }
    
    // 设置剩余时间（转换为秒）
    let remainingSeconds = minutes * 60;
    state.isTimerRunning = true;
    
    // 确定当前步骤的计时器显示
    let timerDisplay;
    
    // 确定哪个计时器应该显示
    const currentStepElement = document.querySelector('.step.active');
    if (!currentStepElement) {
        console.error('未找到活动步骤元素');
        return;
    }
    
    const stepId = currentStepElement.id;
    console.log(`当前步骤ID: ${stepId}`);
    
    // 根据当前步骤选择对应的计时器和步骤名称
    let stepName = '';
    switch (stepId) {
        case 'step1':
            timerDisplay = document.getElementById('timer1');
            stepName = '中文自我介绍';
            break;
        case 'step2':
            // 检查是在英文自我介绍还是翻译环节
            if (document.querySelector('.english-intro-btn:focus')) {
                timerDisplay = document.getElementById('timer2_intro');
                stepName = '英文自我介绍';
            } else {
                timerDisplay = document.getElementById('timer2_translation');
                stepName = '英文翻译';
            }
            break;
        case 'step3':
            timerDisplay = document.getElementById('timer3');
            stepName = '专业课问答';
            break;
        case 'step4':
            timerDisplay = document.getElementById('timer4');
            stepName = '综合面试';
            break;
        default:
            console.error(`未知步骤ID: ${stepId}`);
            return;
    }

    // 更新可拖拽计时器的步骤名称
    const draggableStepName = document.getElementById('draggable-step-name');
    if (draggableStepName) {
        draggableStepName.textContent = stepName;
    }
    
    if (!timerDisplay) {
        console.error('未找到计时器显示元素');
        return;
    }
    
    // 显示计时器
    timerDisplay.classList.remove('hidden');
    
    // 更新计时器显示
    const timeElement = timerDisplay.querySelector('.time');
    if (!timeElement) {
        console.error('未找到时间显示元素');
        return;
    }
    
    // 更新时间显示函数
    function updateTimerText() {
        const mins = Math.floor(remainingSeconds / 60);
        const secs = remainingSeconds % 60;
        const timeText = `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;

        // 更新主计时器
        timeElement.textContent = timeText;

        // 同时更新可拖拽计时器
        const draggableTimerValue = document.getElementById('draggable-timer-value');
        if (draggableTimerValue) {
            draggableTimerValue.textContent = timeText;

            // 同时更新缩小状态下的时间显示
            if (typeof window.updateMinimizedTimer === 'function') {
                window.updateMinimizedTimer();
            }
        }
    }
    
    // 初始更新显示
    updateTimerText();
    
    // 设置计时器
    state.timer = setInterval(() => {
        remainingSeconds--;
        
        if (remainingSeconds <= 0) {
            // 时间到
            clearInterval(state.timer);
            state.isTimerRunning = false;
            
            // 播放提示音效
            const audio = new Audio('data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU...');
            audio.play().catch(e => console.log('无法播放提示音效:', e));
            
            // 提示时间结束
            alert('时间结束！');
            
            // 继续显示计时器，但显示为零
            updateTimerText();
        } else {
            // 更新显示
            updateTimerText();
        }
    }, 1000);
    
    console.log('计时器已启动');
}

// 停止计时器
function stopTimer() {
    if (state.timer) {
        clearInterval(state.timer);
        state.timer = null;
        state.isTimerRunning = false;
        console.log('计时器已停止');
    }
}

// 为进度指示器中的步骤点绑定点击事件
function bindStepPointsEvents() {
    const stepPoints = document.querySelectorAll('.step-point');
    
    stepPoints.forEach(point => {
        point.addEventListener('click', function() {
            const clickedStep = parseInt(this.getAttribute('data-step'));
            console.log(`点击了步骤 ${clickedStep}`);
            
            // 确保不是当前步骤
            if (clickedStep !== examState.currentStep) {
                // 如果点击了已完成的步骤或下一个步骤，则跳转
                if (clickedStep < examState.currentStep || clickedStep === examState.currentStep + 1) {
                    // 停止当前计时器
                    stopTimer();
                    
                    // 隐藏当前步骤
                    const currentStepElement = document.querySelector(`.step.active`);
                    if (currentStepElement) {
                        currentStepElement.classList.remove('active');
                    }
                    
                    // 显示目标步骤
                    const targetStepElement = document.getElementById(`step${clickedStep}`);
                    if (targetStepElement) {
                        targetStepElement.classList.add('active');
                        
                        // 更新当前步骤状态
                        examState.currentStep = clickedStep;
                        
                        // 更新进度指示器
                        updateProgressIndicator();
                        
                        console.log(`已切换到步骤 ${clickedStep}`);
                        
                        // 滚动到新步骤
                        targetStepElement.scrollIntoView({ behavior: 'smooth' });
                    } else {
                        console.error(`未找到目标步骤元素: step${clickedStep}`);
                    }
                } else {
                    // 不允许跳过多个步骤前进
                    alert('请按顺序完成步骤');
                    console.log('尝试跳过步骤被阻止');
                }
            }
        });
    });
    
    console.log('步骤点点击事件已绑定');
}
