/**
 * ExamService - 考试流程服务
 * 负责考试流程的业务逻辑和数据操作
 */

class ExamService extends BaseService {
    constructor() {
        super();
        this.dbService = null;
        this.studentService = null;
        this.questionService = null;
        this.currentExam = null;
        this.examSteps = [
            {
                stepNumber: 1,
                stepName: '中文自我介绍',
                description: '请用中文进行自我介绍',
                timeLimit: 2, // 分钟
                hasQuestions: false,
                questionTypes: []
            },
            {
                stepNumber: 2,
                stepName: '英文自我介绍',
                description: '请用英文进行自我介绍',
                timeLimit: 2,
                hasQuestions: false,
                questionTypes: []
            },
            {
                stepNumber: 3,
                stepName: '英文翻译',
                description: '请翻译抽取的英文句子',
                timeLimit: 5,
                hasQuestions: true,
                questionTypes: ['translation']
            },
            {
                stepNumber: 4,
                stepName: '专业问题',
                description: '请回答抽取的专业问题',
                timeLimit: 10,
                hasQuestions: true,
                questionTypes: ['professional']
            },
            {
                stepNumber: 5,
                stepName: '综合问答',
                description: '综合问答环节',
                timeLimit: 8,
                hasQuestions: false,
                questionTypes: []
            }
        ];
    }

    /**
     * 初始化服务
     */
    async onInitialize() {
        this.dbService = window.databaseService;
        this.studentService = window.studentService;
        this.questionService = window.questionService;
    }

    /**
     * 启动服务
     */
    async onStart() {
        // 服务启动逻辑
    }

    // ==================== 考试流程管理 ====================

    /**
     * 开始考试
     * @param {string} studentNumber - 学生编号（可选，不提供则自动生成）
     * @returns {Promise<Object>} 考试信息
     */
    async startExam(studentNumber = null) {
        try {
            // 获取或创建学生（支持自动编号生成）
            const student = await this.studentService.getOrCreateStudent(studentNumber);

            // 创建考试记录
            const examRecord = await this.createExamRecord(student);

            // 设置当前考试
            this.currentExam = {
                student,
                examRecord,
                startTime: new Date().toISOString(),
                currentStep: 1
            };

            // 开始第一步
            await this.startStep(1);

            this.emit('examStarted', {
                studentNumber: student.studentNumber,
                examRecord,
                currentStep: 1
            });

            return {
                success: true,
                data: {
                    student,
                    examRecord,
                    currentStep: 1,
                    stepInfo: this.getStepInfo(1)
                }
            };
        } catch (error) {
            console.error('开始考试失败:', error);
            throw error;
        }
    }

    /**
     * 开始下一个考生的考试
     * @returns {Promise<Object>} 考试信息
     */
    async startNextStudentExam() {
        try {
            // 如果有当前考试，先完成它
            if (this.currentExam) {
                await this.completeExam();
            }

            // 自动创建下一个考生并开始考试
            return await this.startExam();
        } catch (error) {
            console.error('开始下一个考生考试失败:', error);
            throw error;
        }
    }

    /**
     * 下一步
     * @returns {Promise<Object>} 步骤信息
     */
    async nextStep() {
        try {
            if (!this.currentExam) {
                throw new Error('没有进行中的考试');
            }

            const currentStep = this.currentExam.currentStep;
            const nextStep = currentStep + 1;

            if (nextStep > this.examSteps.length) {
                return await this.completeExam();
            }

            // 完成当前步骤
            await this.completeStep(currentStep);

            // 开始下一步
            await this.startStep(nextStep);

            this.currentExam.currentStep = nextStep;

            this.emit('stepChanged', {
                previousStep: currentStep,
                currentStep: nextStep,
                studentNumber: this.currentExam.student.studentNumber
            });

            return {
                success: true,
                data: {
                    previousStep: currentStep,
                    currentStep: nextStep,
                    stepInfo: this.getStepInfo(nextStep),
                    isCompleted: false
                }
            };
        } catch (error) {
            console.error('下一步失败:', error);
            throw error;
        }
    }

    /**
     * 上一步
     * @returns {Promise<Object>} 步骤信息
     */
    async previousStep() {
        try {
            if (!this.currentExam) {
                throw new Error('没有进行中的考试');
            }

            const currentStep = this.currentExam.currentStep;
            const previousStep = currentStep - 1;

            if (previousStep < 1) {
                throw new Error('已经是第一步');
            }

            this.currentExam.currentStep = previousStep;

            this.emit('stepChanged', {
                previousStep: currentStep,
                currentStep: previousStep,
                studentNumber: this.currentExam.student.studentNumber
            });

            return {
                success: true,
                data: {
                    previousStep: currentStep,
                    currentStep: previousStep,
                    stepInfo: this.getStepInfo(previousStep)
                }
            };
        } catch (error) {
            console.error('上一步失败:', error);
            throw error;
        }
    }

    /**
     * 完成考试
     * @returns {Promise<Object>} 考试结果
     */
    async completeExam() {
        try {
            if (!this.currentExam) {
                throw new Error('没有进行中的考试');
            }

            // 完成当前步骤
            await this.completeStep(this.currentExam.currentStep);

            // 更新考试记录
            const examRecord = this.currentExam.examRecord;
            examRecord.endTime = new Date().toISOString();
            examRecord.status = 'completed';
            examRecord.totalDuration = this.calculateTotalDuration(examRecord);

            await this.updateExamRecord(examRecord);

            // 更新学生状态
            const student = this.currentExam.student;
            student.status = 'completed';
            await this.studentService.updateStudent(student);

            const completedExam = this.currentExam;
            this.currentExam = null;

            this.emit('examCompleted', {
                studentNumber: student.studentNumber,
                examRecord,
                totalDuration: examRecord.totalDuration
            });

            return {
                success: true,
                data: {
                    student,
                    examRecord,
                    isCompleted: true
                }
            };
        } catch (error) {
            console.error('完成考试失败:', error);
            throw error;
        }
    }

    // ==================== 步骤管理 ====================

    /**
     * 开始步骤
     * @param {number} stepNumber - 步骤编号
     */
    async startStep(stepNumber) {
        try {
            const stepInfo = this.getStepInfo(stepNumber);
            if (!stepInfo) {
                throw new Error(`步骤 ${stepNumber} 不存在`);
            }

            // 创建步骤记录
            const stepRecord = {
                examRecordId: this.currentExam.examRecord.id,
                stepNumber,
                stepName: stepInfo.stepName,
                startTime: new Date().toISOString(),
                status: 'in_progress'
            };

            await this.createStepRecord(stepRecord);

            this.emit('stepStarted', {
                stepNumber,
                stepInfo,
                studentNumber: this.currentExam.student.studentNumber
            });
        } catch (error) {
            console.error('开始步骤失败:', error);
            throw error;
        }
    }

    /**
     * 完成步骤
     * @param {number} stepNumber - 步骤编号
     */
    async completeStep(stepNumber) {
        try {
            const stepRecord = {
                stepNumber,
                endTime: new Date().toISOString(),
                status: 'completed'
            };

            await this.updateStepRecord(stepNumber, stepRecord);

            this.emit('stepCompleted', {
                stepNumber,
                studentNumber: this.currentExam.student.studentNumber
            });
        } catch (error) {
            console.error('完成步骤失败:', error);
            throw error;
        }
    }

    /**
     * 获取步骤信息
     * @param {number} stepNumber - 步骤编号
     * @returns {Object} 步骤信息
     */
    getStepInfo(stepNumber) {
        return this.examSteps.find(step => step.stepNumber === stepNumber) || null;
    }

    /**
     * 获取所有步骤信息
     * @returns {Array} 步骤列表
     */
    getAllSteps() {
        return [...this.examSteps];
    }

    // ==================== 考试记录管理 ====================

    /**
     * 创建考试记录
     * @param {Object} student - 学生对象
     * @returns {Promise<Object>} 考试记录
     */
    async createExamRecord(student) {
        try {
            const examRecord = {
                studentId: student.id,
                studentNumber: student.studentNumber,
                startTime: new Date().toISOString(),
                currentStep: 1,
                status: 'in_progress'
            };

            const result = await this.dbService.createExamRecord(examRecord);
            if (!result.success) {
                throw new Error(result.error.message);
            }

            return result.data;
        } catch (error) {
            console.error('创建考试记录失败:', error);
            throw error;
        }
    }

    /**
     * 更新考试记录
     * @param {Object} examRecord - 考试记录
     * @returns {Promise<Object>} 更新后的考试记录
     */
    async updateExamRecord(examRecord) {
        try {
            const result = await this.dbService.updateExamRecord(examRecord.id, examRecord);
            if (!result.success) {
                throw new Error(result.error.message);
            }

            return result.data;
        } catch (error) {
            console.error('更新考试记录失败:', error);
            throw error;
        }
    }

    /**
     * 创建步骤记录
     * @param {Object} stepRecord - 步骤记录
     * @returns {Promise<Object>} 步骤记录
     */
    async createStepRecord(stepRecord) {
        try {
            const result = await this.dbService.createStepRecord(stepRecord);
            if (!result.success) {
                throw new Error(result.error.message);
            }

            return result.data;
        } catch (error) {
            console.error('创建步骤记录失败:', error);
            throw error;
        }
    }

    /**
     * 更新步骤记录
     * @param {number} stepNumber - 步骤编号
     * @param {Object} stepRecord - 步骤记录
     * @returns {Promise<Object>} 更新后的步骤记录
     */
    async updateStepRecord(stepNumber, stepRecord) {
        try {
            const result = await this.dbService.updateStepRecord(stepNumber, stepRecord);
            if (!result.success) {
                throw new Error(result.error.message);
            }

            return result.data;
        } catch (error) {
            console.error('更新步骤记录失败:', error);
            throw error;
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 计算总时长
     * @param {Object} examRecord - 考试记录
     * @returns {number} 总时长（秒）
     */
    calculateTotalDuration(examRecord) {
        if (!examRecord.startTime || !examRecord.endTime) {
            return 0;
        }

        const startTime = new Date(examRecord.startTime);
        const endTime = new Date(examRecord.endTime);
        return Math.floor((endTime - startTime) / 1000);
    }

    /**
     * 获取当前考试
     * @returns {Object|null} 当前考试对象
     */
    getCurrentExam() {
        return this.currentExam;
    }

    /**
     * 获取当前考试状态
     * @returns {Object} 考试状态
     */
    getCurrentExamStatus() {
        if (!this.currentExam) {
            return {
                hasActiveExam: false,
                currentStudent: null,
                currentStep: 0,
                totalSteps: this.examSteps.length
            };
        }

        return {
            hasActiveExam: true,
            currentStudent: this.currentExam.student,
            currentStep: this.currentExam.currentStep,
            totalSteps: this.examSteps.length,
            stepInfo: this.getStepInfo(this.currentExam.currentStep)
        };
    }

    /**
     * 重置考试状态
     */
    resetExamState() {
        this.currentExam = null;
        this.emit('examReset');
    }
}

// 创建全局实例
const examService = new ExamService();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ExamService, examService };
} else {
    window.ExamService = ExamService;
    window.examService = examService;
}
