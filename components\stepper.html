<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤导航组件</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/modern-ui.css">
</head>
<body>
    <!-- 现代化步骤导航系统 -->
    <div class="modern-stepper">
        <!-- 步骤指示器 -->
        <div class="modern-step-indicators">
            <div class="modern-step-indicator active" data-step="1">
                <div class="modern-step-number">1</div>
                <div class="modern-step-title">中文自我介绍</div>
            </div>
            <div class="modern-step-indicator" data-step="2">
                <div class="modern-step-number">2</div>
                <div class="modern-step-title">英文自我介绍</div>
            </div>
            <div class="modern-step-indicator" data-step="3">
                <div class="modern-step-number">3</div>
                <div class="modern-step-title">英文翻译</div>
            </div>
            <div class="modern-step-indicator" data-step="4">
                <div class="modern-step-number">4</div>
                <div class="modern-step-title">专业课问答</div>
            </div>
            <div class="modern-step-indicator" data-step="5">
                <div class="modern-step-number">5</div>
                <div class="modern-step-title">综合面试</div>
            </div>
        </div>
    </div>

    <script>
        class StepperComponent {
            constructor() {
                this.currentStep = 1;
                this.totalSteps = 5;
                this.stepTitles = [
                    '中文自我介绍',
                    '英文自我介绍', 
                    '英文翻译',
                    '专业课问答',
                    '综合面试'
                ];
                this.stepIcons = [
                    'bi-person-circle',
                    'bi-translate',
                    'bi-journal-text',
                    'bi-mortarboard',
                    'bi-chat-dots'
                ];
                
                this.init();
            }
            
            init() {
                this.bindEvents();
                this.updateDisplay();
            }
            
            bindEvents() {
                // 点击步骤指示器切换步骤
                const indicators = document.querySelectorAll('.modern-step-indicator');
                indicators.forEach(indicator => {
                    indicator.addEventListener('click', (e) => {
                        const step = parseInt(indicator.dataset.step);
                        this.goToStep(step);
                    });
                });
            }
            
            goToStep(step) {
                if (step < 1 || step > this.totalSteps) return;
                
                this.currentStep = step;
                this.updateDisplay();
                
                // 触发步骤切换事件
                this.dispatchStepChangeEvent(step);
            }
            
            nextStep() {
                if (this.currentStep < this.totalSteps) {
                    this.goToStep(this.currentStep + 1);
                }
            }
            
            prevStep() {
                if (this.currentStep > 1) {
                    this.goToStep(this.currentStep - 1);
                }
            }
            
            updateDisplay() {
                const indicators = document.querySelectorAll('.modern-step-indicator');
                
                indicators.forEach((indicator, index) => {
                    const stepNumber = index + 1;
                    
                    // 移除所有状态类
                    indicator.classList.remove('active', 'completed', 'disabled');
                    
                    if (stepNumber < this.currentStep) {
                        indicator.classList.add('completed');
                    } else if (stepNumber === this.currentStep) {
                        indicator.classList.add('active');
                    } else {
                        indicator.classList.add('disabled');
                    }
                });
            }
            
            markStepCompleted(step) {
                const indicator = document.querySelector(`[data-step="${step}"]`);
                if (indicator) {
                    indicator.classList.add('completed');
                }
            }
            
            setStepTitle(step, title) {
                const indicator = document.querySelector(`[data-step="${step}"]`);
                if (indicator) {
                    const titleElement = indicator.querySelector('.modern-step-title');
                    if (titleElement) {
                        titleElement.textContent = title;
                    }
                }
            }
            
            getCurrentStep() {
                return this.currentStep;
            }
            
            getCurrentStepTitle() {
                return this.stepTitles[this.currentStep - 1];
            }
            
            dispatchStepChangeEvent(step) {
                const event = new CustomEvent('stepChange', {
                    detail: {
                        currentStep: step,
                        stepTitle: this.stepTitles[step - 1],
                        stepIcon: this.stepIcons[step - 1]
                    }
                });
                document.dispatchEvent(event);
            }
            
            // 禁用/启用步骤
            disableStep(step) {
                const indicator = document.querySelector(`[data-step="${step}"]`);
                if (indicator) {
                    indicator.classList.add('disabled');
                    indicator.style.pointerEvents = 'none';
                }
            }
            
            enableStep(step) {
                const indicator = document.querySelector(`[data-step="${step}"]`);
                if (indicator) {
                    indicator.classList.remove('disabled');
                    indicator.style.pointerEvents = 'auto';
                }
            }
            
            // 设置步骤状态
            setStepStatus(step, status) {
                const indicator = document.querySelector(`[data-step="${step}"]`);
                if (!indicator) return;
                
                // 移除所有状态
                indicator.classList.remove('active', 'completed', 'disabled', 'error');
                
                // 添加新状态
                switch (status) {
                    case 'active':
                        indicator.classList.add('active');
                        break;
                    case 'completed':
                        indicator.classList.add('completed');
                        break;
                    case 'disabled':
                        indicator.classList.add('disabled');
                        break;
                    case 'error':
                        indicator.classList.add('error');
                        break;
                }
            }
        }
        
        // 初始化步骤导航
        const stepperComponent = new StepperComponent();
        
        // 导出到全局作用域
        window.StepperComponent = StepperComponent;
        window.stepperComponent = stepperComponent;
    </script>
</body>
</html>
