/**
 * 学生类 - 管理学生信息和考试状态
 * 遵循单一职责原则，专门负责学生相关的业务逻辑
 */
class Student extends BaseClass {
    constructor(studentNumber = null) {
        super('Student');
        this.studentNumber = studentNumber || null; // 将在initialize中设置
        this.examStartTime = null;
        this.examEndTime = null;
        this.currentStep = 1;
        this.stepHistory = [];
        this.answers = new Map();
        this.timeSpent = new Map();
        this.isExamCompleted = false;
        this.examRecord = null;
    }

    /**
     * 初始化学生
     * @returns {Promise<boolean>} 初始化结果
     */
    async initialize() {
        try {
            // 首先尝试恢复当前正在进行的考试
            const currentExam = await this.getCurrentExam();
            if (currentExam) {
                // 恢复现有考试状态
                this.studentNumber = currentExam.studentNumber;
                this.currentStep = currentExam.currentStep;
                this.translationQuestion = currentExam.translationQuestion || '';
                this.professionalQuestion = currentExam.professionalQuestion || '';
                this.translationQuestionContent = currentExam.translationQuestionContent || '';
                this.professionalQuestionContent = currentExam.professionalQuestionContent || '';

                if (currentExam.examDate) {
                    this.examStartTime = new Date(currentExam.examDate);
                }

                this.log(`恢复学生 ${this.studentNumber} 的考试状态，当前步骤: ${this.currentStep}`);

                // 触发题目恢复事件
                if (this.translationQuestionContent || this.professionalQuestionContent) {
                    document.dispatchEvent(new CustomEvent('questionsRestored', {
                        detail: {
                            studentNumber: this.studentNumber,
                            currentStep: this.currentStep,
                            translationQuestion: this.translationQuestion,
                            professionalQuestion: this.professionalQuestion,
                            translationQuestionContent: this.translationQuestionContent,
                            professionalQuestionContent: this.professionalQuestionContent
                        }
                    }));
                }

                this.initialized = true;
                return true;
            }

            // 如果没有正在进行的考试，生成新的学生编号
            if (!this.studentNumber) {
                this.studentNumber = await this.generateStudentNumber();
            }

            this.log(`新学生 ${this.studentNumber} 初始化成功`);
            this.initialized = true;
            return true;
        } catch (error) {
            this.handleError(error, '学生初始化');
            return false;
        }
    }

    /**
     * 获取当前正在进行的考试
     * @returns {Promise<Object|null>} 当前考试信息或null
     */
    async getCurrentExam() {
        try {
            const response = await fetch('/api/students/current');
            const data = await response.json();

            if (data.success && data.hasCurrentExam) {
                return data.student;
            }
            return null;
        } catch (error) {
            this.log('获取当前考试状态失败', 'error');
            return null;
        }
    }

    /**
     * 生成学生编号（从服务器获取下一个编号）
     * @returns {Promise<string>} 学生编号
     */
    async generateStudentNumber() {
        try {
            const response = await fetch('/api/students/next-number');
            const data = await response.json();

            if (data.success) {
                return data.studentNumber;
            } else {
                this.log('获取学生编号失败，使用默认编号', 'warn');
                return '1';
            }
        } catch (error) {
            this.log('获取学生编号出错，使用默认编号', 'error');
            return '1';
        }
    }

    /**
     * 开始考试
     */
    startExam() {
        this.examStartTime = new Date();
        this.currentStep = 1;
        this.stepHistory = [];
        this.answers.clear();
        this.timeSpent.clear();
        this.isExamCompleted = false;
        
        this.log(`学生 ${this.studentNumber} 开始考试`);
        this.recordStepStart(1);
    }

    /**
     * 结束考试
     */
    async endExam() {
        this.examEndTime = new Date();
        this.isExamCompleted = true;

        this.recordStepEnd(this.currentStep);
        this.generateExamRecord();

        // 更新数据库状态为已完成
        await this.updateExamStatus('已完成');

        this.log(`学生 ${this.studentNumber} 结束考试`);
    }

    /**
     * 进入下一步骤
     * @param {number} stepNumber - 步骤编号
     * @returns {Promise<boolean>} 是否成功进入下一步骤
     */
    async nextStep(stepNumber) {
        if (stepNumber <= this.currentStep) {
            this.log('无法回退到之前的步骤', 'warn');
            return false;
        }

        // 记录当前步骤结束
        this.recordStepEnd(this.currentStep);

        // 进入新步骤
        this.currentStep = stepNumber;
        this.recordStepStart(stepNumber);

        // 保存到数据库
        await this.saveToDatabase();

        this.log(`学生 ${this.studentNumber} 进入步骤 ${stepNumber}`);
        return true;
    }

    /**
     * 记录步骤开始
     * @param {number} stepNumber - 步骤编号
     */
    recordStepStart(stepNumber) {
        const stepRecord = {
            stepNumber: stepNumber,
            startTime: new Date(),
            endTime: null,
            duration: 0
        };
        
        this.stepHistory.push(stepRecord);
    }

    /**
     * 记录步骤结束
     * @param {number} stepNumber - 步骤编号
     */
    recordStepEnd(stepNumber) {
        const stepRecord = this.stepHistory.find(record => 
            record.stepNumber === stepNumber && !record.endTime
        );
        
        if (stepRecord) {
            stepRecord.endTime = new Date();
            stepRecord.duration = stepRecord.endTime - stepRecord.startTime;
            
            // 记录时间消耗
            this.timeSpent.set(stepNumber, Math.floor(stepRecord.duration / 1000));
        }
    }

    /**
     * 保存答案
     * @param {string} questionType - 题目类型
     * @param {string} questionId - 题目ID
     * @param {any} answer - 答案内容
     */
    saveAnswer(questionType, questionId, answer) {
        if (!this.answers.has(questionType)) {
            this.answers.set(questionType, new Map());
        }
        
        this.answers.get(questionType).set(questionId, {
            answer: answer,
            timestamp: new Date(),
            stepNumber: this.currentStep
        });
        
        this.log(`保存 ${questionType} 题目 ${questionId} 的答案`);
    }

    /**
     * 获取答案
     * @param {string} questionType - 题目类型
     * @param {string} questionId - 题目ID
     * @returns {any} 答案内容
     */
    getAnswer(questionType, questionId) {
        const typeAnswers = this.answers.get(questionType);
        if (typeAnswers && typeAnswers.has(questionId)) {
            return typeAnswers.get(questionId).answer;
        }
        return null;
    }

    /**
     * 获取所有答案
     * @returns {Object} 所有答案的对象表示
     */
    getAllAnswers() {
        const allAnswers = {};
        
        this.answers.forEach((typeAnswers, questionType) => {
            allAnswers[questionType] = {};
            typeAnswers.forEach((answerData, questionId) => {
                allAnswers[questionType][questionId] = answerData;
            });
        });
        
        return allAnswers;
    }

    /**
     * 获取步骤用时
     * @param {number} stepNumber - 步骤编号
     * @returns {number} 用时（秒）
     */
    getStepTime(stepNumber) {
        return this.timeSpent.get(stepNumber) || 0;
    }

    /**
     * 获取总用时
     * @returns {number} 总用时（秒）
     */
    getTotalTime() {
        if (this.examStartTime && this.examEndTime) {
            return Math.floor((this.examEndTime - this.examStartTime) / 1000);
        } else if (this.examStartTime) {
            return Math.floor((new Date() - this.examStartTime) / 1000);
        }
        return 0;
    }

    /**
     * 生成考试记录
     */
    generateExamRecord() {
        this.examRecord = {
            studentNumber: this.studentNumber,
            examDate: Utils.formatDate(this.examStartTime),
            startTime: this.examStartTime,
            endTime: this.examEndTime,
            totalTime: this.getTotalTime(),
            stepHistory: [...this.stepHistory],
            answers: this.getAllAnswers(),
            timeSpent: Object.fromEntries(this.timeSpent),
            isCompleted: this.isExamCompleted,
            createdAt: new Date()
        };
    }

    /**
     * 获取考试记录
     * @returns {Object} 考试记录对象
     */
    getExamRecord() {
        if (!this.examRecord) {
            this.generateExamRecord();
        }
        return Utils.deepClone(this.examRecord);
    }

    /**
     * 获取学生状态
     * @returns {Object} 学生状态对象
     */
    getStatus() {
        return {
            studentNumber: this.studentNumber,
            currentStep: this.currentStep,
            isExamStarted: !!this.examStartTime,
            isExamCompleted: this.isExamCompleted,
            totalTime: this.getTotalTime(),
            answersCount: this.getAnswersCount()
        };
    }

    /**
     * 获取答案数量统计
     * @returns {Object} 答案数量统计
     */
    getAnswersCount() {
        const count = {};
        this.answers.forEach((typeAnswers, questionType) => {
            count[questionType] = typeAnswers.size;
        });
        return count;
    }

    /**
     * 验证学生数据
     * @returns {boolean} 验证结果
     */
    validate() {
        if (!Utils.validateInput(this.studentNumber, 'number', { min: 10000000, max: 99999999 })) {
            this.log('学生编号无效', 'error');
            return false;
        }
        
        return true;
    }

    /**
     * 重置学生状态
     */
    reset() {
        this.examStartTime = null;
        this.examEndTime = null;
        this.currentStep = 1;
        this.stepHistory = [];
        this.answers.clear();
        this.timeSpent.clear();
        this.isExamCompleted = false;
        this.examRecord = null;
        
        this.log(`学生 ${this.studentNumber} 状态已重置`);
    }

    /**
     * 导出学生数据
     * @returns {Object} 学生数据对象
     */
    export() {
        return {
            studentNumber: this.studentNumber,
            examStartTime: this.examStartTime,
            examEndTime: this.examEndTime,
            currentStep: this.currentStep,
            stepHistory: [...this.stepHistory],
            answers: this.getAllAnswers(),
            timeSpent: Object.fromEntries(this.timeSpent),
            isExamCompleted: this.isExamCompleted,
            examRecord: this.examRecord
        };
    }

    /**
     * 导入学生数据
     * @param {Object} data - 学生数据对象
     * @returns {boolean} 导入结果
     */
    import(data) {
        try {
            this.studentNumber = data.studentNumber;
            this.examStartTime = data.examStartTime ? new Date(data.examStartTime) : null;
            this.examEndTime = data.examEndTime ? new Date(data.examEndTime) : null;
            this.currentStep = data.currentStep || 1;
            this.stepHistory = data.stepHistory || [];
            this.isExamCompleted = data.isExamCompleted || false;
            this.examRecord = data.examRecord;

            // 重建答案Map
            this.answers.clear();
            if (data.answers) {
                Object.entries(data.answers).forEach(([questionType, typeAnswers]) => {
                    this.answers.set(questionType, new Map(Object.entries(typeAnswers)));
                });
            }

            // 重建时间消耗Map
            this.timeSpent.clear();
            if (data.timeSpent) {
                Object.entries(data.timeSpent).forEach(([stepNumber, time]) => {
                    this.timeSpent.set(parseInt(stepNumber), time);
                });
            }

            this.log(`学生 ${this.studentNumber} 数据导入成功`);
            return true;
        } catch (error) {
            this.handleError(error, '导入学生数据');
            return false;
        }
    }

    /**
     * 从数据库加载考试状态
     * @returns {Promise<boolean>} 加载结果
     */
    async loadFromDatabase() {
        if (!this.studentNumber) {
            return false;
        }

        try {
            const response = await fetch(`/api/students/${this.studentNumber}/status`);
            const data = await response.json();

            if (data.success && data.exists) {
                const student = data.student;
                this.currentStep = student.currentStep || 1;

                // 如果有考试日期，设置为考试开始时间
                if (student.examDate) {
                    this.examStartTime = new Date(student.examDate);
                }

                // 保存题目信息
                this.translationQuestion = student.translationQuestion || '';
                this.professionalQuestion = student.professionalQuestion || '';
                this.translationQuestionContent = student.translationQuestionContent || '';
                this.professionalQuestionContent = student.professionalQuestionContent || '';

                this.log(`从数据库恢复学生 ${this.studentNumber} 状态，当前步骤: ${this.currentStep}`);

                // 如果有题目内容，触发题目恢复事件
                if (this.translationQuestionContent || this.professionalQuestionContent) {
                    this.log('恢复题目内容');
                    // 触发自定义事件通知系统恢复题目显示
                    document.dispatchEvent(new CustomEvent('questionsRestored', {
                        detail: {
                            studentNumber: this.studentNumber,
                            currentStep: this.currentStep,
                            translationQuestion: this.translationQuestion,
                            professionalQuestion: this.professionalQuestion,
                            translationQuestionContent: this.translationQuestionContent,
                            professionalQuestionContent: this.professionalQuestionContent
                        }
                    }));
                }

                return true;
            }

            return false;
        } catch (error) {
            this.log('从数据库加载状态失败', 'error');
            return false;
        }
    }

    /**
     * 保存状态到数据库
     * @returns {Promise<boolean>} 保存结果
     */
    async saveToDatabase() {
        if (!this.studentNumber) {
            this.log('无法保存状态：学生编号为空', 'warn');
            return false;
        }

        try {
            // 获取当前步骤对应的状态名称
            const stepNames = ['中文介绍', '英文介绍', '英文翻译', '专业测试', '综合问答'];
            const status = stepNames[this.currentStep - 1] || '进行中';

            this.log(`正在保存学生 ${this.studentNumber} 状态到数据库，步骤: ${this.currentStep}, 状态: ${status}`);

            const response = await fetch(`/api/students/${this.studentNumber}/step`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    currentStep: this.currentStep,
                    status: status
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.log(`保存学生 ${this.studentNumber} 状态到数据库成功: ${data.message}`);
                return true;
            } else {
                this.log(`保存状态到数据库失败: ${data.error || '未知错误'}`, 'error');
                return false;
            }
        } catch (error) {
            this.log(`保存状态到数据库出错: ${error.message}`, 'error');
            console.error('保存状态详细错误:', error);
            return false;
        }
    }

    /**
     * 保存题目内容到数据库
     * @param {Object} questions - 题目内容对象
     * @returns {Promise<boolean>} 保存结果
     */
    async saveQuestionsToDatabase(questions = {}) {
        if (!this.studentNumber) {
            return false;
        }

        try {
            // 更新本地题目信息
            if (questions.translationQuestion) {
                this.translationQuestion = questions.translationQuestion;
            }
            if (questions.professionalQuestion) {
                this.professionalQuestion = questions.professionalQuestion;
            }
            if (questions.translationQuestionContent) {
                this.translationQuestionContent = questions.translationQuestionContent;
            }
            if (questions.professionalQuestionContent) {
                this.professionalQuestionContent = questions.professionalQuestionContent;
            }

            const response = await fetch(`/api/students/${this.studentNumber}/questions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    translationQuestion: this.translationQuestion || '',
                    professionalQuestion: this.professionalQuestion || '',
                    translationQuestionContent: this.translationQuestionContent || '',
                    professionalQuestionContent: this.professionalQuestionContent || ''
                })
            });

            const data = await response.json();

            if (data.success) {
                this.log(`保存学生 ${this.studentNumber} 题目内容到数据库成功`);
                return true;
            } else {
                this.log('保存题目内容到数据库失败', 'error');
                return false;
            }
        } catch (error) {
            this.log('保存题目内容到数据库出错', 'error');
            return false;
        }
    }

    /**
     * 更新考试状态
     * @param {string} status - 新的考试状态
     * @returns {Promise<boolean>} 更新结果
     */
    async updateExamStatus(status) {
        if (!this.studentNumber) {
            return false;
        }

        try {
            this.log(`正在更新学生 ${this.studentNumber} 考试状态为: ${status}`);

            const response = await fetch(`/api/students/${this.studentNumber}/step`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    currentStep: this.currentStep,
                    status: status
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.log(`更新学生 ${this.studentNumber} 考试状态成功: ${data.message}`);
                return true;
            } else {
                this.log(`更新考试状态失败: ${data.error || '未知错误'}`, 'error');
                return false;
            }
        } catch (error) {
            this.log('更新考试状态出错', 'error');
            return false;
        }
    }
}

// 导出到全局作用域
window.Student = Student;
