/**
 * ExamController - 考试流程控制器
 * 负责考试生命周期管理、步骤流转控制、状态同步协调
 */

class ExamController extends BaseController {
    constructor() {
        super();
        
        // 依赖的服务
        this.studentService = null;
        this.questionService = null;
        this.settingsService = null;
        
        // 控制器状态
        this.state = {
            currentStudent: null,
            currentStep: 1,
            totalSteps: 5,
            isExamActive: false,
            isTimerRunning: false,
            examStartTime: null,
            stepStartTime: null,
            examStatus: 'idle', // idle, active, paused, completed
            stepStatus: 'pending' // pending, active, completed
        };

        // 步骤定义
        this.stepDefinitions = {
            1: { name: '中文自我介绍', hasQuestions: false, defaultTime: 2 },
            2: { name: '英文自我介绍', hasQuestions: false, defaultTime: 2 },
            3: { name: '英文翻译', hasQuestions: true, questionType: 'translation', defaultTime: 5 },
            4: { name: '专业课问答', hasQuestions: true, questionType: 'professional', defaultTime: 10 },
            5: { name: '综合面试', hasQuestions: false, defaultTime: 8 }
        };
    }

    /**
     * 初始化控制器
     */
    async onInitialize() {
        // 获取服务实例
        this.studentService = window.studentService;
        this.questionService = window.questionService;
        this.settingsService = window.settingsService;

        if (!this.studentService || !this.questionService) {
            throw new Error('ExamController 依赖的服务未找到');
        }

        // 监听服务事件
        this.setupServiceListeners();

        // 监听全局事件
        this.setupGlobalListeners();

        this.log('info', 'ExamController 初始化完成');
    }

    /**
     * 设置服务监听器
     */
    setupServiceListeners() {
        // 监听学生服务事件
        this.studentService.on('currentStudentChanged', (student) => {
            this.handleStudentChanged(student);
        });

        this.studentService.on('stepChanged', (data) => {
            this.handleStepChanged(data);
        });

        this.studentService.on('examStarted', (student) => {
            this.handleExamStarted(student);
        });

        this.studentService.on('examCompleted', (student) => {
            this.handleExamCompleted(student);
        });

        // 监听题目服务事件
        this.questionService.on('questionDrawn', (data) => {
            this.handleQuestionDrawn(data);
        });
    }

    /**
     * 设置全局监听器
     */
    setupGlobalListeners() {
        // 监听计时器事件
        this.listenGlobal('TimerController.started', (data) => {
            this.updateState({ isTimerRunning: true, stepStartTime: new Date().toISOString() });
        });

        this.listenGlobal('TimerController.stopped', (data) => {
            this.updateState({ isTimerRunning: false });
        });

        this.listenGlobal('TimerController.timeUp', (data) => {
            this.handleTimeUp(data);
        });

        // 监听UI事件
        this.listenGlobal('UI.nextStepRequested', () => {
            this.nextStep();
        });

        this.listenGlobal('UI.previousStepRequested', () => {
            this.previousStep();
        });

        this.listenGlobal('UI.studentSwitchRequested', (data) => {
            this.switchStudent(data.studentNumber);
        });
    }

    // ==================== 考试初始化 ====================

    /**
     * 初始化考试
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Object>} 初始化结果
     */
    async initializeExam(studentNumber) {
        return this.executeOperation(async () => {
            // 1. 验证学生编号
            if (!studentNumber || studentNumber.trim() === '') {
                throw new Error('学生编号不能为空');
            }

            // 2. 创建或获取学生
            const student = await this.studentService.getOrCreateStudent(studentNumber);

            // 3. 初始化考试状态
            this.updateState({
                currentStudent: student,
                currentStep: student.currentStep,
                examStatus: student.status === 'completed' ? 'completed' : 'idle'
            });

            // 4. 加载系统设置
            await this.loadSystemSettings();

            // 5. 准备UI界面
            this.emit('examInitialized', {
                student,
                currentStep: student.currentStep,
                stepInfo: this.getStepInfo(student.currentStep)
            });

            return {
                student,
                currentStep: student.currentStep,
                isCompleted: student.isExamCompleted()
            };
        }, 'initializeExam');
    }

    /**
     * 开始考试
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Object>} 开始结果
     */
    async startExam(studentNumber) {
        return this.executeOperation(async () => {
            // 1. 检查考试前置条件
            const conditions = [
                () => studentNumber && studentNumber.trim() !== '',
                () => !this.state.isExamActive || this.state.currentStudent?.studentNumber === studentNumber
            ];

            if (!this.validateConditions(conditions, 'startExam')) {
                throw new Error('考试开始条件不满足');
            }

            // 2. 开始考试
            const student = await this.studentService.startExam(studentNumber);

            // 3. 设置当前学生
            await this.studentService.setCurrentStudent(studentNumber);

            // 4. 更新状态
            this.updateState({
                currentStudent: student,
                currentStep: student.currentStep,
                isExamActive: true,
                examStatus: 'active',
                examStartTime: student.startTime
            });

            // 5. 进入第一步
            await this.enterStep(student.currentStep);

            this.emit('examStarted', { student, currentStep: student.currentStep });

            return student;
        }, 'startExam');
    }

    // ==================== 步骤管理 ====================

    /**
     * 切换到下一步
     * @returns {Promise<Object>} 切换结果
     */
    async nextStep() {
        return this.executeOperation(async () => {
            const currentStudent = this.state.currentStudent;
            if (!currentStudent) {
                throw new Error('没有当前学生');
            }

            // 验证切换条件
            const conditions = [
                () => this.state.isExamActive,
                () => currentStudent.canGoToNextStep(),
                () => this.state.currentStep < this.state.totalSteps
            ];

            if (!this.validateConditions(conditions, 'nextStep')) {
                throw new Error('无法进入下一步');
            }

            // 完成当前步骤
            await this.completeCurrentStep();

            // 进入下一步
            const nextStepNumber = this.state.currentStep + 1;
            await this.changeStep(nextStepNumber);

            return {
                previousStep: this.state.currentStep,
                currentStep: nextStepNumber,
                student: currentStudent
            };
        }, 'nextStep');
    }

    /**
     * 返回上一步
     * @returns {Promise<Object>} 切换结果
     */
    async previousStep() {
        return this.executeOperation(async () => {
            const currentStudent = this.state.currentStudent;
            if (!currentStudent) {
                throw new Error('没有当前学生');
            }

            // 验证切换条件
            const conditions = [
                () => this.state.isExamActive,
                () => currentStudent.canGoToPreviousStep(),
                () => this.state.currentStep > 1
            ];

            if (!this.validateConditions(conditions, 'previousStep')) {
                throw new Error('无法返回上一步');
            }

            // 返回上一步
            const previousStepNumber = this.state.currentStep - 1;
            await this.changeStep(previousStepNumber);

            return {
                previousStep: this.state.currentStep,
                currentStep: previousStepNumber,
                student: currentStudent
            };
        }, 'previousStep');
    }

    /**
     * 切换步骤
     * @param {number} stepNumber - 目标步骤编号
     * @returns {Promise<void>}
     */
    async changeStep(stepNumber) {
        const previousStep = this.state.currentStep;

        // 1. 保存当前步骤数据
        if (this.state.currentStudent) {
            await this.saveCurrentStepData();
        }

        // 2. 停止当前计时器
        this.emit('stopTimer');

        // 3. 更新学生状态
        if (this.state.currentStudent) {
            await this.studentService.saveProgress(
                this.state.currentStudent.studentNumber,
                stepNumber,
                { status: 'active' }
            );
        }

        // 4. 更新控制器状态
        this.updateState({
            currentStep: stepNumber,
            stepStatus: 'active'
        });

        // 5. 进入目标步骤
        await this.enterStep(stepNumber);

        this.emit('stepChanged', {
            previousStep,
            currentStep: stepNumber,
            student: this.state.currentStudent
        });
    }

    /**
     * 进入指定步骤
     * @param {number} stepNumber - 步骤编号
     * @returns {Promise<void>}
     */
    async enterStep(stepNumber) {
        const stepInfo = this.getStepInfo(stepNumber);

        // 1. 更新UI显示
        this.emit('stepEntered', {
            stepNumber,
            stepInfo,
            student: this.state.currentStudent
        });

        // 2. 如果步骤有题目，准备题目界面
        if (stepInfo.hasQuestions) {
            this.emit('prepareQuestionInterface', {
                questionType: stepInfo.questionType,
                stepNumber
            });
        }

        // 3. 重置计时器
        this.emit('resetTimer', {
            duration: stepInfo.timeLimit * 60, // 转换为秒
            stepNumber
        });

        this.log('info', `进入步骤 ${stepNumber}: ${stepInfo.name}`);
    }

    /**
     * 完成当前步骤
     * @returns {Promise<void>}
     */
    async completeCurrentStep() {
        const currentStep = this.state.currentStep;
        const currentStudent = this.state.currentStudent;

        if (!currentStudent) {
            return;
        }

        // 保存步骤完成数据
        await this.studentService.saveProgress(
            currentStudent.studentNumber,
            currentStep,
            {
                endTime: new Date().toISOString(),
                status: 'completed'
            }
        );

        this.emit('stepCompleted', {
            stepNumber: currentStep,
            student: currentStudent
        });
    }

    /**
     * 完成考试
     * @returns {Promise<Object>} 完成结果
     */
    async completeExam() {
        return this.executeOperation(async () => {
            const currentStudent = this.state.currentStudent;
            if (!currentStudent) {
                throw new Error('没有当前学生');
            }

            // 1. 完成最后步骤
            await this.completeCurrentStep();

            // 2. 停止所有计时器
            this.emit('stopAllTimers');

            // 3. 完成考试
            await this.studentService.completeExam(currentStudent.studentNumber);

            // 4. 更新状态
            this.updateState({
                isExamActive: false,
                examStatus: 'completed',
                stepStatus: 'completed'
            });

            this.emit('examCompleted', { student: currentStudent });

            return currentStudent;
        }, 'completeExam');
    }

    // ==================== 学生管理 ====================

    /**
     * 切换学生
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Object>} 切换结果
     */
    async switchStudent(studentNumber) {
        return this.executeOperation(async () => {
            // 1. 保存当前学生数据
            if (this.state.currentStudent) {
                await this.saveCurrentStepData();
            }

            // 2. 停止当前计时器
            this.emit('stopTimer');

            // 3. 切换到新学生
            const newStudent = await this.studentService.setCurrentStudent(studentNumber);

            // 4. 恢复考试状态
            await this.restoreExamState(newStudent);

            this.emit('studentSwitched', {
                previousStudent: this.state.currentStudent,
                currentStudent: newStudent
            });

            return newStudent;
        }, 'switchStudent');
    }

    /**
     * 恢复考试状态
     * @param {Student} student - 学生对象
     * @returns {Promise<void>}
     */
    async restoreExamState(student) {
        this.updateState({
            currentStudent: student,
            currentStep: student.currentStep,
            isExamActive: student.status === 'active',
            examStatus: student.status,
            examStartTime: student.startTime
        });

        // 恢复到当前步骤
        await this.enterStep(student.currentStep);
    }

    // ==================== 事件处理 ====================

    /**
     * 处理学生变更
     * @param {Student} student - 学生对象
     */
    handleStudentChanged(student) {
        this.updateState({ currentStudent: student });
        this.emit('currentStudentUpdated', student);
    }

    /**
     * 处理步骤变更
     * @param {Object} data - 步骤变更数据
     */
    handleStepChanged(data) {
        this.updateState({
            currentStep: data.currentStep,
            currentStudent: data.student
        });
    }

    /**
     * 处理考试开始
     * @param {Student} student - 学生对象
     */
    handleExamStarted(student) {
        this.updateState({
            isExamActive: true,
            examStatus: 'active',
            examStartTime: student.startTime
        });
    }

    /**
     * 处理考试完成
     * @param {Student} student - 学生对象
     */
    handleExamCompleted(student) {
        this.updateState({
            isExamActive: false,
            examStatus: 'completed'
        });
    }

    /**
     * 处理题目抽取
     * @param {Object} data - 题目抽取数据
     */
    handleQuestionDrawn(data) {
        // 自动开始计时
        this.emit('startTimer', {
            stepNumber: this.state.currentStep,
            questionType: data.type
        });
    }

    /**
     * 处理时间到达
     * @param {Object} data - 时间数据
     */
    handleTimeUp(data) {
        this.emit('timeUpWarning', {
            stepNumber: this.state.currentStep,
            student: this.state.currentStudent
        });
    }

    // ==================== 工具方法 ====================

    /**
     * 获取步骤信息
     * @param {number} stepNumber - 步骤编号
     * @returns {Object} 步骤信息
     */
    getStepInfo(stepNumber) {
        const definition = this.stepDefinitions[stepNumber];
        if (!definition) {
            throw new Error(`无效的步骤编号: ${stepNumber}`);
        }

        return {
            stepNumber,
            name: definition.name,
            hasQuestions: definition.hasQuestions,
            questionType: definition.questionType,
            timeLimit: definition.defaultTime,
            isFirst: stepNumber === 1,
            isLast: stepNumber === this.state.totalSteps
        };
    }

    /**
     * 获取考试状态
     * @returns {Object} 考试状态
     */
    getExamStatus() {
        return {
            currentStudent: this.state.currentStudent,
            currentStep: this.state.currentStep,
            totalSteps: this.state.totalSteps,
            isExamActive: this.state.isExamActive,
            isTimerRunning: this.state.isTimerRunning,
            examStatus: this.state.examStatus,
            stepStatus: this.state.stepStatus,
            stepInfo: this.getStepInfo(this.state.currentStep)
        };
    }

    /**
     * 保存当前步骤数据
     * @returns {Promise<void>}
     */
    async saveCurrentStepData() {
        if (!this.state.currentStudent) {
            return;
        }

        const stepData = {
            stepNumber: this.state.currentStep,
            status: this.state.stepStatus,
            lastSaved: new Date().toISOString()
        };

        await this.studentService.saveProgress(
            this.state.currentStudent.studentNumber,
            this.state.currentStep,
            stepData
        );
    }

    /**
     * 加载系统设置
     * @returns {Promise<void>}
     */
    async loadSystemSettings() {
        if (this.settingsService) {
            try {
                const settings = await this.settingsService.getSettings();
                // 更新步骤时间设置
                Object.keys(this.stepDefinitions).forEach(stepNum => {
                    const stepNumber = parseInt(stepNum);
                    const timeKey = this.getTimeSettingKey(stepNumber);
                    if (settings.timeSettings[timeKey]) {
                        this.stepDefinitions[stepNumber].defaultTime = settings.timeSettings[timeKey];
                    }
                });
            } catch (error) {
                this.log('warn', '加载系统设置失败，使用默认设置', error);
            }
        }
    }

    /**
     * 获取时间设置键名
     * @param {number} stepNumber - 步骤编号
     * @returns {string} 设置键名
     */
    getTimeSettingKey(stepNumber) {
        const keyMap = {
            1: 'chineseTime',
            2: 'englishTime',
            3: 'translationTime',
            4: 'professionalTime',
            5: 'comprehensiveTime'
        };
        return keyMap[stepNumber];
    }

    /**
     * 销毁控制器
     */
    onDestroy() {
        // 保存当前状态
        if (this.state.currentStudent) {
            this.saveCurrentStepData().catch(error => {
                console.error('保存步骤数据失败:', error);
            });
        }

        // 停止计时器
        this.emit('stopAllTimers');

        this.log('info', 'ExamController 已销毁');
    }
}

// 导出ExamController类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExamController;
} else {
    window.ExamController = ExamController;
}
