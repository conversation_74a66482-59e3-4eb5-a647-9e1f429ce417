<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目网格组件</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/modern-ui.css">
</head>
<body>
    <!-- 现代化题目网格展示区域 -->
    <div class="modern-question-grid-container">
        <div id="question-grid" class="modern-question-grid">
            <!-- 题号将动态生成 -->
        </div>
    </div>

    <!-- 题目显示区域 -->
    <div class="modern-question-container mt-3">
        <div id="question-container">
            <div class="modern-question-placeholder">
                <div class="modern-question-placeholder-icon">
                    <i class="bi bi-file-text"></i>
                </div>
                <div class="modern-question-placeholder-text">题目将在这里显示</div>
                <div class="modern-question-placeholder-hint">请先抽取题目</div>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div id="question-loading" class="d-none modern-loading">
        <div class="modern-loading-spinner"></div>
        <span>正在抽题...</span>
    </div>

    <script>
        class QuestionGridComponent {
            constructor(containerId = 'question-grid') {
                this.container = document.getElementById(containerId);
                this.questionContainer = document.getElementById('question-container');
                this.loadingElement = document.getElementById('question-loading');
                this.currentQuestion = null;
                this.selectedNumber = null;
                this.questions = [];
                
                this.init();
            }
            
            init() {
                // 初始化题目网格
                this.generateGrid();
            }
            
            generateGrid(count = 20) {
                if (!this.container) return;
                
                this.container.innerHTML = '';
                
                for (let i = 1; i <= count; i++) {
                    const gridItem = document.createElement('div');
                    gridItem.className = 'modern-question-grid-item';
                    gridItem.textContent = i;
                    gridItem.dataset.number = i;
                    
                    // 添加点击事件
                    gridItem.addEventListener('click', () => {
                        this.selectQuestion(i);
                    });
                    
                    this.container.appendChild(gridItem);
                }
            }
            
            selectQuestion(number) {
                // 移除之前的选中状态
                const prevSelected = this.container.querySelector('.selected');
                if (prevSelected) {
                    prevSelected.classList.remove('selected');
                }
                
                // 添加新的选中状态
                const newSelected = this.container.querySelector(`[data-number="${number}"]`);
                if (newSelected) {
                    newSelected.classList.add('selected');
                    this.selectedNumber = number;
                    
                    // 触发选择事件
                    this.dispatchSelectionEvent(number);
                }
            }
            
            async drawRandomQuestion(questionType = 'translation') {
                this.showLoading();
                
                try {
                    // 随机选择一个题号
                    const randomNumber = Math.floor(Math.random() * 20) + 1;
                    this.selectQuestion(randomNumber);
                    
                    // 模拟加载延迟
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    // 获取题目内容
                    const question = await this.fetchQuestion(questionType, randomNumber);
                    this.displayQuestion(question);
                    
                } catch (error) {
                    console.error('抽题失败:', error);
                    this.showError('抽题失败，请重试');
                } finally {
                    this.hideLoading();
                }
            }
            
            async fetchQuestion(type, number) {
                // 这里应该调用实际的数据库API
                // 暂时返回模拟数据
                const mockQuestions = {
                    translation: {
                        1: "The rapid development of artificial intelligence has transformed various industries and continues to shape our future.",
                        2: "Climate change represents one of the most pressing challenges facing humanity in the 21st century.",
                        3: "Education plays a crucial role in fostering innovation and preparing students for the digital age."
                    },
                    professional: {
                        1: "请解释机器学习中监督学习和无监督学习的区别，并举例说明。",
                        2: "什么是数据结构？请比较数组和链表的优缺点。",
                        3: "请描述软件开发生命周期的主要阶段及其重要性。"
                    }
                };
                
                return {
                    id: number,
                    type: type,
                    content: mockQuestions[type][number] || `${type}题目 ${number}`,
                    difficulty: Math.floor(Math.random() * 3) + 1 // 1-3难度等级
                };
            }
            
            displayQuestion(question) {
                if (!this.questionContainer) return;
                
                this.currentQuestion = question;
                
                const questionHtml = `
                    <div class="modern-question-display">
                        <div class="modern-question-header">
                            <div class="modern-question-meta">
                                <span class="modern-question-number">题目 ${question.id}</span>
                                <span class="modern-question-difficulty difficulty-${question.difficulty}">
                                    ${'★'.repeat(question.difficulty)}
                                </span>
                            </div>
                        </div>
                        <div class="modern-question-content">
                            <p>${question.content}</p>
                        </div>
                    </div>
                `;
                
                this.questionContainer.innerHTML = questionHtml;
                
                // 添加显示动画
                this.questionContainer.classList.add('question-fade-in');
                setTimeout(() => {
                    this.questionContainer.classList.remove('question-fade-in');
                }, 500);
            }
            
            showLoading() {
                if (this.loadingElement) {
                    this.loadingElement.classList.remove('d-none');
                }
            }
            
            hideLoading() {
                if (this.loadingElement) {
                    this.loadingElement.classList.add('d-none');
                }
            }
            
            showError(message) {
                if (!this.questionContainer) return;
                
                this.questionContainer.innerHTML = `
                    <div class="modern-question-error">
                        <div class="modern-question-error-icon">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="modern-question-error-text">${message}</div>
                    </div>
                `;
            }
            
            reset() {
                // 重置选中状态
                const selected = this.container.querySelector('.selected');
                if (selected) {
                    selected.classList.remove('selected');
                }
                
                // 重置题目显示
                if (this.questionContainer) {
                    this.questionContainer.innerHTML = `
                        <div class="modern-question-placeholder">
                            <div class="modern-question-placeholder-icon">
                                <i class="bi bi-file-text"></i>
                            </div>
                            <div class="modern-question-placeholder-text">题目将在这里显示</div>
                            <div class="modern-question-placeholder-hint">请先抽取题目</div>
                        </div>
                    `;
                }
                
                this.currentQuestion = null;
                this.selectedNumber = null;
            }
            
            getCurrentQuestion() {
                return this.currentQuestion;
            }
            
            getSelectedNumber() {
                return this.selectedNumber;
            }
            
            dispatchSelectionEvent(number) {
                const event = new CustomEvent('questionSelected', {
                    detail: {
                        number: number,
                        question: this.currentQuestion
                    }
                });
                document.dispatchEvent(event);
            }
        }
        
        // 初始化题目网格组件
        const questionGridComponent = new QuestionGridComponent();
        
        // 导出到全局作用域
        window.QuestionGridComponent = QuestionGridComponent;
        window.questionGridComponent = questionGridComponent;
    </script>
</body>
</html>
