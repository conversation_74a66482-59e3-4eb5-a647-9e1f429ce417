/**
 * ExamRecord Model - 考试记录数据模型
 * 负责考试记录相关的数据结构定义和基础操作
 */

class ExamRecord {
    /**
     * 构造函数
     * @param {Object} data - 考试记录数据
     */
    constructor(data = {}) {
        this.id = data.id || null;
        this.studentId = data.studentId || data.student_id || null;
        this.studentNumber = data.studentNumber || data.student_number || '';
        this.startTime = data.startTime || data.start_time || null;
        this.endTime = data.endTime || data.end_time || null;
        this.currentStep = data.currentStep || data.current_step || 1;
        this.totalDuration = data.totalDuration || data.total_duration || 0;
        this.status = data.status || 'in_progress';
        this.stepRecords = data.stepRecords || data.step_records || [];
        this.createdAt = data.createdAt || data.created_at || null;
        this.updatedAt = data.updatedAt || data.updated_at || null;

        // 确保stepRecords是数组格式
        if (typeof this.stepRecords === 'string') {
            try {
                this.stepRecords = JSON.parse(this.stepRecords);
            } catch (e) {
                this.stepRecords = [];
            }
        }

        // 初始化步骤记录
        this.initializeStepRecords();
    }

    /**
     * 初始化步骤记录
     */
    initializeStepRecords() {
        const stepNames = {
            1: '中文自我介绍',
            2: '英文自我介绍',
            3: '英文翻译',
            4: '专业课问答',
            5: '综合面试'
        };

        // 确保所有步骤都有记录
        for (let i = 1; i <= 5; i++) {
            const existingRecord = this.stepRecords.find(r => r.stepNumber === i);
            if (!existingRecord) {
                this.stepRecords.push({
                    stepNumber: i,
                    stepName: stepNames[i],
                    startTime: null,
                    endTime: null,
                    duration: 0,
                    questions: [],
                    status: 'pending'
                });
            }
        }

        // 按步骤编号排序
        this.stepRecords.sort((a, b) => a.stepNumber - b.stepNumber);
    }

    /**
     * 获取指定步骤记录
     * @param {number} stepNumber - 步骤编号
     * @returns {Object|null} 步骤记录
     */
    getStepRecord(stepNumber) {
        return this.stepRecords.find(r => r.stepNumber === stepNumber) || null;
    }

    /**
     * 更新步骤记录
     * @param {number} stepNumber - 步骤编号
     * @param {Object} data - 步骤数据
     */
    updateStepRecord(stepNumber, data) {
        const record = this.getStepRecord(stepNumber);
        if (record) {
            Object.assign(record, data, {
                updatedAt: new Date().toISOString()
            });
        }
    }

    /**
     * 开始步骤
     * @param {number} stepNumber - 步骤编号
     */
    startStep(stepNumber) {
        this.currentStep = stepNumber;
        this.updateStepRecord(stepNumber, {
            startTime: new Date().toISOString(),
            status: 'in_progress'
        });
    }

    /**
     * 完成步骤
     * @param {number} stepNumber - 步骤编号
     * @param {Array} questions - 题目数据
     */
    completeStep(stepNumber, questions = []) {
        const record = this.getStepRecord(stepNumber);
        if (record && record.startTime) {
            const endTime = new Date().toISOString();
            const duration = Math.floor(
                (new Date(endTime) - new Date(record.startTime)) / 1000
            );

            this.updateStepRecord(stepNumber, {
                endTime,
                duration,
                questions,
                status: 'completed'
            });

            // 更新总用时
            this.updateTotalDuration();
        }
    }

    /**
     * 更新总用时
     */
    updateTotalDuration() {
        this.totalDuration = this.stepRecords.reduce((total, record) => {
            return total + (record.duration || 0);
        }, 0);
    }

    /**
     * 获取已完成步骤数
     * @returns {number} 已完成步骤数
     */
    getCompletedStepsCount() {
        return this.stepRecords.filter(r => r.status === 'completed').length;
    }

    /**
     * 获取进行中的步骤数
     * @returns {number} 进行中的步骤数
     */
    getInProgressStepsCount() {
        return this.stepRecords.filter(r => r.status === 'in_progress').length;
    }

    /**
     * 获取总用时（格式化）
     * @returns {string} 格式化的总用时
     */
    getFormattedTotalDuration() {
        return this.formatDuration(this.totalDuration);
    }

    /**
     * 获取平均每步用时
     * @returns {number} 平均每步用时（秒）
     */
    getAverageStepDuration() {
        const completedSteps = this.getCompletedStepsCount();
        return completedSteps > 0 ? Math.round(this.totalDuration / completedSteps) : 0;
    }

    /**
     * 获取平均每步用时（格式化）
     * @returns {string} 格式化的平均每步用时
     */
    getFormattedAverageStepDuration() {
        return this.formatDuration(this.getAverageStepDuration());
    }

    /**
     * 格式化时长
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时长
     */
    formatDuration(seconds) {
        if (!seconds || seconds < 0) return '00:00';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }

    /**
     * 检查考试是否完成
     * @returns {boolean} 考试是否完成
     */
    isCompleted() {
        return this.status === 'completed' || this.getCompletedStepsCount() === 5;
    }

    /**
     * 检查考试是否进行中
     * @returns {boolean} 考试是否进行中
     */
    isInProgress() {
        return this.status === 'in_progress' && this.getCompletedStepsCount() < 5;
    }

    /**
     * 完成考试
     */
    completeExam() {
        this.status = 'completed';
        this.endTime = new Date().toISOString();
        this.updateTotalDuration();
    }

    /**
     * 取消考试
     */
    cancelExam() {
        this.status = 'cancelled';
        this.endTime = new Date().toISOString();
    }

    /**
     * 获取考试进度百分比
     * @returns {number} 进度百分比 (0-100)
     */
    getProgressPercentage() {
        return Math.round((this.getCompletedStepsCount() / 5) * 100);
    }

    /**
     * 获取考试状态显示文本
     * @returns {string} 状态显示文本
     */
    getStatusText() {
        const statusMap = {
            'in_progress': '进行中',
            'completed': '已完成',
            'cancelled': '已取消',
            'paused': '已暂停'
        };
        return statusMap[this.status] || '未知';
    }

    /**
     * 获取考试状态CSS类
     * @returns {string} CSS类名
     */
    getStatusClass() {
        const classMap = {
            'in_progress': 'status-in-progress',
            'completed': 'status-completed',
            'cancelled': 'status-cancelled',
            'paused': 'status-paused'
        };
        return classMap[this.status] || 'status-unknown';
    }

    /**
     * 获取所有使用的题目
     * @returns {Array} 题目数组
     */
    getAllQuestions() {
        const questions = [];
        this.stepRecords.forEach(record => {
            if (record.questions && Array.isArray(record.questions)) {
                questions.push(...record.questions);
            }
        });
        return questions;
    }

    /**
     * 获取指定类型的题目
     * @param {string} type - 题目类型
     * @returns {Array} 题目数组
     */
    getQuestionsByType(type) {
        return this.getAllQuestions().filter(q => q.type === type);
    }

    /**
     * 转换为数据库格式
     * @returns {Object} 数据库格式的数据
     */
    toDatabase() {
        return {
            id: this.id,
            student_id: this.studentId,
            student_number: this.studentNumber,
            start_time: this.startTime,
            end_time: this.endTime,
            current_step: this.currentStep,
            total_duration: this.totalDuration,
            status: this.status,
            step_records: JSON.stringify(this.stepRecords),
            updated_at: new Date().toISOString()
        };
    }

    /**
     * 转换为API格式
     * @returns {Object} API格式的数据
     */
    toAPI() {
        return {
            id: this.id,
            studentId: this.studentId,
            studentNumber: this.studentNumber,
            startTime: this.startTime,
            endTime: this.endTime,
            currentStep: this.currentStep,
            totalDuration: this.totalDuration,
            formattedTotalDuration: this.getFormattedTotalDuration(),
            averageStepDuration: this.getAverageStepDuration(),
            formattedAverageStepDuration: this.getFormattedAverageStepDuration(),
            status: this.status,
            statusText: this.getStatusText(),
            statusClass: this.getStatusClass(),
            stepRecords: this.stepRecords,
            completedStepsCount: this.getCompletedStepsCount(),
            inProgressStepsCount: this.getInProgressStepsCount(),
            progressPercentage: this.getProgressPercentage(),
            isCompleted: this.isCompleted(),
            isInProgress: this.isInProgress(),
            allQuestions: this.getAllQuestions(),
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    /**
     * 验证考试记录数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];

        if (!this.studentNumber || this.studentNumber.trim() === '') {
            errors.push('学生编号不能为空');
        }

        if (!this.startTime) {
            errors.push('开始时间不能为空');
        }

        if (this.currentStep < 1 || this.currentStep > 5) {
            errors.push('当前步骤必须在1-5之间');
        }

        if (!['in_progress', 'completed', 'cancelled', 'paused'].includes(this.status)) {
            errors.push('考试状态无效');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 克隆考试记录对象
     * @returns {ExamRecord} 克隆的考试记录对象
     */
    clone() {
        return new ExamRecord(this.toAPI());
    }

    /**
     * 从数据库数据创建ExamRecord实例
     * @param {Object} dbData - 数据库数据
     * @returns {ExamRecord} ExamRecord实例
     */
    static fromDatabase(dbData) {
        return new ExamRecord(dbData);
    }

    /**
     * 从API数据创建ExamRecord实例
     * @param {Object} apiData - API数据
     * @returns {ExamRecord} ExamRecord实例
     */
    static fromAPI(apiData) {
        return new ExamRecord(apiData);
    }

    /**
     * 从学生数据创建考试记录
     * @param {Student} student - 学生对象
     * @returns {ExamRecord} 考试记录实例
     */
    static fromStudent(student) {
        return new ExamRecord({
            studentId: student.id,
            studentNumber: student.studentNumber,
            startTime: student.startTime || new Date().toISOString(),
            currentStep: student.currentStep,
            status: 'in_progress'
        });
    }
}

// 导出ExamRecord类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExamRecord;
} else {
    window.ExamRecord = ExamRecord;
}
