---
type: "always_apply"
---

一、面向对象设计原则（SOLID）
单一职责原则（SRP）
一个类应该只有一个引起变化的原因。
目的：高内聚，避免一个类承担过多职责。
示例：
反例：UserManager类同时处理用户认证、数据存储和邮件通知。
正例：拆分为UserAuthenticator、UserRepository和EmailService。
开闭原则（OCP）
软件实体（类、模块、函数）应该对扩展开放，对修改关闭。
目的：通过抽象和多态减少修改现有代码的风险。
实现：使用接口、抽象类和策略模式。
示例：通过PaymentStrategy接口支持多种支付方式，新增方式时无需修改原有代码。
里氏替换原则（LSP）
子类可以替换其父类而不影响程序的正确性。
目的：确保继承关系的正确性，避免子类破坏父类的契约。
示例：
反例：Square类继承Rectangle但不允许宽高不同，导致父类方法失效。
正例：使用Shape抽象类，Square和Rectangle作为独立子类。
接口隔离原则（ISP）
客户端不应该依赖它不需要的接口。
目的：细粒度接口，避免类实现不必要的方法。
示例：
反例：Worker接口包含work()和eat()，导致RobotWorker必须实现eat()。
正例：拆分为Workable和Eatable接口。
依赖倒置原则（DIP）
高层模块不应该依赖低层模块，两者都应该依赖抽象。
目的：通过抽象解耦，提高可测试性和灵活性。
实现：依赖注入（DI）、控制反转（IoC）。
示例：OrderService依赖PaymentProcessor接口而非具体实现。
二、通用设计原则
KISS 原则（Keep It Simple, Stupid）
保持简单，避免过度设计。
反例：为简单需求引入复杂的框架或模式。
正例：优先使用简单方案，仅在必要时增加复杂度。
DRY 原则（Don't Repeat Yourself）
消除重复代码，提取公共逻辑。
实现：通过函数、类、继承或组合复用代码。
反例：多个地方复制粘贴相同的验证逻辑。
YAGNI 原则（You Aren't Gonna Need It）
不要实现当前不需要的功能。
目的：避免过度工程，保持代码简洁。
示例：不要为未来可能的需求提前设计复杂的扩展性。
组合优于继承（Composition Over Inheritance）
优先使用对象组合而非类继承。
目的：减少继承带来的耦合和刚性，提高灵活性。
示例：Car类通过组合Engine接口实现，而非继承特定引擎类型。
最小惊讶原则（Principle of Least Surprise）
代码行为应符合用户预期，避免隐藏或意外的副作用。
示例：方法名应准确反映其功能，避免返回意外类型或修改外部状态。
三、性能与资源管理
二八定律（80/20 Rule）
80% 的性能问题源于 20% 的代码。
实践：优先优化关键瓶颈，而非全面优化。
缓存原则
缓存频繁使用的数据或计算结果。
示例：使用 LRU 缓存、Memcached 或 Redis 缓存热点数据。
惰性求值（Lazy Evaluation）
延迟计算直到需要结果。
实现：生成器（Generator）、懒加载（Lazy Loading）。
四、安全性原则
最小权限原则（Principle of Least Privilege）
代码或用户仅拥有完成任务所需的最小权限。
示例：数据库连接使用只读权限，API 接口进行严格权限校验。
防御性编程
假设输入不可信，进行边界检查和错误处理。
实践：输入验证、异常捕获、避免空指针引用。
纵深防御（Defense in Depth）
多层次安全防护（如认证、授权、加密）。
示例：前端表单验证 + 后端参数校验 + 数据库约束。
五、可维护性与可读性
代码即文档（Code as Documentation）
代码应自解释，避免过度注释。
实践：使用有意义的变量名、函数名，遵循一致的命名规范。
松耦合高内聚（Loose Coupling, High Cohesion）
模块间依赖最小化，模块内功能关联最大化。
示例：微服务架构通过 API 通信，避免共享数据库。
单一抽象层次原则（SLAP）
函数或类应在同一抽象层次上操作。
反例：一个函数同时处理业务逻辑和数据库查询。
正例：业务逻辑层调用数据访问层。
六、架构设计原则
关注点分离（SoC, Separation of Concerns）
将不同功能分离到独立模块。
示例：MVC 架构（模型 - 视图 - 控制器）、前后端分离。
CQRS（命令查询职责分离）
将读操作（查询）和写操作（命令）分离。
应用：读写分离的数据库架构、事件溯源系统。
边界上下文（Bounded Context）
在领域驱动设计（DDD）中定义清晰的业务边界。
实践：微服务按业务领域拆分，避免上下文混淆。
七、测试原则
测试驱动开发（TDD）
先写测试，再实现功能。
流程：红（失败的测试）→ 绿（通过的实现）→ 重构。
FIRST 原则（测试用例）
Fast：快速执行
Independent：相互独立
Repeatable：可重复运行
Self-Validating：自动验证结果
Timely：与代码同步编写
测试金字塔
单元测试（底）> 集成测试 > 端到端测试（顶）。