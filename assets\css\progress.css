/* 步骤进度指示器样式 */
.steps-progress {
    margin: 20px 0;
    position: relative;
    height: 40px;
    background-color: #f5f5f5;
    border-radius: 20px;
    padding: 5px;
}

.progress-indicator {
    position: absolute;
    top: 5px;
    left: 5px;
    height: 30px;
    background-color: var(--primary-color);
    transition: width 0.5s ease;
    border-radius: 15px;
    width: 0%;
    z-index: 1;
}

.step-points {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 15px;
    z-index: 2;
}

.step-point {
    position: relative;
    width: 30px;
    height: 30px;
    background-color: #fff;
    border: 2px solid #ddd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 3;
}

.step-label {
    font-weight: bold;
    color: #666;
    font-size: 14px;
}

.step-point.active {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
}

.step-point.active .step-label {
    color: white;
}

.step-point.completed {
    border-color: var(--primary-color);
    background-color: white;
}

.step-point.completed .step-label {
    color: var(--primary-color);
}

/* 步骤标题 */
.step-title {
    position: absolute;
    bottom: -25px;
    transform: translateX(-50%);
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

/* 确保在小屏幕上也能正常显示 */
@media (max-width: 768px) {
    .step-title {
        display: none;
    }
    
    .step-point {
        width: 24px;
        height: 24px;
    }
    
    .step-label {
        font-size: 12px;
    }
}

/* 添加过渡步骤之间的连接线 */
.steps-progress::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 5px;
    right: 5px;
    height: 2px;
    background-color: #ddd;
    transform: translateY(-50%);
    z-index: 0;
} 