/**
 * 数据库管理类 - Flask API 模式
 */
class Database {
    constructor() {
        this.baseUrl = '';
        this.logger = window.logger || console;
    }

    /**
     * 发送 API 请求
     * @param {string} endpoint - API 端点
     * @param {string} method - HTTP 方法
     * @param {Object} data - 请求数据
     * @returns {Promise<Object>} API 响应
     */
    async makeRequest(endpoint, method = 'GET', data = null) {
        try {
            const url = this.baseUrl + endpoint;
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
                options.body = JSON.stringify(data);
            }

            this.logger.debug(`发送请求: ${method} ${url}`, data);

            const response = await fetch(url, options);
            const result = await response.json();

            this.logger.debug('请求响应:', result);

            if (!response.ok) {
                throw new Error(result.message || result.error || `HTTP ${response.status}: ${response.statusText}`);
            }

            return result;
        } catch (error) {
            this.logger.error(`API 请求失败: ${endpoint}`, error);
            throw error;
        }
    }

    /**
     * 初始化数据库连接
     */
    async initialize() {
        try {
            // 测试数据库连接
            await this.getTimeSettings();
            this.logger.info('数据库连接测试成功');
            return true;
        } catch (error) {
            this.logger.error('数据库连接失败', error);
            throw error;
        }
    }

    /**
     * 获取时间设置
     * @returns {Promise<Object>} 时间设置对象
     */
    async getTimeSettings() {
        try {
            const response = await this.makeRequest('/api/settings/time');
            if (response.success) {
                return response.settings;
            } else {
                this.logger.error('获取时间设置失败', response.error);
                return null;
            }
        } catch (error) {
            this.logger.error('获取时间设置失败', error);
            throw error;
        }
    }

    /**
     * 保存时间设置
     * @param {Object} settings - 时间设置对象
     * @returns {Promise<boolean>} 保存结果
     */
    async saveTimeSettings(settings) {
        try {
            const response = await this.makeRequest('/api/settings/time', 'POST', settings);
            if (response.success) {
                this.logger.info('时间设置保存成功');
                return true;
            } else {
                this.logger.error('时间设置保存失败', response.error);
                return false;
            }
        } catch (error) {
            this.logger.error('保存时间设置失败', error);
            throw error;
        }
    }

    /**
     * 获取翻译题目
     * @returns {Promise<Array>} 翻译题目数组
     */
    async getTranslationQuestions() {
        try {
            const response = await this.makeRequest('/api/questions/translation');
            if (response.success) {
                return response.questions || [];
            } else {
                this.logger.error('获取翻译题目失败', response.error);
                return [];
            }
        } catch (error) {
            this.logger.error('获取翻译题目失败', error);
            return [];
        }
    }

    /**
     * 获取专业题目
     * @returns {Promise<Array>} 专业题目数组
     */
    async getProfessionalQuestions() {
        try {
            const response = await this.makeRequest('/api/questions/professional');
            if (response.success) {
                return response.questions || [];
            } else {
                this.logger.error('获取专业题目失败', response.error);
                return [];
            }
        } catch (error) {
            this.logger.error('获取专业题目失败', error);
            return [];
        }
    }

    /**
     * 保存学生考试记录
     * @param {Object} record - 考试记录
     * @returns {Promise<boolean>} 保存结果
     */
    async saveExamRecord(record) {
        try {
            const response = await this.makeRequest('/api/exams/save', 'POST', record);
            if (response.success) {
                this.logger.info('考试记录保存成功');
                return true;
            } else {
                this.logger.error('考试记录保存失败', response.error);
                return false;
            }
        } catch (error) {
            this.logger.error('保存考试记录失败', error);
            throw error;
        }
    }

    // ==================== 学生管理API ====================

    /**
     * 获取学生列表
     * @param {Object} options - 查询选项
     * @returns {Promise<Object>} 学生列表响应
     */
    async getStudents(options = {}) {
        try {
            const params = new URLSearchParams();
            if (options.page) params.append('page', options.page);
            if (options.limit) params.append('limit', options.limit);
            if (options.status) params.append('status', options.status);
            if (options.department) params.append('department', options.department);

            const endpoint = `/api/students${params.toString() ? '?' + params.toString() : ''}`;
            const response = await this.makeRequest(endpoint);

            return {
                success: true,
                data: response.data || { students: [], pagination: { total: 0 } }
            };
        } catch (error) {
            this.logger.error('获取学生列表失败', error);
            return {
                success: false,
                error: { message: error.message }
            };
        }
    }

    /**
     * 获取学生信息
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Object>} 学生信息响应
     */
    async getStudent(studentNumber) {
        try {
            const response = await this.makeRequest(`/api/students/${studentNumber}`);
            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            this.logger.error('获取学生信息失败', error);
            return {
                success: false,
                error: { message: error.message, code: 'NOT_FOUND' }
            };
        }
    }

    /**
     * 创建学生
     * @param {Object} studentData - 学生数据
     * @returns {Promise<Object>} 创建结果
     */
    async createStudent(studentData) {
        try {
            const response = await this.makeRequest('/api/students', 'POST', studentData);
            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            this.logger.error('创建学生失败', error);
            return {
                success: false,
                error: { message: error.message }
            };
        }
    }

    /**
     * 更新学生信息
     * @param {string} studentNumber - 学生编号
     * @param {Object} studentData - 学生数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateStudent(studentNumber, studentData) {
        try {
            const response = await this.makeRequest(`/api/students/${studentNumber}`, 'PUT', studentData);
            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            this.logger.error('更新学生信息失败', error);
            return {
                success: false,
                error: { message: error.message }
            };
        }
    }
    /**
     * 获取学生状态
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Object|null>} 学生状态对象
     */
    async getStudentStatus(studentNumber) {
        try {
            const response = await this.makeRequest(`/api/students/${studentNumber}/status`);
            if (response.success) {
                return response.student;
            } else {
                return null;
            }
        } catch (error) {
            this.logger.error('获取学生状态失败', error);
            return null;
        }
    }

    /**
     * 保存学生状态
     * @param {Object} studentData - 学生数据
     * @returns {Promise<boolean>} 保存结果
     */
    async saveStudentStatus(studentData) {
        try {
            const response = await this.makeRequest('/api/students/status', 'POST', studentData);
            if (response.success) {
                this.logger.info('学生状态保存成功');
                return true;
            } else {
                this.logger.error('学生状态保存失败', response.error);
                return false;
            }
        } catch (error) {
            this.logger.error('保存学生状态失败', error);
            throw error;
        }
    }

    /**
     * 获取考试记录
     * @returns {Promise<Array>} 考试记录数组
     */
    async getExamRecords() {
        try {
            const response = await this.makeRequest('/api/exams/records');
            if (response.success) {
                // 确保返回records字段而不是data字段
                return response.records || response.data || [];
            } else {
                this.logger.error('获取考试记录失败', response.error);
                return [];
            }
        } catch (error) {
            this.logger.error('获取考试记录失败', error);
            return [];
        }
    }

    /**
     * 清空考试记录
     * @returns {Promise<boolean>} 清空结果
     */
    async clearExamRecords() {
        try {
            const response = await this.makeRequest('/api/exams/clear', 'POST');
            if (response.success) {
                this.logger.info('考试记录清空成功');
                return true;
            } else {
                this.logger.error('考试记录清空失败', response.error);
                return false;
            }
        } catch (error) {
            this.logger.error('清空考试记录失败', error);
            throw error;
        }
    }

    /**
     * 清空所有考试记录
     */
    async clearAllExamRecords() {
        try {
            // 使用现有的清空API
            const response = await this.makeRequest('/api/exams/clear', 'POST');

            if (response.success) {
                this.logger.info('所有考试记录已清空');
                return true;
            } else {
                throw new Error(response.message || '清空考试记录失败');
            }
        } catch (error) {
            this.logger.error('清空考试记录失败', error);
            return false;
        }
    }

    /**
     * 重置学生编号计数器
     */
    async resetStudentCounter() {
        try {
            const response = await this.makeRequest('/api/students/reset-counter', 'POST');

            if (response.success) {
                this.logger.info('学生编号计数器已重置');
                return true;
            } else {
                throw new Error(response.message || '重置学生编号计数器失败');
            }
        } catch (error) {
            this.logger.error('重置学生编号计数器失败', error);
            return false;
        }
    }

    /**
     * 获取设置
     * @param {string} key - 设置键
     * @returns {Promise<any>} 设置值
     */
    async getSetting(key) {
        try {
            if (key === 'timeSettings') {
                return await this.getTimeSettings();
            }
            // 其他设置可以在这里添加
            return null;
        } catch (error) {
            this.logger.error(`获取设置失败: ${key}`, error);
            return null;
        }
    }
}

// 导出类
window.Database = Database;
