#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API基础模块 - 提供统一的API响应格式和错误处理
"""

from flask import jsonify, request
from functools import wraps
import traceback
import time
import uuid
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class APIResponse:
    """统一的API响应格式"""
    
    @staticmethod
    def success(data=None, message=None):
        """成功响应"""
        response = {
            "success": True,
            "data": data,
            "error": None,
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "requestId": str(uuid.uuid4())[:8]
        }
        if message:
            response["message"] = message
        return jsonify(response)
    
    @staticmethod
    def error(code, message, details=None, status_code=400):
        """错误响应"""
        response = {
            "success": False,
            "data": None,
            "error": {
                "code": code,
                "message": message,
                "details": details
            },
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "requestId": str(uuid.uuid4())[:8]
        }
        return jsonify(response), status_code

class APIError(Exception):
    """API异常类"""
    
    def __init__(self, code, message, details=None, status_code=400):
        self.code = code
        self.message = message
        self.details = details
        self.status_code = status_code
        super().__init__(message)

# 错误代码定义
class ErrorCodes:
    # 通用错误 (1000-1999)
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    INVALID_REQUEST = "INVALID_REQUEST"
    MISSING_PARAMETER = "MISSING_PARAMETER"
    INVALID_PARAMETER = "INVALID_PARAMETER"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    RATE_LIMITED = "RATE_LIMITED"
    
    # 学生相关错误 (2000-2999)
    STUDENT_NOT_FOUND = "STUDENT_NOT_FOUND"
    STUDENT_ALREADY_EXISTS = "STUDENT_ALREADY_EXISTS"
    INVALID_STUDENT_NUMBER = "INVALID_STUDENT_NUMBER"
    STUDENT_EXAM_IN_PROGRESS = "STUDENT_EXAM_IN_PROGRESS"
    STUDENT_EXAM_COMPLETED = "STUDENT_EXAM_COMPLETED"
    
    # 题目相关错误 (3000-3999)
    QUESTION_NOT_FOUND = "QUESTION_NOT_FOUND"
    QUESTION_ALREADY_USED = "QUESTION_ALREADY_USED"
    NO_AVAILABLE_QUESTIONS = "NO_AVAILABLE_QUESTIONS"
    INVALID_QUESTION_TYPE = "INVALID_QUESTION_TYPE"
    QUESTION_INDEX_CONFLICT = "QUESTION_INDEX_CONFLICT"
    
    # 考试相关错误 (4000-4999)
    EXAM_NOT_STARTED = "EXAM_NOT_STARTED"
    EXAM_ALREADY_COMPLETED = "EXAM_ALREADY_COMPLETED"
    INVALID_STEP_TRANSITION = "INVALID_STEP_TRANSITION"
    STEP_NOT_COMPLETED = "STEP_NOT_COMPLETED"
    TIMER_NOT_RUNNING = "TIMER_NOT_RUNNING"
    
    # 数据库错误 (5000-5999)
    DATABASE_CONNECTION_ERROR = "DATABASE_CONNECTION_ERROR"
    DATABASE_QUERY_ERROR = "DATABASE_QUERY_ERROR"
    DATABASE_CONSTRAINT_ERROR = "DATABASE_CONSTRAINT_ERROR"
    DATABASE_TRANSACTION_ERROR = "DATABASE_TRANSACTION_ERROR"
    DATABASE_BACKUP_ERROR = "DATABASE_BACKUP_ERROR"

def api_route(methods=['GET']):
    """API路由装饰器，提供统一的错误处理和日志记录"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            request_id = str(uuid.uuid4())[:8]
            start_time = time.time()
            
            try:
                # 记录请求开始
                logger.info(f"[{request_id}] {request.method} {request.path} - 开始处理")
                
                # 验证请求方法
                if request.method not in methods:
                    raise APIError(
                        ErrorCodes.INVALID_REQUEST,
                        f"不支持的请求方法: {request.method}",
                        status_code=405
                    )
                
                # 执行API函数
                result = func(*args, **kwargs)
                
                # 记录请求完成
                duration = time.time() - start_time
                logger.info(f"[{request_id}] {request.method} {request.path} - 完成 ({duration:.3f}s)")
                
                return result
                
            except APIError as e:
                # 处理API异常
                duration = time.time() - start_time
                logger.warning(f"[{request_id}] {request.method} {request.path} - API错误: {e.message} ({duration:.3f}s)")
                return APIResponse.error(e.code, e.message, e.details, e.status_code)
                
            except Exception as e:
                # 处理未知异常
                duration = time.time() - start_time
                logger.error(f"[{request_id}] {request.method} {request.path} - 未知错误: {str(e)} ({duration:.3f}s)")
                logger.error(traceback.format_exc())
                return APIResponse.error(
                    ErrorCodes.UNKNOWN_ERROR,
                    "服务器内部错误",
                    str(e),
                    500
                )
        
        return wrapper
    return decorator

def validate_json():
    """验证JSON请求体"""
    if not request.is_json:
        raise APIError(
            ErrorCodes.INVALID_REQUEST,
            "请求必须是JSON格式",
            {"content_type": request.content_type}
        )
    
    try:
        return request.get_json()
    except Exception as e:
        raise APIError(
            ErrorCodes.INVALID_REQUEST,
            "无效的JSON格式",
            str(e)
        )

def validate_required_fields(data, required_fields):
    """验证必需字段"""
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None:
            missing_fields.append(field)
    
    if missing_fields:
        raise APIError(
            ErrorCodes.MISSING_PARAMETER,
            f"缺少必需字段: {', '.join(missing_fields)}",
            {"missing_fields": missing_fields}
        )

def validate_student_number(student_number):
    """验证学生编号格式"""
    if not student_number:
        raise APIError(
            ErrorCodes.INVALID_STUDENT_NUMBER,
            "学生编号不能为空"
        )
    
    # 转换为字符串进行验证
    student_number = str(student_number).strip()
    
    if not student_number:
        raise APIError(
            ErrorCodes.INVALID_STUDENT_NUMBER,
            "学生编号不能为空"
        )
    
    # 可以添加更多验证规则，比如长度、格式等
    if len(student_number) > 20:
        raise APIError(
            ErrorCodes.INVALID_STUDENT_NUMBER,
            "学生编号长度不能超过20个字符"
        )
    
    return student_number

def validate_question_type(question_type):
    """验证题目类型"""
    valid_types = ['translation', 'professional']
    if question_type not in valid_types:
        raise APIError(
            ErrorCodes.INVALID_QUESTION_TYPE,
            f"无效的题目类型: {question_type}",
            {"valid_types": valid_types}
        )
    return question_type

def validate_step_number(step_number):
    """验证步骤编号"""
    try:
        step_number = int(step_number)
        if step_number < 1 or step_number > 5:
            raise ValueError()
        return step_number
    except (ValueError, TypeError):
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            "步骤编号必须是1-5之间的整数",
            {"step_number": step_number}
        )

def validate_pagination(page=None, limit=None):
    """验证分页参数"""
    try:
        page = int(page) if page is not None else 1
        limit = int(limit) if limit is not None else 10
        
        if page < 1:
            page = 1
        if limit < 1:
            limit = 10
        if limit > 100:
            limit = 100
            
        return page, limit
    except (ValueError, TypeError):
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            "分页参数必须是正整数",
            {"page": page, "limit": limit}
        )

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_path):
        self.database_path = database_path
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.database_path)
            conn.row_factory = sqlite3.Row
            return conn
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise APIError(
                ErrorCodes.DATABASE_CONNECTION_ERROR,
                "数据库连接失败",
                str(e),
                500
            )
    
    def execute_query(self, query, params=None, fetch_one=False, fetch_all=False):
        """执行数据库查询"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch_one:
                result = cursor.fetchone()
                return dict(result) if result else None
            elif fetch_all:
                results = cursor.fetchall()
                return [dict(row) for row in results]
            else:
                conn.commit()
                return cursor.lastrowid
                
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库查询失败: {e}")
            raise APIError(
                ErrorCodes.DATABASE_QUERY_ERROR,
                "数据库查询失败",
                str(e),
                500
            )
        finally:
            if conn:
                conn.close()
    
    def execute_transaction(self, operations):
        """执行数据库事务"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            results = []
            for query, params in operations:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                results.append(cursor.lastrowid)
            
            conn.commit()
            return results
            
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库事务失败: {e}")
            raise APIError(
                ErrorCodes.DATABASE_TRANSACTION_ERROR,
                "数据库事务失败",
                str(e),
                500
            )
        finally:
            if conn:
                conn.close()

# 全局数据库管理器实例
db_manager = DatabaseManager('assets/data/interview_system.db')
