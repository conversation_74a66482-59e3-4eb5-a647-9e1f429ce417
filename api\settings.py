#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统设置API - 处理系统设置相关的所有操作
"""

from flask import Blueprint, request
from .base import (
    APIResponse, APIError, ErrorCodes, api_route,
    validate_json, db_manager
)
import json
import time

settings_bp = Blueprint('settings', __name__, url_prefix='/api/settings')

# 默认设置
DEFAULT_SETTINGS = {
    "timeSettings": {
        "chineseTime": 2,
        "englishTime": 2,
        "translationTime": 5,
        "professionalTime": 10,
        "comprehensiveTime": 8
    },
    "systemSettings": {
        "debugMode": False,
        "autoSave": True,
        "theme": "light",
        "language": "zh-CN"
    },
    "uiSettings": {
        "fontSize": "medium",
        "showAnimations": True,
        "compactMode": False
    },
    "examSettings": {
        "autoStartTimer": False,
        "showQuestionGrid": True,
        "allowStepBack": True,
        "confirmBeforeNext": False
    },
    "dataSettings": {
        "autoBackup": True,
        "backupInterval": 24,
        "maxBackups": 10,
        "exportFormat": "json"
    }
}

@settings_bp.route('', methods=['GET'])
@api_route(['GET'])
def get_all_settings():
    """获取所有设置"""
    settings = {}
    
    # 获取所有设置记录
    setting_records = db_manager.execute_query(
        "SELECT * FROM settings",
        fetch_all=True
    )
    
    # 按分类组织设置
    for record in setting_records:
        category = record['category']
        key = record['key']
        
        if category not in settings:
            settings[category] = {}
        
        # 解析值
        try:
            value = json.loads(record['value'])
        except:
            value = record['value']
        
        settings[category][key] = value
    
    # 合并默认设置
    for category, category_settings in DEFAULT_SETTINGS.items():
        if category not in settings:
            settings[category] = {}
        
        for key, default_value in category_settings.items():
            if key not in settings[category]:
                settings[category][key] = default_value
    
    return APIResponse.success(settings)

@settings_bp.route('/<category>', methods=['GET'])
@api_route(['GET'])
def get_category_settings(category):
    """获取分类设置"""
    # 验证分类
    if category not in DEFAULT_SETTINGS:
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            f"无效的设置分类: {category}",
            {"valid_categories": list(DEFAULT_SETTINGS.keys())}
        )
    
    # 获取该分类的设置
    setting_records = db_manager.execute_query(
        "SELECT * FROM settings WHERE category = ?",
        (category,),
        fetch_all=True
    )
    
    settings = {}
    for record in setting_records:
        key = record['key']
        try:
            value = json.loads(record['value'])
        except:
            value = record['value']
        settings[key] = value
    
    # 合并默认设置
    default_category_settings = DEFAULT_SETTINGS.get(category, {})
    for key, default_value in default_category_settings.items():
        if key not in settings:
            settings[key] = default_value
    
    return APIResponse.success(settings)

@settings_bp.route('/<category>/<key>', methods=['GET'])
@api_route(['GET'])
def get_setting(category, key):
    """获取单个设置"""
    # 验证分类
    if category not in DEFAULT_SETTINGS:
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            f"无效的设置分类: {category}"
        )
    
    # 获取设置值
    setting = db_manager.execute_query(
        "SELECT * FROM settings WHERE category = ? AND key = ?",
        (category, key),
        fetch_one=True
    )
    
    if setting:
        try:
            value = json.loads(setting['value'])
        except:
            value = setting['value']
    else:
        # 返回默认值
        default_value = DEFAULT_SETTINGS.get(category, {}).get(key)
        if default_value is None:
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"设置项 {category}.{key} 不存在"
            )
        value = default_value
    
    response_data = {
        "category": category,
        "key": key,
        "value": value
    }
    
    return APIResponse.success(response_data)

@settings_bp.route('/<category>/<key>', methods=['PUT'])
@api_route(['PUT'])
def update_setting(category, key):
    """更新单个设置"""
    data = validate_json()
    
    if 'value' not in data:
        raise APIError(
            ErrorCodes.MISSING_PARAMETER,
            "缺少必需字段: value"
        )
    
    # 验证分类
    if category not in DEFAULT_SETTINGS:
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            f"无效的设置分类: {category}"
        )
    
    # 验证设置项
    if key not in DEFAULT_SETTINGS.get(category, {}):
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            f"设置项 {category}.{key} 不存在"
        )
    
    value = data['value']
    
    # 验证设置值
    validate_setting_value(category, key, value)
    
    # 检查设置是否已存在
    existing_setting = db_manager.execute_query(
        "SELECT * FROM settings WHERE category = ? AND key = ?",
        (category, key),
        fetch_one=True
    )
    
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    
    if existing_setting:
        # 更新现有设置
        db_manager.execute_query(
            "UPDATE settings SET value = ?, updated_at = ? WHERE category = ? AND key = ?",
            (json.dumps(value), current_time, category, key)
        )
    else:
        # 创建新设置
        db_manager.execute_query(
            "INSERT INTO settings (category, key, value, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
            (category, key, json.dumps(value), current_time, current_time)
        )
    
    response_data = {
        "category": category,
        "key": key,
        "value": value,
        "updatedAt": current_time
    }
    
    return APIResponse.success(response_data, "设置更新成功")

@settings_bp.route('', methods=['PUT'])
@api_route(['PUT'])
def update_multiple_settings():
    """批量更新设置"""
    data = validate_json()
    
    if not data:
        raise APIError(
            ErrorCodes.INVALID_REQUEST,
            "请求体不能为空"
        )
    
    updated_settings = {}
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 批量更新操作
    operations = []
    
    for category, category_settings in data.items():
        if category not in DEFAULT_SETTINGS:
            continue  # 跳过无效分类
        
        if not isinstance(category_settings, dict):
            continue  # 跳过非字典类型
        
        updated_settings[category] = {}
        
        for key, value in category_settings.items():
            if key not in DEFAULT_SETTINGS.get(category, {}):
                continue  # 跳过无效设置项
            
            # 验证设置值
            try:
                validate_setting_value(category, key, value)
            except APIError:
                continue  # 跳过无效值
            
            # 检查设置是否已存在
            existing_setting = db_manager.execute_query(
                "SELECT * FROM settings WHERE category = ? AND key = ?",
                (category, key),
                fetch_one=True
            )
            
            if existing_setting:
                operations.append((
                    "UPDATE settings SET value = ?, updated_at = ? WHERE category = ? AND key = ?",
                    (json.dumps(value), current_time, category, key)
                ))
            else:
                operations.append((
                    "INSERT INTO settings (category, key, value, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
                    (category, key, json.dumps(value), current_time, current_time)
                ))
            
            updated_settings[category][key] = value
    
    # 执行批量操作
    if operations:
        db_manager.execute_transaction(operations)
    
    return APIResponse.success(updated_settings, f"成功更新 {len(operations)} 个设置")

@settings_bp.route('/reset', methods=['POST'])
@api_route(['POST'])
def reset_settings():
    """重置设置为默认值"""
    data = validate_json() if request.is_json else {}
    categories = data.get('categories', list(DEFAULT_SETTINGS.keys()))
    
    # 验证分类
    invalid_categories = [cat for cat in categories if cat not in DEFAULT_SETTINGS]
    if invalid_categories:
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            f"无效的设置分类: {', '.join(invalid_categories)}"
        )
    
    # 删除指定分类的设置
    for category in categories:
        db_manager.execute_query(
            "DELETE FROM settings WHERE category = ?",
            (category,)
        )
    
    # 重新插入默认设置
    operations = []
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    
    for category in categories:
        category_settings = DEFAULT_SETTINGS.get(category, {})
        for key, value in category_settings.items():
            operations.append((
                "INSERT INTO settings (category, key, value, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
                (category, key, json.dumps(value), current_time, current_time)
            ))
    
    if operations:
        db_manager.execute_transaction(operations)
    
    # 返回重置后的设置
    reset_settings = {}
    for category in categories:
        reset_settings[category] = DEFAULT_SETTINGS.get(category, {})
    
    return APIResponse.success(reset_settings, f"成功重置 {len(categories)} 个分类的设置")

def validate_setting_value(category, key, value):
    """验证设置值"""
    # 时间设置验证
    if category == "timeSettings":
        if not isinstance(value, (int, float)) or value <= 0:
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"时间设置 {key} 必须是正数"
            )
        if value > 60:  # 最大60分钟
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"时间设置 {key} 不能超过60分钟"
            )
    
    # 系统设置验证
    elif category == "systemSettings":
        if key in ["debugMode", "autoSave"] and not isinstance(value, bool):
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"系统设置 {key} 必须是布尔值"
            )
        elif key == "theme" and value not in ["light", "dark", "auto"]:
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"主题设置必须是 light、dark 或 auto"
            )
        elif key == "language" and not isinstance(value, str):
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"语言设置必须是字符串"
            )
    
    # UI设置验证
    elif category == "uiSettings":
        if key in ["showAnimations", "compactMode"] and not isinstance(value, bool):
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"UI设置 {key} 必须是布尔值"
            )
        elif key == "fontSize" and value not in ["small", "medium", "large"]:
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"字体大小必须是 small、medium 或 large"
            )
    
    # 考试设置验证
    elif category == "examSettings":
        if not isinstance(value, bool):
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"考试设置 {key} 必须是布尔值"
            )
    
    # 数据设置验证
    elif category == "dataSettings":
        if key in ["autoBackup"] and not isinstance(value, bool):
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"数据设置 {key} 必须是布尔值"
            )
        elif key in ["backupInterval", "maxBackups"] and (not isinstance(value, int) or value <= 0):
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"数据设置 {key} 必须是正整数"
            )
        elif key == "exportFormat" and value not in ["json", "csv", "xlsx"]:
            raise APIError(
                ErrorCodes.INVALID_PARAMETER,
                f"导出格式必须是 json、csv 或 xlsx"
            )
