/**
 * 基础类 - 为所有类提供公共功能
 * 遵循面向对象设计原则，提供统一的错误处理和日志记录
 */
class BaseClass {
    constructor(name = 'BaseClass') {
        this.className = name;
        this.initialized = false;
        this.eventListeners = new Map();
        this.logger = window.logger || console;
    }

    /**
     * 初始化方法 - 子类必须实现
     */
    async initialize() {
        throw new Error(`${this.className} must implement initialize() method`);
    }

    /**
     * 设置初始化状态
     * @param {boolean} status - 初始化状态
     */
    setInitialized(status) {
        this.initialized = status;
        if (status) {
            this.logger.info(`${this.className} initialized successfully`);
        }
    }

    /**
     * 检查是否已初始化
     * @returns {boolean} 初始化状态
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 便捷的日志记录方法
     * @param {string} message - 日志消息
     * @param {string} level - 日志级别 (info, warn, error, debug)
     */
    log(message, level = 'info') {
        if (this.logger && typeof this.logger[level] === 'function') {
            this.logger[level](`[${this.className}] ${message}`);
        } else {
            console[level](`[${this.className}] ${message}`);
        }
    }

    /**
     * 生成唯一ID
     * @param {string} prefix - ID前缀
     * @returns {string} 唯一ID
     */
    generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 统一错误处理
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handleError(error, context = '') {
        const errorMessage = `[${this.className}] ${context ? context + ': ' : ''}${error.message}`;
        this.logger.error(errorMessage, error);
        
        // 触发错误事件
        this.emit('error', {
            error,
            context,
            className: this.className,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 添加事件监听器
     * @param {Element} element - DOM元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     * @param {Object} options - 事件选项
     * @returns {string} 监听器ID
     */
    addEventListener(element, event, handler, options = {}) {
        const listenerId = this.generateId('listener');
        const wrappedHandler = (e) => {
            try {
                handler.call(this, e);
            } catch (error) {
                this.handleError(error, `event handler for ${event}`);
            }
        };

        this.eventListeners.set(listenerId, {
            element,
            event,
            handler: wrappedHandler,
            originalHandler: handler,
            options
        });

        element.addEventListener(event, wrappedHandler, options);
        return listenerId;
    }

    /**
     * 移除事件监听器
     * @param {string} listenerId - 监听器ID
     * @returns {boolean} 是否成功移除
     */
    removeEventListener(listenerId) {
        const listener = this.eventListeners.get(listenerId);
        if (!listener) {
            return false;
        }

        const { element, event, handler, options } = listener;
        element.removeEventListener(event, handler, options);
        this.eventListeners.delete(listenerId);
        return true;
    }

    /**
     * 移除所有事件监听器
     */
    removeAllEventListeners() {
        for (const [listenerId] of this.eventListeners) {
            this.removeEventListener(listenerId);
        }
    }

    /**
     * 触发自定义事件
     * @param {string} eventName - 事件名称
     * @param {*} data - 事件数据
     */
    emit(eventName, data = null) {
        const event = new CustomEvent(`${this.className}:${eventName}`, {
            detail: data,
            bubbles: true,
            cancelable: true
        });
        
        document.dispatchEvent(event);
    }

    /**
     * 监听自定义事件
     * @param {string} eventName - 事件名称
     * @param {Function} handler - 事件处理函数
     * @returns {string} 监听器ID
     */
    on(eventName, handler) {
        return this.addEventListener(document, `${this.className}:${eventName}`, handler);
    }

    /**
     * 移除自定义事件监听器
     * @param {string} eventName - 事件名称
     * @param {string} listenerId - 监听器ID
     */
    off(eventName, listenerId) {
        this.removeEventListener(listenerId);
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    debounce(func, delay) {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    throttle(func, delay) {
        let lastCall = 0;
        return (...args) => {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    }

    /**
     * 延迟执行
     * @param {number} ms - 延迟时间（毫秒）
     * @returns {Promise} Promise对象
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 重试机制
     * @param {Function} func - 要重试的函数
     * @param {number} maxRetries - 最大重试次数
     * @param {number} retryDelay - 重试间隔（毫秒）
     * @returns {Promise} Promise对象
     */
    async retry(func, maxRetries = 3, retryDelay = 1000) {
        let lastError;
        
        for (let i = 0; i <= maxRetries; i++) {
            try {
                return await func();
            } catch (error) {
                lastError = error;
                if (i < maxRetries) {
                    this.logger.warn(`${this.className}: Retry ${i + 1}/${maxRetries} failed, retrying in ${retryDelay}ms...`);
                    await this.delay(retryDelay);
                }
            }
        }
        
        throw lastError;
    }

    /**
     * 验证必需参数
     * @param {Object} params - 参数对象
     * @param {Array} requiredFields - 必需字段数组
     * @throws {Error} 参数验证失败时抛出错误
     */
    validateRequiredParams(params, requiredFields) {
        const missingFields = requiredFields.filter(field => 
            params[field] === undefined || params[field] === null
        );
        
        if (missingFields.length > 0) {
            throw new Error(`Missing required parameters: ${missingFields.join(', ')}`);
        }
    }

    /**
     * 深拷贝对象
     * @param {*} obj - 要拷贝的对象
     * @returns {*} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const cloned = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }
        
        return obj;
    }

    /**
     * 获取类的状态信息
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            className: this.className,
            initialized: this.initialized,
            eventListenersCount: this.eventListeners.size,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 销毁实例
     */
    destroy() {
        this.removeAllEventListeners();
        this.initialized = false;
        this.logger.info(`${this.className} destroyed`);
    }
}

// 导出到全局
window.BaseClass = BaseClass;
