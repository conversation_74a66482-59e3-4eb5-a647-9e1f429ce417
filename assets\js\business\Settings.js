/**
 * 设置管理类 - 管理系统设置和配置
 * 遵循单一职责原则，专门负责设置相关的业务逻辑
 */
class Settings extends BaseClass {
    constructor(database = null) {
        super('Settings');
        this.database = database;
        this.settings = new Map();
        this.defaultSettings = this.getDefaultSettings();
        this.listeners = new Map();
    }

    /**
     * 初始化设置管理器
     * @returns {Promise<boolean>} 初始化结果
     */
    async initialize() {
        try {
            await this.loadSettings();
            this.setupDefaultListeners();
            this.log('设置管理器初始化成功');
            this.initialized = true;
            return true;
        } catch (error) {
            this.handleError(error, '设置管理器初始化');
            return false;
        }
    }

    /**
     * 获取默认设置
     * @returns {Object} 默认设置对象
     */
    getDefaultSettings() {
        return {
            // 时间设置
            timeSettings: {
                chineseTime: 1,
                englishTime: 1,
                translationTime: 4,
                professionalTime: 5
            },
            
            // 界面设置
            uiSettings: {
                theme: 'light',
                language: 'zh-CN',
                fontSize: 'medium',
                showTimer: true,
                showProgress: true,
                enableAnimations: true
            },
            
            // 考试设置
            examSettings: {
                autoNextStep: true,  // 时间到后自动进入下一环节
                showQuestionNumbers: true,
                randomizeQuestions: true,
                allowPause: true,
                warningTime: 30
            },
            
            // 系统设置
            systemSettings: {
                autoSave: true,
                autoSaveInterval: 30,
                debugMode: false,
                logLevel: 'info'
            }
        };
    }

    /**
     * 加载设置
     */
    async loadSettings() {
        try {
            // 从数据库加载时间设置
            if (this.database) {
                const timeSettings = await this.database.getSetting('timeSettings', this.getDefaultTimeSettings());
                if (timeSettings) {
                    this.settings.set('timeSettings', timeSettings);
                }
            }

            // 从localStorage加载其他设置
            this.loadFromLocalStorage();
            
            // 应用默认设置（如果某些设置不存在）
            this.applyDefaultSettings();
            
            this.log('设置加载完成');
        } catch (error) {
            this.handleError(error, '加载设置');
            // 使用默认设置
            this.applyDefaultSettings();
        }
    }

    /**
     * 从localStorage加载设置
     */
    loadFromLocalStorage() {
        const settingKeys = ['uiSettings', 'examSettings', 'systemSettings'];
        
        settingKeys.forEach(key => {
            try {
                const stored = localStorage.getItem(`examSystem_${key}`);
                if (stored) {
                    const parsed = Utils.safeJsonParse(stored);
                    if (parsed) {
                        this.settings.set(key, parsed);
                    }
                }
            } catch (error) {
                this.log(`加载 ${key} 失败: ${error.message}`, 'warn');
            }
        });
    }

    /**
     * 应用默认设置
     */
    applyDefaultSettings() {
        Object.entries(this.defaultSettings).forEach(([key, defaultValue]) => {
            if (!this.settings.has(key)) {
                this.settings.set(key, Utils.deepClone(defaultValue));
            } else {
                // 合并默认设置（添加缺失的键）
                const current = this.settings.get(key);
                const merged = { ...defaultValue, ...current };
                this.settings.set(key, merged);
            }
        });
    }

    /**
     * 获取设置值
     * @param {string} category - 设置分类
     * @param {string} key - 设置键名
     * @returns {any} 设置值
     */
    get(category, key = null) {
        const categorySettings = this.settings.get(category);
        if (!categorySettings) {
            return null;
        }
        
        if (key === null) {
            return Utils.deepClone(categorySettings);
        }
        
        return categorySettings[key];
    }

    /**
     * 设置值
     * @param {string} category - 设置分类
     * @param {string} key - 设置键名
     * @param {any} value - 设置值
     * @returns {Promise<boolean>} 设置结果
     */
    async set(category, key, value) {
        try {
            if (!this.settings.has(category)) {
                this.settings.set(category, {});
            }
            
            const categorySettings = this.settings.get(category);
            const oldValue = categorySettings[key];
            categorySettings[key] = value;
            
            // 保存设置
            await this.saveSettings(category);
            
            // 触发变更事件
            this.notifyChange(category, key, value, oldValue);
            
            this.log(`设置 ${category}.${key} = ${value}`);
            return true;
        } catch (error) {
            this.handleError(error, `设置 ${category}.${key}`);
            return false;
        }
    }

    /**
     * 批量设置
     * @param {string} category - 设置分类
     * @param {Object} values - 设置值对象
     * @returns {Promise<boolean>} 设置结果
     */
    async setMultiple(category, values) {
        try {
            if (!this.settings.has(category)) {
                this.settings.set(category, {});
            }
            
            const categorySettings = this.settings.get(category);
            const changes = [];
            
            Object.entries(values).forEach(([key, value]) => {
                const oldValue = categorySettings[key];
                categorySettings[key] = value;
                changes.push({ key, value, oldValue });
            });
            
            // 保存设置
            await this.saveSettings(category);
            
            // 触发变更事件
            changes.forEach(({ key, value, oldValue }) => {
                this.notifyChange(category, key, value, oldValue);
            });
            
            this.log(`批量设置 ${category}: ${Object.keys(values).join(', ')}`);
            return true;
        } catch (error) {
            this.handleError(error, `批量设置 ${category}`);
            return false;
        }
    }

    /**
     * 保存设置
     * @param {string} category - 设置分类
     */
    async saveSettings(category) {
        const categorySettings = this.settings.get(category);
        if (!categorySettings) {
            return;
        }
        
        if (category === 'timeSettings' && this.database) {
            // 时间设置保存到数据库
            await this.database.saveTimeSettings(categorySettings);
        } else {
            // 其他设置保存到localStorage
            try {
                localStorage.setItem(
                    `examSystem_${category}`,
                    Utils.safeJsonStringify(categorySettings)
                );
            } catch (error) {
                this.handleError(error, `保存设置到localStorage: ${category}`);
            }
        }
    }

    /**
     * 重置设置
     * @param {string} category - 设置分类
     * @returns {Promise<boolean>} 重置结果
     */
    async reset(category) {
        try {
            const defaultValue = this.defaultSettings[category];
            if (defaultValue) {
                this.settings.set(category, Utils.deepClone(defaultValue));
                await this.saveSettings(category);
                
                // 触发重置事件
                this.notifyReset(category);
                
                this.log(`重置设置分类: ${category}`);
                return true;
            }
            return false;
        } catch (error) {
            this.handleError(error, `重置设置 ${category}`);
            return false;
        }
    }

    /**
     * 重置所有设置
     * @returns {Promise<boolean>} 重置结果
     */
    async resetAll() {
        try {
            const categories = Object.keys(this.defaultSettings);
            for (const category of categories) {
                await this.reset(category);
            }
            
            this.log('所有设置已重置');
            return true;
        } catch (error) {
            this.handleError(error, '重置所有设置');
            return false;
        }
    }

    /**
     * 添加设置变更监听器
     * @param {string} category - 设置分类
     * @param {string} key - 设置键名
     * @param {Function} callback - 回调函数
     */
    addListener(category, key, callback) {
        const listenerKey = `${category}.${key}`;
        if (!this.listeners.has(listenerKey)) {
            this.listeners.set(listenerKey, []);
        }
        
        this.listeners.get(listenerKey).push(callback);
        this.log(`添加设置监听器: ${listenerKey}`);
    }

    /**
     * 移除设置变更监听器
     * @param {string} category - 设置分类
     * @param {string} key - 设置键名
     * @param {Function} callback - 回调函数
     */
    removeListener(category, key, callback) {
        const listenerKey = `${category}.${key}`;
        const listeners = this.listeners.get(listenerKey);
        
        if (listeners) {
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
                this.log(`移除设置监听器: ${listenerKey}`);
            }
        }
    }

    /**
     * 通知设置变更
     * @param {string} category - 设置分类
     * @param {string} key - 设置键名
     * @param {any} newValue - 新值
     * @param {any} oldValue - 旧值
     */
    notifyChange(category, key, newValue, oldValue) {
        const listenerKey = `${category}.${key}`;
        const listeners = this.listeners.get(listenerKey);
        
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(newValue, oldValue, category, key);
                } catch (error) {
                    this.handleError(error, `执行设置监听器: ${listenerKey}`);
                }
            });
        }
    }

    /**
     * 通知设置重置
     * @param {string} category - 设置分类
     */
    notifyReset(category) {
        const categorySettings = this.settings.get(category);
        if (categorySettings) {
            Object.keys(categorySettings).forEach(key => {
                this.notifyChange(category, key, categorySettings[key], null);
            });
        }
    }

    /**
     * 设置默认监听器
     */
    setupDefaultListeners() {
        // 监听调试模式变更
        this.addListener('systemSettings', 'debugMode', (enabled) => {
            if (window.Logger) {
                const level = enabled ? 'debug' : 'info';
                // 更新所有Logger实例的日志级别
                document.querySelectorAll('*').forEach(el => {
                    if (el.logger && el.logger.setLevel) {
                        el.logger.setLevel(level);
                    }
                });
            }
        });

        // 监听主题变更
        this.addListener('uiSettings', 'theme', (theme) => {
            document.documentElement.setAttribute('data-theme', theme);
        });

        // 监听字体大小变更
        this.addListener('uiSettings', 'fontSize', (fontSize) => {
            document.documentElement.setAttribute('data-font-size', fontSize);
        });
    }

    /**
     * 获取所有设置
     * @returns {Object} 所有设置对象
     */
    getAll() {
        const allSettings = {};
        this.settings.forEach((value, key) => {
            allSettings[key] = Utils.deepClone(value);
        });
        return allSettings;
    }

    /**
     * 验证设置值
     * @param {string} category - 设置分类
     * @param {string} key - 设置键名
     * @param {any} value - 设置值
     * @returns {boolean} 验证结果
     */
    validate(category, key, value) {
        const validators = {
            timeSettings: {
                chineseTime: (v) => Utils.validateInput(v, 'number', { min: 1, max: 60 }),
                englishTime: (v) => Utils.validateInput(v, 'number', { min: 1, max: 60 }),
                translationTime: (v) => Utils.validateInput(v, 'number', { min: 1, max: 60 }),
                professionalTime: (v) => Utils.validateInput(v, 'number', { min: 1, max: 60 })
            },
            uiSettings: {
                theme: (v) => ['light', 'dark'].includes(v),
                language: (v) => ['zh-CN', 'en-US'].includes(v),
                fontSize: (v) => ['small', 'medium', 'large'].includes(v)
            },
            systemSettings: {
                autoSaveInterval: (v) => Utils.validateInput(v, 'number', { min: 10, max: 300 })
            }
        };

        const categoryValidators = validators[category];
        if (categoryValidators && categoryValidators[key]) {
            return categoryValidators[key](value);
        }

        return true; // 默认通过验证
    }

    /**
     * 导出设置
     * @returns {Object} 设置数据对象
     */
    export() {
        return this.getAll();
    }

    /**
     * 导入设置
     * @param {Object} data - 设置数据对象
     * @returns {Promise<boolean>} 导入结果
     */
    async import(data) {
        try {
            for (const [category, categorySettings] of Object.entries(data)) {
                if (this.defaultSettings[category]) {
                    await this.setMultiple(category, categorySettings);
                }
            }
            
            this.log('设置数据导入成功');
            return true;
        } catch (error) {
            this.handleError(error, '导入设置数据');
            return false;
        }
    }

    /**
     * 获取默认时间设置
     * @returns {Object} 默认时间设置
     */
    getDefaultTimeSettings() {
        return {
            chineseTime: 1,       // 中文介绍时间（分钟）
            englishTime: 1,       // 英文介绍时间（分钟）
            translationTime: 4,   // 翻译时间（分钟）
            professionalTime: 5   // 专业题时间（分钟）
        };
    }

    /**
     * 销毁设置管理器
     */
    destroy() {
        this.listeners.clear();
        this.settings.clear();
        super.destroy();
    }
}

// 导出到全局作用域
window.Settings = Settings;
