#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
题目管理API - 处理题目相关的所有操作
"""

from flask import Blueprint, request
from .base import (
    APIResponse, APIError, ErrorCodes, api_route,
    validate_json, validate_required_fields, validate_question_type,
    validate_student_number, db_manager
)
import json
import random
import time

questions_bp = Blueprint('questions', __name__, url_prefix='/api/questions')

@questions_bp.route('/<question_type>', methods=['GET'])
@api_route(['GET'])
def get_questions(question_type):
    """获取题目列表"""
    question_type = validate_question_type(question_type)
    
    # 确定表名
    table_name = f"{question_type}_questions"
    
    # 获取题目列表
    questions = db_manager.execute_query(
        f"SELECT * FROM {table_name} ORDER BY question_index",
        fetch_all=True
    )
    
    # 格式化题目数据
    questions_data = []
    for question in questions:
        try:
            question_data = json.loads(question['question_data'])
        except:
            question_data = {"text": question['question_data']}
        
        questions_data.append({
            "id": f"{question_type}_{question['question_index']}",
            "type": question_type,
            "index": question['question_index'],
            "title": f"{get_question_type_name(question_type)}题{question['question_index']}",
            "content": question_data,
            "isActive": True,
            "createdAt": question.get('created_at'),
            "tags": json.loads(question.get('tags', '[]')),
            "difficulty": question.get('difficulty', 'medium')
        })
    
    response_data = {
        "questions": questions_data,
        "total": len(questions_data),
        "type": question_type
    }
    
    return APIResponse.success(response_data)

@questions_bp.route('/<question_type>/<int:index>', methods=['GET'])
@api_route(['GET'])
def get_question(question_type, index):
    """获取指定题目"""
    question_type = validate_question_type(question_type)
    
    # 确定表名
    table_name = f"{question_type}_questions"
    
    # 获取题目
    question = db_manager.execute_query(
        f"SELECT * FROM {table_name} WHERE question_index = ?",
        (index,),
        fetch_one=True
    )
    
    if not question:
        raise APIError(
            ErrorCodes.QUESTION_NOT_FOUND,
            f"{get_question_type_name(question_type)}题{index}不存在"
        )
    
    # 格式化题目数据
    try:
        question_data = json.loads(question['question_data'])
    except:
        question_data = {"text": question['question_data']}
    
    response_data = {
        "id": f"{question_type}_{index}",
        "type": question_type,
        "index": index,
        "title": f"{get_question_type_name(question_type)}题{index}",
        "content": question_data,
        "isActive": True,
        "createdAt": question.get('created_at'),
        "tags": json.loads(question.get('tags', '[]')),
        "difficulty": question.get('difficulty', 'medium')
    }
    
    return APIResponse.success(response_data)

@questions_bp.route('/<question_type>/used', methods=['GET'])
@api_route(['GET'])
def get_used_questions(question_type):
    """获取已使用题目"""
    question_type = validate_question_type(question_type)
    
    # 获取已使用的题目
    used_questions = db_manager.execute_query(
        """SELECT sq.*, s.student_number 
           FROM student_questions sq
           JOIN students s ON sq.student_id = s.id
           WHERE sq.question_type = ?
           ORDER BY sq.created_at DESC""",
        (question_type,),
        fetch_all=True
    )
    
    # 格式化数据
    used_questions_data = []
    used_numbers = []
    
    for question in used_questions:
        used_questions_data.append({
            "questionId": f"{question_type}_{question['question_index']}",
            "questionIndex": question['question_index'],
            "studentNumber": question['student_number'],
            "usedAt": question['created_at']
        })
        
        if question['question_index'] not in used_numbers:
            used_numbers.append(question['question_index'])
    
    response_data = {
        "usedQuestions": used_questions_data,
        "usedNumbers": used_numbers,
        "type": question_type
    }
    
    return APIResponse.success(response_data)

@questions_bp.route('/<question_type>/stats', methods=['GET'])
@api_route(['GET'])
def get_question_stats(question_type):
    """获取题目统计"""
    question_type = validate_question_type(question_type)
    
    # 获取总题目数
    table_name = f"{question_type}_questions"
    total_result = db_manager.execute_query(
        f"SELECT COUNT(*) as total FROM {table_name}",
        fetch_one=True
    )
    total = total_result['total'] if total_result else 0
    
    # 获取已使用题目数
    used_result = db_manager.execute_query(
        "SELECT COUNT(DISTINCT question_index) as used FROM student_questions WHERE question_type = ?",
        (question_type,),
        fetch_one=True
    )
    used = used_result['used'] if used_result else 0
    
    # 获取已使用的题号
    used_numbers_result = db_manager.execute_query(
        "SELECT DISTINCT question_index FROM student_questions WHERE question_type = ? ORDER BY question_index",
        (question_type,),
        fetch_all=True
    )
    used_numbers = [row['question_index'] for row in used_numbers_result]
    
    # 获取所有题号
    all_numbers_result = db_manager.execute_query(
        f"SELECT question_index FROM {table_name} ORDER BY question_index",
        fetch_all=True
    )
    all_numbers = [row['question_index'] for row in all_numbers_result]
    
    # 计算可用题号
    available_numbers = [num for num in all_numbers if num not in used_numbers]
    
    available = len(available_numbers)
    usage_rate = round((used / total * 100) if total > 0 else 0, 1)
    
    response_data = {
        "type": question_type,
        "total": total,
        "used": used,
        "available": available,
        "usageRate": usage_rate,
        "usedNumbers": used_numbers,
        "availableNumbers": available_numbers
    }
    
    return APIResponse.success(response_data)

@questions_bp.route('/mark-used', methods=['POST'])
@api_route(['POST'])
def mark_question_used():
    """标记题目为已使用"""
    data = validate_json()
    validate_required_fields(data, ['questionType', 'questionIndex', 'studentNumber'])
    
    question_type = validate_question_type(data['questionType'])
    question_index = int(data['questionIndex'])
    student_number = validate_student_number(data['studentNumber'])
    
    # 获取学生信息
    student = db_manager.execute_query(
        "SELECT * FROM students WHERE student_number = ?",
        (student_number,),
        fetch_one=True
    )
    
    if not student:
        raise APIError(
            ErrorCodes.STUDENT_NOT_FOUND,
            f"学生编号 {student_number} 不存在"
        )
    
    # 检查题目是否存在
    table_name = f"{question_type}_questions"
    question = db_manager.execute_query(
        f"SELECT * FROM {table_name} WHERE question_index = ?",
        (question_index,),
        fetch_one=True
    )
    
    if not question:
        raise APIError(
            ErrorCodes.QUESTION_NOT_FOUND,
            f"{get_question_type_name(question_type)}题{question_index}不存在"
        )
    
    # 检查题目是否已被该学生使用
    existing_record = db_manager.execute_query(
        """SELECT * FROM student_questions 
           WHERE student_id = ? AND question_type = ? AND question_index = ?""",
        (student['id'], question_type, question_index),
        fetch_one=True
    )
    
    if existing_record:
        raise APIError(
            ErrorCodes.QUESTION_ALREADY_USED,
            f"学生{student_number}已经使用过{get_question_type_name(question_type)}题{question_index}"
        )
    
    # 标记题目为已使用
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    db_manager.execute_query(
        """INSERT INTO student_questions 
           (student_id, question_type, question_id, question_index, question_data, created_at)
           VALUES (?, ?, ?, ?, ?, ?)""",
        (
            student['id'],
            question_type,
            data.get('questionId', f"{question_type}_{question_index}"),
            question_index,
            question['question_data'],
            current_time
        )
    )
    
    return APIResponse.success({
        "questionId": f"{question_type}_{question_index}",
        "questionType": question_type,
        "questionIndex": question_index,
        "studentNumber": student_number,
        "markedAt": current_time
    }, "题目标记成功")

@questions_bp.route('', methods=['POST'])
@api_route(['POST'])
def create_question():
    """创建题目"""
    data = validate_json()
    validate_required_fields(data, ['type', 'index', 'content'])
    
    question_type = validate_question_type(data['type'])
    question_index = int(data['index'])
    content = data['content']
    title = data.get('title', f"{get_question_type_name(question_type)}题{question_index}")
    tags = data.get('tags', [])
    difficulty = data.get('difficulty', 'medium')
    
    # 确定表名
    table_name = f"{question_type}_questions"
    
    # 检查题目编号是否已存在
    existing_question = db_manager.execute_query(
        f"SELECT * FROM {table_name} WHERE question_index = ?",
        (question_index,),
        fetch_one=True
    )
    
    if existing_question:
        raise APIError(
            ErrorCodes.QUESTION_INDEX_CONFLICT,
            f"{get_question_type_name(question_type)}题{question_index}已存在"
        )
    
    # 创建题目
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    question_id = db_manager.execute_query(
        f"""INSERT INTO {table_name} 
            (question_index, question_data, created_at, tags, difficulty)
            VALUES (?, ?, ?, ?, ?)""",
        (
            question_index,
            json.dumps(content),
            current_time,
            json.dumps(tags),
            difficulty
        )
    )
    
    response_data = {
        "id": f"{question_type}_{question_index}",
        "type": question_type,
        "index": question_index,
        "title": title,
        "content": content,
        "tags": tags,
        "difficulty": difficulty,
        "createdAt": current_time
    }
    
    return APIResponse.success(response_data, "题目创建成功")

@questions_bp.route('/<question_id>', methods=['PUT'])
@api_route(['PUT'])
def update_question(question_id):
    """更新题目"""
    data = validate_json()
    
    # 解析题目ID
    try:
        question_type, question_index = question_id.split('_', 1)
        question_index = int(question_index)
        question_type = validate_question_type(question_type)
    except:
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            f"无效的题目ID: {question_id}"
        )
    
    # 确定表名
    table_name = f"{question_type}_questions"
    
    # 检查题目是否存在
    question = db_manager.execute_query(
        f"SELECT * FROM {table_name} WHERE question_index = ?",
        (question_index,),
        fetch_one=True
    )
    
    if not question:
        raise APIError(
            ErrorCodes.QUESTION_NOT_FOUND,
            f"{get_question_type_name(question_type)}题{question_index}不存在"
        )
    
    # 准备更新数据
    update_fields = []
    update_values = []
    
    if 'content' in data:
        update_fields.append("question_data = ?")
        update_values.append(json.dumps(data['content']))
    
    if 'tags' in data:
        update_fields.append("tags = ?")
        update_values.append(json.dumps(data['tags']))
    
    if 'difficulty' in data:
        update_fields.append("difficulty = ?")
        update_values.append(data['difficulty'])
    
    # 执行更新
    if update_fields:
        update_values.append(question_index)
        db_manager.execute_query(
            f"UPDATE {table_name} SET {', '.join(update_fields)} WHERE question_index = ?",
            update_values
        )
    
    # 返回更新后的题目
    return get_question(question_type, question_index)

def get_question_type_name(question_type):
    """获取题目类型名称"""
    type_names = {
        'translation': '翻译',
        'professional': '专业'
    }
    return type_names.get(question_type, question_type)
