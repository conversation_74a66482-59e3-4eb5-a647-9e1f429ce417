#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
考试记录API - 处理考试记录相关的所有操作
"""

from flask import Blueprint, request
from .base import (
    APIResponse, APIError, ErrorCodes, api_route,
    validate_json, validate_required_fields, validate_student_number,
    validate_pagination, db_manager
)
import json
import time

exam_records_bp = Blueprint('exam_records', __name__, url_prefix='/api/exam-records')

@exam_records_bp.route('', methods=['POST'])
@api_route(['POST'])
def create_exam_record():
    """创建考试记录"""
    data = validate_json()
    validate_required_fields(data, ['studentNumber'])
    
    student_number = validate_student_number(data['studentNumber'])
    start_time = data.get('startTime', time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 检查学生是否存在
    student = db_manager.execute_query(
        "SELECT * FROM students WHERE student_number = ?",
        (student_number,),
        fetch_one=True
    )
    
    if not student:
        raise APIError(
            ErrorCodes.STUDENT_NOT_FOUND,
            f"学生编号 {student_number} 不存在"
        )
    
    # 检查是否已有进行中的考试记录
    existing_record = db_manager.execute_query(
        "SELECT * FROM exam_records WHERE student_number = ? AND status IN ('进行中', '准备中')",
        (student_number,),
        fetch_one=True
    )
    
    if existing_record:
        raise APIError(
            ErrorCodes.STUDENT_EXAM_IN_PROGRESS,
            f"学生 {student_number} 已有进行中的考试"
        )
    
    # 创建考试记录
    record_id = db_manager.execute_query(
        """INSERT INTO exam_records (student_number, exam_date, current_step, status)
           VALUES (?, ?, 1, '进行中')""",
        (student_number, start_time)
    )
    
    response_data = {
        "id": record_id,
        "studentNumber": student_number,
        "startTime": start_time,
        "currentStep": 1,
        "status": "in_progress",
        "createdAt": start_time
    }
    
    return APIResponse.success(response_data, "考试记录创建成功")

@exam_records_bp.route('', methods=['GET'])
@api_route(['GET'])
def get_exam_records():
    """获取考试记录列表"""
    page = request.args.get('page', 1)
    limit = request.args.get('limit', 10)
    status = request.args.get('status')
    date = request.args.get('date')
    student_number = request.args.get('studentNumber')
    
    page, limit = validate_pagination(page, limit)
    offset = (page - 1) * limit
    
    # 构建查询条件
    where_conditions = []
    query_params = []
    
    if status:
        status_map = {
            'in_progress': '进行中',
            'completed': '已完成',
            'cancelled': '已取消'
        }
        db_status = status_map.get(status, status)
        where_conditions.append("er.status = ?")
        query_params.append(db_status)
    
    if date:
        where_conditions.append("DATE(er.exam_date) = ?")
        query_params.append(date)
    
    if student_number:
        where_conditions.append("er.student_number = ?")
        query_params.append(student_number)
    
    where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
    
    # 获取总数
    total_query = f"SELECT COUNT(*) as total FROM exam_records er{where_clause}"
    total_result = db_manager.execute_query(total_query, query_params, fetch_one=True)
    total = total_result['total'] if total_result else 0
    
    # 获取考试记录列表
    records_query = f"""
        SELECT er.*, s.name as student_name
        FROM exam_records er
        LEFT JOIN students s ON er.student_number = s.student_number
        {where_clause}
        ORDER BY er.exam_date DESC
        LIMIT ? OFFSET ?
    """
    query_params.extend([limit, offset])
    records = db_manager.execute_query(records_query, query_params, fetch_all=True)
    
    # 格式化记录数据
    records_data = []
    for record in records:
        # 获取步骤记录
        step_records = get_step_records_for_exam(record['id'])
        
        # 计算总用时
        total_duration = sum(step.get('duration', 0) for step in step_records)
        
        records_data.append({
            "id": record['id'],
            "studentNumber": record['student_number'],
            "studentName": record.get('student_name', ''),
            "startTime": record['exam_date'],
            "endTime": record.get('end_time'),
            "totalDuration": total_duration,
            "status": convert_status_to_api(record['status']),
            "currentStep": record['current_step'],
            "stepRecords": step_records,
            "createdAt": record.get('created_at', record['exam_date'])
        })
    
    # 计算分页信息
    total_pages = (total + limit - 1) // limit
    
    response_data = {
        "records": records_data,
        "pagination": {
            "page": page,
            "limit": limit,
            "total": total,
            "totalPages": total_pages
        }
    }
    
    return APIResponse.success(response_data)

@exam_records_bp.route('/student/<student_number>', methods=['GET'])
@api_route(['GET'])
def get_student_exam_records(student_number):
    """获取学生考试记录"""
    student_number = validate_student_number(student_number)
    
    # 获取学生的所有考试记录
    records = db_manager.execute_query(
        """SELECT er.*, s.name as student_name
           FROM exam_records er
           LEFT JOIN students s ON er.student_number = s.student_number
           WHERE er.student_number = ?
           ORDER BY er.exam_date DESC""",
        (student_number,),
        fetch_all=True
    )
    
    # 格式化记录数据
    records_data = []
    for record in records:
        # 获取步骤记录
        step_records = get_step_records_for_exam(record['id'])
        
        # 获取题目记录
        student = db_manager.execute_query(
            "SELECT id FROM students WHERE student_number = ?",
            (student_number,),
            fetch_one=True
        )
        
        questions_data = []
        if student:
            questions = db_manager.execute_query(
                "SELECT * FROM student_questions WHERE student_id = ? ORDER BY created_at",
                (student['id'],),
                fetch_all=True
            )
            
            for question in questions:
                questions_data.append({
                    "type": question['question_type'],
                    "index": question['question_index'],
                    "selectedAt": question['created_at'],
                    "data": json.loads(question['question_data']) if question['question_data'] else {}
                })
        
        records_data.append({
            "id": record['id'],
            "studentNumber": record['student_number'],
            "studentName": record.get('student_name', ''),
            "startTime": record['exam_date'],
            "endTime": record.get('end_time'),
            "status": convert_status_to_api(record['status']),
            "currentStep": record['current_step'],
            "stepRecords": step_records,
            "questions": questions_data,
            "createdAt": record.get('created_at', record['exam_date'])
        })
    
    return APIResponse.success(records_data)

@exam_records_bp.route('/<int:record_id>', methods=['PUT'])
@api_route(['PUT'])
def update_exam_record(record_id):
    """更新考试记录"""
    data = validate_json()
    
    # 检查记录是否存在
    record = db_manager.execute_query(
        "SELECT * FROM exam_records WHERE id = ?",
        (record_id,),
        fetch_one=True
    )
    
    if not record:
        raise APIError(
            ErrorCodes.EXAM_NOT_STARTED,
            f"考试记录 {record_id} 不存在"
        )
    
    # 准备更新数据
    update_fields = []
    update_values = []
    
    if 'endTime' in data:
        update_fields.append("end_time = ?")
        update_values.append(data['endTime'])
    
    if 'status' in data:
        db_status = convert_status_to_db(data['status'])
        update_fields.append("status = ?")
        update_values.append(db_status)
    
    if 'currentStep' in data:
        update_fields.append("current_step = ?")
        update_values.append(data['currentStep'])
    
    if 'totalDuration' in data:
        update_fields.append("total_duration = ?")
        update_values.append(data['totalDuration'])
    
    # 执行更新
    if update_fields:
        update_values.append(record_id)
        db_manager.execute_query(
            f"UPDATE exam_records SET {', '.join(update_fields)} WHERE id = ?",
            update_values
        )
    
    # 返回更新后的记录
    updated_record = db_manager.execute_query(
        "SELECT * FROM exam_records WHERE id = ?",
        (record_id,),
        fetch_one=True
    )
    
    response_data = {
        "id": updated_record['id'],
        "studentNumber": updated_record['student_number'],
        "startTime": updated_record['exam_date'],
        "endTime": updated_record.get('end_time'),
        "status": convert_status_to_api(updated_record['status']),
        "currentStep": updated_record['current_step'],
        "updatedAt": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    return APIResponse.success(response_data, "考试记录更新成功")

def get_step_records_for_exam(exam_record_id):
    """获取考试的步骤记录"""
    # 这里可以扩展为从专门的步骤记录表获取数据
    # 目前返回基本的步骤信息
    step_names = {
        1: "中文自我介绍",
        2: "英文自我介绍",
        3: "英文翻译",
        4: "专业课问答",
        5: "综合面试"
    }
    
    step_records = []
    for step_num in range(1, 6):
        step_records.append({
            "stepNumber": step_num,
            "stepName": step_names[step_num],
            "status": "pending",  # 可以根据实际情况设置
            "duration": 0,  # 可以根据实际情况计算
            "startTime": None,
            "endTime": None
        })
    
    return step_records

def convert_status_to_api(db_status):
    """将数据库状态转换为API状态"""
    status_map = {
        '准备中': 'preparing',
        '进行中': 'in_progress',
        '已完成': 'completed',
        '已取消': 'cancelled'
    }
    return status_map.get(db_status, db_status)

def convert_status_to_db(api_status):
    """将API状态转换为数据库状态"""
    status_map = {
        'preparing': '准备中',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
    }
    return status_map.get(api_status, api_status)
