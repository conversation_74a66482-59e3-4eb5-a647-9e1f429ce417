<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .log-area {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>按钮事件测试</h1>
        
        <div id="status" class="status">正在加载...</div>
        
        <h3>测试按钮</h3>
        <button id="next-step" class="test-button">下一步 (next-step)</button>
        <button id="prev-step" class="test-button">上一步 (prev-step)</button>
        <button id="start-intro-timer" class="test-button">开始中文介绍计时</button>
        <button id="start-english-intro-timer" class="test-button">开始英文介绍计时</button>
        <button id="draw-translation-question" class="test-button">抽取翻译题</button>
        <button id="draw-professional-question" class="test-button">抽取专业题</button>
        <button id="exam-records" class="test-button">考试记录</button>
        <button id="next-student" class="test-button">下一名考生</button>
        
        <h3>事件日志</h3>
        <div id="log" class="log-area"></div>
        
        <button onclick="clearLog()" class="test-button" style="background-color: #6c757d;">清空日志</button>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="assets/js/utils/logger.js"></script>
    <script src="assets/js/utils/baseClass.js"></script>
    <script src="assets/js/core/database.js"></script>
    <script src="assets/js/core/timer.js"></script>
    <script src="assets/js/core/draggableTimer.js"></script>
    <script src="assets/js/ui/ui.js"></script>
    <script src="assets/js/ui/eventManager.js"></script>
    <script src="assets/js/business/Student.js"></script>
    <script src="assets/js/business/ExamStep.js"></script>
    <script src="assets/js/business/Question.js"></script>
    <script src="assets/js/business/Settings.js"></script>
    <script src="assets/js/core/ExamSystem.js"></script>
    <script src="assets/js/buttonManager.js"></script>

    <script>
        let logElement;
        let statusElement;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            logElement.textContent = '';
        }

        function updateStatus(message, isError = false) {
            statusElement.textContent = message;
            statusElement.className = `status ${isError ? 'error' : 'success'}`;
        }

        async function initializeTest() {
            logElement = document.getElementById('log');
            statusElement = document.getElementById('status');
            
            try {
                log('开始初始化测试环境...');
                
                // 创建ExamSystem实例
                const examSystem = new ExamSystem();
                log('ExamSystem实例已创建');
                
                // 初始化ExamSystem
                const success = await examSystem.initialize();
                if (success) {
                    // 暴露到全局作用域
                    window.examSystem = examSystem;
                    log('ExamSystem初始化成功，已暴露到全局作用域');
                    
                    // 创建ButtonManager实例
                    const buttonManager = new ButtonManager();
                    log('ButtonManager实例已创建并绑定事件');
                    
                    updateStatus('✅ 初始化成功，可以测试按钮点击');
                } else {
                    throw new Error('ExamSystem初始化失败');
                }
                
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`);
                updateStatus(`❌ 初始化失败: ${error.message}`, true);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializeTest);
    </script>
</body>
</html>
