/**
 * 考试计时器模块
 * 提供计时、暂停、重置等功能，支持正计时和倒计时两种模式
 */

// 计时器状态
const timerState = {
  // 计时器ID
  timerId: null,
  // 计时开始时间
  startTime: 0,
  // 当前计时时间（毫秒）
  currentTime: 0,
  // 暂停时的累积时间
  pausedTime: 0,
  // 计时器是否运行中
  isRunning: false,
  // 计时器是否暂停
  isPaused: false,
  // 计时器类型（'countdown' 倒计时 / 'stopwatch' 秒表）
  timerType: 'stopwatch',
  // 倒计时总时长（毫秒）
  countdownDuration: 0,
  // 计时器容器元素
  container: null,
  // 计时器显示元素
  displayElement: null,
  // 警告阈值（剩余时间百分比，触发警告样式）
  warningThreshold: 30,
  // 危险阈值（剩余时间百分比，触发危险样式）
  dangerThreshold: 10,
  // 回调函数
  callbacks: {
    onStart: null,
    onPause: null,
    onResume: null,
    onReset: null,
    onTick: null,
    onComplete: null
  }
};

/**
 * 初始化计时器
 * @param {Object} options 初始化选项
 * @param {string} options.containerId 计时器容器ID
 * @param {string} options.timerType 计时器类型 'countdown'或'stopwatch'
 * @param {number} options.duration 倒计时持续时间（分钟），仅在倒计时模式有效
 * @param {number} options.warningThreshold 警告阈值（百分比）
 * @param {number} options.dangerThreshold 危险阈值（百分比）
 * @param {Object} options.callbacks 回调函数对象
 * @returns {Object} 计时器控制对象
 */
function initTimer(options = {}) {
  // 默认选项
  const defaultOptions = {
    containerId: 'timer-container',
    timerType: 'stopwatch',
    duration: 30,
    warningThreshold: 30,
    dangerThreshold: 10,
    callbacks: {}
  };

  // 合并选项
  const settings = { ...defaultOptions, ...options };

  // 重置状态
  resetTimerState();

  // 设置计时器类型
  timerState.timerType = settings.timerType;

  // 设置倒计时持续时间（转换为毫秒）
  if (settings.timerType === 'countdown') {
    timerState.countdownDuration = settings.duration * 60 * 1000;
    timerState.currentTime = timerState.countdownDuration;
  }

  // 设置阈值
  timerState.warningThreshold = settings.warningThreshold;
  timerState.dangerThreshold = settings.dangerThreshold;

  // 设置回调
  if (settings.callbacks) {
    timerState.callbacks = { ...timerState.callbacks, ...settings.callbacks };
  }

  // 初始化UI
  initTimerUI(settings.containerId);

  // 绑定事件
  bindTimerEvents();

  // 更新显示
  updateTimerDisplay();

  // 返回计时器控制接口
  return {
    start: startTimer,
    pause: pauseTimer,
    resume: resumeTimer,
    reset: resetTimer,
    getRemainingTime: getRemainingTime,
    getElapsedTime: getElapsedTime,
    isRunning: () => timerState.isRunning,
    isPaused: () => timerState.isPaused
  };
}

/**
 * 重置计时器状态
 */
function resetTimerState() {
  // 清除计时器
  if (timerState.timerId) {
    clearInterval(timerState.timerId);
  }

  // 重置状态
  timerState.timerId = null;
  timerState.startTime = 0;
  timerState.currentTime = 0;
  timerState.pausedTime = 0;
  timerState.isRunning = false;
  timerState.isPaused = false;
}

/**
 * 初始化计时器UI
 * @param {string} containerId 容器ID
 */
function initTimerUI(containerId) {
  // 获取容器元素
  const container = document.getElementById(containerId);
  if (!container) {
    console.error(`计时器容器 #${containerId} 未找到`);
    return;
  }

  timerState.container = container;

  // 清空容器
  container.innerHTML = '';

  // 创建计时器标题
  const title = document.createElement('div');
  title.className = 'timer-title';
  title.textContent = timerState.timerType === 'countdown' ? '剩余时间' : '已用时间';

  // 创建计时器显示
  const display = document.createElement('div');
  display.className = 'timer-display timer-normal';
  display.textContent = formatTime(timerState.currentTime);
  timerState.displayElement = display;

  // 创建按钮容器
  const controls = document.createElement('div');
  controls.className = 'timer-controls';

  // 创建开始按钮
  const startBtn = document.createElement('button');
  startBtn.className = 'btn';
  startBtn.textContent = '开始';
  startBtn.id = 'timer-start';

  // 创建暂停按钮
  const pauseBtn = document.createElement('button');
  pauseBtn.className = 'btn';
  pauseBtn.textContent = '暂停';
  pauseBtn.id = 'timer-pause';
  pauseBtn.disabled = true;

  // 创建重置按钮
  const resetBtn = document.createElement('button');
  resetBtn.className = 'btn';
  resetBtn.textContent = '重置';
  resetBtn.id = 'timer-reset';
  resetBtn.disabled = true;

  // 添加按钮到控制容器
  controls.appendChild(startBtn);
  controls.appendChild(pauseBtn);
  controls.appendChild(resetBtn);

  // 添加元素到容器
  container.appendChild(title);
  container.appendChild(display);
  container.appendChild(controls);
}

/**
 * 绑定计时器事件
 */
function bindTimerEvents() {
  if (!timerState.container) return;

  // 获取按钮
  const startBtn = timerState.container.querySelector('#timer-start');
  const pauseBtn = timerState.container.querySelector('#timer-pause');
  const resetBtn = timerState.container.querySelector('#timer-reset');

  // 绑定开始按钮
  if (startBtn) {
    startBtn.addEventListener('click', () => {
      if (timerState.isPaused) {
        resumeTimer();
      } else {
        startTimer();
      }
    });
  }

  // 绑定暂停按钮
  if (pauseBtn) {
    pauseBtn.addEventListener('click', pauseTimer);
  }

  // 绑定重置按钮
  if (resetBtn) {
    resetBtn.addEventListener('click', resetTimer);
  }
}

/**
 * 开始计时
 */
function startTimer() {
  if (timerState.isRunning) return;

  // 设置开始时间
  timerState.startTime = Date.now();

  // 如果是倒计时且当前时间为0，则重置为总时长
  if (timerState.timerType === 'countdown' && timerState.currentTime === 0) {
    timerState.currentTime = timerState.countdownDuration;
  }

  // 开始计时
  timerState.isRunning = true;
  timerState.isPaused = false;

  // 更新按钮状态
  updateTimerButtonStates();

  // 启动定时器
  timerState.timerId = setInterval(updateTimer, 100);

  // 触发回调
  if (typeof timerState.callbacks.onStart === 'function') {
    timerState.callbacks.onStart();
  }
}

/**
 * 暂停计时
 */
function pauseTimer() {
  if (!timerState.isRunning || timerState.isPaused) return;

  // 暂停计时器
  clearInterval(timerState.timerId);

  // 更新状态
  timerState.isPaused = true;
  timerState.pausedTime = Date.now() - timerState.startTime;

  // 更新按钮状态
  updateTimerButtonStates();

  // 触发回调
  if (typeof timerState.callbacks.onPause === 'function') {
    timerState.callbacks.onPause();
  }
}

/**
 * 恢复计时
 */
function resumeTimer() {
  if (!timerState.isPaused) return;

  // 更新开始时间
  timerState.startTime = Date.now() - timerState.pausedTime;

  // 更新状态
  timerState.isPaused = false;

  // 更新按钮状态
  updateTimerButtonStates();

  // 启动定时器
  timerState.timerId = setInterval(updateTimer, 100);

  // 触发回调
  if (typeof timerState.callbacks.onResume === 'function') {
    timerState.callbacks.onResume();
  }
}

/**
 * 重置计时器
 */
function resetTimer() {
  // 清除计时器
  clearInterval(timerState.timerId);

  // 重置状态
  timerState.isRunning = false;
  timerState.isPaused = false;
  timerState.startTime = 0;
  timerState.pausedTime = 0;

  // 重置时间
  if (timerState.timerType === 'countdown') {
    timerState.currentTime = timerState.countdownDuration;
  } else {
    timerState.currentTime = 0;
  }

  // 更新显示
  updateTimerDisplay();

  // 更新按钮状态
  updateTimerButtonStates();

  // 触发回调
  if (typeof timerState.callbacks.onReset === 'function') {
    timerState.callbacks.onReset();
  }
}

/**
 * 更新计时器
 */
function updateTimer() {
  // 计算当前时间
  if (timerState.timerType === 'countdown') {
    // 倒计时：剩余时间 = 总时间 - 已过时间
    const elapsed = Date.now() - timerState.startTime;
    timerState.currentTime = Math.max(0, timerState.countdownDuration - elapsed);

    // 如果倒计时结束
    if (timerState.currentTime <= 0) {
      clearInterval(timerState.timerId);
      timerState.isRunning = false;
      timerState.currentTime = 0;

      // 触发完成回调
      if (typeof timerState.callbacks.onComplete === 'function') {
        timerState.callbacks.onComplete();
      }
    }
  } else {
    // 秒表：当前时间 = 已过时间
    timerState.currentTime = Date.now() - timerState.startTime;
  }

  // 更新显示
  updateTimerDisplay();

  // 触发tick回调
  if (typeof timerState.callbacks.onTick === 'function') {
    timerState.callbacks.onTick(timerState.currentTime);
  }
}

/**
 * 更新计时器显示
 */
function updateTimerDisplay() {
  if (!timerState.displayElement) return;

  // 更新时间显示
  timerState.displayElement.textContent = formatTime(timerState.currentTime);

  // 更新样式
  if (timerState.timerType === 'countdown') {
    const remainingPercentage = (timerState.currentTime / timerState.countdownDuration) * 100;

    // 移除所有状态类
    timerState.displayElement.classList.remove('timer-normal', 'timer-warning', 'timer-danger');

    // 添加对应状态类
    if (remainingPercentage <= timerState.dangerThreshold) {
      timerState.displayElement.classList.add('timer-danger');
    } else if (remainingPercentage <= timerState.warningThreshold) {
      timerState.displayElement.classList.add('timer-warning');
    } else {
      timerState.displayElement.classList.add('timer-normal');
    }
  }
}

/**
 * 更新计时器按钮状态
 */
function updateTimerButtonStates() {
  if (!timerState.container) return;

  // 获取按钮
  const startBtn = timerState.container.querySelector('#timer-start');
  const pauseBtn = timerState.container.querySelector('#timer-pause');
  const resetBtn = timerState.container.querySelector('#timer-reset');

  if (startBtn) {
    // 更新开始按钮
    if (timerState.isPaused) {
      startBtn.textContent = '继续';
      startBtn.disabled = false;
    } else if (timerState.isRunning) {
      startBtn.disabled = true;
    } else {
      startBtn.textContent = '开始';
      startBtn.disabled = false;
    }
  }

  if (pauseBtn) {
    // 更新暂停按钮
    pauseBtn.disabled = !timerState.isRunning || timerState.isPaused;
  }

  if (resetBtn) {
    // 更新重置按钮
    resetBtn.disabled = !timerState.isRunning && !timerState.isPaused &&
      (timerState.timerType === 'stopwatch' ? timerState.currentTime === 0 : timerState.currentTime === timerState.countdownDuration);
  }
}

/**
 * 格式化时间为 MM:SS.MS 格式
 * @param {number} time 时间（毫秒）
 * @returns {string} 格式化的时间字符串
 */
function formatTime(time) {
  const totalSeconds = Math.floor(time / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  const milliseconds = Math.floor((time % 1000) / 10);

  // 格式化为 MM:SS.MS
  return `${padZero(minutes)}:${padZero(seconds)}.${padZero(milliseconds)}`;
}

/**
 * 数字补零
 * @param {number} num 数字
 * @returns {string} 补零后的字符串
 */
function padZero(num) {
  return num.toString().padStart(2, '0');
}

/**
 * 获取剩余时间（毫秒）
 * @returns {number} 剩余时间
 */
function getRemainingTime() {
  if (timerState.timerType !== 'countdown') return 0;
  return timerState.currentTime;
}

/**
 * 获取已用时间（毫秒）
 * @returns {number} 已用时间
 */
function getElapsedTime() {
  if (timerState.timerType === 'countdown') {
    return timerState.countdownDuration - timerState.currentTime;
  }
  return timerState.currentTime;
}

// 导出计时器模块到全局作用域
window.TimerModule = {
  initTimer,
  formatTime
};
/**
 * Timer类 - 面向对象的计时器封装
 */
class Timer extends BaseClass {
  constructor() {
    super('Timer');
    this.timerId = null;
    this.isRunning = false;
    this.callbacks = { onTick: null, onComplete: null };
  }

  async initialize() {
    this.setInitialized(true);
    this.log('计时器初始化成功');
    return true;
  }

  start() {
    if (this.duration <= 0) {
      this.log('计时器时长未设置', 'warn');
      return;
    }

    this.isRunning = true;
    this.isPaused = false;
    this.startTime = Date.now();
    this.pausedTime = 0;
    this.remaining = this.duration;

    this._startTicking();
    this.log('计时器开始');
  }

  pause() {
    if (!this.isRunning) return;

    this.isPaused = true;
    this.pausedTime += Date.now() - this.startTime;

    if (this.timerId) {
      clearInterval(this.timerId);
      this.timerId = null;
    }
    this.log('计时器暂停');
  }

  /**
   * 设置计时器时长
   * @param {number} duration - 时长（秒）
   */
  setDuration(duration) {
    this.duration = duration;
    this.remaining = duration;

    // 重置计时器状态，确保从头开始
    this.startTime = 0;
    this.pausedTime = 0;

    this.log(`设置计时器时长: ${duration}秒 (状态已重置)`);
  }

  /**
   * 停止计时器
   */
  stop() {
    this.isRunning = false;
    this.isPaused = false;
    if (this.timerId) {
      clearInterval(this.timerId);
      this.timerId = null;
    }

    // 重置计时器状态，确保下次开始时从头计时
    this.startTime = 0;
    this.pausedTime = 0;
    this.remaining = this.duration;  // 重置为完整时长

    this.log('计时器停止并重置');
  }

  /**
   * 恢复计时器
   */
  resume() {
    if (!this.isPaused) return;

    this.isPaused = false;
    this.startTime = Date.now();
    this._startTicking();
    this.log('计时器恢复');
  }

  /**
   * 获取计时器状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      duration: this.duration || 0,
      remaining: this.remaining || 0,
      elapsed: (this.duration || 0) - (this.remaining || 0)
    };
  }

  on(event, callback) {
    if (this.callbacks.hasOwnProperty(event)) {
      this.callbacks[event] = callback;
    }
  }

  /**
   * 开始计时循环
   * @private
   */
  _startTicking() {
    this.timerId = setInterval(() => {
      if (!this.isRunning || this.isPaused) return;

      const elapsed = (Date.now() - this.startTime + (this.pausedTime || 0)) / 1000;
      this.remaining = Math.max(0, (this.duration || 0) - elapsed);

      // 触发tick事件
      if (this.callbacks.onTick) {
        this.callbacks.onTick({
          remaining: this.remaining,
          elapsed: elapsed,
          duration: this.duration || 0
        });
      }

      // 检查是否完成
      if (this.remaining <= 0) {
        this.stop();
        if (this.callbacks.onComplete) {
          this.callbacks.onComplete();
        }
      }
    }, 1000);
  }

  destroy() {
    this.stop();
    super.destroy();
  }
}

// 导出Timer类
window.Timer = Timer;