# 研究生复试系统 - 逻辑接口文档

## 架构概述

本系统采用标准MVC架构，严格分离数据、业务逻辑和视图层：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     View        │    │   Controller    │    │     Model       │
│   (UI Layer)    │◄──►│ (Logic Layer)   │◄──►│  (Data Layer)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  UI Components  │    │   Controllers   │    │    Services     │
│  - ExamView     │    │ - ExamController│    │ - DatabaseSvc   │
│  - QuestionView │    │ - QuestionCtrl  │    │ - QuestionSvc   │
│  - TimerView    │    │ - StudentCtrl   │    │ - StudentSvc    │
│  - SettingsView │    │ - SettingsCtrl  │    │ - SettingsSvc   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 核心原则

1. **单一职责**：每个模块只负责一个特定功能
2. **依赖倒置**：高层模块不依赖低层模块，都依赖抽象
3. **接口隔离**：使用接口定义模块间通信
4. **数据驱动**：所有交互通过标准数据格式

## 数据传递格式

### 标准响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

### 标准请求格式
```typescript
interface ApiRequest<T> {
  action: string;
  data: T;
  requestId?: string;
  timestamp: string;
}
```

## Model层接口

### Student Model
```typescript
interface Student {
  id: number;
  studentNumber: string;
  currentStep: number;
  startTime: string;
  status: 'active' | 'completed' | 'paused';
  stepData: {
    [stepNumber: number]: StepData;
  };
}

interface StepData {
  startTime?: string;
  endTime?: string;
  duration?: number;
  questions?: Question[];
  status: 'pending' | 'active' | 'completed';
}
```

### Question Model
```typescript
interface Question {
  id: string;
  type: 'translation' | 'professional';
  index: number;
  title: string;
  content: any;
  isUsed: boolean;
  usedBy?: string;
  usedAt?: string;
}
```

### ExamRecord Model
```typescript
interface ExamRecord {
  id: number;
  studentId: number;
  studentNumber: string;
  startTime: string;
  endTime?: string;
  currentStep: number;
  totalDuration: number;
  stepRecords: StepRecord[];
  status: 'in_progress' | 'completed' | 'cancelled';
}

interface StepRecord {
  stepNumber: number;
  stepName: string;
  startTime: string;
  endTime?: string;
  duration: number;
  questions: Question[];
  status: 'completed' | 'in_progress' | 'skipped';
}
```

### Settings Model
```typescript
interface Settings {
  timeSettings: {
    chineseTime: number;
    englishTime: number;
    translationTime: number;
    professionalTime: number;
    comprehensiveTime: number;
  };
  systemSettings: {
    debugMode: boolean;
    autoSave: boolean;
    theme: 'light' | 'dark';
  };
  uiSettings: {
    fontSize: 'small' | 'medium' | 'large';
    language: 'zh-CN' | 'en-US';
  };
}
```

## Controller层接口

### ExamController
```typescript
interface ExamController {
  // 考试流程控制
  startExam(studentNumber: string): Promise<ApiResponse<Student>>;
  nextStep(): Promise<ApiResponse<Student>>;
  previousStep(): Promise<ApiResponse<Student>>;
  completeExam(): Promise<ApiResponse<ExamRecord>>;
  
  // 考生管理
  switchStudent(studentNumber: string): Promise<ApiResponse<Student>>;
  getCurrentStudent(): Promise<ApiResponse<Student>>;
  
  // 状态查询
  getExamStatus(): Promise<ApiResponse<ExamStatus>>;
  getStepInfo(stepNumber: number): Promise<ApiResponse<StepInfo>>;
}

interface ExamStatus {
  currentStudent: Student;
  currentStep: number;
  totalSteps: number;
  isTimerRunning: boolean;
  remainingTime: number;
}

interface StepInfo {
  stepNumber: number;
  stepName: string;
  description: string;
  timeLimit: number;
  hasQuestions: boolean;
  questionTypes: string[];
}
```

### QuestionController
```typescript
interface QuestionController {
  // 题目抽取
  drawQuestion(type: 'translation' | 'professional'): Promise<ApiResponse<Question>>;
  
  // 题目状态管理
  getUsedQuestions(type: string): Promise<ApiResponse<number[]>>;
  getAvailableQuestions(type: string): Promise<ApiResponse<Question[]>>;
  markQuestionAsUsed(questionId: string, studentNumber: string): Promise<ApiResponse<void>>;
  
  // 题目查询
  getQuestionByNumber(type: string, number: number): Promise<ApiResponse<Question>>;
  getQuestionStats(type: string): Promise<ApiResponse<QuestionStats>>;
}

interface QuestionStats {
  total: number;
  used: number;
  available: number;
  usageRate: number;
}
```

### TimerController
```typescript
interface TimerController {
  // 计时器控制
  startTimer(stepNumber: number): Promise<ApiResponse<TimerState>>;
  pauseTimer(): Promise<ApiResponse<TimerState>>;
  resetTimer(): Promise<ApiResponse<TimerState>>;
  stopTimer(): Promise<ApiResponse<TimerState>>;
  
  // 计时器状态
  getTimerState(): Promise<ApiResponse<TimerState>>;
  setTimerDuration(duration: number): Promise<ApiResponse<TimerState>>;
}

interface TimerState {
  isRunning: boolean;
  isPaused: boolean;
  duration: number;
  remainingTime: number;
  startTime?: string;
  endTime?: string;
}
```

### StudentController
```typescript
interface StudentController {
  // 学生管理
  createStudent(studentNumber: string): Promise<ApiResponse<Student>>;
  getStudent(studentNumber: string): Promise<ApiResponse<Student>>;
  updateStudent(student: Partial<Student>): Promise<ApiResponse<Student>>;
  
  // 学生状态
  saveStudentProgress(studentNumber: string, stepData: StepData): Promise<ApiResponse<void>>;
  getStudentProgress(studentNumber: string): Promise<ApiResponse<Student>>;
  
  // 考试记录
  getExamRecords(): Promise<ApiResponse<ExamRecord[]>>;
  getStudentRecord(studentNumber: string): Promise<ApiResponse<ExamRecord>>;
}
```

### SettingsController
```typescript
interface SettingsController {
  // 设置管理
  getSettings(): Promise<ApiResponse<Settings>>;
  updateSettings(settings: Partial<Settings>): Promise<ApiResponse<Settings>>;
  resetSettings(): Promise<ApiResponse<Settings>>;
  
  // 特定设置
  getTimeSettings(): Promise<ApiResponse<Settings['timeSettings']>>;
  updateTimeSettings(timeSettings: Partial<Settings['timeSettings']>): Promise<ApiResponse<Settings['timeSettings']>>;
}
```

## View层接口

### ExamView
```typescript
interface ExamView {
  // 显示更新
  updateStepDisplay(stepInfo: StepInfo): void;
  updateStudentInfo(student: Student): void;
  updateProgressIndicator(current: number, total: number): void;
  
  // 用户交互事件
  onNextStep: () => void;
  onPreviousStep: () => void;
  onSwitchStudent: (studentNumber: string) => void;
  
  // 状态显示
  showLoading(message: string): void;
  hideLoading(): void;
  showError(error: string): void;
  showSuccess(message: string): void;
}
```

### QuestionView
```typescript
interface QuestionView {
  // 题目显示
  displayQuestion(question: Question): void;
  displayQuestionGrid(questions: Question[], usedQuestions: number[]): void;
  showQuestionAnimation(selectedNumber: number): void;
  
  // 用户交互
  onDrawQuestion: (type: string) => void;
  onQuestionSelected: (questionId: string) => void;
  
  // 状态更新
  updateQuestionStats(stats: QuestionStats): void;
  highlightSelectedQuestion(number: number): void;
}
```

### TimerView
```typescript
interface TimerView {
  // 计时器显示
  updateTimerDisplay(state: TimerState): void;
  updateTimeRemaining(seconds: number): void;
  
  // 用户交互
  onStartTimer: () => void;
  onPauseTimer: () => void;
  onResetTimer: () => void;
  
  // 状态指示
  showTimerRunning(): void;
  showTimerPaused(): void;
  showTimerStopped(): void;
}
```

## Service层接口

### DatabaseService
```typescript
interface DatabaseService {
  // 基础CRUD操作
  create<T>(table: string, data: T): Promise<T>;
  read<T>(table: string, id: string | number): Promise<T | null>;
  update<T>(table: string, id: string | number, data: Partial<T>): Promise<T>;
  delete(table: string, id: string | number): Promise<boolean>;
  
  // 查询操作
  query<T>(sql: string, params?: any[]): Promise<T[]>;
  findOne<T>(table: string, conditions: any): Promise<T | null>;
  findMany<T>(table: string, conditions?: any): Promise<T[]>;
  
  // 事务操作
  transaction<T>(callback: (tx: Transaction) => Promise<T>): Promise<T>;
}
```

### QuestionService
```typescript
interface QuestionService {
  // 题目管理
  getAllQuestions(type: string): Promise<Question[]>;
  getQuestionByNumber(type: string, number: number): Promise<Question | null>;
  getUsedQuestions(type: string): Promise<number[]>;
  markQuestionAsUsed(questionId: string, studentNumber: string): Promise<void>;
  
  // 题目统计
  getQuestionStats(type: string): Promise<QuestionStats>;
  resetQuestionUsage(type?: string): Promise<void>;
}
```

### StudentService
```typescript
interface StudentService {
  // 学生管理
  createStudent(studentNumber: string): Promise<Student>;
  getStudent(studentNumber: string): Promise<Student | null>;
  updateStudent(studentNumber: string, data: Partial<Student>): Promise<Student>;
  
  // 进度管理
  saveProgress(studentNumber: string, stepNumber: number, stepData: StepData): Promise<void>;
  getProgress(studentNumber: string): Promise<Student>;
  
  // 记录管理
  createExamRecord(student: Student): Promise<ExamRecord>;
  updateExamRecord(recordId: number, data: Partial<ExamRecord>): Promise<ExamRecord>;
  getExamRecords(): Promise<ExamRecord[]>;
}
```

### SettingsService
```typescript
interface SettingsService {
  // 设置管理
  getSettings(): Promise<Settings>;
  updateSettings(settings: Partial<Settings>): Promise<Settings>;
  resetSettings(): Promise<Settings>;
  
  // 设置监听
  onSettingsChange(callback: (settings: Settings) => void): void;
  offSettingsChange(callback: (settings: Settings) => void): void;
}
```

## 错误处理

### 错误代码定义
```typescript
enum ErrorCode {
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INVALID_REQUEST = 'INVALID_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  
  // 学生相关错误
  STUDENT_NOT_FOUND = 'STUDENT_NOT_FOUND',
  STUDENT_ALREADY_EXISTS = 'STUDENT_ALREADY_EXISTS',
  
  // 题目相关错误
  QUESTION_NOT_FOUND = 'QUESTION_NOT_FOUND',
  NO_AVAILABLE_QUESTIONS = 'NO_AVAILABLE_QUESTIONS',
  QUESTION_ALREADY_USED = 'QUESTION_ALREADY_USED',
  
  // 考试相关错误
  EXAM_NOT_STARTED = 'EXAM_NOT_STARTED',
  INVALID_STEP = 'INVALID_STEP',
  TIMER_NOT_RUNNING = 'TIMER_NOT_RUNNING',
  
  // 数据库错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR'
}
```

## 事件系统

### 事件定义
```typescript
interface SystemEvent {
  type: string;
  data: any;
  timestamp: string;
  source: string;
}

// 考试事件
interface ExamEvent extends SystemEvent {
  type: 'exam.started' | 'exam.step.changed' | 'exam.completed';
  data: {
    studentNumber: string;
    stepNumber?: number;
    previousStep?: number;
  };
}

// 题目事件
interface QuestionEvent extends SystemEvent {
  type: 'question.drawn' | 'question.used' | 'question.displayed';
  data: {
    questionId: string;
    questionType: string;
    questionNumber: number;
    studentNumber: string;
  };
}

// 计时器事件
interface TimerEvent extends SystemEvent {
  type: 'timer.started' | 'timer.paused' | 'timer.stopped' | 'timer.tick';
  data: {
    stepNumber: number;
    remainingTime: number;
    duration: number;
  };
}
```

## 模块通信规范

1. **Controller ↔ Service**：通过Promise返回标准ApiResponse格式
2. **Controller ↔ View**：通过事件系统和回调函数
3. **Service ↔ Model**：通过标准数据模型接口
4. **跨模块通信**：通过事件总线(EventBus)

## 初始化流程

```typescript
// 系统启动顺序
1. DatabaseService.init()
2. SettingsService.init()
3. QuestionService.init()
4. StudentService.init()
5. Controllers.init()
6. Views.init()
7. EventBus.init()
```

这个接口文档定义了各层之间的清晰边界和通信协议，确保模块间只通过标准数据格式进行交互。
