/**
 * 题目类 - 管理单个题目的数据和状态
 * 遵循单一职责原则，专门负责题目相关的业务逻辑
 */
class Question extends BaseClass {
    constructor(id, type, content, options = {}) {
        super('Question');
        this.id = id || Utils.generateId('question');
        this.type = type; // 'translation' 或 'professional'
        this.content = content;
        this.difficulty = options.difficulty || 'medium';
        this.category = options.category || '';
        this.tags = options.tags || [];
        this.createdAt = options.createdAt || new Date();
        this.updatedAt = options.updatedAt || new Date();
        this.usageCount = options.usageCount || 0;
        this.lastUsed = options.lastUsed || null;
        this.isActive = options.isActive !== false;
        this.metadata = options.metadata || {};
    }

    /**
     * 初始化题目
     * @returns {Promise<boolean>} 初始化结果
     */
    async initialize() {
        try {
            this.validate();
            this.log(`题目 ${this.id} 初始化成功`);
            this.initialized = true;
            return true;
        } catch (error) {
            this.handleError(error, '题目初始化');
            return false;
        }
    }

    /**
     * 验证题目数据
     * @returns {boolean} 验证结果
     */
    validate() {
        if (!Utils.validateInput(this.id, 'string', { minLength: 1 })) {
            throw new Error('题目ID无效');
        }

        if (!['translation', 'professional'].includes(this.type)) {
            throw new Error('题目类型无效');
        }

        if (!Utils.validateInput(this.content, 'string', { minLength: 1 })) {
            throw new Error('题目内容无效');
        }

        return true;
    }

    /**
     * 更新题目内容
     * @param {string} content - 新的题目内容
     */
    updateContent(content) {
        if (Utils.validateInput(content, 'string', { minLength: 1 })) {
            this.content = content;
            this.updatedAt = new Date();
            this.log(`题目 ${this.id} 内容已更新`);
        } else {
            this.log('题目内容无效', 'error');
        }
    }

    /**
     * 设置难度
     * @param {string} difficulty - 难度级别 ('easy', 'medium', 'hard')
     */
    setDifficulty(difficulty) {
        const validDifficulties = ['easy', 'medium', 'hard'];
        if (validDifficulties.includes(difficulty)) {
            this.difficulty = difficulty;
            this.updatedAt = new Date();
            this.log(`题目 ${this.id} 难度设置为: ${difficulty}`);
        } else {
            this.log('无效的难度级别', 'error');
        }
    }

    /**
     * 设置分类
     * @param {string} category - 分类名称
     */
    setCategory(category) {
        this.category = category;
        this.updatedAt = new Date();
        this.log(`题目 ${this.id} 分类设置为: ${category}`);
    }

    /**
     * 添加标签
     * @param {string} tag - 标签名称
     */
    addTag(tag) {
        if (tag && !this.tags.includes(tag)) {
            this.tags.push(tag);
            this.updatedAt = new Date();
            this.log(`题目 ${this.id} 添加标签: ${tag}`);
        }
    }

    /**
     * 移除标签
     * @param {string} tag - 标签名称
     */
    removeTag(tag) {
        const index = this.tags.indexOf(tag);
        if (index > -1) {
            this.tags.splice(index, 1);
            this.updatedAt = new Date();
            this.log(`题目 ${this.id} 移除标签: ${tag}`);
        }
    }

    /**
     * 记录使用
     */
    recordUsage() {
        this.usageCount++;
        this.lastUsed = new Date();
        this.log(`题目 ${this.id} 使用次数: ${this.usageCount}`);
    }

    /**
     * 激活题目
     */
    activate() {
        this.isActive = true;
        this.updatedAt = new Date();
        this.log(`题目 ${this.id} 已激活`);
    }

    /**
     * 停用题目
     */
    deactivate() {
        this.isActive = false;
        this.updatedAt = new Date();
        this.log(`题目 ${this.id} 已停用`);
    }

    /**
     * 设置元数据
     * @param {string} key - 键名
     * @param {any} value - 值
     */
    setMetadata(key, value) {
        this.metadata[key] = value;
        this.updatedAt = new Date();
        this.log(`题目 ${this.id} 设置元数据: ${key}`);
    }

    /**
     * 获取元数据
     * @param {string} key - 键名
     * @returns {any} 元数据值
     */
    getMetadata(key) {
        return this.metadata[key];
    }

    /**
     * 获取题目摘要
     * @returns {Object} 题目摘要对象
     */
    getSummary() {
        return {
            id: this.id,
            type: this.type,
            contentPreview: this.content.substring(0, 100) + (this.content.length > 100 ? '...' : ''),
            difficulty: this.difficulty,
            category: this.category,
            tagsCount: this.tags.length,
            usageCount: this.usageCount,
            lastUsed: this.lastUsed,
            isActive: this.isActive
        };
    }

    /**
     * 导出题目数据
     * @returns {Object} 题目数据对象
     */
    export() {
        return {
            id: this.id,
            type: this.type,
            content: this.content,
            difficulty: this.difficulty,
            category: this.category,
            tags: [...this.tags],
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            usageCount: this.usageCount,
            lastUsed: this.lastUsed,
            isActive: this.isActive,
            metadata: Utils.deepClone(this.metadata)
        };
    }

    /**
     * 导入题目数据
     * @param {Object} data - 题目数据对象
     * @returns {boolean} 导入结果
     */
    import(data) {
        try {
            this.id = data.id;
            this.type = data.type;
            this.content = data.content;
            this.difficulty = data.difficulty || 'medium';
            this.category = data.category || '';
            this.tags = data.tags || [];
            this.createdAt = data.createdAt ? new Date(data.createdAt) : new Date();
            this.updatedAt = data.updatedAt ? new Date(data.updatedAt) : new Date();
            this.usageCount = data.usageCount || 0;
            this.lastUsed = data.lastUsed ? new Date(data.lastUsed) : null;
            this.isActive = data.isActive !== false;
            this.metadata = data.metadata || {};
            
            this.validate();
            this.log(`题目 ${this.id} 数据导入成功`);
            return true;
        } catch (error) {
            this.handleError(error, '导入题目数据');
            return false;
        }
    }
}

/**
 * 题库类 - 管理题目集合和题目选择逻辑
 * 遵循单一职责原则，专门负责题库管理
 */
class QuestionBank extends BaseClass {
    constructor() {
        super('QuestionBank');
        this.questions = new Map();
        this.usedQuestions = new Set();
        this.categories = new Set();
        this.tags = new Set();
        this.translationQuestions = [];
        this.professionalQuestions = [];
    }

    /**
     * 初始化题库
     * @returns {Promise<boolean>} 初始化结果
     */
    async initialize() {
        try {
            await this.loadQuestions();
            this.buildIndices();
            this.log('题库初始化成功');
            this.initialized = true;
            return true;
        } catch (error) {
            this.handleError(error, '题库初始化');
            return false;
        }
    }

    /**
     * 加载题目数据
     */
    async loadQuestions() {
        try {
            // 从数据库API加载翻译题
            await this.loadTranslationQuestionsFromAPI();

            // 从数据库API加载专业题
            await this.loadProfessionalQuestionsFromAPI();

            // 如果数据库没有数据，尝试从全局变量加载（向后兼容）
            if (this.questions.size === 0) {
                this.loadQuestionsFromGlobalVariables();
            }

            this.log(`加载了 ${this.questions.size} 道题目`);
        } catch (error) {
            this.handleError(error, '加载题目数据');
            // 如果API加载失败，尝试从全局变量加载
            this.loadQuestionsFromGlobalVariables();
        }
    }

    /**
     * 从API加载翻译题
     */
    async loadTranslationQuestionsFromAPI() {
        try {
            const response = await fetch('/api/questions/translation?limit=1000');
            const data = await response.json();

            if (data.questions && Array.isArray(data.questions)) {
                data.questions.forEach(questionData => {
                    try {
                        // 解析题目数据
                        let content;
                        if (typeof questionData.question_data === 'string') {
                            content = JSON.parse(questionData.question_data);
                        } else {
                            content = questionData.question_data;
                        }

                        const question = new Question(
                            `translation_${questionData.id}`,
                            'translation',
                            content,
                            {
                                category: 'translation',
                                dbId: questionData.id,
                                questionIndex: questionData.question_index
                            }
                        );
                        this.addQuestion(question);
                    } catch (parseError) {
                        this.log(`解析翻译题 ${questionData.id} 失败: ${parseError.message}`, 'warn');
                        this.log(`原始数据: ${JSON.stringify(questionData.question_data)}`, 'debug');
                    }
                });
                this.log(`从API加载了 ${data.questions.length} 道翻译题`);
            }
        } catch (error) {
            this.log(`从API加载翻译题失败: ${error.message}`, 'warn');
        }
    }

    /**
     * 从API加载专业题
     */
    async loadProfessionalQuestionsFromAPI() {
        try {
            const response = await fetch('/api/questions/professional?limit=1000');
            const data = await response.json();

            if (data.questions && Array.isArray(data.questions)) {
                data.questions.forEach(questionData => {
                    try {
                        // 解析题目数据
                        let content;
                        if (typeof questionData.question_data === 'string') {
                            content = JSON.parse(questionData.question_data);
                        } else {
                            content = questionData.question_data;
                        }

                        const question = new Question(
                            `professional_${questionData.id}`,
                            'professional',
                            content,
                            {
                                category: 'professional',
                                dbId: questionData.id,
                                questionIndex: questionData.question_index
                            }
                        );
                        this.addQuestion(question);
                    } catch (parseError) {
                        this.log(`解析专业题 ${questionData.id} 失败: ${parseError.message}`, 'warn');
                        this.log(`原始数据: ${JSON.stringify(questionData.question_data)}`, 'debug');
                    }
                });
                this.log(`从API加载了 ${data.questions.length} 道专业题`);
            }
        } catch (error) {
            this.log(`从API加载专业题失败: ${error.message}`, 'warn');
        }
    }

    /**
     * 从全局变量加载题目数据（向后兼容）
     */
    loadQuestionsFromGlobalVariables() {
        // 加载翻译题
        if (window.translationQuestions) {
            window.translationQuestions.forEach((content, index) => {
                const question = new Question(
                    `translation_${index + 1}`,
                    'translation',
                    content,
                    { category: 'translation' }
                );
                this.addQuestion(question);
            });
        }

        // 加载专业题
        if (window.professionalQuestions) {
            window.professionalQuestions.forEach((content, index) => {
                const question = new Question(
                    `professional_${index + 1}`,
                    'professional',
                    content,
                    { category: 'professional' }
                );
                this.addQuestion(question);
            });
        }

        this.log(`从全局变量加载了 ${this.questions.size} 道题目`);
    }

    /**
     * 构建索引
     */
    buildIndices() {
        this.translationQuestions = [];
        this.professionalQuestions = [];
        this.categories.clear();
        this.tags.clear();

        this.questions.forEach(question => {
            // 按类型分类
            if (question.type === 'translation') {
                this.translationQuestions.push(question);
            } else if (question.type === 'professional') {
                this.professionalQuestions.push(question);
            }

            // 收集分类和标签
            if (question.category) {
                this.categories.add(question.category);
            }
            question.tags.forEach(tag => this.tags.add(tag));
        });

        this.log('题库索引构建完成');
    }

    /**
     * 添加题目
     * @param {Question} question - 题目对象
     * @returns {boolean} 添加结果
     */
    addQuestion(question) {
        if (!(question instanceof Question)) {
            this.log('必须是Question实例', 'error');
            return false;
        }

        this.questions.set(question.id, question);
        this.buildIndices();
        this.log(`添加题目: ${question.id}`);
        return true;
    }

    /**
     * 移除题目
     * @param {string} questionId - 题目ID
     * @returns {boolean} 移除结果
     */
    removeQuestion(questionId) {
        if (this.questions.has(questionId)) {
            this.questions.delete(questionId);
            this.buildIndices();
            this.log(`移除题目: ${questionId}`);
            return true;
        }
        return false;
    }

    /**
     * 获取题目
     * @param {string} questionId - 题目ID
     * @returns {Question|null} 题目对象
     */
    getQuestion(questionId) {
        return this.questions.get(questionId) || null;
    }

    /**
     * 随机选择题目
     * @param {string} type - 题目类型
     * @param {number} count - 选择数量
     * @param {Object} options - 选择选项
     * @returns {Array} 选中的题目数组
     */
    selectRandomQuestions(type, count = 1, options = {}) {
        let availableQuestions = [];

        if (type === 'translation') {
            availableQuestions = this.translationQuestions.filter(q => 
                q.isActive && !this.usedQuestions.has(q.id)
            );
        } else if (type === 'professional') {
            availableQuestions = this.professionalQuestions.filter(q => 
                q.isActive && !this.usedQuestions.has(q.id)
            );
        }

        // 应用过滤条件
        if (options.difficulty) {
            availableQuestions = availableQuestions.filter(q => q.difficulty === options.difficulty);
        }

        if (options.category) {
            availableQuestions = availableQuestions.filter(q => q.category === options.category);
        }

        if (options.tags && options.tags.length > 0) {
            availableQuestions = availableQuestions.filter(q => 
                options.tags.some(tag => q.tags.includes(tag))
            );
        }

        // 随机选择
        const selectedQuestions = Utils.getRandomElements(availableQuestions, count);
        
        // 记录使用
        selectedQuestions.forEach(question => {
            question.recordUsage();
            this.usedQuestions.add(question.id);
        });

        this.log(`选择了 ${selectedQuestions.length} 道 ${type} 题目`);
        return selectedQuestions;
    }

    /**
     * 获取题目统计
     * @returns {Object} 统计信息
     */
    getStatistics() {
        const stats = {
            total: this.questions.size,
            active: 0,
            inactive: 0,
            translation: this.translationQuestions.length,
            professional: this.professionalQuestions.length,
            used: this.usedQuestions.size,
            categories: this.categories.size,
            tags: this.tags.size,
            byDifficulty: { easy: 0, medium: 0, hard: 0 }
        };

        this.questions.forEach(question => {
            if (question.isActive) {
                stats.active++;
            } else {
                stats.inactive++;
            }
            
            stats.byDifficulty[question.difficulty]++;
        });

        return stats;
    }

    /**
     * 重置已使用题目
     */
    resetUsedQuestions() {
        this.usedQuestions.clear();
        this.log('已使用题目列表已重置');
    }

    /**
     * 完全重置题库状态
     * 清空所有使用记录和统计信息
     */
    resetAllQuestionStates() {
        // 清空已使用题目列表
        this.usedQuestions.clear();

        // 重置所有题目的使用统计
        this.questions.forEach(question => {
            question.usageCount = 0;
            question.lastUsed = null;
        });

        // 重置分类题目数组中的统计
        this.translationQuestions.forEach(question => {
            question.usageCount = 0;
            question.lastUsed = null;
        });

        this.professionalQuestions.forEach(question => {
            question.usageCount = 0;
            question.lastUsed = null;
        });

        this.log('所有题目状态已重置');
    }

    /**
     * 获取已使用的题目列表
     * @param {string} type - 题目类型
     * @returns {Promise<Array>} 已使用的题目编号数组
     */
    async getUsedQuestions(type) {
        if (type) {
            try {
                // 从数据库获取已使用的题目
                const response = await fetch(`/api/questions/${type}/used`);
                const data = await response.json();

                if (data.success && data.usedQuestions) {
                    // 返回题目编号数组
                    return data.usedQuestions.map(q => parseInt(q.question_index || q.questionIndex || 1));
                } else {
                    this.log(`获取${type}已使用题目失败: ${data.error || '未知错误'}`, 'warn');
                    return [];
                }
            } catch (error) {
                this.log(`获取${type}已使用题目出错: ${error.message}`, 'error');
                // 降级到内存中的数据
                const typeQuestions = type === 'translation' ? this.translationQuestions : this.professionalQuestions;
                const usedIds = typeQuestions.filter(q => this.usedQuestions.has(q.id)).map(q => q.id);

                // 将题目ID转换为题号
                const usedNumbers = usedIds.map(id => {
                    const parts = id.split('_');
                    return parts.length > 1 ? parseInt(parts[1]) : parseInt(id);
                }).filter(num => !isNaN(num));

                return usedNumbers;
            }
        }
        return Array.from(this.usedQuestions);
    }

    /**
     * 重置已使用题目记录
     */
    resetUsedQuestions() {
        this.usedQuestions.clear();
        this.log('已重置所有已使用题目记录');
    }

    /**
     * 检查题目是否已被使用
     * @param {string} questionId - 题目ID
     * @returns {boolean} 是否已使用
     */
    isQuestionUsed(questionId) {
        return this.usedQuestions.has(questionId);
    }

    /**
     * 标记题目为已使用
     * @param {string} questionId - 题目ID
     */
    markQuestionAsUsed(questionId) {
        this.usedQuestions.add(questionId);
        const question = this.getQuestion(questionId);
        if (question) {
            question.recordUsage();
        }
        this.log(`标记题目 ${questionId} 为已使用`);
    }

    /**
     * 导出题库数据
     * @returns {Object} 题库数据对象
     */
    export() {
        const questionsData = [];
        this.questions.forEach(question => {
            questionsData.push(question.export());
        });

        return {
            questions: questionsData,
            usedQuestions: Array.from(this.usedQuestions),
            statistics: this.getStatistics()
        };
    }

    /**
     * 导入题库数据
     * @param {Object} data - 题库数据对象
     * @returns {boolean} 导入结果
     */
    import(data) {
        try {
            this.questions.clear();
            
            if (data.questions) {
                data.questions.forEach(questionData => {
                    const question = new Question();
                    if (question.import(questionData)) {
                        this.questions.set(question.id, question);
                    }
                });
            }

            if (data.usedQuestions) {
                this.usedQuestions = new Set(data.usedQuestions);
            }

            this.buildIndices();
            this.log('题库数据导入成功');
            return true;
        } catch (error) {
            this.handleError(error, '导入题库数据');
            return false;
        }
    }
}

// 导出到全局作用域
window.Question = Question;
window.QuestionBank = QuestionBank;
