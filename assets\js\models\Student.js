/**
 * Student Model - 学生数据模型
 * 负责学生相关的数据结构定义和基础操作
 */

class Student {
    /**
     * 构造函数
     * @param {Object} data - 学生数据
     */
    constructor(data = {}) {
        this.id = data.id || null;
        this.studentNumber = data.studentNumber || data.student_number || '';
        this.currentStep = data.currentStep || data.current_step || 1;
        this.startTime = data.startTime || data.start_time || null;
        this.status = data.status || 'active';
        this.stepData = data.stepData || data.step_data || {};
        this.createdAt = data.createdAt || data.created_at || null;
        this.updatedAt = data.updatedAt || data.updated_at || null;
        
        // 确保stepData是对象格式
        if (typeof this.stepData === 'string') {
            try {
                this.stepData = JSON.parse(this.stepData);
            } catch (e) {
                this.stepData = {};
            }
        }
        
        // 初始化所有步骤数据
        this.initializeStepData();
    }

    /**
     * 初始化步骤数据
     */
    initializeStepData() {
        const stepNames = {
            1: '中文自我介绍',
            2: '英文自我介绍', 
            3: '英文翻译',
            4: '专业课问答',
            5: '综合面试'
        };

        for (let i = 1; i <= 5; i++) {
            if (!this.stepData[i]) {
                this.stepData[i] = {
                    stepNumber: i,
                    stepName: stepNames[i],
                    startTime: null,
                    endTime: null,
                    duration: 0,
                    questions: [],
                    status: 'pending'
                };
            }
        }
    }

    /**
     * 获取当前步骤数据
     * @returns {Object} 当前步骤数据
     */
    getCurrentStepData() {
        return this.stepData[this.currentStep] || null;
    }

    /**
     * 更新步骤数据
     * @param {number} stepNumber - 步骤编号
     * @param {Object} data - 步骤数据
     */
    updateStepData(stepNumber, data) {
        if (!this.stepData[stepNumber]) {
            this.initializeStepData();
        }
        
        this.stepData[stepNumber] = {
            ...this.stepData[stepNumber],
            ...data,
            updatedAt: new Date().toISOString()
        };
    }

    /**
     * 开始步骤
     * @param {number} stepNumber - 步骤编号
     */
    startStep(stepNumber) {
        this.currentStep = stepNumber;
        this.updateStepData(stepNumber, {
            startTime: new Date().toISOString(),
            status: 'active'
        });
    }

    /**
     * 完成步骤
     * @param {number} stepNumber - 步骤编号
     * @param {Array} questions - 题目数据
     */
    completeStep(stepNumber, questions = []) {
        const stepData = this.stepData[stepNumber];
        if (stepData && stepData.startTime) {
            const endTime = new Date().toISOString();
            const duration = Math.floor(
                (new Date(endTime) - new Date(stepData.startTime)) / 1000
            );
            
            this.updateStepData(stepNumber, {
                endTime,
                duration,
                questions,
                status: 'completed'
            });
        }
    }

    /**
     * 获取总用时
     * @returns {number} 总用时（秒）
     */
    getTotalDuration() {
        let total = 0;
        Object.values(this.stepData).forEach(step => {
            if (step.duration) {
                total += step.duration;
            }
        });
        return total;
    }

    /**
     * 获取已完成步骤数
     * @returns {number} 已完成步骤数
     */
    getCompletedStepsCount() {
        return Object.values(this.stepData).filter(
            step => step.status === 'completed'
        ).length;
    }

    /**
     * 检查是否可以进入下一步
     * @returns {boolean} 是否可以进入下一步
     */
    canGoToNextStep() {
        return this.currentStep < 5;
    }

    /**
     * 检查是否可以返回上一步
     * @returns {boolean} 是否可以返回上一步
     */
    canGoToPreviousStep() {
        return this.currentStep > 1;
    }

    /**
     * 检查考试是否完成
     * @returns {boolean} 考试是否完成
     */
    isExamCompleted() {
        return this.getCompletedStepsCount() === 5 || this.status === 'completed';
    }

    /**
     * 获取步骤进度百分比
     * @returns {number} 进度百分比 (0-100)
     */
    getProgressPercentage() {
        return Math.round((this.currentStep / 5) * 100);
    }

    /**
     * 转换为数据库格式
     * @returns {Object} 数据库格式的数据
     */
    toDatabase() {
        return {
            id: this.id,
            student_number: this.studentNumber,
            current_step: this.currentStep,
            start_time: this.startTime,
            status: this.status,
            step_data: JSON.stringify(this.stepData),
            updated_at: new Date().toISOString()
        };
    }

    /**
     * 转换为API格式
     * @returns {Object} API格式的数据
     */
    toAPI() {
        return {
            id: this.id,
            studentNumber: this.studentNumber,
            currentStep: this.currentStep,
            startTime: this.startTime,
            status: this.status,
            stepData: this.stepData,
            totalDuration: this.getTotalDuration(),
            completedSteps: this.getCompletedStepsCount(),
            progressPercentage: this.getProgressPercentage(),
            isCompleted: this.isExamCompleted(),
            canGoNext: this.canGoToNextStep(),
            canGoPrevious: this.canGoToPreviousStep(),
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    /**
     * 验证学生数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];

        if (!this.studentNumber || this.studentNumber.trim() === '') {
            errors.push('学生编号不能为空');
        }

        if (this.currentStep < 1 || this.currentStep > 5) {
            errors.push('当前步骤必须在1-5之间');
        }

        if (!['active', 'completed', 'paused'].includes(this.status)) {
            errors.push('学生状态无效');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 克隆学生对象
     * @returns {Student} 克隆的学生对象
     */
    clone() {
        return new Student(this.toAPI());
    }

    /**
     * 重置学生数据
     */
    reset() {
        this.currentStep = 1;
        this.status = 'active';
        this.startTime = null;
        this.stepData = {};
        this.initializeStepData();
    }

    /**
     * 从数据库数据创建Student实例
     * @param {Object} dbData - 数据库数据
     * @returns {Student} Student实例
     */
    static fromDatabase(dbData) {
        return new Student(dbData);
    }

    /**
     * 从API数据创建Student实例
     * @param {Object} apiData - API数据
     * @returns {Student} Student实例
     */
    static fromAPI(apiData) {
        return new Student(apiData);
    }

    /**
     * 创建新学生
     * @param {string} studentNumber - 学生编号
     * @returns {Student} 新学生实例
     */
    static create(studentNumber) {
        return new Student({
            studentNumber,
            startTime: new Date().toISOString(),
            status: 'active'
        });
    }
}

// 导出Student类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Student;
} else {
    window.Student = Student;
}
