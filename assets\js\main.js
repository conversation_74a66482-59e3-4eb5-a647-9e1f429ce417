/**
 * 面试系统主入口文件 - 重构版
 * 负责启动和初始化整个MVC架构的应用系统
 */

// 应用全局变量
let interviewApp = null;

/**
 * 应用启动函数
 */
async function startInterviewApplication() {
    try {
        console.log('🚀 启动面试系统应用（重构版）...');
        
        // 1. 检查依赖
        checkDependencies();
        
        // 2. 创建应用实例
        interviewApp = new Application({
            name: '研究生复试系统',
            version: '2.0.0',
            debug: window.location.hostname === 'localhost'
        });
        
        // 3. 添加生命周期钩子
        setupApplicationHooks();
        
        // 4. 初始化应用
        await interviewApp.initialize();
        
        // 5. 启动应用
        await interviewApp.start();
        
        // 6. 设置全局引用
        window.interviewApp = interviewApp;
        
        console.log('✅ 面试系统应用启动成功');
        
        // 7. 显示启动完成提示
        showStartupNotification();
        
    } catch (error) {
        console.error('❌ 应用启动失败:', error);
        showErrorNotification('应用启动失败', error.message);
        throw error;
    }
}

/**
 * 检查依赖
 */
function checkDependencies() {
    const requiredClasses = [
        'Application',
        'SystemManager', 
        'ConfigManager',
        'EventBus',
        'ServiceManager',
        'ControllerManager',
        'ViewManager',
        'Utils'
    ];
    
    const missingClasses = [];
    
    for (const className of requiredClasses) {
        if (!window[className]) {
            missingClasses.push(className);
        }
    }
    
    if (missingClasses.length > 0) {
        throw new Error(`缺少必需的类: ${missingClasses.join(', ')}`);
    }
    
    console.log('✅ 依赖检查通过');
}

/**
 * 设置应用生命周期钩子
 */
function setupApplicationHooks() {
    // 初始化前钩子
    interviewApp.addHook('beforeInit', async () => {
        console.log('📋 准备初始化应用...');
        showLoadingMessage('正在初始化系统...');
    });
    
    // 初始化后钩子
    interviewApp.addHook('afterInit', async () => {
        console.log('✅ 应用初始化完成');
        hideLoadingMessage();
    });
    
    // 启动前钩子
    interviewApp.addHook('beforeStart', async () => {
        console.log('🎯 准备启动应用...');
        showLoadingMessage('正在启动系统...');
    });
    
    // 启动后钩子
    interviewApp.addHook('afterStart', async () => {
        console.log('🎉 应用启动完成');
        hideLoadingMessage();
        
        // 检查是否有保存的状态需要恢复
        checkSavedState();
    });
}

/**
 * 检查保存的状态
 */
function checkSavedState() {
    try {
        const savedState = localStorage.getItem('interview_app_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            
            // 检查是否有进行中的考试
            if (state.examInProgress && state.currentStudent) {
                showRestoreStateDialog(state);
            }
        }
    } catch (error) {
        console.warn('检查保存状态失败:', error);
    }
}

/**
 * 显示加载消息
 */
function showLoadingMessage(message) {
    // 创建或更新加载提示
    let loadingEl = document.getElementById('app-loading');
    
    if (!loadingEl) {
        loadingEl = document.createElement('div');
        loadingEl.id = 'app-loading';
        loadingEl.className = 'app-loading';
        loadingEl.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-message">${message}</div>
            </div>
        `;
        document.body.appendChild(loadingEl);
        
        // 添加样式
        addLoadingStyles();
    } else {
        const messageEl = loadingEl.querySelector('.loading-message');
        if (messageEl) {
            messageEl.textContent = message;
        }
    }
    
    loadingEl.style.display = 'flex';
}

/**
 * 隐藏加载消息
 */
function hideLoadingMessage() {
    const loadingEl = document.getElementById('app-loading');
    if (loadingEl) {
        loadingEl.style.display = 'none';
    }
}

/**
 * 添加加载样式
 */
function addLoadingStyles() {
    if (document.getElementById('app-loading-styles')) return;
    
    const styles = document.createElement('style');
    styles.id = 'app-loading-styles';
    styles.textContent = `
        .app-loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-content {
            text-align: center;
            padding: 2rem;
            background: #fff;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        .loading-message {
            color: #374151;
            font-size: 1rem;
            font-weight: 500;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    
    document.head.appendChild(styles);
}

/**
 * 显示启动完成通知
 */
function showStartupNotification() {
    showToast('系统启动完成', 'success');
}

/**
 * 显示错误通知
 */
function showErrorNotification(title, message) {
    showToast(`${title}: ${message}`, 'error');
}

/**
 * 显示恢复状态对话框
 */
function showRestoreStateDialog(state) {
    const message = `检测到上次有未完成的考试（学生：${state.currentStudent}），是否恢复？`;
    
    if (confirm(message)) {
        // 恢复状态
        if (interviewApp && interviewApp.setState) {
            interviewApp.setState({
                currentStudent: state.currentStudent,
                currentStep: state.currentStep,
                examInProgress: state.examInProgress
            });
        }
        
        showToast('已恢复上次考试状态', 'info');
    } else {
        // 清除保存的状态
        localStorage.removeItem('interview_app_state');
        showToast('已清除上次考试状态', 'info');
    }
}

/**
 * 显示提示消息
 */
function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `app-toast app-toast-${type}`;
    toast.textContent = message;
    
    // 添加样式
    addToastStyles();
    
    // 添加到页面
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

/**
 * 添加提示样式
 */
function addToastStyles() {
    if (document.getElementById('app-toast-styles')) return;
    
    const styles = document.createElement('style');
    styles.id = 'app-toast-styles';
    styles.textContent = `
        .app-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: #fff;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }
        
        .app-toast.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .app-toast-success {
            background: #10b981;
        }
        
        .app-toast-error {
            background: #ef4444;
        }
        
        .app-toast-info {
            background: #3b82f6;
        }
        
        .app-toast-warning {
            background: #f59e0b;
        }
    `;
    
    document.head.appendChild(styles);
}

/**
 * 应用重启函数
 */
async function restartInterviewApplication() {
    try {
        console.log('🔄 重启应用...');
        showLoadingMessage('正在重启系统...');
        
        if (interviewApp) {
            await interviewApp.restart();
        } else {
            await startInterviewApplication();
        }
        
        hideLoadingMessage();
        showToast('系统重启完成', 'success');
        
    } catch (error) {
        console.error('❌ 应用重启失败:', error);
        hideLoadingMessage();
        showErrorNotification('应用重启失败', error.message);
    }
}

/**
 * 应用停止函数
 */
async function stopInterviewApplication() {
    try {
        console.log('🛑 停止应用...');
        
        if (interviewApp) {
            await interviewApp.stop();
            interviewApp = null;
            window.interviewApp = null;
        }
        
        showToast('系统已停止', 'info');
        
    } catch (error) {
        console.error('❌ 应用停止失败:', error);
        showErrorNotification('应用停止失败', error.message);
    }
}

// 导出全局函数
window.startInterviewApplication = startInterviewApplication;
window.restartInterviewApplication = restartInterviewApplication;
window.stopInterviewApplication = stopInterviewApplication;

// 页面加载完成后自动启动应用
document.addEventListener('DOMContentLoaded', () => {
    console.log('📄 页面加载完成，准备启动重构版应用...');
    
    // 延迟启动，确保所有脚本都已加载
    setTimeout(() => {
        startInterviewApplication().catch(error => {
            console.error('自动启动应用失败:', error);
        });
    }, 100);
});

console.log('📦 重构版应用入口文件加载完成');
