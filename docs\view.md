# 研究生复试系统 - View UI层文档

## View层架构概述

View层是MVC架构中的表现层，专门负责用户界面的渲染、用户交互的处理和视觉反馈的展示。View层严格遵循"只负责显示"的原则，不包含任何业务逻辑。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     User        │───►│      View       │───►│   Controller    │
│   (用户操作)     │◄───│   (界面显示)     │◄───│   (业务逻辑)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  User Actions   │    │  UI Components  │    │  Data Updates   │
│  - 点击按钮      │    │ - 数据渲染       │    │  - 状态变更      │
│  - 输入文本      │    │ - 事件绑定       │    │  - 内容更新      │
│  - 选择选项      │    │ - 动画效果       │    │  - 界面刷新      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## View设计原则

### 1. 单一职责原则
每个View组件只负责特定的UI功能：
- **ExamView**: 考试主界面和流程显示
- **QuestionView**: 题目显示和抽题界面
- **TimerView**: 计时器显示和控制
- **ProgressView**: 进度指示和状态显示
- **SettingsView**: 设置界面和配置管理

### 2. 数据驱动原则
View组件通过数据驱动更新，不直接操作DOM：
```javascript
class BaseView {
    render(data) {
        this.data = data;
        this.updateDOM();
    }
    
    updateDOM() {
        // 根据this.data更新界面
    }
}
```

### 3. 事件委托原则
View组件只触发事件，不处理业务逻辑：
```javascript
class ExamView {
    onNextStepClick() {
        this.emit('nextStepRequested', {
            currentStep: this.data.currentStep
        });
    }
}
```

## ExamView - 考试主界面组件

### 职责范围
- 考试整体布局管理
- 步骤导航显示
- 学生信息展示
- 考试状态指示

### 界面结构
```html
<div class="exam-container">
    <!-- 头部信息栏 -->
    <header class="exam-header">
        <div class="student-info">
            <span class="student-number">考生编号: {{studentNumber}}</span>
            <span class="exam-status">{{examStatus}}</span>
        </div>
        <div class="system-controls">
            <button class="settings-btn">系统设置</button>
            <button class="switch-student-btn">切换考生</button>
        </div>
    </header>

    <!-- 三栏布局 -->
    <main class="exam-main">
        <!-- 左侧进度面板 -->
        <aside class="progress-panel">
            <div class="step-list">
                <div class="step-item" data-step="1">
                    <div class="step-icon">1</div>
                    <div class="step-info">
                        <div class="step-title">中文自我介绍</div>
                        <div class="step-time">2分钟</div>
                        <div class="step-status">{{stepStatus}}</div>
                    </div>
                </div>
                <!-- 其他步骤... -->
            </div>
        </aside>

        <!-- 中央内容区 -->
        <section class="content-area">
            <div class="content-header">
                <h2 class="current-step-title">
                    <i class="step-icon"></i>
                    {{currentStepTitle}}
                </h2>
            </div>
            <div class="content-body">
                <!-- 动态内容区域 -->
                <div id="step-content"></div>
            </div>
            <div class="content-footer">
                <div class="navigation-controls">
                    <button class="prev-step-btn">上一步</button>
                    <button class="next-step-btn">下一步</button>
                </div>
            </div>
        </section>

        <!-- 右侧控制面板 -->
        <aside class="control-panel">
            <div class="timer-section">
                <div class="timer-display">{{timerDisplay}}</div>
                <div class="timer-controls">
                    <button class="start-timer-btn">开始计时</button>
                    <button class="pause-timer-btn">暂停</button>
                    <button class="reset-timer-btn">重置</button>
                </div>
            </div>
            <div class="action-section">
                <button class="draw-question-btn">抽取题目</button>
                <button class="complete-step-btn">完成步骤</button>
            </div>
        </aside>
    </main>

    <!-- 底部状态栏 -->
    <footer class="exam-footer">
        <div class="progress-bar">
            <div class="progress-fill" style="width: {{progressPercentage}}%"></div>
        </div>
        <div class="status-info">
            <span class="current-step">第{{currentStep}}步</span>
            <span class="total-time">总用时: {{totalTime}}</span>
        </div>
    </footer>
</div>
```

### 核心方法

#### 渲染考试界面
```javascript
class ExamView {
    render(examData) {
        this.updateStudentInfo(examData.student);
        this.updateStepList(examData.steps);
        this.updateCurrentStep(examData.currentStep);
        this.updateProgress(examData.progress);
        this.updateControls(examData.controls);
    }

    updateStudentInfo(student) {
        this.elements.studentNumber.textContent = student.studentNumber;
        this.elements.examStatus.textContent = student.status;
        this.elements.examStatus.className = `exam-status ${student.statusClass}`;
    }

    updateStepList(steps) {
        steps.forEach((step, index) => {
            const stepElement = this.elements.stepItems[index];
            stepElement.querySelector('.step-title').textContent = step.title;
            stepElement.querySelector('.step-time').textContent = step.timeDisplay;
            stepElement.querySelector('.step-status').textContent = step.statusText;
            stepElement.className = `step-item ${step.statusClass}`;
        });
    }
}
```

#### 事件处理
```javascript
class ExamView {
    bindEvents() {
        // 步骤导航
        this.elements.nextStepBtn.addEventListener('click', () => {
            this.emit('nextStepRequested');
        });

        this.elements.prevStepBtn.addEventListener('click', () => {
            this.emit('previousStepRequested');
        });

        // 学生切换
        this.elements.switchStudentBtn.addEventListener('click', () => {
            this.showStudentSwitchDialog();
        });

        // 系统设置
        this.elements.settingsBtn.addEventListener('click', () => {
            this.emit('settingsRequested');
        });
    }

    showStudentSwitchDialog() {
        const dialog = new StudentSwitchDialog();
        dialog.on('studentSelected', (studentNumber) => {
            this.emit('studentSwitchRequested', { studentNumber });
        });
        dialog.show();
    }
}
```

## QuestionView - 题目显示组件

### 职责范围
- 题目内容渲染
- 抽题动画展示
- 题目网格显示
- 题目状态管理

### 界面结构
```html
<div class="question-container">
    <!-- 题目网格 -->
    <div class="question-grid" id="question-grid">
        <div class="grid-header">
            <h3>{{questionType}}题目</h3>
            <div class="grid-stats">
                <span class="total-count">总计: {{totalCount}} 题</span>
                <span class="used-count">已抽取: {{usedCount}} 题</span>
                <span class="available-count">可抽取: {{availableCount}} 题</span>
            </div>
        </div>
        <div class="grid-body">
            <div class="question-numbers">
                <!-- 动态生成题号 -->
                <div class="question-number available" data-number="1">1</div>
                <div class="question-number used" data-number="2">2</div>
                <!-- 更多题号... -->
            </div>
        </div>
    </div>

    <!-- 抽题动画区域 -->
    <div class="draw-animation" id="draw-animation">
        <div class="animation-container">
            <div class="spinning-numbers"></div>
            <div class="selected-number"></div>
        </div>
    </div>

    <!-- 题目内容显示 -->
    <div class="question-content" id="question-content">
        <div class="question-header">
            <h3 class="question-title">{{questionTitle}}</h3>
            <div class="question-meta">
                <span class="question-type">{{questionType}}</span>
                <span class="question-number">第{{questionNumber}}题</span>
            </div>
        </div>
        <div class="question-body">
            <div class="question-text">{{questionText}}</div>
            <div class="question-options" v-if="hasOptions">
                <!-- 选择题选项 -->
            </div>
        </div>
    </div>

    <!-- 控制按钮 -->
    <div class="question-controls">
        <button class="draw-question-btn">抽取题目</button>
        <button class="show-grid-btn">显示题目网格</button>
        <button class="hide-question-btn">隐藏题目</button>
    </div>
</div>
```

### 核心方法

#### 生成题目网格
```javascript
class QuestionView {
    generateQuestionGrid(gridData) {
        const container = this.elements.questionNumbers;
        container.innerHTML = '';

        gridData.forEach(item => {
            const numberElement = document.createElement('div');
            numberElement.className = `question-number ${item.statusClass}`;
            numberElement.dataset.number = item.number;
            numberElement.textContent = item.number;
            
            // 添加状态指示
            if (item.isUsed) {
                numberElement.setAttribute('title', `已被使用 - ${item.usedBy || '未知'}`);
            } else if (item.isAvailable) {
                numberElement.setAttribute('title', '可抽取');
            } else {
                numberElement.setAttribute('title', '不存在');
            }

            container.appendChild(numberElement);
        });

        this.updateGridStats(gridData);
    }

    updateGridStats(gridData) {
        const total = gridData.length;
        const used = gridData.filter(item => item.isUsed).length;
        const available = gridData.filter(item => item.isAvailable).length;

        this.elements.totalCount.textContent = total;
        this.elements.usedCount.textContent = used;
        this.elements.availableCount.textContent = available;
    }
}
```

#### 抽题动画
```javascript
class QuestionView {
    async playDrawAnimation(selectedNumber, duration = 3000) {
        const animationContainer = this.elements.drawAnimation;
        const spinningNumbers = animationContainer.querySelector('.spinning-numbers');
        const selectedNumberElement = animationContainer.querySelector('.selected-number');

        // 显示动画容器
        animationContainer.style.display = 'block';

        // 创建旋转数字效果
        const numbers = Array.from({length: 20}, (_, i) => i + 1);
        let currentIndex = 0;
        
        const spinInterval = setInterval(() => {
            spinningNumbers.textContent = numbers[currentIndex % numbers.length];
            currentIndex++;
        }, 100);

        // 等待动画时间
        await new Promise(resolve => setTimeout(resolve, duration));

        // 停止旋转，显示选中数字
        clearInterval(spinInterval);
        spinningNumbers.style.display = 'none';
        selectedNumberElement.textContent = selectedNumber;
        selectedNumberElement.style.display = 'block';

        // 高亮选中的题号
        this.highlightSelectedNumber(selectedNumber);

        // 等待一段时间后隐藏动画
        setTimeout(() => {
            animationContainer.style.display = 'none';
            spinningNumbers.style.display = 'block';
            selectedNumberElement.style.display = 'none';
        }, 2000);
    }

    highlightSelectedNumber(number) {
        // 移除之前的高亮
        this.elements.questionNumbers.querySelectorAll('.highlighted').forEach(el => {
            el.classList.remove('highlighted');
        });

        // 高亮选中的题号
        const selectedElement = this.elements.questionNumbers.querySelector(`[data-number="${number}"]`);
        if (selectedElement) {
            selectedElement.classList.add('highlighted');
            selectedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
}
```

#### 显示题目内容
```javascript
class QuestionView {
    displayQuestion(question) {
        const container = this.elements.questionContent;
        
        // 更新题目信息
        container.querySelector('.question-title').textContent = question.title;
        container.querySelector('.question-type').textContent = question.typeText;
        container.querySelector('.question-number').textContent = `第${question.index}题`;
        
        // 根据题目类型渲染内容
        if (question.type === 'translation') {
            this.renderTranslationQuestion(question);
        } else if (question.type === 'professional') {
            this.renderProfessionalQuestion(question);
        }

        // 显示题目容器
        container.style.display = 'block';
        container.scrollIntoView({ behavior: 'smooth' });
    }

    renderTranslationQuestion(question) {
        const questionText = this.elements.questionContent.querySelector('.question-text');
        questionText.innerHTML = `
            <div class="translation-question">
                <div class="english-text">
                    <h4>请翻译以下英文：</h4>
                    <p class="english-content">${question.content.english}</p>
                </div>
                <div class="translation-area">
                    <h4>中文翻译：</h4>
                    <textarea class="translation-input" placeholder="请在此输入中文翻译..."></textarea>
                </div>
            </div>
        `;
    }

    renderProfessionalQuestion(question) {
        const questionText = this.elements.questionContent.querySelector('.question-text');
        questionText.innerHTML = `
            <div class="professional-question">
                <div class="question-prompt">
                    <h4>专业问题：</h4>
                    <p class="question-content">${question.content.question}</p>
                </div>
                <div class="answer-area">
                    <h4>请回答：</h4>
                    <textarea class="answer-input" placeholder="请在此输入您的回答..."></textarea>
                </div>
            </div>
        `;
    }
}
```

## TimerView - 计时器显示组件

### 职责范围
- 计时器数字显示
- 计时器状态指示
- 计时器控制按钮
- 时间警告提示

### 界面结构
```html
<div class="timer-container">
    <div class="timer-display">
        <div class="time-digits">
            <span class="minutes">05</span>
            <span class="separator">:</span>
            <span class="seconds">00</span>
        </div>
        <div class="timer-status">
            <span class="status-text">{{statusText}}</span>
            <div class="status-indicator {{statusClass}}"></div>
        </div>
    </div>

    <div class="timer-controls">
        <button class="timer-btn start-btn" data-action="start">
            <i class="icon-play"></i>
            <span>开始</span>
        </button>
        <button class="timer-btn pause-btn" data-action="pause">
            <i class="icon-pause"></i>
            <span>暂停</span>
        </button>
        <button class="timer-btn reset-btn" data-action="reset">
            <i class="icon-reset"></i>
            <span>重置</span>
        </button>
    </div>

    <div class="timer-progress">
        <div class="progress-bar">
            <div class="progress-fill" style="width: {{progressPercentage}}%"></div>
        </div>
        <div class="time-info">
            <span class="elapsed-time">已用时: {{elapsedTime}}</span>
            <span class="remaining-time">剩余: {{remainingTime}}</span>
        </div>
    </div>
</div>
```

### 核心方法

#### 更新计时器显示
```javascript
class TimerView {
    updateDisplay(timerData) {
        const { remaining, duration, status } = timerData;
        
        // 更新时间显示
        const minutes = Math.floor(remaining / 60);
        const seconds = remaining % 60;
        
        this.elements.minutes.textContent = minutes.toString().padStart(2, '0');
        this.elements.seconds.textContent = seconds.toString().padStart(2, '0');
        
        // 更新状态
        this.updateStatus(status);
        
        // 更新进度条
        this.updateProgress(remaining, duration);
        
        // 检查时间警告
        this.checkTimeWarning(remaining);
    }

    updateStatus(status) {
        const statusMap = {
            'running': { text: '计时中', class: 'status-running' },
            'paused': { text: '已暂停', class: 'status-paused' },
            'stopped': { text: '已停止', class: 'status-stopped' },
            'completed': { text: '时间到', class: 'status-completed' }
        };

        const statusInfo = statusMap[status] || statusMap['stopped'];
        this.elements.statusText.textContent = statusInfo.text;
        this.elements.statusIndicator.className = `status-indicator ${statusInfo.class}`;
    }

    updateProgress(remaining, duration) {
        const elapsed = duration - remaining;
        const progressPercentage = (elapsed / duration) * 100;
        
        this.elements.progressFill.style.width = `${progressPercentage}%`;
        this.elements.elapsedTime.textContent = this.formatTime(elapsed);
        this.elements.remainingTime.textContent = this.formatTime(remaining);
    }

    checkTimeWarning(remaining) {
        // 时间警告逻辑
        if (remaining <= 60 && remaining > 30) {
            this.showWarning('1分钟警告', 'warning');
        } else if (remaining <= 30 && remaining > 10) {
            this.showWarning('30秒警告', 'urgent');
        } else if (remaining <= 10 && remaining > 0) {
            this.showWarning('10秒警告', 'critical');
        } else if (remaining === 0) {
            this.showWarning('时间到！', 'timeout');
        }
    }
}
```

#### 计时器动画效果
```javascript
class TimerView {
    addTimerAnimations() {
        // 数字跳动效果
        this.elements.timeDigits.addEventListener('transitionend', () => {
            this.elements.timeDigits.classList.remove('digit-change');
        });

        // 进度条动画
        this.elements.progressFill.style.transition = 'width 1s ease-in-out';

        // 警告闪烁效果
        this.warningAnimation = null;
    }

    animateDigitChange() {
        this.elements.timeDigits.classList.add('digit-change');
    }

    showWarning(message, level) {
        const warningElement = document.createElement('div');
        warningElement.className = `timer-warning ${level}`;
        warningElement.textContent = message;
        
        this.elements.timerContainer.appendChild(warningElement);
        
        // 自动移除警告
        setTimeout(() => {
            if (warningElement.parentNode) {
                warningElement.parentNode.removeChild(warningElement);
            }
        }, 3000);

        // 闪烁效果
        if (level === 'critical' || level === 'timeout') {
            this.startBlinkingEffect();
        }
    }

    startBlinkingEffect() {
        let blinkCount = 0;
        const maxBlinks = 6;
        
        this.warningAnimation = setInterval(() => {
            this.elements.timerDisplay.classList.toggle('blinking');
            blinkCount++;
            
            if (blinkCount >= maxBlinks) {
                clearInterval(this.warningAnimation);
                this.elements.timerDisplay.classList.remove('blinking');
            }
        }, 500);
    }
}
```

## ProgressView - 进度指示组件

### 职责范围
- 考试整体进度显示
- 步骤完成状态指示
- 时间统计展示
- 状态图标管理

### 界面结构
```html
<div class="progress-container">
    <!-- 步骤进度条 -->
    <div class="step-progress">
        <div class="progress-track">
            <div class="progress-line" style="width: {{progressPercentage}}%"></div>
        </div>
        <div class="step-markers">
            <div class="step-marker completed" data-step="1">
                <div class="marker-icon">✓</div>
                <div class="marker-label">步骤1</div>
            </div>
            <div class="step-marker current" data-step="2">
                <div class="marker-icon">2</div>
                <div class="marker-label">步骤2</div>
            </div>
            <!-- 其他步骤标记... -->
        </div>
    </div>

    <!-- 详细进度信息 -->
    <div class="progress-details">
        <div class="progress-stats">
            <div class="stat-item">
                <span class="stat-label">已完成步骤</span>
                <span class="stat-value">{{completedSteps}}/5</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">总用时</span>
                <span class="stat-value">{{totalTime}}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">平均用时</span>
                <span class="stat-value">{{averageTime}}</span>
            </div>
        </div>
    </div>

    <!-- 步骤列表 -->
    <div class="step-list">
        <div class="step-item completed" data-step="1">
            <div class="step-status-icon">✓</div>
            <div class="step-info">
                <div class="step-title">中文自我介绍</div>
                <div class="step-duration">用时: 1分45秒</div>
            </div>
        </div>
        <!-- 其他步骤... -->
    </div>
</div>
```

### 核心方法

#### 更新进度显示
```javascript
class ProgressView {
    updateProgress(progressData) {
        this.updateProgressBar(progressData.percentage);
        this.updateStepMarkers(progressData.steps);
        this.updateProgressStats(progressData.stats);
        this.updateStepList(progressData.stepDetails);
    }

    updateProgressBar(percentage) {
        this.elements.progressLine.style.width = `${percentage}%`;
        
        // 添加动画效果
        this.elements.progressLine.style.transition = 'width 0.5s ease-in-out';
    }

    updateStepMarkers(steps) {
        steps.forEach((step, index) => {
            const marker = this.elements.stepMarkers[index];
            const icon = marker.querySelector('.marker-icon');
            
            // 更新标记状态
            marker.className = `step-marker ${step.status}`;
            
            // 更新图标
            if (step.status === 'completed') {
                icon.textContent = '✓';
            } else if (step.status === 'current') {
                icon.textContent = step.number;
            } else {
                icon.textContent = step.number;
            }
        });
    }
}
```

## 响应式设计和适配

### 断点设置
```css
/* 响应式断点 */
@media (max-width: 1200px) {
    .exam-main {
        grid-template-columns: 250px 1fr 200px;
    }
}

@media (max-width: 992px) {
    .exam-main {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }
    
    .progress-panel,
    .control-panel {
        order: 3;
    }
}

@media (max-width: 768px) {
    .timer-display .time-digits {
        font-size: 2rem;
    }
    
    .question-grid .question-numbers {
        grid-template-columns: repeat(5, 1fr);
    }
}
```

### 动态布局调整
```javascript
class ResponsiveManager {
    constructor() {
        this.breakpoints = {
            mobile: 768,
            tablet: 992,
            desktop: 1200
        };
        
        this.init();
    }

    init() {
        window.addEventListener('resize', this.handleResize.bind(this));
        this.handleResize(); // 初始化
    }

    handleResize() {
        const width = window.innerWidth;
        
        if (width <= this.breakpoints.mobile) {
            this.applyMobileLayout();
        } else if (width <= this.breakpoints.tablet) {
            this.applyTabletLayout();
        } else {
            this.applyDesktopLayout();
        }
    }

    applyMobileLayout() {
        // 移动端布局调整
        document.body.classList.add('mobile-layout');
        document.body.classList.remove('tablet-layout', 'desktop-layout');
    }
}
```

View层严格遵循"只负责显示"的原则，通过数据驱动的方式更新界面，通过事件机制与Controller层通信，确保了良好的分离度和可维护性。
