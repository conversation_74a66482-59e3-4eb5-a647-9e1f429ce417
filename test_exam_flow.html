<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .step-content {
            margin: 10px 0;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .current-step {
            border-color: #007bff;
            background: #e7f3ff;
        }
        .completed-step {
            border-color: #28a745;
            background: #e8f5e8;
        }
        .timer {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        .student-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎓 研究生复试流程测试</h1>
        
        <div class="student-info">
            <strong>考生信息：</strong>
            <span id="studentInfo">未选择考生</span>
            <button class="btn btn-primary" onclick="selectStudent()">选择考生</button>
        </div>

        <div class="timer" id="timer">计时器: 00:00</div>

        <!-- 步骤1：中文自我介绍 -->
        <div class="step current-step" id="step1">
            <h3>🗣️ 步骤1：中文自我介绍 (2分钟)</h3>
            <div class="step-content">
                <p>请考生用中文进行自我介绍，时间限制2分钟。</p>
                <button class="btn btn-success" onclick="startStep(1)">开始计时</button>
                <button class="btn btn-warning" onclick="pauseTimer()">暂停</button>
                <button class="btn btn-secondary" onclick="completeStep(1)">完成步骤</button>
            </div>
        </div>

        <!-- 步骤2：英文自我介绍 -->
        <div class="step" id="step2">
            <h3>🌍 步骤2：英文自我介绍 (2分钟)</h3>
            <div class="step-content">
                <p>请考生用英文进行自我介绍，时间限制2分钟。</p>
                <button class="btn btn-success" onclick="startStep(2)">开始计时</button>
                <button class="btn btn-warning" onclick="pauseTimer()">暂停</button>
                <button class="btn btn-secondary" onclick="completeStep(2)">完成步骤</button>
            </div>
        </div>

        <!-- 步骤3：英文翻译 -->
        <div class="step" id="step3">
            <h3>📝 步骤3：英文翻译 (5分钟)</h3>
            <div class="step-content">
                <p>请翻译抽取的英文句子，时间限制5分钟。</p>
                <button class="btn btn-primary" onclick="drawQuestion('translation')">抽取题目</button>
                <button class="btn btn-success" onclick="startStep(3)">开始计时</button>
                <button class="btn btn-warning" onclick="pauseTimer()">暂停</button>
                <button class="btn btn-secondary" onclick="completeStep(3)">完成步骤</button>
                <div id="translationQuestion" style="margin-top: 10px; font-style: italic;"></div>
            </div>
        </div>

        <!-- 步骤4：专业题目 -->
        <div class="step" id="step4">
            <h3>🎓 步骤4：专业题目 (10分钟)</h3>
            <div class="step-content">
                <p>请回答抽取的专业问题，时间限制10分钟。</p>
                <select id="subjectSelect" onchange="updateSubject()">
                    <option value="">选择专业科目</option>
                    <option value="computer">计算机科学</option>
                    <option value="math">数学</option>
                    <option value="physics">物理学</option>
                    <option value="chemistry">化学</option>
                </select>
                <button class="btn btn-primary" onclick="drawQuestion('professional')">抽取题目</button>
                <button class="btn btn-success" onclick="startStep(4)">开始计时</button>
                <button class="btn btn-warning" onclick="pauseTimer()">暂停</button>
                <button class="btn btn-secondary" onclick="completeStep(4)">完成步骤</button>
                <div id="professionalQuestion" style="margin-top: 10px; font-style: italic;"></div>
            </div>
        </div>

        <!-- 步骤5：综合问答 -->
        <div class="step" id="step5">
            <h3>💼 步骤5：综合问答 (8分钟)</h3>
            <div class="step-content">
                <p>综合问答环节，时间限制8分钟。</p>
                <button class="btn btn-success" onclick="startStep(5)">开始计时</button>
                <button class="btn btn-warning" onclick="pauseTimer()">暂停</button>
                <button class="btn btn-secondary" onclick="completeStep(5)">完成步骤</button>
            </div>
        </div>

        <!-- 步骤6：下一考生 -->
        <div class="step" id="step6">
            <h3>👥 步骤6：下一考生</h3>
            <div class="step-content">
                <p>当前考生面试完成，准备下一考生。</p>
                <button class="btn btn-primary" onclick="nextStudent()">下一考生</button>
                <button class="btn btn-secondary" onclick="resetExam()">重置考试</button>
            </div>
        </div>

        <div class="log" id="log">
            <strong>操作日志：</strong><br>
            系统已就绪，请选择考生开始考试...<br>
        </div>
    </div>

    <script>
        // 考试状态
        let currentStudent = null;
        let currentStep = 1;
        let timer = null;
        let timeRemaining = 0;
        let isTimerRunning = false;

        // 步骤时间限制（秒）
        const stepTimeLimit = {
            1: 120,  // 2分钟
            2: 120,  // 2分钟
            3: 300,  // 5分钟
            4: 600,  // 10分钟
            5: 480   // 8分钟
        };

        // 题目库
        const questions = {
            translation: [
                "The rapid development of artificial intelligence has transformed many industries.",
                "Climate change poses significant challenges to global sustainability.",
                "Education plays a crucial role in shaping future generations.",
                "Technology has revolutionized the way we communicate and work.",
                "Scientific research requires rigorous methodology and careful analysis."
            ],
            professional: {
                computer: [
                    "请解释面向对象编程的三大特性。",
                    "什么是数据结构？请举例说明。",
                    "请描述TCP/IP协议的工作原理。",
                    "什么是算法复杂度？如何分析？",
                    "请解释数据库的ACID特性。"
                ],
                math: [
                    "请解释微积分的基本定理。",
                    "什么是线性代数？有哪些应用？",
                    "请描述概率论的基本概念。",
                    "什么是数学归纳法？如何使用？",
                    "请解释统计学的基本原理。"
                ],
                physics: [
                    "请解释牛顿三大定律。",
                    "什么是量子力学？有哪些特点？",
                    "请描述电磁学的基本原理。",
                    "什么是相对论？有哪些应用？",
                    "请解释热力学定律。"
                ],
                chemistry: [
                    "请解释化学键的类型和特点。",
                    "什么是化学反应？如何分类？",
                    "请描述有机化学的基本概念。",
                    "什么是化学平衡？如何影响？",
                    "请解释酸碱理论。"
                ]
            }
        };

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function selectStudent() {
            const studentNumber = prompt('请输入考生编号:');
            if (studentNumber) {
                currentStudent = studentNumber;
                document.getElementById('studentInfo').textContent = `考生编号: ${studentNumber}`;
                log(`选择考生: ${studentNumber}`);
                updateStepDisplay();
            }
        }

        function startStep(step) {
            if (!currentStudent) {
                alert('请先选择考生！');
                return;
            }

            currentStep = step;
            timeRemaining = stepTimeLimit[step];
            updateStepDisplay();
            startTimer();
            log(`开始步骤${step}: ${getStepName(step)}`);
        }

        function startTimer() {
            if (isTimerRunning) return;
            
            isTimerRunning = true;
            timer = setInterval(() => {
                timeRemaining--;
                updateTimerDisplay();
                
                if (timeRemaining <= 0) {
                    stopTimer();
                    alert(`步骤${currentStep}时间到！`);
                    log(`步骤${currentStep}时间到`);
                }
            }, 1000);
        }

        function pauseTimer() {
            if (timer) {
                clearInterval(timer);
                isTimerRunning = false;
                log(`暂停计时器`);
            }
        }

        function stopTimer() {
            if (timer) {
                clearInterval(timer);
                timer = null;
                isTimerRunning = false;
            }
        }

        function updateTimerDisplay() {
            const minutes = Math.floor(timeRemaining / 60);
            const seconds = timeRemaining % 60;
            document.getElementById('timer').textContent = 
                `计时器: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        function completeStep(step) {
            stopTimer();
            document.getElementById(`step${step}`).classList.add('completed-step');
            document.getElementById(`step${step}`).classList.remove('current-step');
            
            if (step < 5) {
                currentStep = step + 1;
                document.getElementById(`step${currentStep}`).classList.add('current-step');
            }
            
            log(`完成步骤${step}: ${getStepName(step)}`);
            updateStepDisplay();
        }

        function drawQuestion(type) {
            if (type === 'translation') {
                const question = questions.translation[Math.floor(Math.random() * questions.translation.length)];
                document.getElementById('translationQuestion').textContent = `题目: ${question}`;
                log(`抽取英文翻译题目`);
            } else if (type === 'professional') {
                const subject = document.getElementById('subjectSelect').value;
                if (!subject) {
                    alert('请先选择专业科目！');
                    return;
                }
                const subjectQuestions = questions.professional[subject];
                const question = subjectQuestions[Math.floor(Math.random() * subjectQuestions.length)];
                document.getElementById('professionalQuestion').textContent = `题目: ${question}`;
                log(`抽取${getSubjectName(subject)}专业题目`);
            }
        }

        function updateSubject() {
            const subject = document.getElementById('subjectSelect').value;
            if (subject) {
                log(`选择专业科目: ${getSubjectName(subject)}`);
            }
        }

        function nextStudent() {
            if (confirm('确定要切换到下一考生吗？当前进度将被重置。')) {
                resetExam();
                selectStudent();
            }
        }

        function resetExam() {
            stopTimer();
            currentStudent = null;
            currentStep = 1;
            timeRemaining = 0;
            
            // 重置界面
            document.getElementById('studentInfo').textContent = '未选择考生';
            document.getElementById('timer').textContent = '计时器: 00:00';
            document.getElementById('translationQuestion').textContent = '';
            document.getElementById('professionalQuestion').textContent = '';
            document.getElementById('subjectSelect').value = '';
            
            // 重置步骤状态
            for (let i = 1; i <= 6; i++) {
                const stepElement = document.getElementById(`step${i}`);
                stepElement.classList.remove('current-step', 'completed-step');
                if (i === 1) {
                    stepElement.classList.add('current-step');
                }
            }
            
            log('重置考试状态');
        }

        function updateStepDisplay() {
            // 更新步骤显示逻辑
        }

        function getStepName(step) {
            const names = {
                1: '中文自我介绍',
                2: '英文自我介绍', 
                3: '英文翻译',
                4: '专业题目',
                5: '综合问答'
            };
            return names[step] || '未知步骤';
        }

        function getSubjectName(subject) {
            const names = {
                computer: '计算机科学',
                math: '数学',
                physics: '物理学',
                chemistry: '化学'
            };
            return names[subject] || '未知科目';
        }

        // 初始化
        updateTimerDisplay();
        log('考试流程测试系统已启动');
    </script>
</body>
</html>
