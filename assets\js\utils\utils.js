/**
 * 工具类 - 提供系统通用的工具方法
 * 遵循单一职责原则，每个方法都有明确的功能
 */
class Utils {
    /**
     * 格式化时间显示
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串 (MM:SS)
     */
    static formatTime(seconds) {
        if (typeof seconds !== 'number' || seconds < 0) {
            return '00:00';
        }

        // 确保只显示整秒数，去掉小数部分
        const totalSeconds = Math.floor(seconds);
        const mins = Math.floor(totalSeconds / 60);
        const secs = totalSeconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * 生成唯一ID
     * @param {string} prefix - ID前缀
     * @returns {string} 唯一ID
     */
    static generateId(prefix = 'id') {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `${prefix}_${timestamp}_${random}`;
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 深拷贝对象
     * @param {any} obj - 要拷贝的对象
     * @returns {any} 深拷贝后的对象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }

        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }

        if (obj instanceof Array) {
            return obj.map(item => Utils.deepClone(item));
        }

        if (typeof obj === 'object') {
            const clonedObj = {};
            for (let key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = Utils.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }

        return obj;
    }

    /**
     * 验证输入数据
     * @param {any} value - 要验证的值
     * @param {string} type - 期望的类型
     * @param {Object} options - 验证选项
     * @returns {boolean} 验证结果
     */
    static validateInput(value, type, options = {}) {
        switch (type) {
            case 'number':
                if (typeof value !== 'number' || isNaN(value)) return false;
                if (options.min !== undefined && value < options.min) return false;
                if (options.max !== undefined && value > options.max) return false;
                return true;

            case 'string':
                if (typeof value !== 'string') return false;
                if (options.minLength !== undefined && value.length < options.minLength) return false;
                if (options.maxLength !== undefined && value.length > options.maxLength) return false;
                if (options.pattern && !options.pattern.test(value)) return false;
                return true;

            case 'email':
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return typeof value === 'string' && emailPattern.test(value);

            case 'required':
                return value !== null && value !== undefined && value !== '';

            default:
                return true;
        }
    }

    // ==================== 扩展工具方法 ====================

    /**
     * 格式化字符串
     * @param {string} template - 模板字符串
     * @param {Object} data - 数据对象
     * @returns {string} 格式化后的字符串
     */
    static formatString(template, data) {
        return template.replace(/\{(\w+)\}/g, (match, key) => {
            return data[key] !== undefined ? data[key] : match;
        });
    }

    /**
     * 截断字符串
     * @param {string} str - 原字符串
     * @param {number} length - 最大长度
     * @param {string} suffix - 后缀
     * @returns {string} 截断后的字符串
     */
    static truncate(str, length, suffix = '...') {
        if (!str || str.length <= length) return str;
        return str.substring(0, length) + suffix;
    }

    /**
     * 首字母大写
     * @param {string} str - 字符串
     * @returns {string} 首字母大写的字符串
     */
    static capitalize(str) {
        if (!str) return str;
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 生成随机数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机数
     */
    static randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 数字补零
     * @param {number} num - 数字
     * @param {number} length - 长度
     * @returns {string} 补零后的字符串
     */
    static padZero(num, length = 2) {
        return num.toString().padStart(length, '0');
    }

    /**
     * 格式化日期
     * @param {Date|string|number} date - 日期
     * @param {string} format - 格式
     * @returns {string} 格式化后的时间
     */
    static formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';

        const year = d.getFullYear();
        const month = this.padZero(d.getMonth() + 1);
        const day = this.padZero(d.getDate());
        const hour = this.padZero(d.getHours());
        const minute = this.padZero(d.getMinutes());
        const second = this.padZero(d.getSeconds());

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hour)
            .replace('mm', minute)
            .replace('ss', second);
    }

    /**
     * 格式化时长
     * @param {number} seconds - 秒数
     * @returns {string} 格式化后的时长
     */
    static formatDuration(seconds) {
        if (seconds < 0) seconds = 0;

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${this.padZero(hours)}:${this.padZero(minutes)}:${this.padZero(secs)}`;
        } else {
            return `${this.padZero(minutes)}:${this.padZero(secs)}`;
        }
    }

    /**
     * 数组去重
     * @param {Array} arr - 数组
     * @param {string} key - 对象数组的去重键
     * @returns {Array} 去重后的数组
     */
    static unique(arr, key = null) {
        if (!Array.isArray(arr)) return [];

        if (key) {
            const seen = new Set();
            return arr.filter(item => {
                const value = item[key];
                if (seen.has(value)) return false;
                seen.add(value);
                return true;
            });
        }

        return [...new Set(arr)];
    }

    /**
     * 数组分组
     * @param {Array} arr - 数组
     * @param {string|Function} key - 分组键或函数
     * @returns {Object} 分组后的对象
     */
    static groupBy(arr, key) {
        if (!Array.isArray(arr)) return {};

        return arr.reduce((groups, item) => {
            const groupKey = typeof key === 'function' ? key(item) : item[key];
            if (!groups[groupKey]) groups[groupKey] = [];
            groups[groupKey].push(item);
            return groups;
        }, {});
    }

    /**
     * 获取嵌套属性值
     * @param {Object} obj - 对象
     * @param {string} path - 属性路径
     * @param {*} defaultValue - 默认值
     * @returns {*} 属性值
     */
    static get(obj, path, defaultValue = undefined) {
        const keys = path.split('.');
        let result = obj;

        for (const key of keys) {
            if (result === null || result === undefined) {
                return defaultValue;
            }
            result = result[key];
        }

        return result !== undefined ? result : defaultValue;
    }

    /**
     * 设置嵌套属性值
     * @param {Object} obj - 对象
     * @param {string} path - 属性路径
     * @param {*} value - 属性值
     */
    static set(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        let target = obj;

        for (const key of keys) {
            if (!target[key] || typeof target[key] !== 'object') {
                target[key] = {};
            }
            target = target[key];
        }

        target[lastKey] = value;
    }

    /**
     * 安全的JSON解析
     * @param {string} jsonString - JSON字符串
     * @param {any} defaultValue - 解析失败时的默认值
     * @returns {any} 解析结果或默认值
     */
    static safeJsonParse(jsonString, defaultValue = null) {
        try {
            return JSON.parse(jsonString);
        } catch (error) {
            console.warn('JSON解析失败:', error.message);
            return defaultValue;
        }
    }

    /**
     * 安全的JSON字符串化
     * @param {any} obj - 要字符串化的对象
     * @param {string} defaultValue - 失败时的默认值
     * @returns {string} JSON字符串或默认值
     */
    static safeJsonStringify(obj, defaultValue = '{}') {
        try {
            return JSON.stringify(obj);
        } catch (error) {
            console.warn('JSON字符串化失败:', error.message);
            return defaultValue;
        }
    }

    /**
     * 获取当前时间戳
     * @returns {number} 时间戳
     */
    static getCurrentTimestamp() {
        return Date.now();
    }

    /**
     * 格式化日期
     * @param {Date|number} date - 日期对象或时间戳
     * @param {string} format - 格式字符串
     * @returns {string} 格式化的日期字符串
     */
    static formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        if (isNaN(d.getTime())) {
            return '';
        }

        const year = d.getFullYear();
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const day = d.getDate().toString().padStart(2, '0');
        const hours = d.getHours().toString().padStart(2, '0');
        const minutes = d.getMinutes().toString().padStart(2, '0');
        const seconds = d.getSeconds().toString().padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    /**
     * 延迟执行
     * @param {number} ms - 延迟时间（毫秒）
     * @returns {Promise} Promise对象
     */
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 检查是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    static isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * 获取随机数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机数
     */
    static getRandomNumber(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 数组随机排序
     * @param {Array} array - 要排序的数组
     * @returns {Array} 随机排序后的数组
     */
    static shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    /**
     * 从数组中随机选择元素
     * @param {Array} array - 源数组
     * @param {number} count - 选择数量
     * @returns {Array} 随机选择的元素数组
     */
    static getRandomElements(array, count = 1) {
        if (count >= array.length) {
            return Utils.shuffleArray(array);
        }
        
        const shuffled = Utils.shuffleArray(array);
        return shuffled.slice(0, count);
    }

    /**
     * 清理HTML标签
     * @param {string} html - HTML字符串
     * @returns {string} 清理后的文本
     */
    static stripHtml(html) {
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    }

    /**
     * 转义HTML特殊字符
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    static escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 导出到全局作用域
window.Utils = Utils;
