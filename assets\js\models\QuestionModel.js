/**
 * QuestionModel - 题目数据模型
 * 管理题目相关的数据结构和验证
 */

class QuestionModel extends BaseModel {
    constructor(data = {}) {
        super(data);
        
        // 题目基本信息
        this.title = data.title || '';
        this.content = data.content || '';
        this.type = data.type || 'text'; // text, choice, translation, professional
        this.category = data.category || '';
        this.subject = data.subject || '';
        this.difficulty = data.difficulty || 'medium'; // easy, medium, hard
        
        // 题目选项（选择题用）
        this.options = data.options || [];
        this.correctAnswer = data.correctAnswer || '';
        
        // 评分标准
        this.maxScore = data.maxScore || 100;
        this.scoringCriteria = data.scoringCriteria || [];
        
        // 时间限制
        this.timeLimit = data.timeLimit || null; // 秒数
        this.suggestedTime = data.suggestedTime || null;
        
        // 使用统计
        this.usageCount = data.usageCount || 0;
        this.averageScore = data.averageScore || null;
        this.lastUsed = data.lastUsed || null;
        
        // 状态信息
        this.isActive = data.isActive !== undefined ? data.isActive : true;
        this.isPublic = data.isPublic !== undefined ? data.isPublic : true;
        
        // 标签和分类
        this.tags = data.tags || [];
        this.keywords = data.keywords || [];
        
        // 附加信息
        this.source = data.source || '';
        this.author = data.author || '';
        this.notes = data.notes || '';
    }

    /**
     * 验证属性是否有效
     * @param {string} property - 属性名
     * @returns {boolean} 是否有效
     */
    isValidProperty(property) {
        const validProperties = [
            'title', 'content', 'type', 'category', 'subject', 'difficulty',
            'options', 'correctAnswer', 'maxScore', 'scoringCriteria',
            'timeLimit', 'suggestedTime', 'usageCount', 'averageScore', 'lastUsed',
            'isActive', 'isPublic', 'tags', 'keywords', 'source', 'author', 'notes'
        ];
        return validProperties.includes(property);
    }

    /**
     * 自定义验证
     * @returns {Object} 验证结果
     */
    customValidate() {
        const errors = [];

        // 标题验证
        if (!this.title || this.title.trim() === '') {
            errors.push('题目标题不能为空');
        } else if (this.title.length > 200) {
            errors.push('题目标题长度不能超过200个字符');
        }

        // 内容验证
        if (!this.content || this.content.trim() === '') {
            errors.push('题目内容不能为空');
        } else if (this.content.length > 5000) {
            errors.push('题目内容长度不能超过5000个字符');
        }

        // 类型验证
        const validTypes = ['text', 'choice', 'translation', 'professional'];
        if (!validTypes.includes(this.type)) {
            errors.push('题目类型无效');
        }

        // 难度验证
        const validDifficulties = ['easy', 'medium', 'hard'];
        if (!validDifficulties.includes(this.difficulty)) {
            errors.push('难度级别无效');
        }

        // 选择题验证
        if (this.type === 'choice') {
            if (!Array.isArray(this.options) || this.options.length < 2) {
                errors.push('选择题至少需要2个选项');
            }
            if (!this.correctAnswer || this.correctAnswer.trim() === '') {
                errors.push('选择题必须设置正确答案');
            }
        }

        // 分数验证
        if (this.maxScore <= 0 || this.maxScore > 1000) {
            errors.push('最高分数必须在1-1000之间');
        }

        // 时间限制验证
        if (this.timeLimit !== null && (this.timeLimit <= 0 || this.timeLimit > 7200)) {
            errors.push('时间限制必须在1-7200秒之间');
        }

        return { errors };
    }

    /**
     * 是否为选择题
     * @returns {boolean} 是否为选择题
     */
    isChoiceQuestion() {
        return this.type === 'choice';
    }

    /**
     * 是否为翻译题
     * @returns {boolean} 是否为翻译题
     */
    isTranslationQuestion() {
        return this.type === 'translation';
    }

    /**
     * 是否为专业题
     * @returns {boolean} 是否为专业题
     */
    isProfessionalQuestion() {
        return this.type === 'professional';
    }

    /**
     * 获取难度显示文本
     * @returns {string} 难度文本
     */
    getDifficultyText() {
        const difficultyMap = {
            'easy': '简单',
            'medium': '中等',
            'hard': '困难'
        };
        return difficultyMap[this.difficulty] || '未知';
    }

    /**
     * 获取类型显示文本
     * @returns {string} 类型文本
     */
    getTypeText() {
        const typeMap = {
            'text': '文本题',
            'choice': '选择题',
            'translation': '翻译题',
            'professional': '专业题'
        };
        return typeMap[this.type] || '未知类型';
    }

    /**
     * 添加选项（选择题用）
     * @param {string} option - 选项内容
     */
    addOption(option) {
        if (this.type === 'choice' && option && option.trim() !== '') {
            this.options.push(option.trim());
            this.touch();
        }
    }

    /**
     * 移除选项（选择题用）
     * @param {number} index - 选项索引
     */
    removeOption(index) {
        if (this.type === 'choice' && index >= 0 && index < this.options.length) {
            this.options.splice(index, 1);
            this.touch();
        }
    }

    /**
     * 设置正确答案
     * @param {string} answer - 正确答案
     */
    setCorrectAnswer(answer) {
        this.correctAnswer = answer;
        this.touch();
    }

    /**
     * 检查答案是否正确
     * @param {string} answer - 用户答案
     * @returns {boolean} 是否正确
     */
    checkAnswer(answer) {
        if (this.type === 'choice') {
            return answer === this.correctAnswer;
        }
        // 其他类型的题目需要人工评分
        return null;
    }

    /**
     * 增加使用次数
     */
    incrementUsage() {
        this.usageCount++;
        this.lastUsed = new Date().toISOString();
        this.touch();
    }

    /**
     * 更新平均分数
     * @param {number} score - 新分数
     */
    updateAverageScore(score) {
        if (this.averageScore === null) {
            this.averageScore = score;
        } else {
            // 简单的移动平均
            this.averageScore = (this.averageScore + score) / 2;
        }
        this.touch();
    }

    /**
     * 添加标签
     * @param {string} tag - 标签
     */
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
            this.touch();
        }
    }

    /**
     * 移除标签
     * @param {string} tag - 标签
     */
    removeTag(tag) {
        const index = this.tags.indexOf(tag);
        if (index > -1) {
            this.tags.splice(index, 1);
            this.touch();
        }
    }

    /**
     * 添加关键词
     * @param {string} keyword - 关键词
     */
    addKeyword(keyword) {
        if (!this.keywords.includes(keyword)) {
            this.keywords.push(keyword);
            this.touch();
        }
    }

    /**
     * 获取格式化的时间限制
     * @returns {string} 格式化的时间
     */
    getFormattedTimeLimit() {
        if (!this.timeLimit) {
            return '无限制';
        }
        
        const minutes = Math.floor(this.timeLimit / 60);
        const seconds = this.timeLimit % 60;
        
        if (minutes > 0) {
            return seconds > 0 ? `${minutes}分${seconds}秒` : `${minutes}分钟`;
        } else {
            return `${seconds}秒`;
        }
    }

    /**
     * 获取最后使用时间的格式化文本
     * @returns {string} 格式化的时间
     */
    getFormattedLastUsed() {
        if (!this.lastUsed) {
            return '从未使用';
        }
        return new Date(this.lastUsed).toLocaleString('zh-CN');
    }

    /**
     * 激活题目
     */
    activate() {
        this.isActive = true;
        this.touch();
    }

    /**
     * 停用题目
     */
    deactivate() {
        this.isActive = false;
        this.touch();
    }

    /**
     * 设为公开
     */
    makePublic() {
        this.isPublic = true;
        this.touch();
    }

    /**
     * 设为私有
     */
    makePrivate() {
        this.isPublic = false;
        this.touch();
    }
}

// 导出QuestionModel类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QuestionModel;
} else {
    window.QuestionModel = QuestionModel;
}
