#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
初始化数据库脚本
"""

import sqlite3
import os
import json

print("开始初始化数据库...")

DATABASE = 'interview_system.db'

# 如果数据库文件已存在，先删除它
if os.path.exists(DATABASE):
    print(f"删除现有数据库文件: {DATABASE}")
    os.remove(DATABASE)

print(f"创建新数据库文件: {DATABASE}")
conn = sqlite3.connect(DATABASE)
cursor = conn.cursor()

# 创建翻译题目表
print("创建translation_questions表...")
cursor.execute('''
CREATE TABLE IF NOT EXISTS translation_questions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    question_index INTEGER DEFAULT 0,
    question_data TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
''')

# 创建专业课题目表
print("创建professional_questions表...")
cursor.execute('''
CREATE TABLE IF NOT EXISTS professional_questions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    question_index INTEGER DEFAULT 0,
    question_data TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
''')

# 创建考试记录表
print("创建exam_records表...")
cursor.execute('''
CREATE TABLE IF NOT EXISTS exam_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_number TEXT NOT NULL,
    translation_question TEXT,
    professional_question TEXT,
    exam_date TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
''')

# 创建设置表
print("创建settings表...")
cursor.execute('''
CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY CHECK (id = 1),
    chinese_intro REAL NOT NULL DEFAULT 2.0,
    english_intro REAL NOT NULL DEFAULT 2.0,
    translation REAL NOT NULL DEFAULT 5.0,
    professional REAL NOT NULL DEFAULT 10.0,
    comprehensive REAL NOT NULL DEFAULT 5.0
)
''')

# 插入默认设置
cursor.execute('''
INSERT OR IGNORE INTO settings (id, chinese_intro, english_intro, translation, professional, comprehensive)
VALUES (1, 2.0, 2.0, 5.0, 10.0, 5.0)
''')

# 示例翻译题目
translation_examples = [
    {
        "data": [
            ["txt", "The rapid development of artificial intelligence has brought both opportunities and challenges to society."],
            ["txt", "While AI technologies have revolutionized many industries, they have also raised concerns about job displacement, privacy, and ethical issues."],
            ["txt", "It is important for policymakers, businesses, and the public to work together to ensure that AI is developed and used responsibly to benefit humanity."]
        ]
    },
    {
        "data": [
            ["txt", "Climate change is one of the most pressing issues facing our planet today."],
            ["txt", "Rising global temperatures have led to more frequent extreme weather events, sea level rise, and disruptions to ecosystems."],
            ["txt", "To address this challenge, countries around the world need to reduce greenhouse gas emissions, transition to renewable energy sources, and develop sustainable practices."]
        ]
    },
    {
        "data": [
            ["txt", "The global pandemic has accelerated the digital transformation of education."],
            ["txt", "Online learning platforms, virtual classrooms, and digital educational resources have become essential tools for students and educators."],
            ["txt", "While this shift has created new opportunities for flexible and personalized learning, it has also highlighted challenges such as the digital divide."]
        ]
    }
]

# 示例专业课题目
professional_examples = [
    {
        "data": [
            ["txt", "请解释计算机网络中的TCP/IP协议栈，并详细描述每一层的主要功能及其相互关系。"],
            ["txt", "分析TCP和UDP的区别，以及它们各自适用的应用场景。"]
        ]
    },
    {
        "data": [
            ["txt", "请分析数据库管理系统中的事务特性（ACID）及实现机制。"],
            ["txt", "讨论在分布式系统中保证事务一致性的挑战与解决方案。"]
        ]
    },
    {
        "data": [
            ["txt", "请详细阐述机器学习中的监督学习、无监督学习和强化学习的区别。"],
            ["txt", "结合实际应用场景进行分析，并讨论各自的优缺点。"]
        ]
    }
]

# 导入示例题目
print("导入示例翻译题目...")
for i, example in enumerate(translation_examples):
    cursor.execute(
        'INSERT INTO translation_questions (question_index, question_data) VALUES (?, ?)',
        (i, json.dumps(example["data"]))
    )

print("导入示例专业课题目...")
for i, example in enumerate(professional_examples):
    cursor.execute(
        'INSERT INTO professional_questions (question_index, question_data) VALUES (?, ?)',
        (i, json.dumps(example["data"]))
    )

# 添加一些示例考试记录
print("创建示例考试记录...")
example_records = [
    {
        "student_number": "2023001", 
        "translation_question": "题目 #1", 
        "professional_question": "题目 #2", 
        "exam_date": "2025-04-15 10:30:00"
    },
    {
        "student_number": "2023002", 
        "translation_question": "题目 #2", 
        "professional_question": "题目 #1", 
        "exam_date": "2025-04-16 14:45:00"
    }
]

for record in example_records:
    cursor.execute(
        'INSERT INTO exam_records (student_number, translation_question, professional_question, exam_date) VALUES (?, ?, ?, ?)',
        (record["student_number"], record["translation_question"], record["professional_question"], record["exam_date"])
    )

conn.commit()
conn.close()

print("数据库初始化完成！") 