@echo off
echo ====================================
echo Graduate Interview System - Portable
echo ====================================

REM Check if portable Python exists
if not exist "python_portable\python.exe" (
    echo Error: Portable Python environment not found
    echo Please run setup_portable.bat first
    pause
    exit /b 1
)

REM Set environment variables
set PYTHONPATH=%CD%
set PYTHONHOME=%CD%\python_portable

echo Starting application...
echo Python path: %CD%\python_portable\python.exe
echo Working directory: %CD%

REM Start Flask application
python_portable\python.exe app.py

echo Application exited
pause
