/**
 * QuestionEditorView - 题库编辑视图
 * 提供题库的增删改查功能界面
 */

class QuestionEditorView extends BaseView {
    constructor(container) {
        super(container);
        
        // 视图状态
        this.state = {
            currentQuestionType: 'translation', // translation, professional
            currentSubject: 'computer', // 专业题科目
            questions: [],
            selectedQuestion: null,
            isEditing: false,
            searchQuery: '',
            filterType: 'all',
            sortBy: 'index',
            sortOrder: 'asc'
        };

        // 题目类型配置
        this.questionTypes = {
            translation: {
                name: '英文翻译题',
                icon: '🌍',
                description: '英文句子翻译题目'
            },
            professional: {
                name: '专业课题目',
                icon: '🎓',
                description: '各专业课程相关题目'
            }
        };

        // 专业科目配置
        this.subjects = {
            computer: '计算机科学',
            math: '数学',
            physics: '物理学',
            chemistry: '化学',
            biology: '生物学',
            economics: '经济学'
        };
    }

    /**
     * 获取视图模板
     */
    getTemplate() {
        return `
            <div class="question-editor-container">
                <!-- 头部工具栏 -->
                <div class="editor-header">
                    <div class="header-left">
                        <h2 class="editor-title">
                            <i class="icon">📚</i>
                            题库编辑器
                        </h2>
                        <div class="question-type-tabs">
                            <button class="tab-btn active" data-action="switchType" data-type="translation">
                                🌍 翻译题
                            </button>
                            <button class="tab-btn" data-action="switchType" data-type="professional">
                                🎓 专业题
                            </button>
                        </div>
                    </div>
                    <div class="header-right">
                        <button class="btn btn-primary" data-action="addQuestion">
                            <i class="icon">➕</i>
                            添加题目
                        </button>
                        <button class="btn btn-secondary" data-action="importQuestions">
                            <i class="icon">📥</i>
                            导入
                        </button>
                        <button class="btn btn-secondary" data-action="exportQuestions">
                            <i class="icon">📤</i>
                            导出
                        </button>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="editor-content">
                    <!-- 左侧：题目列表 -->
                    <div class="questions-panel">
                        <div class="panel-header">
                            <div class="search-bar">
                                <input type="text" class="search-input" placeholder="搜索题目..." 
                                       data-field="searchQuery">
                                <button class="search-btn" data-action="searchQuestions">🔍</button>
                            </div>
                            <div class="filter-controls">
                                <select class="filter-select" data-field="filterType">
                                    <option value="all">全部题目</option>
                                    <option value="active">启用题目</option>
                                    <option value="inactive">禁用题目</option>
                                    <option value="used">已使用</option>
                                    <option value="unused">未使用</option>
                                </select>
                                <select class="sort-select" data-field="sortBy">
                                    <option value="index">按编号</option>
                                    <option value="title">按标题</option>
                                    <option value="difficulty">按难度</option>
                                    <option value="usageCount">按使用次数</option>
                                    <option value="createdAt">按创建时间</option>
                                </select>
                                <button class="sort-order-btn" data-action="toggleSortOrder">
                                    <span class="sort-icon">⬆️</span>
                                </button>
                            </div>
                            <div class="subject-selector" style="display: none;">
                                <label>专业科目:</label>
                                <select class="subject-select" data-field="currentSubject">
                                    <option value="computer">计算机科学</option>
                                    <option value="math">数学</option>
                                    <option value="physics">物理学</option>
                                    <option value="chemistry">化学</option>
                                    <option value="biology">生物学</option>
                                    <option value="economics">经济学</option>
                                </select>
                            </div>
                        </div>
                        <div class="questions-list" id="questionsList">
                            <!-- 题目列表将在这里动态生成 -->
                        </div>
                        <div class="list-footer">
                            <div class="questions-count">
                                共 <span id="questionsCount">0</span> 道题目
                            </div>
                            <div class="pagination">
                                <button class="btn btn-sm" data-action="prevPage">上一页</button>
                                <span class="page-info">第 <span id="currentPage">1</span> 页</span>
                                <button class="btn btn-sm" data-action="nextPage">下一页</button>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：题目编辑器 -->
                    <div class="editor-panel">
                        <div class="editor-header-bar">
                            <h3 class="editor-panel-title">
                                <span id="editorTitle">选择题目进行编辑</span>
                            </h3>
                            <div class="editor-actions">
                                <button class="btn btn-sm btn-success" data-action="saveQuestion" style="display: none;">
                                    💾 保存
                                </button>
                                <button class="btn btn-sm btn-secondary" data-action="cancelEdit" style="display: none;">
                                    ❌ 取消
                                </button>
                                <button class="btn btn-sm btn-danger" data-action="deleteQuestion" style="display: none;">
                                    🗑️ 删除
                                </button>
                            </div>
                        </div>
                        <div class="editor-form" id="editorForm">
                            <!-- 编辑表单将在这里动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 统计信息面板 -->
                <div class="stats-panel">
                    <div class="stats-card">
                        <div class="stat-item">
                            <span class="stat-label">总题目数</span>
                            <span class="stat-value" id="totalQuestions">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">已使用</span>
                            <span class="stat-value" id="usedQuestions">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">未使用</span>
                            <span class="stat-value" id="unusedQuestions">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">启用题目</span>
                            <span class="stat-value" id="activeQuestions">0</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取视图样式
     */
    getStyles() {
        return `
            .question-editor-container {
                height: 100vh;
                display: flex;
                flex-direction: column;
                background: #f8f9fa;
            }

            .editor-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                background: white;
                border-bottom: 1px solid #dee2e6;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .header-left {
                display: flex;
                align-items: center;
                gap: 30px;
            }

            .editor-title {
                margin: 0;
                color: #333;
                font-size: 24px;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .question-type-tabs {
                display: flex;
                gap: 5px;
            }

            .tab-btn {
                padding: 10px 20px;
                border: 1px solid #dee2e6;
                background: #f8f9fa;
                color: #666;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .tab-btn:hover {
                background: #e9ecef;
            }

            .tab-btn.active {
                background: #007bff;
                color: white;
                border-color: #007bff;
            }

            .header-right {
                display: flex;
                gap: 10px;
            }

            .editor-content {
                flex: 1;
                display: flex;
                gap: 20px;
                padding: 20px;
                overflow: hidden;
            }

            .questions-panel {
                width: 400px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                display: flex;
                flex-direction: column;
            }

            .panel-header {
                padding: 20px;
                border-bottom: 1px solid #dee2e6;
            }

            .search-bar {
                display: flex;
                margin-bottom: 15px;
            }

            .search-input {
                flex: 1;
                padding: 10px;
                border: 1px solid #dee2e6;
                border-radius: 6px 0 0 6px;
                outline: none;
            }

            .search-btn {
                padding: 10px 15px;
                border: 1px solid #dee2e6;
                border-left: none;
                background: #f8f9fa;
                border-radius: 0 6px 6px 0;
                cursor: pointer;
            }

            .filter-controls {
                display: flex;
                gap: 10px;
                align-items: center;
            }

            .filter-select, .sort-select {
                padding: 8px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: white;
            }

            .sort-order-btn {
                padding: 8px;
                border: 1px solid #dee2e6;
                background: white;
                border-radius: 4px;
                cursor: pointer;
            }

            .subject-selector {
                margin-top: 15px;
                padding-top: 15px;
                border-top: 1px solid #dee2e6;
            }

            .subject-selector label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
                color: #333;
            }

            .subject-select {
                width: 100%;
                padding: 8px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: white;
            }

            .questions-list {
                flex: 1;
                overflow-y: auto;
                padding: 10px;
            }

            .list-footer {
                padding: 15px 20px;
                border-top: 1px solid #dee2e6;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .questions-count {
                font-size: 14px;
                color: #666;
            }

            .pagination {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .editor-panel {
                flex: 1;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                display: flex;
                flex-direction: column;
            }

            .editor-header-bar {
                padding: 20px;
                border-bottom: 1px solid #dee2e6;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .editor-panel-title {
                margin: 0;
                color: #333;
                font-size: 18px;
            }

            .editor-actions {
                display: flex;
                gap: 10px;
            }

            .editor-form {
                flex: 1;
                padding: 20px;
                overflow-y: auto;
            }

            .stats-panel {
                padding: 0 20px 20px;
            }

            .stats-card {
                background: white;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                display: flex;
                gap: 30px;
            }

            .stat-item {
                text-align: center;
            }

            .stat-label {
                display: block;
                font-size: 14px;
                color: #666;
                margin-bottom: 5px;
            }

            .stat-value {
                display: block;
                font-size: 24px;
                font-weight: bold;
                color: #007bff;
            }

            /* 响应式设计 */
            @media (max-width: 1200px) {
                .editor-content {
                    flex-direction: column;
                }
                
                .questions-panel {
                    width: 100%;
                    height: 300px;
                }
                
                .stats-card {
                    flex-wrap: wrap;
                    gap: 15px;
                }
            }
        `;
    }

    /**
     * 初始化视图
     */
    onInitialize() {
        this.setupEventListeners();
        this.loadQuestions();
        this.updateStats();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听来自Controller的事件
        this.listenGlobal('QuestionEditorController.questionsLoaded', (data) => {
            this.handleQuestionsLoaded(data);
        });

        this.listenGlobal('QuestionEditorController.questionSaved', (data) => {
            this.handleQuestionSaved(data);
        });

        this.listenGlobal('QuestionEditorController.questionDeleted', (data) => {
            this.handleQuestionDeleted(data);
        });

        this.listenGlobal('QuestionEditorController.statsUpdated', (data) => {
            this.handleStatsUpdated(data);
        });

        // 监听表单字段变化
        this.container.addEventListener('input', (event) => {
            if (event.target.hasAttribute('data-field')) {
                this.handleFieldChange(event.target);
            }
        });

        // 监听选择框变化
        this.container.addEventListener('change', (event) => {
            if (event.target.hasAttribute('data-field')) {
                this.handleFieldChange(event.target);
            }
        });
    }

    /**
     * 处理动作
     */
    handleAction(action, target, event) {
        switch (action) {
            case 'switchType':
                this.handleSwitchType(target.getAttribute('data-type'));
                break;
            case 'addQuestion':
                this.handleAddQuestion();
                break;
            case 'importQuestions':
                this.handleImportQuestions();
                break;
            case 'exportQuestions':
                this.handleExportQuestions();
                break;
            case 'searchQuestions':
                this.handleSearchQuestions();
                break;
            case 'toggleSortOrder':
                this.handleToggleSortOrder();
                break;
            case 'selectQuestion':
                this.handleSelectQuestion(target.getAttribute('data-question-id'));
                break;
            case 'saveQuestion':
                this.handleSaveQuestion();
                break;
            case 'cancelEdit':
                this.handleCancelEdit();
                break;
            case 'deleteQuestion':
                this.handleDeleteQuestion();
                break;
            case 'prevPage':
                this.handlePrevPage();
                break;
            case 'nextPage':
                this.handleNextPage();
                break;
            default:
                super.handleAction(action, target, event);
        }
    }

    /**
     * 处理字段变化
     */
    handleFieldChange(field) {
        const fieldName = field.getAttribute('data-field');
        const value = field.value;

        this.updateState({ [fieldName]: value });

        // 特殊处理
        switch (fieldName) {
            case 'currentSubject':
                this.loadQuestions();
                break;
            case 'filterType':
            case 'sortBy':
                this.filterAndSortQuestions();
                break;
            case 'searchQuery':
                // 实时搜索（防抖）
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.filterAndSortQuestions();
                }, 300);
                break;
        }
    }

    /**
     * 切换题目类型
     */
    handleSwitchType(type) {
        this.updateState({ currentQuestionType: type });

        // 更新标签页状态
        this.container.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        this.container.querySelector(`[data-type="${type}"]`).classList.add('active');

        // 显示/隐藏专业科目选择器
        const subjectSelector = this.container.querySelector('.subject-selector');
        if (type === 'professional') {
            subjectSelector.style.display = 'block';
        } else {
            subjectSelector.style.display = 'none';
        }

        // 重新加载题目
        this.loadQuestions();
        this.clearEditor();
    }

    /**
     * 加载题目列表
     */
    loadQuestions() {
        this.emit('loadQuestionsRequested', {
            type: this.state.currentQuestionType,
            subject: this.state.currentSubject
        });
    }

    /**
     * 处理题目加载完成
     */
    handleQuestionsLoaded(data) {
        this.updateState({ questions: data.questions });
        this.filterAndSortQuestions();
        this.updateStats();
    }

    /**
     * 过滤和排序题目
     */
    filterAndSortQuestions() {
        let filteredQuestions = [...this.state.questions];

        // 搜索过滤
        if (this.state.searchQuery) {
            const query = this.state.searchQuery.toLowerCase();
            filteredQuestions = filteredQuestions.filter(q =>
                q.title?.toLowerCase().includes(query) ||
                q.content?.toLowerCase().includes(query) ||
                q.index?.toString().includes(query)
            );
        }

        // 状态过滤
        switch (this.state.filterType) {
            case 'active':
                filteredQuestions = filteredQuestions.filter(q => q.isActive);
                break;
            case 'inactive':
                filteredQuestions = filteredQuestions.filter(q => !q.isActive);
                break;
            case 'used':
                filteredQuestions = filteredQuestions.filter(q => q.usageCount > 0);
                break;
            case 'unused':
                filteredQuestions = filteredQuestions.filter(q => q.usageCount === 0);
                break;
        }

        // 排序
        filteredQuestions.sort((a, b) => {
            let aVal = a[this.state.sortBy];
            let bVal = b[this.state.sortBy];

            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }

            if (this.state.sortOrder === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });

        this.renderQuestionsList(filteredQuestions);
    }

    /**
     * 渲染题目列表
     */
    renderQuestionsList(questions) {
        const listContainer = this.container.querySelector('#questionsList');

        if (questions.length === 0) {
            listContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📝</div>
                    <div class="empty-title">暂无题目</div>
                    <div class="empty-description">点击"添加题目"开始创建题目</div>
                </div>
            `;
            return;
        }

        const questionsHTML = questions.map(question => this.renderQuestionItem(question)).join('');
        listContainer.innerHTML = questionsHTML;

        // 更新计数
        this.container.querySelector('#questionsCount').textContent = questions.length;
    }

    /**
     * 渲染单个题目项
     */
    renderQuestionItem(question) {
        const statusIcon = question.isActive ? '✅' : '❌';
        const usageIcon = question.usageCount > 0 ? '📊' : '📝';
        const difficultyColor = {
            'easy': '#28a745',
            'medium': '#ffc107',
            'hard': '#dc3545'
        }[question.difficulty] || '#6c757d';

        return `
            <div class="question-item ${this.state.selectedQuestion?.id === question.id ? 'selected' : ''}"
                 data-action="selectQuestion" data-question-id="${question.id}">
                <div class="question-header">
                    <div class="question-index">#${question.index}</div>
                    <div class="question-status">
                        <span class="status-icon" title="${question.isActive ? '启用' : '禁用'}">${statusIcon}</span>
                        <span class="usage-icon" title="使用次数: ${question.usageCount}">${usageIcon}</span>
                    </div>
                </div>
                <div class="question-title">${question.title || '无标题'}</div>
                <div class="question-content">${this.truncateText(question.content, 60)}</div>
                <div class="question-meta">
                    <span class="difficulty" style="color: ${difficultyColor}">
                        ${this.getDifficultyText(question.difficulty)}
                    </span>
                    <span class="usage-count">使用 ${question.usageCount} 次</span>
                </div>
            </div>
        `;
    }

    /**
     * 处理添加题目
     */
    handleAddQuestion() {
        const newQuestion = {
            id: null,
            type: this.state.currentQuestionType,
            subject: this.state.currentSubject,
            index: this.getNextQuestionIndex(),
            title: '',
            content: '',
            difficulty: 'medium',
            isActive: true,
            usageCount: 0,
            tags: [],
            notes: ''
        };

        this.updateState({
            selectedQuestion: newQuestion,
            isEditing: true
        });

        this.renderEditor(newQuestion);
        this.showEditorActions(true);
    }

    /**
     * 获取下一个题目编号
     */
    getNextQuestionIndex() {
        if (this.state.questions.length === 0) {
            return 1;
        }

        const maxIndex = Math.max(...this.state.questions.map(q => q.index || 0));
        return maxIndex + 1;
    }

    /**
     * 处理选择题目
     */
    handleSelectQuestion(questionId) {
        const question = this.state.questions.find(q => q.id === questionId);
        if (question) {
            this.updateState({
                selectedQuestion: question,
                isEditing: false
            });

            this.renderEditor(question);
            this.showEditorActions(false);
            this.highlightSelectedQuestion(questionId);
        }
    }

    /**
     * 高亮选中的题目
     */
    highlightSelectedQuestion(questionId) {
        this.container.querySelectorAll('.question-item').forEach(item => {
            item.classList.remove('selected');
        });

        const selectedItem = this.container.querySelector(`[data-question-id="${questionId}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
        }
    }

    /**
     * 渲染编辑器
     */
    renderEditor(question) {
        const editorForm = this.container.querySelector('#editorForm');
        const editorTitle = this.container.querySelector('#editorTitle');

        if (question.id) {
            editorTitle.textContent = `编辑题目 #${question.index}`;
        } else {
            editorTitle.textContent = '添加新题目';
        }

        const formHTML = this.getEditorFormHTML(question);
        editorForm.innerHTML = formHTML;

        // 绑定表单事件
        this.bindEditorEvents();
    }

    /**
     * 获取编辑器表单HTML
     */
    getEditorFormHTML(question) {
        const isTranslation = question.type === 'translation';
        const subjectOptions = Object.entries(this.subjects)
            .map(([key, name]) => `<option value="${key}" ${question.subject === key ? 'selected' : ''}>${name}</option>`)
            .join('');

        return `
            <div class="editor-form-content">
                <div class="form-row">
                    <div class="form-group">
                        <label for="questionIndex">题目编号 *</label>
                        <input type="number" id="questionIndex" class="form-input"
                               value="${question.index || ''}" min="1" required>
                    </div>
                    <div class="form-group">
                        <label for="questionDifficulty">难度级别 *</label>
                        <select id="questionDifficulty" class="form-select" required>
                            <option value="easy" ${question.difficulty === 'easy' ? 'selected' : ''}>简单</option>
                            <option value="medium" ${question.difficulty === 'medium' ? 'selected' : ''}>中等</option>
                            <option value="hard" ${question.difficulty === 'hard' ? 'selected' : ''}>困难</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="questionStatus">状态</label>
                        <select id="questionStatus" class="form-select">
                            <option value="true" ${question.isActive ? 'selected' : ''}>启用</option>
                            <option value="false" ${!question.isActive ? 'selected' : ''}>禁用</option>
                        </select>
                    </div>
                </div>

                ${!isTranslation ? `
                <div class="form-row">
                    <div class="form-group">
                        <label for="questionSubject">专业科目 *</label>
                        <select id="questionSubject" class="form-select" required>
                            ${subjectOptions}
                        </select>
                    </div>
                </div>
                ` : ''}

                <div class="form-group">
                    <label for="questionTitle">题目标题 *</label>
                    <input type="text" id="questionTitle" class="form-input"
                           value="${question.title || ''}" placeholder="请输入题目标题" required>
                </div>

                <div class="form-group">
                    <label for="questionContent">题目内容 *</label>
                    <textarea id="questionContent" class="form-textarea" rows="6"
                              placeholder="${isTranslation ? '请输入要翻译的英文句子' : '请输入题目内容'}" required>${question.content || ''}</textarea>
                </div>

                ${isTranslation ? `
                <div class="form-group">
                    <label for="questionAnswer">参考答案</label>
                    <textarea id="questionAnswer" class="form-textarea" rows="4"
                              placeholder="请输入参考翻译（可选）">${question.answer || ''}</textarea>
                </div>
                ` : ''}

                <div class="form-group">
                    <label for="questionTags">标签</label>
                    <input type="text" id="questionTags" class="form-input"
                           value="${(question.tags || []).join(', ')}"
                           placeholder="请输入标签，用逗号分隔">
                </div>

                <div class="form-group">
                    <label for="questionNotes">备注</label>
                    <textarea id="questionNotes" class="form-textarea" rows="3"
                              placeholder="请输入备注信息（可选）">${question.notes || ''}</textarea>
                </div>

                <div class="form-info">
                    <div class="info-item">
                        <span class="info-label">创建时间:</span>
                        <span class="info-value">${question.createdAt ? new Date(question.createdAt).toLocaleString() : '新建题目'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">使用次数:</span>
                        <span class="info-value">${question.usageCount || 0} 次</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">最后使用:</span>
                        <span class="info-value">${question.lastUsedAt ? new Date(question.lastUsedAt).toLocaleString() : '从未使用'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定编辑器事件
     */
    bindEditorEvents() {
        // 实时验证
        const requiredFields = this.container.querySelectorAll('#editorForm [required]');
        requiredFields.forEach(field => {
            field.addEventListener('input', () => {
                this.validateField(field);
            });
        });
    }

    /**
     * 验证字段
     */
    validateField(field) {
        const isValid = field.checkValidity();
        field.classList.toggle('invalid', !isValid);
        return isValid;
    }

    /**
     * 显示编辑器操作按钮
     */
    showEditorActions(isEditing) {
        const saveBtn = this.container.querySelector('[data-action="saveQuestion"]');
        const cancelBtn = this.container.querySelector('[data-action="cancelEdit"]');
        const deleteBtn = this.container.querySelector('[data-action="deleteQuestion"]');

        if (isEditing) {
            saveBtn.style.display = 'inline-flex';
            cancelBtn.style.display = 'inline-flex';
            deleteBtn.style.display = this.state.selectedQuestion?.id ? 'inline-flex' : 'none';
        } else {
            saveBtn.style.display = 'none';
            cancelBtn.style.display = 'none';
            deleteBtn.style.display = this.state.selectedQuestion?.id ? 'inline-flex' : 'none';
        }
    }

    /**
     * 清空编辑器
     */
    clearEditor() {
        const editorForm = this.container.querySelector('#editorForm');
        const editorTitle = this.container.querySelector('#editorTitle');

        editorTitle.textContent = '选择题目进行编辑';
        editorForm.innerHTML = `
            <div class="empty-editor">
                <div class="empty-icon">📝</div>
                <div class="empty-title">请选择题目</div>
                <div class="empty-description">从左侧列表中选择一个题目进行编辑，或点击"添加题目"创建新题目</div>
            </div>
        `;

        this.showEditorActions(false);
        this.updateState({ selectedQuestion: null, isEditing: false });
    }

    /**
     * 处理保存题目
     */
    handleSaveQuestion() {
        const formData = this.getFormData();

        if (!this.validateFormData(formData)) {
            return;
        }

        const isNew = !this.state.selectedQuestion?.id;

        this.emit('saveQuestionRequested', {
            question: formData,
            isNew
        });
    }

    /**
     * 获取表单数据
     */
    getFormData() {
        const form = this.container.querySelector('#editorForm');

        return {
            id: this.state.selectedQuestion?.id || null,
            type: this.state.currentQuestionType,
            subject: this.state.currentQuestionType === 'professional' ?
                     form.querySelector('#questionSubject')?.value : null,
            index: parseInt(form.querySelector('#questionIndex')?.value) || 0,
            title: form.querySelector('#questionTitle')?.value || '',
            content: form.querySelector('#questionContent')?.value || '',
            answer: form.querySelector('#questionAnswer')?.value || '',
            difficulty: form.querySelector('#questionDifficulty')?.value || 'medium',
            isActive: form.querySelector('#questionStatus')?.value === 'true',
            tags: form.querySelector('#questionTags')?.value.split(',').map(t => t.trim()).filter(t => t),
            notes: form.querySelector('#questionNotes')?.value || '',
            usageCount: this.state.selectedQuestion?.usageCount || 0,
            createdAt: this.state.selectedQuestion?.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
    }

    /**
     * 验证表单数据
     */
    validateFormData(data) {
        const errors = [];

        if (!data.title.trim()) {
            errors.push('题目标题不能为空');
        }

        if (!data.content.trim()) {
            errors.push('题目内容不能为空');
        }

        if (data.index <= 0) {
            errors.push('题目编号必须大于0');
        }

        if (data.type === 'professional' && !data.subject) {
            errors.push('专业题必须选择科目');
        }

        if (errors.length > 0) {
            this.showToast(errors.join('；'), 'error');
            return false;
        }

        return true;
    }

    /**
     * 工具方法
     */
    truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    getDifficultyText(difficulty) {
        const difficultyMap = {
            'easy': '简单',
            'medium': '中等',
            'hard': '困难'
        };
        return difficultyMap[difficulty] || '未知';
    }

    showToast(message, type = 'info') {
        // 调用全局Toast函数
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            console.log(`Toast: ${message}`);
        }
    }
}
