/**
 * 研究生复试题库管理系统
 * 版权所有 © 2025 王文通
 * 联系方式：<EMAIL>
 */

// 全局变量
const state = {
    translationPage: 1,
    translationPageSize: 10,
    translationTotal: 0,
    translationSearch: '',
    translationTagFilter: '',
    translationDifficultyFilter: '',
    
    professionalPage: 1,
    professionalPageSize: 10,
    professionalTotal: 0,
    professionalSearch: '',
    professionalTagFilter: '',
    professionalDifficultyFilter: '',
    
    activeTab: 'translation',
    
    selectedTranslations: [],
    selectedProfessionals: [],
    
    commonTags: {
        translation: ['英译汉', '汉译英', '专业术语', '长句翻译', '文献摘要'],
        professional: ['数学', '物理', '计算机', '经济学', '管理学', '机械', '电子', '通信']
    }
};

// 添加无标签的样式
const style = document.createElement('style');
style.textContent = `
    .tag-empty {
        background-color: #e0e0e0;
        color: #666;
    }
`;
document.head.appendChild(style);

// 在文件顶部声明全局变量
let translationTagsList = [];
let professionalTagsList = [];

/**
 * 初始化页面
 */
document.addEventListener('DOMContentLoaded', () => {
    // 初始化标签页切换
    initTabs();
    
    // 加载所有标签数据
    loadAllTags();
    
    // 加载题库
    loadTranslations();
    loadProfessionals();
    
    // 初始化添加按钮事件
    document.getElementById('addTranslationBtn').addEventListener('click', () => openTranslationModal());
    document.getElementById('addProfessionalBtn').addEventListener('click', () => openProfessionalModal());
    
    // 初始化批量添加按钮事件
    document.getElementById('batchAddTranslationBtn').addEventListener('click', () => openBatchAddModal('translation'));
    document.getElementById('batchAddProfessionalBtn').addEventListener('click', () => openBatchAddModal('professional'));
    
    // 初始化搜索功能
    initSearch();
    
    // 初始化导入/导出功能
    document.getElementById('importBtn').addEventListener('click', openImportModal);
    document.getElementById('exportBtn').addEventListener('click', exportQuestions);
    
    // 初始化模态框关闭按钮
    document.querySelectorAll('.close-btn, .cancel-btn').forEach(btn => {
        btn.addEventListener('click', closeAllModals);
    });
    
    // 初始化保存按钮
    document.getElementById('saveTranslationBtn').addEventListener('click', saveTranslation);
    document.getElementById('saveProfessionalBtn').addEventListener('click', saveProfessional);
    
    // 初始化添加题目项按钮
    document.getElementById('addTranslationItemBtn').addEventListener('click', () => addQuestionItem('translation'));
    document.getElementById('addProfessionalItemBtn').addEventListener('click', () => addQuestionItem('professional'));
    
    // 初始化导入确认按钮
    document.getElementById('confirmImportBtn').addEventListener('click', importQuestions);
    
    // 初始化批量操作相关事件
    initBatchOperations();
    
    // 初始化批量添加模态框相关事件
    initBatchAddModal();
    
    // 初始化标签模态框相关事件
    initTagModal();
    
    // 初始化全局标签选择器
    initTagSelector();
    
    // 初始化难度模态框相关事件
    initDifficultyModal();
    
    // 初始化标签过滤器事件处理
    initTagFilters();
    initPageSizeSelectors(); // <-- 添加这一行调用
});

/**
 * 初始化标签页切换
 */
function initTabs() {
    const tabs = document.querySelectorAll('.tab');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 移除所有tab的active类
            tabs.forEach(t => t.classList.remove('active'));
            
            // 添加当前tab的active类
            tab.classList.add('active');
            
            // 获取目标内容区
            const tabName = tab.getAttribute('data-tab');
            state.activeTab = tabName;
            
            // 隐藏所有内容区
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 显示目标内容区
            document.getElementById(`${tabName}-tab`).classList.add('active');
        });
    });
}

/**
 * 初始化搜索功能
 */
function initSearch() {
    // 翻译题搜索
    document.getElementById('searchTranslationBtn').addEventListener('click', () => {
        state.translationSearch = document.getElementById('searchTranslation').value.trim();
        state.translationPage = 1;
        loadTranslations();
    });
    
    document.getElementById('resetTranslationBtn').addEventListener('click', () => {
        document.getElementById('searchTranslation').value = '';
        state.translationSearch = '';
        state.translationPage = 1;
        loadTranslations();
    });
    
    // 专业题搜索
    document.getElementById('searchProfessionalBtn').addEventListener('click', () => {
        state.professionalSearch = document.getElementById('searchProfessional').value.trim();
        state.professionalPage = 1;
        loadProfessionals();
    });
    
    document.getElementById('resetProfessionalBtn').addEventListener('click', () => {
        document.getElementById('searchProfessional').value = '';
        state.professionalSearch = '';
        state.professionalPage = 1;
        loadProfessionals();
    });
}

/**
 * 加载翻译题库
 */
async function loadTranslations() {
    try {
        const translationList = document.getElementById('translationList');
        translationList.innerHTML = '<div class="no-questions">加载中...</div>';
        
        // 保留offset计算，用于计算序号
        const offset = (state.translationPage - 1) * state.translationPageSize;
        const searchParam = state.translationSearch ? `&search=${encodeURIComponent(state.translationSearch)}` : '';
        const tagParam = state.translationTagFilter ? `&tag=${encodeURIComponent(state.translationTagFilter)}` : '';
        const difficultyParam = state.translationDifficultyFilter ? `&difficulty=${encodeURIComponent(state.translationDifficultyFilter)}` : '';
        
        // 使用page参数替代offset参数
        const url = `/api/questions/translation?page=${state.translationPage}&limit=${state.translationPageSize}${searchParam}${tagParam}${difficultyParam}`;
        console.log('请求URL:', url);
        
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`加载翻译题目失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('接收到的翻译题数据:', data);
        
        if (data.total !== undefined) {
            state.translationTotal = data.total;
            console.log('翻译题总数:', state.translationTotal);
        } else {
            state.translationTotal = data.questions ? data.questions.length + offset : 0;
            if (state.translationTotal >= state.translationPageSize) {
                 console.warn('API响应缺少 `total` 字段，分页可能不准确。估算总数:', state.translationTotal);
                 // 可选：给用户一个提示
                 // toast('警告：无法获取准确的总题目数，分页可能不完整。', 'warning');
            } else {
                console.log('API响应缺少 `total` 字段，但当前页数据不足一页，使用估算总数:', state.translationTotal);
            }
        }
        
        translationList.innerHTML = '';
        
        if (!data.questions || data.questions.length === 0) {
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'no-questions';
            emptyDiv.innerHTML = `
                <p>题库为空或无匹配结果</p>
                <p>
                    <button class="btn btn-success add-btn">添加新题目</button>
                </p>
            `;
            translationList.appendChild(emptyDiv);
            emptyDiv.querySelector('.add-btn').addEventListener('click', () => openTranslationModal());
            state.translationTotal = 0;
        } else {
            data.questions.forEach((question, index) => {
                const serialNumber = offset + index + 1;
                translationList.appendChild(createQuestionElement(question, 'translation', serialNumber));
            });
        }
        
        updatePagination('translation');
    } catch (error) {
        console.error('Error loading translations:', error);
        toast('加载翻译题目失败: ' + error.message, 'error');
        const translationList = document.getElementById('translationList');
        translationList.innerHTML = `
            <div class="no-questions error">
                <p>加载失败: ${error.message}</p>
                <button class="btn btn-warning retry-btn">重试</button>
            </div>
        `;
        translationList.querySelector('.retry-btn').addEventListener('click', loadTranslations);
        state.translationTotal = 0;
        updatePagination('translation');
    }
}

/**
 * 加载专业题库
 */
async function loadProfessionals() {
    try {
        const professionalList = document.getElementById('professionalList');
        professionalList.innerHTML = '<div class="no-questions">加载中...</div>';
        
        // 保留offset计算，用于计算序号
        const offset = (state.professionalPage - 1) * state.professionalPageSize;
        const searchParam = state.professionalSearch ? `&search=${encodeURIComponent(state.professionalSearch)}` : '';
        const tagParam = state.professionalTagFilter ? `&tag=${encodeURIComponent(state.professionalTagFilter)}` : '';
        const difficultyParam = state.professionalDifficultyFilter ? `&difficulty=${encodeURIComponent(state.professionalDifficultyFilter)}` : '';
        
        // 使用page参数替代offset参数
        const url = `/api/questions/professional?page=${state.professionalPage}&limit=${state.professionalPageSize}${searchParam}${tagParam}${difficultyParam}`;
        console.log('请求URL:', url);
        
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`加载专业题目失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('接收到的专业题数据:', data);
        
        if (data.total !== undefined) {
            state.professionalTotal = data.total;
             console.log('专业题总数:', state.professionalTotal);
        } else {
            state.professionalTotal = data.questions ? data.questions.length + offset : 0;
             if (state.professionalTotal >= state.professionalPageSize) {
                 console.warn('API响应缺少 `total` 字段，分页可能不准确。估算总数:', state.professionalTotal);
                 // 可选：给用户一个提示
                 // toast('警告：无法获取准确的总题目数，分页可能不完整。', 'warning');
            } else {
                 console.log('API响应缺少 `total` 字段，但当前页数据不足一页，使用估算总数:', state.professionalTotal);
            }
        }
        
        professionalList.innerHTML = '';
        
        if (!data.questions || data.questions.length === 0) {
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'no-questions';
            emptyDiv.innerHTML = `
                <p>题库为空或无匹配结果</p>
                <p>
                    <button class="btn btn-success add-btn">添加新题目</button>
                </p>
            `;
            professionalList.appendChild(emptyDiv);
            emptyDiv.querySelector('.add-btn').addEventListener('click', () => openProfessionalModal());
            state.professionalTotal = 0;
        } else {
            data.questions.forEach((question, index) => {
                const serialNumber = offset + index + 1;
                professionalList.appendChild(createQuestionElement(question, 'professional', serialNumber));
            });
        }
        
        updatePagination('professional');
    } catch (error) {
        console.error('Error loading professionals:', error);
        toast('加载专业题目失败: ' + error.message, 'error');
        const professionalList = document.getElementById('professionalList');
        professionalList.innerHTML = `
            <div class="no-questions error">
                <p>加载失败: ${error.message}</p>
                <button class="btn btn-warning retry-btn">重试</button>
            </div>
        `;
        professionalList.querySelector('.retry-btn').addEventListener('click', loadProfessionals);
        state.professionalTotal = 0;
        updatePagination('professional');
    }
}

/**
 * 创建题目元素
 */
function createQuestionElement(question, type, index = null) {
    const element = document.createElement('div');
    element.className = 'question-item';
    
    // 打印完整的question对象以便调试
    console.log('创建题目元素，接收到的题目数据:', question);
    
    // 确保从API返回的结构中正确提取ID
    // 由于API格式可能有变化，我们尝试从不同位置获取ID
    let questionId = null;
    if (question.id !== undefined) {
        questionId = question.id;
    } else if (question._id !== undefined) {
        questionId = question._id;
    } else if (question.question_id !== undefined) {
        questionId = question.question_id;
    }
    
    console.log(`提取的题目ID: ${questionId}, 类型: ${typeof questionId}`);
    
    // 如果ID是数字类型，确保转换为字符串以便在DOM中使用
    if (typeof questionId === 'number') {
        questionId = String(questionId);
    }
    
    if (!questionId) {
        console.warn('警告: 题目缺少ID');
    }
    
    // 解析question_data
    let questionData = [];
    try {
        if (question.question_data) {
            if (typeof question.question_data === 'string') {
                questionData = JSON.parse(question.question_data);
            } else {
                questionData = question.question_data;
            }
        }
    } catch (e) {
        console.error('解析question_data出错:', e);
        questionData = [['txt', '数据格式错误']];
    }
    
    // 构建items数组，适配原代码期望的结构
    const items = [];
    if (Array.isArray(questionData)) {
        questionData.forEach(item => {
            if (Array.isArray(item) && item.length >= 2) {
                items.push({
                    type: item[0] === 'txt' ? '文本' : item[0],
                    content: item[1] || ''
                });
            }
        });
    }
    
    // 如果items为空，添加一个默认项
    if (items.length === 0) {
        items.push({ type: '文本', content: '无题目内容' });
    }
    
    // 获取第一个题目项作为标题
    const titleItem = items.find(item => item.type === '题目') || items[0] || { content: '无题目内容' };
    
    // 获取标签和难度 (确保tags始终是数组)
    let tags = Array.isArray(question.tags) ? question.tags : 
                (typeof question.tags === 'string' ? question.tags.split(',').map(t => t.trim()) : []);
    
    console.log('原始标签数据:', question.tags);
    
    // 处理可能的Unicode编码字符串标签
    tags = tags.map(tag => {
        // 先进行URL解码
        let decodedTag = tag;
        try {
            decodedTag = decodeURIComponent(tag);
        } catch (e) {
            console.log('URL解码失败，使用原始标签:', e.message);
        }
        
        // 检查是否为JSON字符串格式（数组或带引号的字符串）
        if (typeof decodedTag === 'string') {
            console.log('处理标签:', decodedTag);
            
            // 处理形如 ["\\u6c49\\u8bd1\\u82f1"] 的情况
            if (decodedTag.startsWith('[') && decodedTag.endsWith(']')) {
                try {
                    // 尝试解析JSON数组
                    const parsed = JSON.parse(decodedTag);
                    console.log('解析JSON数组结果:', parsed);
                    // 如果解析后是数组，取第一个元素
                    if (Array.isArray(parsed) && parsed.length > 0) {
                        return parsed[0];
                    }
                    return typeof parsed === 'string' ? parsed : decodedTag;
                } catch (e) {
                    console.log('JSON解析失败，尝试直接移除括号:', e.message);
                    // 如果不是有效的JSON，尝试直接解码字符串内容
                    const content = decodedTag.substring(1, decodedTag.length - 1);
                    
                    // 移除多余的转义符号 (例如 "\\u6c49" 变成 "\u6c49")
                    const unescaped = content.replace(/\\\\u/g, '\\u');
                    
                    // 处理 \uXXXX 格式的Unicode编码
                    if (unescaped.includes('\\u')) {
                        try {
                            return JSON.parse('"' + unescaped + '"');
                        } catch (e2) {
                            console.log('Unicode解码失败:', e2.message);
                            return unescaped;
                        }
                    }
                    
                    return content;
                }
            } 
            // 处理形如 "\u6c49\u8bd1\u82f1" 的情况
            else if (decodedTag.startsWith('"') && decodedTag.endsWith('"')) {
                try {
                    // 尝试解析JSON字符串
                    const parsed = JSON.parse(decodedTag);
                    console.log('解析JSON字符串结果:', parsed);
                    return parsed;
                } catch (e) {
                    console.log('JSON字符串解析失败:', e.message);
                    return decodedTag;
                }
            }
            // 处理直接的Unicode转义序列
            else if (decodedTag.includes('\\u')) {
                try {
                    // 需要将字符串放入引号中以让JSON.parse正确解析Unicode
                    const decoded = JSON.parse('"' + decodedTag + '"');
                    console.log('Unicode解码结果:', decoded);
                    return decoded;
                } catch (e) {
                    console.log('Unicode直接解码失败:', e.message);
                    return decodedTag;
                }
            }
        }
        return decodedTag;
    });
    
    console.log('处理后的标签:', tags);
    
    const difficulty = question.difficulty || '';
    
    // 创建难度标记HTML
    let difficultyHtml = '';
    if (difficulty) {
        const difficultyText = difficulty === 'easy' ? '简单' : (difficulty === 'medium' ? '中等' : '困难');
        difficultyHtml = `<div class="difficulty difficulty-${difficulty}">${difficultyText}</div>`;
    }
    
    // 计算序号
    let serialNumber = '';
    if (index !== null) {
        serialNumber = `<span class="question-serial">${index}.</span> `;
    } else {
        // 如果没有提供索引，使用当前页面和ID计算
        const page = type === 'translation' ? state.translationPage : state.professionalPage;
        const pageSize = type === 'translation' ? state.translationPageSize : state.professionalPageSize;
        const currentIndex = (page - 1) * pageSize + Array.from(document.querySelectorAll(`#${type}List .question-item`)).length + 1;
        serialNumber = `<span class="question-serial">${currentIndex}.</span> `;
    }
    
    element.innerHTML = `
        <div class="question-header">
            <div class="checkbox-container">
                <input type="checkbox" class="question-checkbox" data-id="${questionId || ''}">
            </div>
            <div class="question-title">${serialNumber}${titleItem.content.slice(0, 50)}${titleItem.content.length > 50 ? '...' : ''} ${difficultyHtml}</div>
            <div class="question-actions">
                <button class="btn btn-warning edit-btn">编辑</button>
                <button class="btn btn-danger delete-btn" data-id="${questionId || ''}">删除</button>
            </div>
        </div>
        <div class="question-content">
            ${items.map(item => `
                <p><strong>${item.type}:</strong> ${item.content}</p>
            `).join('')}
        </div>
        ${tags.length > 0 ? `
            <div class="tag-container">
                ${tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>
        ` : `
            <div class="tag-container">
                <span class="tag tag-empty">无标签</span>
            </div>
        `}
    `;
    
    // 添加事件监听
    element.querySelector('.edit-btn').addEventListener('click', () => {
        if (type === 'translation') {
            openTranslationModal({...question, items, _id: questionId});
        } else {
            openProfessionalModal({...question, items, _id: questionId});
        }
    });
    
    element.querySelector('.delete-btn').addEventListener('click', () => {
        // 获取按钮上的ID属性，以确保使用最新的值
        const btnId = element.querySelector('.delete-btn').getAttribute('data-id');
        console.log('点击删除按钮，题目ID:', btnId, '类型:', type);
        
        // 确保ID存在并且有效
        if (!btnId) {
            console.error('题目ID不存在，无法删除');
            toast('删除失败: 题目ID不存在');
            return;
        }
        
        deleteQuestion(btnId, type);
    });
    
    // 添加复选框事件
    element.querySelector('.question-checkbox').addEventListener('change', () => {
        updateSelectedQuestions(type);
    });
    
    return element;
}

/**
 * 更新分页控件
 */
function updatePagination(type) {
    const total = type === 'translation' ? state.translationTotal : state.professionalTotal;
    const pageSize = type === 'translation' ? state.translationPageSize : state.professionalPageSize;
    const currentPage = type === 'translation' ? state.translationPage : state.professionalPage;
    
    const paginationElement = document.getElementById(`${type}Pagination`);
    paginationElement.innerHTML = ''; // 清空现有分页
    
    // 确保 total 是有效数字
    if (isNaN(total) || total <= 0) {
        console.log('总题目数为0或无效，不显示分页');
        return; // 如果总数为0或无效，不显示分页
    }
    
    const totalPages = Math.ceil(total / pageSize);
    
    // 只有多于一页时才显示分页
    if (totalPages <= 1) {
         console.log('总页数不足一页，不显示分页');
        return;
    }
    
    const maxPagesToShow = 5; // 最多显示的页码按钮数 (不含上一页/下一页/省略号)
    const halfPagesToShow = Math.floor(maxPagesToShow / 2);
    
    function createPageItem(pageNumber, text = null, isDisabled = false, isActive = false) {
        const pageItem = document.createElement('div');
        pageItem.className = 'page-item';
        pageItem.textContent = text !== null ? text : pageNumber;
        if (isDisabled) {
            pageItem.classList.add('disabled');
        }
        if (isActive) {
            pageItem.classList.add('active');
        }
    
        if (!isDisabled) {
            pageItem.addEventListener('click', () => {
                if (type === 'translation') {
                    state.translationPage = pageNumber;
                    loadTranslations();
                } else {
                    state.professionalPage = pageNumber;
                    loadProfessionals();
                }
                 // 点击后滚动到列表顶部（可选）
                document.getElementById(`${type}-tab`).scrollIntoView({ behavior: 'smooth' });
            });
        }
        return pageItem;
    }
    
     function createEllipsisItem() {
        const ellipsisItem = document.createElement('div');
        ellipsisItem.className = 'page-item disabled';
        ellipsisItem.textContent = '...';
        return ellipsisItem;
    }
    
    // 添加 "上一页" 按钮
    paginationElement.appendChild(
        createPageItem(currentPage - 1, '上一页', currentPage === 1)
    );
    
    // 计算显示的页码范围
    let startPage, endPage;
    if (totalPages <= maxPagesToShow + 2) { // 如果总页数不多，全部显示
        startPage = 1;
        endPage = totalPages;
    } else {
        // 根据当前页决定显示范围
        if (currentPage <= halfPagesToShow + 1) {
            startPage = 1;
            endPage = maxPagesToShow;
        } else if (currentPage >= totalPages - halfPagesToShow) {
            startPage = totalPages - maxPagesToShow + 1;
            endPage = totalPages;
        } else {
            startPage = currentPage - halfPagesToShow;
            endPage = currentPage + halfPagesToShow;
        }
    }
    
     // 添加首页按钮 (如果需要)
    if (startPage > 1) {
        paginationElement.appendChild(createPageItem(1, 1, false, false));
        if (startPage > 2) {
            paginationElement.appendChild(createEllipsisItem());
        }
    }
    
    // 添加中间的页码按钮
    for (let i = startPage; i <= endPage; i++) {
        paginationElement.appendChild(
            createPageItem(i, i, false, i === currentPage)
        );
    }
    
     // 添加末页按钮 (如果需要)
    if (endPage < totalPages) {
         if (endPage < totalPages - 1) {
            paginationElement.appendChild(createEllipsisItem());
        }
        paginationElement.appendChild(createPageItem(totalPages, totalPages, false, false));
    }
    
    
    // 添加 "下一页" 按钮
    paginationElement.appendChild(
        createPageItem(currentPage + 1, '下一页', currentPage === totalPages)
    );
    
    // 添加总数和当前页信息 (可选)
    const infoDiv = document.createElement('div');
    infoDiv.className = 'pagination-info';
    infoDiv.textContent = `共 ${total} 条 / ${totalPages} 页`;
    infoDiv.style.marginLeft = '15px'; // 添加一些间距
    infoDiv.style.lineHeight = '36px'; // 与按钮垂直对齐
    paginationElement.appendChild(infoDiv);
}

/**
 * 打开翻译题目编辑模态框
 */
function openTranslationModal(question = null) {
    // 清空表单
    document.getElementById('translationId').value = '';
    document.getElementById('translationIndex').value = '';
    document.getElementById('translationItems').innerHTML = '';
    document.getElementById('translationTags').value = '';
    document.getElementById('translationDifficulty').value = 'medium'; // 默认设置为中等难度
    
    // 更新标题
    document.getElementById('translationModalTitle').textContent = question ? '编辑翻译题目' : '添加翻译题目';
    
    // 如果是编辑模式，填充表单
    if (question) {
        document.getElementById('translationId').value = question.id;
        document.getElementById('translationIndex').value = question.question_index;
        
        // 填充标签
        if (question.tags && Array.isArray(question.tags)) {
            // URL解码每个标签
            const decodedTags = question.tags.map(tag => {
                try {
                    return decodeURIComponent(tag);
                } catch (e) {
                    console.log('模态框标签URL解码失败:', e.message);
                    return tag;
                }
            });
            document.getElementById('translationTags').value = decodedTags.join(', ');
        }
        
        // 填充难度
        if (question.difficulty) {
            document.getElementById('translationDifficulty').value = question.difficulty;
        }
        
        // 解析题目内容
        try {
            const questionData = typeof question.question_data === 'string'
                ? JSON.parse(question.question_data)
                : question.question_data;
            
            // 添加题目项
            if (Array.isArray(questionData)) {
                questionData.forEach(q => addQuestionItem('translation', q));
            } else {
                // 如果数据格式不正确，添加一个空项
                addQuestionItem('translation');
                toast('题目数据格式不正确，已创建空白项');
            }
        } catch (e) {
            console.error('Error parsing question data in modal:', e);
            // 添加一个空白项
            addQuestionItem('translation');
            toast('解析题目数据失败，已创建空白项');
        }
    } else {
        // 添加一个空的题目项
        addQuestionItem('translation');
    }
    
    // 显示模态框
    document.getElementById('translationModal').classList.add('show');
    
    // 初始化标签选择器 - 确保在模态框显示后初始化
    setTimeout(() => {
        const tagItems = document.querySelectorAll('#commonTranslationTags .tag-item');
        console.log(`找到 ${tagItems.length} 个翻译题目标签选择器`);
        
        tagItems.forEach(item => {
            // 移除旧的事件处理
            item.removeEventListener('click', tagClickHandler);
            // 添加新的事件处理
            item.addEventListener('click', tagClickHandler);
        });
    }, 0);
}

/**
 * 打开专业题目编辑模态框
 */
function openProfessionalModal(question = null) {
    // 清空表单
    document.getElementById('professionalId').value = '';
    document.getElementById('professionalIndex').value = '';
    document.getElementById('professionalItems').innerHTML = '';
    document.getElementById('professionalTags').value = '';
    document.getElementById('professionalDifficulty').value = 'medium'; // 默认设置为中等难度
    
    // 更新标题
    document.getElementById('professionalModalTitle').textContent = question ? '编辑专业题目' : '添加专业题目';
    
    // 如果是编辑模式，填充表单
    if (question) {
        document.getElementById('professionalId').value = question.id;
        document.getElementById('professionalIndex').value = question.question_index;
        
        // 填充标签
        if (question.tags && Array.isArray(question.tags)) {
            // URL解码每个标签
            const decodedTags = question.tags.map(tag => {
                try {
                    return decodeURIComponent(tag);
                } catch (e) {
                    console.log('模态框标签URL解码失败:', e.message);
                    return tag;
                }
            });
            document.getElementById('professionalTags').value = decodedTags.join(', ');
        }
        
        // 填充难度
        if (question.difficulty) {
            document.getElementById('professionalDifficulty').value = question.difficulty;
        }
        
        // 解析题目内容
        try {
            const questionData = typeof question.question_data === 'string'
                ? JSON.parse(question.question_data)
                : question.question_data;
            
            // 添加题目项
            if (Array.isArray(questionData)) {
                questionData.forEach(q => addQuestionItem('professional', q));
            } else {
                // 如果数据格式不正确，添加一个空项
                addQuestionItem('professional');
                toast('题目数据格式不正确，已创建空白项');
            }
        } catch (e) {
            console.error('Error parsing question data in modal:', e);
            // 添加一个空白项
            addQuestionItem('professional');
            toast('解析题目数据失败，已创建空白项');
        }
    } else {
        // 添加一个空的题目项
        addQuestionItem('professional');
    }
    
    // 显示模态框
    document.getElementById('professionalModal').classList.add('show');
    
    // 初始化标签选择器 - 确保在模态框显示后初始化
    setTimeout(() => {
        const tagItems = document.querySelectorAll('#commonProfessionalTags .tag-item');
        console.log(`找到 ${tagItems.length} 个专业题目标签选择器`);
        
        tagItems.forEach(item => {
            // 移除旧的事件处理
            item.removeEventListener('click', tagClickHandler);
            // 添加新的事件处理
            item.addEventListener('click', tagClickHandler);
        });
    }, 0);
}

/**
 * 添加题目项
 */
function addQuestionItem(type, data = null) {
    const container = document.getElementById(`${type}Items`);
    const itemIndex = container.children.length;
    
    const itemElement = document.createElement('div');
    itemElement.className = 'item-editor';
    
    // 设置默认值
    const itemType = data ? data[0] : 'txt';
    const itemContent = data ? data[1] : '';
    
    // 构建HTML
    itemElement.innerHTML = `
        <div class="item-type">
            <select class="item-type-select">
                <option value="txt" ${itemType === 'txt' ? 'selected' : ''}>文本</option>
                <option value="img" ${itemType === 'img' ? 'selected' : ''}>图片</option>
            </select>
        </div>
        <div class="item-content">
            <input type="text" class="item-content-input" value="${itemContent}" placeholder="${itemType === 'txt' ? '输入题目内容...' : '输入图片URL...'}">
            ${itemType === 'img' && itemContent ? `<img src="${itemContent}" class="preview-image" alt="题目图片">` : ''}
        </div>
        <div class="item-actions">
            <button type="button" class="btn btn-warning item-move-up" ${itemIndex === 0 ? 'disabled' : ''}>↑</button>
            <button type="button" class="btn btn-warning item-move-down">↓</button>
            <button type="button" class="btn btn-danger item-delete" ${container.children.length <= 1 ? 'disabled' : ''}>×</button>
        </div>
    `;
    
    // 添加事件监听
    // 类型选择改变事件
    itemElement.querySelector('.item-type-select').addEventListener('change', function() {
        const contentInput = itemElement.querySelector('.item-content-input');
        contentInput.placeholder = this.value === 'txt' ? '输入题目内容...' : '输入图片URL...';
        contentInput.value = '';
        
        // 移除预览图片
        const previewImage = itemElement.querySelector('.preview-image');
        if (previewImage) {
            previewImage.remove();
        }
    });
    
    // 内容输入框改变事件（仅用于图片预览）
    itemElement.querySelector('.item-content-input').addEventListener('change', function() {
        if (itemElement.querySelector('.item-type-select').value === 'img') {
            // 移除现有预览
            const existingPreview = itemElement.querySelector('.preview-image');
            if (existingPreview) {
                existingPreview.remove();
            }
            
            // 添加新预览
            if (this.value) {
                const img = document.createElement('img');
                img.src = this.value;
                img.alt = '题目图片';
                img.className = 'preview-image';
                itemElement.querySelector('.item-content').appendChild(img);
            }
        }
    });
    
    // 上移按钮
    itemElement.querySelector('.item-move-up').addEventListener('click', function() {
        if (itemIndex > 0) {
            container.insertBefore(itemElement, container.children[itemIndex - 1]);
            updateItemButtons(container);
        }
    });
    
    // 下移按钮
    itemElement.querySelector('.item-move-down').addEventListener('click', function() {
        if (itemIndex < container.children.length - 1) {
            container.insertBefore(itemElement, container.children[itemIndex + 2]);
            updateItemButtons(container);
        }
    });
    
    // 删除按钮
    itemElement.querySelector('.item-delete').addEventListener('click', function() {
        if (container.children.length > 1) {
            itemElement.remove();
            updateItemButtons(container);
        }
    });
    
    // 添加到容器
    container.appendChild(itemElement);
    updateItemButtons(container);
}

/**
 * 更新题目项按钮状态
 */
function updateItemButtons(container) {
    const items = container.children;
    
    for (let i = 0; i < items.length; i++) {
        // 更新上移按钮
        items[i].querySelector('.item-move-up').disabled = i === 0;
        
        // 更新下移按钮
        items[i].querySelector('.item-move-down').disabled = i === items.length - 1;
        
        // 更新删除按钮
        items[i].querySelector('.item-delete').disabled = items.length <= 1;
    }
}

/**
 * 保存翻译题目
 */
async function saveTranslation() {
    try {
        const id = document.getElementById('translationId').value;
        const questionIndex = document.getElementById('translationIndex').value || -1;
        
        // 收集题目项数据
        const items = document.getElementById('translationItems').children;
        const questionData = [];
        
        for (let i = 0; i < items.length; i++) {
            const type = items[i].querySelector('.item-type-select').value;
            const content = items[i].querySelector('.item-content-input').value.trim();
            
            if (!content) {
                toast('请填写所有题目内容');
                return;
            }
            
            questionData.push([type, content]);
        }
        
        // 获取标签 - 添加空值检查
        let tags = [];
        const tagsElement = document.getElementById('translationTags');
        if (tagsElement && tagsElement.value) {
            const tagsInput = tagsElement.value.trim();
            tags = tagsInput ? tagsInput.split(/\s*,\s*/) : [];
            
            // URL编码标签
            tags = tags.map(tag => encodeURIComponent(tag));
        }
        
        // 获取难度 - 添加空值检查
        let difficulty = 'medium';
        const difficultyElement = document.getElementById('translationDifficulty');
        if (difficultyElement) {
            difficulty = difficultyElement.value;
        }
        
        // 准备请求数据
        const requestData = {
            question_data: JSON.stringify(questionData),
            question_index: questionIndex,
            tags: tags, // 使用纯文本字符串数组,
            difficulty: difficulty
        };
        
        console.log('保存翻译题目数据:', requestData);
        
        // 发送请求
        let response;
        if (id) {
            // 更新模式
            response = await fetch(`/api/questions/translation/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
        } else {
            // 添加模式
            response = await fetch('/api/questions/translation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
        }
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error('Failed to save translation question: ' + (errorData.error || response.statusText));
        }
        
        // 关闭模态框
        closeAllModals();
        
        // 重新加载题库
        loadTranslations();
        
        // 显示成功消息
        toast(id ? '翻译题目已更新' : '翻译题目已添加');
    } catch (error) {
        console.error('Error saving translation:', error);
        toast('保存翻译题目失败: ' + error.message);
    }
}

/**
 * 保存专业题目
 */
async function saveProfessional() {
    try {
        const id = document.getElementById('professionalId').value;
        const questionIndex = document.getElementById('professionalIndex').value || -1;
        
        // 收集题目项数据
        const items = document.getElementById('professionalItems').children;
        const questionData = [];
        
        for (let i = 0; i < items.length; i++) {
            const type = items[i].querySelector('.item-type-select').value;
            const content = items[i].querySelector('.item-content-input').value.trim();
            
            if (!content) {
                toast('请填写所有题目内容');
                return;
            }
            
            questionData.push([type, content]);
        }
        
        // 获取标签 - 添加空值检查
        let tags = [];
        const tagsElement = document.getElementById('professionalTags');
        if (tagsElement && tagsElement.value) {
            const tagsInput = tagsElement.value.trim();
            tags = tagsInput ? tagsInput.split(/\s*,\s*/) : [];
            
            // URL编码标签
            tags = tags.map(tag => encodeURIComponent(tag));
        }
        
        // 获取难度 - 添加空值检查
        let difficulty = 'medium';
        const difficultyElement = document.getElementById('professionalDifficulty');
        if (difficultyElement) {
            difficulty = difficultyElement.value;
        }
        
        // 准备请求数据
        const requestData = {
            question_data: JSON.stringify(questionData),
            question_index: questionIndex,
            tags: tags, // 使用纯文本字符串数组,
            difficulty: difficulty
        };
        
        console.log('保存专业题目数据:', requestData);
        
        // 发送请求
        let response;
        if (id) {
            // 更新模式
            response = await fetch(`/api/questions/professional/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
        } else {
            // 添加模式
            response = await fetch('/api/questions/professional', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
        }
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error('Failed to save professional question: ' + (errorData.error || response.statusText));
        }
        
        // 关闭模态框
        closeAllModals();
        
        // 重新加载题库
        loadProfessionals();
        
        // 显示成功消息
        toast(id ? '专业题目已更新' : '专业题目已添加');
    } catch (error) {
        console.error('Error saving professional:', error);
        toast('保存专业题目失败: ' + error.message);
    }
}

/**
 * 删除题目
 */
async function deleteQuestion(id, type) {
    console.log(`正在删除题目，ID: ${id}, 类型: ${type}`);
    if (!id) {
        toast('删除失败: 题目ID不存在');
        return;
    }
    
    // 确保ID是一个字符串
    const questionId = String(id);
    
    if (!confirm(`确定要删除这个${type === 'translation' ? '翻译' : '专业'}题目吗？`)) {
        return;
    }
    
    try {
        toast('删除中...');
        
        // 构建API路径
        const apiUrl = `/api/questions/${type}/${questionId}`;
        console.log(`发送删除请求到: ${apiUrl}`);
        
        // 发送删除请求到后端API
        const response = await fetch(apiUrl, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log(`删除请求响应状态: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
            let errorMessage = `状态码: ${response.status}`;
            try {
            const errorData = await response.json();
                errorMessage += `, 错误信息: ${errorData.error || '未知错误'}`;
                throw new Error('Failed to delete question: ' + (errorData.error || response.statusText));
            } catch (jsonError) {
                throw new Error(`删除失败: ${errorMessage}`);
            }
        }
        
        // 删除成功
        toast(`${type === 'translation' ? '翻译' : '专业'}题目删除成功`);
        
        // 重新加载题目
        if (type === 'translation') {
            loadTranslations();
        } else {
            loadProfessionals();
        }
    } catch (error) {
        console.error(`Error deleting ${type} question:`, error);
        toast(`删除${type === 'translation' ? '翻译' : '专业'}题目失败: ` + error.message);
    }
}

/**
 * 打开导入模态框
 */
function openImportModal() {
    document.getElementById('importFile').value = '';
    document.getElementById('importTranslation').checked = true;
    document.getElementById('importModal').classList.add('show');
}

/**
 * 导入题库
 */
async function importQuestions() {
    const fileInput = document.getElementById('importFile');
    
    if (!fileInput.files || fileInput.files.length === 0) {
        toast('请选择JSON文件');
        return;
    }
    
    const file = fileInput.files[0];
    if (!file.name.endsWith('.json')) {
        toast('请选择JSON格式的文件');
        return;
    }
    
    try {
        // 读取文件
        const fileContent = await readFileAsText(file);
        let questions;
        
        try {
            questions = JSON.parse(fileContent);
        } catch (e) {
            toast('JSON解析失败，请检查文件格式');
            return;
        }
        
        // 检查数据格式
        if (!Array.isArray(questions)) {
            toast('无效的题库格式，应为题目数组');
            return;
        }
        
        toast('正在导入题库，请稍候...');
        
        // 获取题库类型
        const type = document.querySelector('input[name="importType"]:checked').value;
        
        // 发送请求
        const response = await fetch(`/api/questions/${type}/import`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ questions })
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error('Failed to import questions: ' + (errorData.error || response.statusText));
        }
        
        const result = await response.json();
        
        // 关闭模态框
        closeAllModals();
        
        // 重新加载题库
        if (type === 'translation') {
            loadTranslations();
        } else {
            loadProfessionals();
        }
        
        // 显示成功消息
        toast(`成功导入 ${result.imported} 个题目`);
    } catch (error) {
        console.error('Error importing questions:', error);
        toast('导入题库失败: ' + error.message);
    }
}

/**
 * 导出题库
 */
async function exportQuestions() {
    try {
        // 获取当前活动的标签页
        const type = state.activeTab;
        
        toast('正在准备导出数据...');
        
        // 获取所有题目
        const response = await fetch(`/api/questions/${type}/export`);
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error('Failed to export questions: ' + (errorData.error || response.statusText));
        }
        
        const questions = await response.json();
        
        // 转换为JSON字符串
        const json = JSON.stringify(questions, null, 2);
        
        // 创建下载链接
        const blob = new Blob([json], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${type}_questions_${new Date().toISOString().slice(0, 10)}.json`;
        a.click();
        
        // 清理
        URL.revokeObjectURL(url);
        
        // 显示成功消息
        toast(`成功导出 ${questions.length} 个题目`);
    } catch (error) {
        console.error('Error exporting questions:', error);
        toast('导出题库失败: ' + error.message);
    }
}

/**
 * 辅助函数：读取文件内容
 */
function readFileAsText(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => {
            const buffer = e.target.result;
            const uint8Array = new Uint8Array(buffer);

            // 尝试使用UTF-8解码
            try {
                const utf8Decoder = new TextDecoder('utf-8', { fatal: true }); // 使用fatal选项，解码失败会抛出异常
                const text = utf8Decoder.decode(uint8Array);
                // 检查是否存在UTF-8 BOM (EF BB BF)
                if (text.charCodeAt(0) === 0xFEFF) {
                    // 如果存在BOM，则移除它
                    resolve(text.slice(1));
                } else {
                    resolve(text);
                }
                console.log('使用UTF-8解码成功');
            } catch (utf8Error) {
                console.warn('UTF-8解码失败，尝试使用GBK解码:', utf8Error.message);
                // 尝试使用GBK解码
                try {
                    const gbkDecoder = new TextDecoder('gbk');
                    const text = gbkDecoder.decode(uint8Array);
                    resolve(text);
                    console.log('使用GBK解码成功');
                } catch (gbkError) {
                    console.error('GBK解码也失败:', gbkError.message);
                    // 如果GBK也失败，回退到不严格的UTF-8解码（可能会产生乱码）
                    const fallbackDecoder = new TextDecoder('utf-8');
                    resolve(fallbackDecoder.decode(uint8Array));
                    console.log('回退到非严格UTF-8解码');
                    // 或者直接拒绝
                    // reject(new Error('无法确定文件编码，UTF-8和GBK解码均失败'));
                }
            }
        };
        reader.onerror = e => reject(e);
        reader.readAsArrayBuffer(file); // 读取为ArrayBuffer
    });
}

/**
 * 关闭所有模态框
 */
function closeAllModals() {
    document.querySelectorAll('.modal').forEach(modal => {
        modal.classList.remove('show');
    });
}

/**
 * 显示Toast消息
 */
function toast(message, type = 'success') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

/**
 * 初始化批量操作相关事件
 */
function initBatchOperations() {
    // 全选/取消全选
    document.getElementById('selectAllTranslation').addEventListener('change', (e) => {
        const checkboxes = document.querySelectorAll('#translationList .question-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = e.target.checked);
        updateSelectedQuestions('translation');
    });
    
    document.getElementById('selectAllProfessional').addEventListener('change', (e) => {
        const checkboxes = document.querySelectorAll('#professionalList .question-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = e.target.checked);
        updateSelectedQuestions('professional');
    });
    
    // 应用批量操作
    document.getElementById('applyBulkActionTranslation').addEventListener('click', () => {
        applyBulkAction('translation');
    });
    
    document.getElementById('applyBulkActionProfessional').addEventListener('click', () => {
        applyBulkAction('professional');
    });
    
    // 应用标签
    document.getElementById('applyTagsBtn').addEventListener('click', applyTags);
    
    // 应用难度
    document.getElementById('applyDifficultyBtn').addEventListener('click', applyDifficulty);
}

/**
 * 更新选中的题目
 */
function updateSelectedQuestions(type) {
    const checkboxes = document.querySelectorAll(`#${type}List .question-checkbox:checked`);
    const ids = Array.from(checkboxes).map(checkbox => checkbox.getAttribute('data-id'));
    
    if (type === 'translation') {
        state.selectedTranslations = ids;
    } else {
        state.selectedProfessionals = ids;
    }
    
    console.log(`已选中的${type}题目IDs:`, ids);
}

/**
 * 应用批量操作
 */
function applyBulkAction(type) {
    const action = document.getElementById(`bulkAction${type.charAt(0).toUpperCase() + type.slice(1)}`).value;
    const selectedIds = type === 'translation' ? state.selectedTranslations : state.selectedProfessionals;
    
    if (!action) {
        toast('请选择要执行的操作');
        return;
    }
    
    if (selectedIds.length === 0) {
        toast('请选择要操作的题目');
        return;
    }
    
    switch (action) {
        case 'delete':
            confirmBulkDelete(selectedIds, type);
            break;
        case 'tag':
            openTagModal(selectedIds, type);
            break;
        case 'difficulty':
            openDifficultyModal(selectedIds, type);
            break;
        case 'export':
            exportSelectedQuestions(selectedIds, type);
            break;
        default:
            toast('未知操作');
    }
}

/**
 * 确认批量删除
 */
function confirmBulkDelete(ids, type) {
    if (confirm(`确定要删除选中的 ${ids.length} 个题目吗？此操作不可恢复！`)) {
        bulkDeleteQuestions(ids, type);
    }
}

/**
 * 批量删除题目
 */
async function bulkDeleteQuestions(ids, type) {
    try {
        toast('正在删除，请稍候...');
        
        // 改用Promise.allSettled来获取每个请求的结果
        const promises = ids.map(id => 
            fetch(`/api/questions/${type}/${id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(async response => {
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ error: response.statusText }));
                    throw new Error(`Failed to delete question ${id}: ${errorData.error || response.statusText}`);
                }
                return { id, success: true };
            }).catch(error => {
                console.error(`Error deleting question ${id}:`, error);
                return { id, success: false, error: error.message };
            })
        );
        
        const results = await Promise.allSettled(promises);
        
        // 统计成功和失败的数量
        const succeeded = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
        const failed = ids.length - succeeded;
        
        if (failed > 0) {
            toast(`删除完成: ${succeeded} 个成功, ${failed} 个失败`);
        } else {
            toast(`成功删除 ${succeeded} 个题目`);
        }
        
        // 重新加载题目
        if (type === 'translation') {
            loadTranslations();
        } else {
            loadProfessionals();
        }
    } catch (error) {
        console.error('Error bulk deleting questions:', error);
        toast('批量删除失败: ' + error.message);
    }
}

/**
 * 导出选中的题目
 */
async function exportSelectedQuestions(ids, type) {
    try {
        toast('正在准备导出数据...');
        
        const promises = ids.map(id => 
            fetch(`/api/questions/${type}/${id}`).then(res => res.json())
        );
        
        const questions = await Promise.all(promises);
        
        const data = JSON.stringify(questions, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${type}_questions_selected_${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        toast(`成功导出 ${questions.length} 个题目`);
    } catch (error) {
        console.error('Error exporting selected questions:', error);
        toast('导出选中题目失败: ' + error.message);
    }
}

/**
 * 初始化批量添加模态框
 */
function initBatchAddModal() {
    // 切换批量导入方式标签
    const batchImportTabs = document.querySelectorAll('.batch-import-tab');
    
    batchImportTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 移除所有tab的active类
            batchImportTabs.forEach(t => t.classList.remove('active'));
            
            // 添加当前tab的active类
            tab.classList.add('active');
            
            // 获取目标内容区
            const tabName = tab.getAttribute('data-tab');
            
            // 隐藏所有内容区
            document.querySelectorAll('.batch-import-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 显示目标内容区
            document.getElementById(`${tabName}-content`).classList.add('active');
        });
    });
    
    // 预览按钮
    document.getElementById('previewBatchBtn').addEventListener('click', previewBatchQuestions);
    
    // 导入按钮
    document.getElementById('importBatchBtn').addEventListener('click', importBatchQuestions);
    
    // 添加模板格式选择器
    const templateFormatContainer = document.getElementById('templateFormatContainer');
    if (!templateFormatContainer) {
        // 创建模板格式选择器
        const formatSelector = document.createElement('div');
        formatSelector.className = 'form-group template-format-selector';
        formatSelector.innerHTML = `
            <label for="templateFormat">选择模板格式:</label>
            <select id="templateFormat" class="form-control">
                <option value="csv">CSV</option>
                <option value="json">JSON</option>
                <option value="txt">文本(TXT)</option>
            </select>
        `;
        
        // 找到下载模板按钮
        const downloadTemplateBtn = document.getElementById('downloadTemplateBtn');
        if (downloadTemplateBtn && downloadTemplateBtn.parentNode) {
            // 在下载按钮前插入格式选择器
            downloadTemplateBtn.parentNode.insertBefore(formatSelector, downloadTemplateBtn);
        }
    }
    
    // 下载模板按钮
    document.getElementById('downloadTemplateBtn').addEventListener('click', downloadTemplate);
}

/**
 * 打开批量添加模态框
 */
function openBatchAddModal(type) {
    // 设置默认题目类型
    document.getElementById('batchQuestionType').value = type;
    
    // 清空输入
    document.getElementById('batchTextInput').value = '';
    document.getElementById('batchFileUpload').value = '';
    document.getElementById('batchPreviewArea').style.display = 'none';
    
    // 显示模态框
    document.getElementById('batchAddModal').classList.add('show');
}

/**
 * 预览批量添加的题目
 */
function previewBatchQuestions() {
    const activeTab = document.querySelector('.batch-import-tab.active').getAttribute('data-tab');
    const type = document.getElementById('batchQuestionType').value;
    let questions = [];
    
    if (activeTab === 'text-input') {
        const text = document.getElementById('batchTextInput').value.trim();
        if (!text) {
            toast('请输入题目内容');
            return;
        }
        
        questions = parseTextInput(text);
    } else {
        const fileInput = document.getElementById('batchFileUpload');
        if (!fileInput.files || fileInput.files.length === 0) {
            toast('请选择文件');
            return;
        }
        
        // 使用异步处理文件解析
        toast('正在解析文件...');
        parseFileInput(fileInput.files[0]).then(parsedQuestions => {
            if (parsedQuestions.length === 0) {
                toast('未解析到有效的题目，请检查文件格式');
                return;
            }
            
            // 显示预览
            displayQuestionPreview(parsedQuestions);
        }).catch(error => {
            console.error('文件解析错误:', error);
            toast('文件解析失败: ' + error.message);
        });
        
        return; // 提前返回，避免执行下面的代码
    }
    
    if (questions.length === 0) {
        toast('未解析到有效的题目，请检查格式');
        return;
    }
    
    // 显示预览
    displayQuestionPreview(questions);
}

/**
 * 显示题目预览
 * @param {Array} questions - 要预览的题目数组
 */
function displayQuestionPreview(questions) {
    const previewArea = document.getElementById('batchPreviewArea');
    const previewContent = document.getElementById('batchPreviewContent');
    const previewCount = document.getElementById('previewCount');
    
    previewCount.textContent = `共 ${questions.length} 个题目`;
    previewContent.innerHTML = '';
    
    questions.forEach((q, index) => {
        const questionPreview = document.createElement('div');
        questionPreview.className = 'question-item';
        questionPreview.innerHTML = `
            <div class="question-header">
                <div class="question-title"><span class="question-serial">${index + 1}.</span> ${q.items && q.items.length > 0 ? q.items[0].content.slice(0, 50) + (q.items[0].content.length > 50 ? '...' : '') : '无题目内容'}</div>
            </div>
            <div class="question-content">
                ${q.items ? q.items.map(item => `
                    <p><strong>${item.type}:</strong> ${item.content}</p>
                `).join('') : '无题目内容'}
                ${q.difficulty ? `<p><strong>难度:</strong> ${q.difficulty}</p>` : ''}
                ${q.tags ? `<p><strong>标签:</strong> ${q.tags.join(', ')}</p>` : ''}
            </div>
        `;
        previewContent.appendChild(questionPreview);
    });
    
    previewArea.style.display = 'block';
    toast(`共解析 ${questions.length} 个题目`);
    
    // 将解析结果保存到全局变量，以便导入时使用
    window.parsedQuestions = questions;
}

/**
 * 解析文本输入的题目
 */
function parseTextInput(text) {
    // 检查是否为CSV格式（以逗号分隔的数据）
    if (text.includes(',') && !text.includes('：') && !text.includes('---')) {
        console.log('检测到CSV格式的文本，尝试按CSV格式解析');
        
        // 按行分割
        const lines = text.split(/\r?\n/).filter(line => line.trim());
        if (lines.length === 0) return [];
        
        // 判断是否含有标题行
        const firstLine = lines[0].toLowerCase();
        const hasHeader = firstLine.includes('题目') || 
                         firstLine.includes('选项') || 
                         firstLine.includes('答案') ||
                         firstLine.includes('标签') ||
                         firstLine.includes('难度');
        
        // 如果似乎有标题行，则调用CSV解析方法
        if (hasHeader) {
            console.log('检测到CSV标题行，使用CSV解析器解析');
            return parseCsvFile(text);
        }
        
        // 没有标题行，自己构建结构
        console.log('没有检测到CSV标题行，按列构建题目');
        const questions = [];
        
        // 从第一行开始解析（没有标题行）
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;
            
            // 分割列
            const columns = parseCSVLine(line);
            console.log(`第 ${i+1} 行解析结果:`, columns);
            
            // 每列作为一个题目
            columns.forEach(column => {
                if (column.trim()) {
                    questions.push({
                        items: [{
                            type: '题目',
                            content: column.trim()
                        }]
                    });
                }
            });
        }
        
        console.log(`从CSV格式文本解析出 ${questions.length} 个题目`);
        return questions;
    }
    
    // 原始的分隔符格式解析
    const questionTexts = text.split(/\s*---\s*/);
    const questions = [];
    
    questionTexts.forEach(qText => {
        qText = qText.trim();
        if (!qText) return;
        
        const question = {
            items: []
        };
        
        // 按行分割
        const lines = qText.split('\n');
        
        lines.forEach(line => {
            line = line.trim();
            if (!line) return;
            
            if (line.startsWith('题目：')) {
                question.items.push({
                    type: '题目',
                    content: line.substring(3).trim()
                });
            } else if (line.startsWith('选项：')) {
                question.items.push({
                    type: '选项',
                    content: line.substring(3).trim()
                });
            } else if (line.startsWith('答案：')) {
                question.items.push({
                    type: '答案',
                    content: line.substring(3).trim()
                });
            } else if (line.startsWith('难度：')) {
                const difficulty = line.substring(3).trim();
                if (difficulty === '简单') {
                    question.difficulty = 'easy';
                } else if (difficulty === '中等') {
                    question.difficulty = 'medium';
                } else if (difficulty === '困难') {
                    question.difficulty = 'hard';
                }
            } else if (line.startsWith('标签：')) {
                question.tags = line.substring(3).trim().split(/\s*,\s*/);
            } else if (line.includes('：')) {
                // 其他自定义的键值对
                const [type, content] = line.split('：', 2);
                question.items.push({
                    type: type.trim(),
                    content: content.trim()
                });
            } else {
                // 没有冒号的行当作题目内容处理
                if (!question.items.length) {
                    question.items.push({
                        type: '题目',
                        content: line
                    });
                } else {
                    // 追加到上一行
                    question.items[question.items.length - 1].content += '\n' + line;
                }
            }
        });
        
        if (question.items.length > 0) {
            questions.push(question);
        }
    });
    
    return questions;
}

/**
 * 导入批量添加的题目
 */
async function importBatchQuestions() {
    const activeTab = document.querySelector('.batch-import-tab.active').getAttribute('data-tab');
    const type = document.getElementById('batchQuestionType').value;
    let questions = [];
    
    // 检查是否已经有预览解析结果
    if (window.parsedQuestions && window.parsedQuestions.length > 0) {
        questions = window.parsedQuestions;
    } else {
        if (activeTab === 'text-input') {
            const text = document.getElementById('batchTextInput').value.trim();
            if (!text) {
                toast('请输入题目内容');
                return;
            }
            
            questions = parseTextInput(text);
        } else {
            const fileInput = document.getElementById('batchFileUpload');
            if (!fileInput.files || fileInput.files.length === 0) {
                toast('请选择文件');
                return;
            }
            
            try {
                toast('正在解析文件...');
                questions = await parseFileInput(fileInput.files[0]);
            } catch (error) {
                console.error('文件解析错误:', error);
                toast('文件解析失败: ' + error.message);
                return;
            }
        }
    }
    
    if (questions.length === 0) {
        toast('未解析到有效的题目，请检查格式');
        return;
    }
    
    try {
        toast(`正在导入 ${questions.length} 个题目，请稍候...`);
        
        // 改进：使用统一的API格式，将数据统一为question_data格式
        const promises = questions.map(question => {
            // 将题目项转换为API需要的格式
            const questionData = question.items.map(item => {
                const type = item.type === '题目' ? 'txt' : 
                            item.type === '文本' ? 'txt' : 
                            item.type === '图片' ? 'img' : 'txt';
                return [type, item.content];
            });
            
            return fetch(`/api/questions/${type}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    question_data: JSON.stringify(questionData),
                    tags: question.tags || [],
                    difficulty: question.difficulty || 'medium'
                })
            });
        });
        
        const results = await Promise.allSettled(promises);
        const successCount = results.filter(r => r.status === 'fulfilled').length;
        
        toast(`成功导入 ${successCount} 个题目${successCount < questions.length ? `，${questions.length - successCount} 个导入失败` : ''}`);
        
        // 重新加载题目
        if (type === 'translation') {
            loadTranslations();
        } else {
            loadProfessionals();
        }
        
        // 清除解析结果
        window.parsedQuestions = null;
        
        // 关闭模态框
        closeAllModals();
    } catch (error) {
        console.error('Error importing batch questions:', error);
        toast('批量导入失败: ' + error.message);
    }
}

/**
 * 下载模板文件
 */
function downloadTemplate() {
    const type = document.getElementById('batchQuestionType').value;
    const templateName = `${type}_template`;
    
    // 获取所选的模板格式
    const templateFormat = document.getElementById('templateFormat') ? 
        document.getElementById('templateFormat').value : 'csv';
    
    switch (templateFormat) {
        case 'json':
            downloadJsonTemplate(templateName);
            break;
        case 'txt':
            downloadTxtTemplate(templateName);
            break;
        case 'csv':
        default:
            downloadCsvTemplate(templateName);
            break;
    }
}

/**
 * 下载CSV模板
 * @param {string} templateName - 模板文件名
 */
function downloadCsvTemplate(templateName) {
    // 创建CSV内容
    let csvContent = 'data:text/csv;charset=utf-8,题目,题目\n';
    csvContent += '这是示例题目11,这是示例题目21\n';
    csvContent += '这是示例题目12,这是示例题目22\n';
    csvContent += '这是示例题目13,这是示例题目23\n';
    csvContent += '这是示例题目14,这是示例题目24\n';
    csvContent += '这是示例题目15,这是示例题目25\n';
    csvContent += '这是示例题目16,这是示例题目26\n';
    
    // 创建下载链接
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `${templateName}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * 下载JSON模板
 * @param {string} templateName - 模板文件名
 */
function downloadJsonTemplate(templateName) {
    // 创建JSON内容
    const jsonData = [
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目11"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目12"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目13"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目14"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目15"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目16"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目21"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目22"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目23"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目24"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目25"
                }
            ]
        },
        {
            "items": [
                {
                    "type": "题目",
                    "content": "这是示例题目26"
                }
            ]
        }
    ];
    
    // 转换为JSON字符串
    const jsonContent = JSON.stringify(jsonData, null, 2);
    
    // 创建Blob对象
    const blob = new Blob([jsonContent], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = `${templateName}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 释放URL对象
    URL.revokeObjectURL(url);
}

/**
 * 下载TXT模板
 * @param {string} templateName - 模板文件名
 */
function downloadTxtTemplate(templateName) {
    // 创建TXT内容
    const txtContent = `题目,题目
这是示例题目11,这是示例题目21
这是示例题目12,这是示例题目22
这是示例题目13,这是示例题目23
这是示例题目14,这是示例题目24
这是示例题目15,这是示例题目25
这是示例题目16,这是示例题目26`;
    
    // 创建Blob对象
    const blob = new Blob([txtContent], {type: 'text/plain'});
    const url = URL.createObjectURL(blob);
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = `${templateName}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 释放URL对象
    URL.revokeObjectURL(url);
}

/**
 * 初始化标签模态框
 */
function initTagModal() {
    // 展示常用标签
    function renderCommonTags(containerId, tagList) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        
        tagList.forEach(tag => {
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-item';
            tagElement.textContent = tag;
            
            // 直接在这里绑定点击事件
            tagElement.addEventListener('click', tagClickHandler);
            
            container.appendChild(tagElement);
        });
    }
    
    // 初始化常用标签到各个标签选择器
    renderCommonTags('commonTranslationTags', state.commonTags.translation);
    renderCommonTags('commonProfessionalTags', state.commonTags.professional);
    
    // 为批量标签模态框也添加渲染函数
    renderCommonTags('commonBulkTags', [...state.commonTags.translation, ...state.commonTags.professional]);
}

/**
 * 打开标签模态框
 */
function openTagModal(ids, type) {
    state.bulkTagIds = ids;
    state.bulkTagType = type;
    
    // 重置输入
    document.getElementById('bulkTags').value = '';
    document.getElementById('selectedTagsPreview').style.display = 'none';
    
    // 渲染常用标签
    const commonTags = document.getElementById('commonBulkTags');
    commonTags.innerHTML = '';
    
    const tagList = type === 'translation' ? state.commonTags.translation : state.commonTags.professional;
    
    tagList.forEach(tag => {
        const tagElement = document.createElement('div');
        tagElement.className = 'tag-item';
        tagElement.textContent = tag;
        
        // 使用通用的点击处理函数
        tagElement.addEventListener('click', function() {
            // 批量标签模态框需要特殊处理，因为它有不同的预览机制
            this.classList.toggle('selected');
            
            // 更新预览
            updateSelectedTagsPreview();
        });
        
        commonTags.appendChild(tagElement);
    });
    
    // 显示模态框
    document.getElementById('tagModal').classList.add('show');
}

// 批量标签预览更新函数
function updateSelectedTagsPreview() {
    const tagsInput = document.getElementById('bulkTags').value;
    const inputTags = tagsInput ? tagsInput.split(',').map(t => t.trim()).filter(t => t) : [];
    
    const selectedTagItems = document.querySelectorAll('#commonBulkTags .tag-item.selected');
    const selectedTags = Array.from(selectedTagItems).map(el => el.textContent);
    
    const allTags = [...new Set([...inputTags, ...selectedTags])];
    
    const preview = document.getElementById('selectedTagsPreview');
    if (allTags.length > 0) {
        preview.style.display = 'block';
        preview.innerHTML = '<h4>已选标签：</h4>' + 
            allTags.map(tag => `<span class="tag">${tag}</span>`).join('');
    } else {
        preview.style.display = 'none';
    }
}

/**
 * 应用标签
 */
async function applyTags() {
    const ids = state.bulkTagIds;
    const type = state.bulkTagType;
    
    if (!ids || ids.length === 0) {
        toast('未选择题目');
        return;
    }
    
    // 获取输入的标签
    const tagsInput = document.getElementById('bulkTags').value.trim();
    let tags = tagsInput ? tagsInput.split(/\s*,\s*/) : [];
    
    // 获取选中的常用标签
    const selectedTagElements = document.querySelectorAll('#commonBulkTags .tag-item.selected');
    const selectedTags = Array.from(selectedTagElements).map(el => el.textContent);
    
    // 合并标签并去重
    tags = [...new Set([...tags, ...selectedTags])];
    
    // URL编码所有标签
    tags = tags.map(tag => encodeURIComponent(tag));
    
    if (tags.length === 0) {
        toast('请输入或选择至少一个标签');
        return;
    }
    
    try {
        toast(`正在为 ${ids.length} 个题目添加标签，请稍候...`);
        
        const promises = ids.map(async id => {
            try {
            // 先获取题目的当前数据
            const response = await fetch(`/api/questions/${type}/${id}`);
                if (!response.ok) {
                    return false;
                }
                
            const question = await response.json();
                
                // 确保我们有正确的数据格式
                let questionData = question.question_data;
            
            // 合并现有标签和新标签
                let currentTags = question.tags || [];
                
                console.log('批量添加标签 - 原始标签数据:', question.tags);
                
                // 处理可能的Unicode编码字符串标签
                currentTags = currentTags.map(tag => {
                    // 先进行URL解码
                    let decodedTag = tag;
                    try {
                        decodedTag = decodeURIComponent(tag);
                    } catch (e) {
                        console.log('批量URL解码失败，使用原始标签:', e.message);
                    }
                    
                    // 检查是否为JSON字符串格式（数组或带引号的字符串）
                    if (typeof decodedTag === 'string') {
                        console.log('批量处理标签:', decodedTag);
                        
                        // 处理形如 ["\\u6c49\\u8bd1\\u82f1"] 的情况
                        if (decodedTag.startsWith('[') && decodedTag.endsWith(']')) {
                            try {
                                // 尝试解析JSON数组
                                const parsed = JSON.parse(decodedTag);
                                console.log('批量解析JSON数组结果:', parsed);
                                // 如果解析后是数组，取第一个元素
                                if (Array.isArray(parsed) && parsed.length > 0) {
                                    return parsed[0];
                                }
                                return typeof parsed === 'string' ? parsed : decodedTag;
                            } catch (e) {
                                console.log('批量JSON解析失败，尝试直接移除括号:', e.message);
                                // 如果不是有效的JSON，尝试直接解码字符串内容
                                const content = decodedTag.substring(1, decodedTag.length - 1);
                                
                                // 移除多余的转义符号 (例如 "\\u6c49" 变成 "\u6c49")
                                const unescaped = content.replace(/\\\\u/g, '\\u');
                                
                                // 处理 \uXXXX 格式的Unicode编码
                                if (unescaped.includes('\\u')) {
                                    try {
                                        return JSON.parse('"' + unescaped + '"');
                                    } catch (e2) {
                                        console.log('批量Unicode解码失败:', e2.message);
                                        return unescaped;
                                    }
                                }
                                
                                return content;
                            }
                        } 
                        // 处理形如 "\u6c49\u8bd1\u82f1" 的情况
                        else if (decodedTag.startsWith('"') && decodedTag.endsWith('"')) {
                            try {
                                // 尝试解析JSON字符串
                                const parsed = JSON.parse(decodedTag);
                                console.log('批量解析JSON字符串结果:', parsed);
                                return parsed;
                            } catch (e) {
                                console.log('批量JSON字符串解析失败:', e.message);
                                return decodedTag;
                            }
                        }
                        // 处理直接的Unicode转义序列
                        else if (decodedTag.includes('\\u')) {
                            try {
                                // 需要将字符串放入引号中以让JSON.parse正确解析Unicode
                                const decoded = JSON.parse('"' + decodedTag + '"');
                                console.log('批量Unicode解码结果:', decoded);
                                return decoded;
                            } catch (e) {
                                console.log('批量Unicode直接解码失败:', e.message);
                                return decodedTag;
                            }
                        }
                    }
                    return decodedTag;
                });
                
                console.log('批量处理后的标签:', currentTags);
                console.log('要添加的新标签:', tags);
                
            const newTags = [...new Set([...currentTags, ...tags])];
            
            // 更新题目
                const updateResponse = await fetch(`/api/questions/${type}/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                        question_data: typeof questionData === 'string' ? questionData : JSON.stringify(questionData),
                        tags: newTags,
                        difficulty: question.difficulty || 'medium'
                })
            });
                
                return updateResponse.ok;
            } catch (error) {
                console.error(`Error processing question ${id}:`, error);
                return false;
            }
        });
        
        const results = await Promise.all(promises);
        const successCount = results.filter(Boolean).length;
        
        toast(`成功为 ${successCount} 个题目添加标签${successCount < ids.length ? `，${ids.length - successCount} 个操作失败` : ''}`);
        
        // 重新加载题目
        if (type === 'translation') {
            loadTranslations();
        } else {
            loadProfessionals();
        }
        
        // 关闭模态框
        closeAllModals();
    } catch (error) {
        console.error('Error applying tags:', error);
        toast('添加标签失败: ' + error.message);
    }
}

/**
 * 初始化难度模态框
 */
function initDifficultyModal() {
    // 难度选择
}

/**
 * 打开难度模态框
 */
function openDifficultyModal(ids, type) {
    state.bulkDifficultyIds = ids;
    state.bulkDifficultyType = type;
    
    // 重置选择
    document.getElementById('bulkDifficulty').value = 'easy';
    
    // 显示模态框
    document.getElementById('difficultyModal').classList.add('show');
}

/**
 * 应用难度
 */
async function applyDifficulty() {
    const ids = state.bulkDifficultyIds;
    const type = state.bulkDifficultyType;
    
    if (!ids || ids.length === 0) {
        toast('未选择题目');
        return;
    }
    
    // 获取选择的难度
    const difficulty = document.getElementById('bulkDifficulty').value;
    
    try {
        toast(`正在为 ${ids.length} 个题目设置难度，请稍候...`);
        
        const promises = ids.map(async id => {
            try {
            // 先获取题目的当前数据
            const response = await fetch(`/api/questions/${type}/${id}`);
                if (!response.ok) {
                    return false;
                }
                
            const question = await response.json();
                
                // 确保我们有正确的数据格式
                let questionData = question.question_data;
            
            // 更新题目
                const updateResponse = await fetch(`/api/questions/${type}/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                        question_data: typeof questionData === 'string' ? questionData : JSON.stringify(questionData),
                        tags: question.tags || [],
                        difficulty: difficulty
                })
            });
                
                return updateResponse.ok;
            } catch (error) {
                console.error(`Error processing question ${id}:`, error);
                return false;
            }
        });
        
        const results = await Promise.all(promises);
        const successCount = results.filter(Boolean).length;
        
        toast(`成功为 ${successCount} 个题目设置难度${successCount < ids.length ? `，${ids.length - successCount} 个操作失败` : ''}`);
        
        // 重新加载题目
        if (type === 'translation') {
            loadTranslations();
        } else {
            loadProfessionals();
        }
        
        // 关闭模态框
        closeAllModals();
    } catch (error) {
        console.error('Error applying difficulty:', error);
        toast('设置难度失败: ' + error.message);
    }
}

// 标签点击事件 - 修改函数定义
function initTagSelector(selector) {
    // 如果没有传递选择器，则初始化所有标签
    const tagItems = selector 
        ? document.querySelectorAll(`${selector} .tag-item`) 
        : document.querySelectorAll('.tag-item');
    
    console.log(`初始化标签选择器 ${selector || '全部'}, 找到 ${tagItems.length} 个标签`);
    
    tagItems.forEach(function(item) {
        // 先移除可能存在的旧事件监听
        item.removeEventListener('click', tagClickHandler);
        
        // 添加新的事件监听
        item.addEventListener('click', tagClickHandler);
    });
}

// 标签点击处理函数 - 抽出来作为单独的函数以便移除事件
function tagClickHandler() {
    console.log('标签被点击:', this.textContent);
    this.classList.toggle('selected');
    
    // 找到关联的标签输入框
    const tagInput = this.closest('.form-group').querySelector('input[type="text"]');
    if (tagInput) {
        updateTagInput(tagInput);
    }
}

// 更新标签输入框
function updateTagInput(input) {
    // 获取所有选中的标签
    const selectedTags = Array.from(
        input.closest('.form-group').querySelectorAll('.tag-item.selected')
    ).map(tag => tag.textContent);
    
    console.log('选中的标签:', selectedTags);
    
    // 获取输入框中已有的标签
    const existingTags = input.value.split(',')
        .map(tag => tag.trim())
        .filter(tag => tag && !selectedTags.includes(tag));
    
    // 合并并更新输入框
    const allTags = [...existingTags, ...selectedTags].filter(tag => tag);
    input.value = allTags.join(', ');
    console.log('更新输入框的标签:', input.value);
}

/**
 * 获取所有可用标签并填充到筛选下拉菜单
 */
async function loadAllTags() {
    try {
        // 获取翻译题目的所有标签
        const translationResponse = await fetch('/api/questions/tags/translation');
        
        if (translationResponse.ok) {
            const translationData = await translationResponse.json();
            if (translationData.success && translationData.tags) {
                translationTagsList = translationData.tags;
                console.log('加载翻译题库标签成功:', translationTagsList);
                
                // 更新常用标签列表
                if (!Array.isArray(state.commonTags.translation)) {
                    state.commonTags.translation = [];
                }
                
                // 将新标签添加到常用标签中
                translationTagsList.forEach(tag => {
                    if (!state.commonTags.translation.includes(tag)) {
                        state.commonTags.translation.push(tag);
                    }
                });
                
                // 填充标签筛选下拉框
                const translationTagFilter = document.getElementById('translation-tag-filter');
                if (translationTagFilter) {
                    // 清空下拉框
                    translationTagFilter.innerHTML = '<option value="">所有标签</option>';
                    
                    // 添加标签选项
                    translationTagsList.forEach(tag => {
                        const option = document.createElement('option');
                        option.value = tag;
                        option.textContent = tag;
                        translationTagFilter.appendChild(option);
                    });
                }
            }
        } else {
            console.error('获取翻译标签失败:', await translationResponse.text());
        }
        
        // 获取专业题目的所有标签
        const professionalResponse = await fetch('/api/questions/tags/professional');
        
        if (professionalResponse.ok) {
            const professionalData = await professionalResponse.json();
            if (professionalData.success && professionalData.tags) {
                professionalTagsList = professionalData.tags;
                console.log('加载专业题库标签成功:', professionalTagsList);
                
                // 更新常用标签列表
                if (!Array.isArray(state.commonTags.professional)) {
                    state.commonTags.professional = [];
                }
                
                // 将新标签添加到常用标签中
                professionalTagsList.forEach(tag => {
                    if (!state.commonTags.professional.includes(tag)) {
                        state.commonTags.professional.push(tag);
                    }
                });
                
                // 填充标签筛选下拉框
                const professionalTagFilter = document.getElementById('professional-tag-filter');
                if (professionalTagFilter) {
                    // 清空下拉框
                    professionalTagFilter.innerHTML = '<option value="">所有标签</option>';
                    
                    // 添加标签选项
                    professionalTagsList.forEach(tag => {
                        const option = document.createElement('option');
                        option.value = tag;
                        option.textContent = tag;
                        professionalTagFilter.appendChild(option);
                    });
                }
            }
        } else {
            console.error('获取专业标签失败:', await professionalResponse.text());
        }
        
        // 初始化标签模态框
        initTagModal();
        
    } catch (error) {
        console.error('加载标签失败:', error);
    }
}

// 初始化标签过滤器事件处理
function initTagFilters() {
    // 翻译题标签过滤器
    document.getElementById('tagFilterTranslation').addEventListener('change', function() {
        state.translationTagFilter = this.value;
        state.translationPage = 1;
        loadTranslations();
    });
    
    // 专业题标签过滤器
    document.getElementById('tagFilterProfessional').addEventListener('change', function() {
        state.professionalTagFilter = this.value;
        state.professionalPage = 1;
        loadProfessionals();
    });
}

// 更新commonTags对象，将从服务器获取的标签添加到已有标签中
function updateCommonTags() {
    if (!commonTags) commonTags = {};
    
    if (!commonTags.translation) commonTags.translation = [];
    if (!commonTags.professional) commonTags.professional = [];
    if (!commonTags.bulk) commonTags.bulk = [];
    
    // 添加从服务器获取的翻译标签
    translationTagsList.forEach(tag => {
        if (!commonTags.translation.includes(tag)) {
            commonTags.translation.push(tag);
        }
        if (!commonTags.bulk.includes(tag)) {
            commonTags.bulk.push(tag);
        }
    });
    
    // 添加从服务器获取的专业标签
    professionalTagsList.forEach(tag => {
        if (!commonTags.professional.includes(tag)) {
            commonTags.professional.push(tag);
        }
        if (!commonTags.bulk.includes(tag)) {
            commonTags.bulk.push(tag);
        }
    });
    
    console.log('更新后的commonTags:', commonTags);
}

/**
 * 解析文件内容
 * @param {File} file - 上传的文件
 * @returns {Promise<Array>} - 解析的题目数组
 */
async function parseFileInput(file) {
    try {
        // 记录开始解析时间
        console.time('文件解析耗时');
        
        // 检测文件类型
        const fileExt = file.name.split('.').pop().toLowerCase();
        console.log(`解析文件: ${file.name}, 类型: ${fileExt}, 大小: ${(file.size / 1024).toFixed(2)}KB`);
        
        // 读取文件内容
        const fileContent = await readFileAsText(file);
        console.log(`文件内容长度: ${fileContent.length} 字符`);
        
        // 根据文件扩展名选择解析方法
        let result = [];
        switch (fileExt) {
            case 'json':
                result = parseJsonFile(fileContent);
                break;
            case 'csv':
                result = parseCsvFile(fileContent);
                break;
            case 'txt':
                result = parseTextInput(fileContent);
                break;
            default:
                toast('不支持的文件格式，请使用JSON、CSV或TXT格式');
                break;
        }
        
        // 记录解析完成时间
        console.timeEnd('文件解析耗时');
        console.log(`成功解析 ${result.length} 个题目`);
        
        return result;
    } catch (error) {
        console.error('解析文件失败:', error);
        toast('解析文件失败: ' + error.message);
        return [];
    }
}

/**
 * 解析JSON格式的题目文件
 * @param {string} content - 文件内容
 * @returns {Array} - 题目数组
 */
function parseJsonFile(content) {
    try {
        const data = JSON.parse(content);
        
        // 如果是数组，则直接使用
        if (Array.isArray(data)) {
            return data.map(item => formatQuestion(item));
        } 
        // 如果是对象，尝试查找questions字段
        else if (data.questions && Array.isArray(data.questions)) {
            return data.questions.map(item => formatQuestion(item));
        }
        // 否则尝试将对象本身当作单个题目处理
        else {
            const result = formatQuestion(data);
            return result ? [result] : [];
        }
    } catch (error) {
        console.error('解析JSON失败:', error);
        toast('JSON解析失败，请检查文件格式');
        return [];
    }
}

/**
 * 解析CSV格式的题目文件
 * @param {string} content - 文件内容
 * @returns {Array} - 题目数组
 */
function parseCsvFile(content) {
    try {
        console.log('CSV文件内容前100个字符:', content.substring(0, 100));
        
        // 检查编码问题
        if (content.includes('')) {
            console.warn('CSV文件内容包含无法识别的字符，可能存在编码问题');
        }
        
        // 分割行
        const lines = content.split(/\r?\n/).filter(line => line.trim());
        if (lines.length === 0) {
            console.warn('CSV文件没有有效内容行');
            return [];
        }
        
        console.log(`CSV文件共有 ${lines.length} 行内容`);
        console.log('CSV文件首行:', lines[0]);
        
        // 获取标题行
        const headers = parseCSVLine(lines[0]);
        console.log('解析的CSV标题:', headers);
        
        // 如果标题行解析为空或包含乱码，尝试使用常规标题
        if (headers.length === 0 || headers.some(h => h.includes(''))) {
            console.warn('CSV标题行解析有问题，使用默认标题');
            // 使用默认标题
            headers.length = 0;
            headers.push('题目', '选项', '答案', '难度', '标签');
        }
        
        // 解析数据行
        const questions = [];
        for (let i = 1; i < lines.length; i++) {
            // 跳过空行
            if (!lines[i].trim()) continue;
            
            const values = parseCSVLine(lines[i]);
            if (values.length === 0 || values.every(v => !v)) {
                console.log(`跳过第 ${i+1} 行，无有效内容`);
                continue;
            }
            
            const question = {
                items: []
            };
            
            console.log(`处理第 ${i+1} 行:`, values);
            
            // 映射CSV列到题目字段
            headers.forEach((header, index) => {
                // 处理值可能不足的情况
                if (index >= values.length) return;
                
                let value = values[index] || '';
                
                // 检测并处理乱码
                if (value.includes('')) {
                    console.warn(`第 ${i+1} 行第 ${index+1} 列 (${header}) 包含乱码:`, value);
                }
                
                // 根据标题确定字段类型
                if (header.includes('题目')) {
                    question.items.push({
                        type: '题目',
                        content: value
                    });
                } else if (header.includes('选项')) {
                    question.items.push({
                        type: '选项',
                        content: value
                    });
                } else if (header.includes('答案')) {
                    question.items.push({
                        type: '答案',
                        content: value
                    });
                } else if (header.includes('难度')) {
                    if (value.includes('简单')) {
                        question.difficulty = 'easy';
                    } else if (value.includes('中等')) {
                        question.difficulty = 'medium';
                    } else if (value.includes('困难')) {
                        question.difficulty = 'hard';
                    } else {
                        question.difficulty = value || 'medium';
                    }
                } else if (header.includes('标签')) {
                    question.tags = value.split(/\s*[,;]\s*/).filter(tag => tag);
                } else if (value) {
                    // 其他字段当作自定义题目项
                    question.items.push({
                        type: header,
                        content: value
                    });
                }
            });
            
            // 如果有题目项，则添加到结果中
            if (question.items.length > 0) {
                questions.push(question);
                console.log(`成功解析第 ${i+1} 行为题目:`, question.items[0]);
            } else {
                console.warn(`第 ${i+1} 行未提取到题目项:`, values);
            }
        }
        
        console.log(`CSV解析完成，共解析出 ${questions.length} 个题目`);
        return questions;
    } catch (error) {
        console.error('解析CSV失败:', error);
        toast('CSV解析失败，请检查文件格式');
        return [];
    }
}

/**
 * 解析CSV行，处理引号和逗号
 * @param {string} line - CSV行内容
 * @returns {Array} - 字段数组
 */
function parseCSVLine(line) {
    // 对于空行直接返回空数组
    if (!line || !line.trim()) {
        return [];
    }
    
    // 如果是较简单的行（没有引号），使用简单的分割方法
    if (!line.includes('"')) {
        // 直接分割并去除空格，不再进行URL解码
        return line.split(',').map(field => field.trim());
    }
    
    const result = [];
    let current = '';
    let inQuotes = false;
    
    // 打印被处理的行（调试用）
    // console.log('正在解析CSV行:', line); // 减少控制台输出
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
            // 处理转义的引号 (""表示")
            if (inQuotes && line[i + 1] === '"') {
                current += '"';
                i++;
            } else {
                // 切换引号状态
                inQuotes = !inQuotes;
            }
        } else if (char === ',' && !inQuotes) {
            // 遇到分隔符且不在引号内时，添加当前字段到结果中
            // 只进行trim，不再进行URL解码
            result.push(current.trim());
            current = '';
        } else {
            // 普通字符
            current += char;
        }
    }
    
    // 添加最后一个字段
    // 只进行trim，不再进行URL解码
    result.push(current.trim());
    
    // 打印解析结果（调试用）
    // console.log('CSV行解析结果:', result); // 减少控制台输出
    
    return result;
}

/**
 * 格式化问题对象，确保结构一致
 * @param {Object} question - 原始题目对象
 * @returns {Object} - 格式化后的题目对象
 */
function formatQuestion(question) {
    const result = {
        items: []
    };
    
    // 处理标签
    if (question.tags) {
        result.tags = Array.isArray(question.tags) ? 
            question.tags : 
            String(question.tags).split(/\s*[,;]\s*/).filter(t => t);
    }
    
    // 处理难度
    if (question.difficulty) {
        result.difficulty = question.difficulty;
    }
    
    // 处理题目内容
    if (question.question_data) {
        try {
            // 处理数据结构
            let questionData;
            if (typeof question.question_data === 'string') {
                questionData = JSON.parse(question.question_data);
            } else {
                questionData = question.question_data;
            }
            
            // 将问题数据转换为items数组
            if (Array.isArray(questionData)) {
                questionData.forEach(item => {
                    if (Array.isArray(item) && item.length >= 2) {
                        const type = item[0] === 'txt' ? '题目' : 
                                    item[0] === 'img' ? '图片' : item[0];
                        result.items.push({
                            type: type,
                            content: item[1] || ''
                        });
                    }
                });
            }
        } catch (e) {
            console.error('解析question_data失败:', e);
        }
    }
    
    // 如果没有从question_data提取到items，尝试从其他字段提取
    if (result.items.length === 0) {
        // 处理可能的题目字段
        if (question.content || question.question || question.text) {
            result.items.push({
                type: '题目',
                content: question.content || question.question || question.text
            });
        }
        
        // 处理可能的选项字段
        if (question.options || question.choices) {
            const options = question.options || question.choices;
            if (typeof options === 'string') {
                result.items.push({
                    type: '选项',
                    content: options
                });
            } else if (Array.isArray(options)) {
                result.items.push({
                    type: '选项',
                    content: options.join('\n')
                });
            }
        }
        
        // 处理可能的答案字段
        if (question.answer || question.correct_answer) {
            result.items.push({
                type: '答案',
                content: question.answer || question.correct_answer
            });
        }
        
        // 处理其他可能的字段
        Object.entries(question).forEach(([key, value]) => {
            // 跳过已处理的字段
            if (['tags', 'difficulty', 'content', 'question', 'text', 'options', 'choices', 'answer', 'correct_answer', 'question_data'].includes(key)) {
                return;
            }
            
            // 添加其他字段
            if (value && typeof value === 'string') {
                result.items.push({
                    type: key,
                    content: value
                });
            } else if (value && typeof value !== 'object') {
                result.items.push({
                    type: key,
                    content: String(value)
                });
            }
        });
    }
    
    return result.items.length > 0 ? result : null;
}

/**
 * 初始化每页显示数量选择器
 */
function initPageSizeSelectors() {
    const pageSizeTranslationSelect = document.getElementById('pageSizeTranslation');
    const pageSizeProfessionalSelect = document.getElementById('pageSizeProfessional');

    if (pageSizeTranslationSelect) {
        // 使用 state 中的值设置初始选项
        pageSizeTranslationSelect.value = state.translationPageSize;

        pageSizeTranslationSelect.addEventListener('change', (event) => {
            const newSize = parseInt(event.target.value, 10);
            // 只有当值改变时才执行操作
            if (state.translationPageSize !== newSize) {
                state.translationPageSize = newSize;
                state.translationPage = 1; // 重置到第一页
                loadTranslations(); // 重新加载数据
                // 可选: 保存选择到 localStorage
                // localStorage.setItem('translationPageSize', newSize);
            }
        });
    } else {
        console.warn('未找到翻译题的页面大小选择器 (ID: pageSizeTranslation)');
    }

    if (pageSizeProfessionalSelect) {
        // 使用 state 中的值设置初始选项
        pageSizeProfessionalSelect.value = state.professionalPageSize;

        pageSizeProfessionalSelect.addEventListener('change', (event) => {
            const newSize = parseInt(event.target.value, 10);
            // 只有当值改变时才执行操作
            if (state.professionalPageSize !== newSize) {
                state.professionalPageSize = newSize;
                state.professionalPage = 1; // 重置到第一页
                loadProfessionals(); // 重新加载数据
                // 可选: 保存选择到 localStorage
                // localStorage.setItem('professionalPageSize', newSize);
            }
        });
    } else {
         console.warn('未找到专业题的页面大小选择器 (ID: pageSizeProfessional)');
    }

    // 可选: 从 localStorage 读取保存的值
    // const savedTranslationPageSize = localStorage.getItem('translationPageSize');
    // if (savedTranslationPageSize) {
    //     state.translationPageSize = parseInt(savedTranslationPageSize, 10);
    //     if (pageSizeTranslationSelect) pageSizeTranslationSelect.value = state.translationPageSize;
    // }
    // const savedProfessionalPageSize = localStorage.getItem('professionalPageSize');
    // if (savedProfessionalPageSize) {
    //     state.professionalPageSize = parseInt(savedProfessionalPageSize, 10);
    //     if (pageSizeProfessionalSelect) pageSizeProfessionalSelect.value = state.professionalPageSize;
    // }
}