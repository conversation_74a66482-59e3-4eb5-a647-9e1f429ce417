<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版题库编辑器 - 研究生复试系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .nav-buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        .btn-light {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        .btn-light:hover {
            background: rgba(255,255,255,0.3);
        }
        .content {
            display: flex;
            height: 600px;
        }
        .sidebar {
            width: 300px;
            border-right: 1px solid #dee2e6;
            padding: 20px;
            overflow-y: auto;
        }
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        .question-type-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #666;
        }
        .tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 14px;
        }
        .question-list {
            margin-bottom: 20px;
        }
        .question-item {
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .question-item:hover {
            background: #f8f9fa;
            border-color: #007bff;
        }
        .question-item.selected {
            background: #e3f2fd;
            border-color: #007bff;
        }
        .question-index {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .question-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .question-content {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .question-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
        }
        .editor-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .status-bar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #666;
        }
        .stats {
            display: flex;
            gap: 20px;
        }
        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .stat-value {
            font-weight: bold;
            color: #007bff;
        }
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 15px;
            min-width: 300px;
            z-index: 1000;
            border-left: 4px solid #007bff;
        }
        .toast.success {
            border-left-color: #28a745;
        }
        .toast.error {
            border-left-color: #dc3545;
        }
        .toast.warning {
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>📚 简化版题库编辑器</h1>
            <div class="nav-buttons">
                <a href="docs/editor.md" target="_blank" class="btn btn-light">📖 帮助文档</a>
                <a href="/" class="btn btn-light">🏠 返回主系统</a>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="content">
            <!-- 左侧边栏 -->
            <div class="sidebar">
                <!-- 题目类型标签 -->
                <div class="question-type-tabs">
                    <div class="tab active" data-type="translation">🌍 翻译题</div>
                    <div class="tab" data-type="professional">🎓 专业题</div>
                </div>

                <!-- 搜索框 -->
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索题目..." id="searchInput">
                </div>

                <!-- 题目列表 -->
                <div class="question-list" id="questionList">
                    <div class="empty-state">
                        <div class="empty-icon">📝</div>
                        <div>正在加载题目...</div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button class="btn btn-primary" onclick="addNewQuestion()">➕ 添加题目</button>
                    <button class="btn btn-secondary" onclick="refreshQuestions()">🔄 刷新</button>
                </div>
            </div>

            <!-- 主要编辑区域 -->
            <div class="main-content">
                <div id="editorArea">
                    <div class="empty-state">
                        <div class="empty-icon">✏️</div>
                        <h3>选择题目进行编辑</h3>
                        <p>从左侧列表中选择一个题目进行编辑，或点击"添加题目"创建新题目</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="stats">
                <div class="stat-item">
                    <span>总题目:</span>
                    <span class="stat-value" id="totalCount">0</span>
                </div>
                <div class="stat-item">
                    <span>翻译题:</span>
                    <span class="stat-value" id="translationCount">0</span>
                </div>
                <div class="stat-item">
                    <span>专业题:</span>
                    <span class="stat-value" id="professionalCount">0</span>
                </div>
            </div>
            <div>简化版题库编辑器 v1.0</div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentQuestionType = 'translation';
        let questions = [];
        let selectedQuestion = null;
        let isEditing = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('简化版题库编辑器已加载');
            initializeEditor();
        });

        // 初始化编辑器
        function initializeEditor() {
            // 绑定事件
            bindEvents();

            // 加载题目
            loadQuestions();

            showToast('编辑器已就绪', 'success');
        }

        // 绑定事件
        function bindEvents() {
            // 标签页切换
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    switchQuestionType(this.getAttribute('data-type'));
                });
            });

            // 搜索
            document.getElementById('searchInput').addEventListener('input', function() {
                filterQuestions(this.value);
            });
        }

        // 切换题目类型
        function switchQuestionType(type) {
            currentQuestionType = type;

            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');

            // 重新加载题目
            loadQuestions();

            // 清空编辑器
            clearEditor();
        }

        // 加载题目
        async function loadQuestions() {
            try {
                showToast('正在加载题目...', 'info');

                let url = '/api/questions';
                if (currentQuestionType === 'translation') {
                    url += '?type=translation';
                } else {
                    url += '?type=professional&subject=computer';
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.success) {
                    questions = data.questions || [];
                    renderQuestionList();
                    updateStats();
                    showToast(`加载了 ${questions.length} 道题目`, 'success');
                } else {
                    throw new Error(data.message || '加载题目失败');
                }
            } catch (error) {
                console.error('加载题目失败:', error);
                questions = getMockQuestions(); // 使用模拟数据
                renderQuestionList();
                updateStats();
                showToast('使用模拟数据', 'warning');
            }
        }

        // 获取模拟数据
        function getMockQuestions() {
            if (currentQuestionType === 'translation') {
                return [
                    {
                        id: 'translation_1',
                        type: 'translation',
                        index: 1,
                        title: '翻译题1',
                        content: 'The rapid development of artificial intelligence has transformed many industries.',
                        answer: '人工智能的快速发展已经改变了许多行业。',
                        difficulty: 'medium',
                        isActive: true,
                        usageCount: 0
                    },
                    {
                        id: 'translation_2',
                        type: 'translation',
                        index: 2,
                        title: '翻译题2',
                        content: 'Climate change poses significant challenges to global sustainability.',
                        answer: '气候变化对全球可持续发展构成了重大挑战。',
                        difficulty: 'medium',
                        isActive: true,
                        usageCount: 2
                    }
                ];
            } else {
                return [
                    {
                        id: 'professional_1',
                        type: 'professional',
                        subject: 'computer',
                        index: 1,
                        title: '面向对象编程',
                        content: '请解释面向对象编程的三大特性，并举例说明。',
                        difficulty: 'medium',
                        isActive: true,
                        usageCount: 1
                    }
                ];
            }
        }

        // 渲染题目列表
        function renderQuestionList() {
            const listContainer = document.getElementById('questionList');

            if (questions.length === 0) {
                listContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📝</div>
                        <div>暂无题目</div>
                        <p>点击"添加题目"开始创建</p>
                    </div>
                `;
                return;
            }

            const questionsHTML = questions.map(question => `
                <div class="question-item" onclick="selectQuestion('${question.id}')">
                    <div class="question-index">#${question.index}</div>
                    <div class="question-title">${question.title || '无标题'}</div>
                    <div class="question-content">${truncateText(question.content, 80)}</div>
                    <div class="question-meta">
                        <span>难度: ${getDifficultyText(question.difficulty)}</span>
                        <span>使用: ${question.usageCount || 0} 次</span>
                    </div>
                </div>
            `).join('');

            listContainer.innerHTML = questionsHTML;
        }

        // 选择题目
        function selectQuestion(questionId) {
            selectedQuestion = questions.find(q => q.id === questionId);

            if (selectedQuestion) {
                // 更新选中状态
                document.querySelectorAll('.question-item').forEach(item => {
                    item.classList.remove('selected');
                });
                event.target.closest('.question-item').classList.add('selected');

                // 显示编辑器
                showEditor(selectedQuestion);
            }
        }

        // 显示编辑器
        function showEditor(question) {
            const editorArea = document.getElementById('editorArea');
            const isTranslation = question.type === 'translation';

            editorArea.innerHTML = `
                <div class="editor-form">
                    <h3>${question.id ? '编辑题目' : '添加题目'} #${question.index}</h3>

                    <div class="form-group">
                        <label class="form-label">题目编号</label>
                        <input type="number" class="form-input" id="questionIndex" value="${question.index || ''}" min="1">
                    </div>

                    <div class="form-group">
                        <label class="form-label">题目标题</label>
                        <input type="text" class="form-input" id="questionTitle" value="${question.title || ''}" placeholder="请输入题目标题">
                    </div>

                    <div class="form-group">
                        <label class="form-label">题目内容</label>
                        <textarea class="form-textarea" id="questionContent" placeholder="${isTranslation ? '请输入要翻译的英文句子' : '请输入题目内容'}">${question.content || ''}</textarea>
                    </div>

                    ${isTranslation ? `
                    <div class="form-group">
                        <label class="form-label">参考答案</label>
                        <textarea class="form-textarea" id="questionAnswer" placeholder="请输入参考翻译">${question.answer || ''}</textarea>
                    </div>
                    ` : ''}

                    <div class="form-group">
                        <label class="form-label">难度级别</label>
                        <select class="form-select" id="questionDifficulty">
                            <option value="easy" ${question.difficulty === 'easy' ? 'selected' : ''}>简单</option>
                            <option value="medium" ${question.difficulty === 'medium' ? 'selected' : ''}>中等</option>
                            <option value="hard" ${question.difficulty === 'hard' ? 'selected' : ''}>困难</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <button class="btn btn-success" onclick="saveQuestion()">💾 保存</button>
                        <button class="btn btn-secondary" onclick="clearEditor()">❌ 取消</button>
                        ${question.id ? '<button class="btn btn-danger" onclick="deleteQuestion()">🗑️ 删除</button>' : ''}
                    </div>
                </div>
            `;

            isEditing = true;
        }

        // 添加新题目
        function addNewQuestion() {
            const newQuestion = {
                id: null,
                type: currentQuestionType,
                subject: currentQuestionType === 'professional' ? 'computer' : null,
                index: getNextQuestionIndex(),
                title: '',
                content: '',
                answer: '',
                difficulty: 'medium',
                isActive: true,
                usageCount: 0
            };

            selectedQuestion = newQuestion;
            showEditor(newQuestion);
        }

        // 获取下一个题目编号
        function getNextQuestionIndex() {
            if (questions.length === 0) return 1;
            const maxIndex = Math.max(...questions.map(q => q.index || 0));
            return maxIndex + 1;
        }

        // 保存题目
        async function saveQuestion() {
            if (!selectedQuestion) return;

            // 获取表单数据
            const formData = {
                id: selectedQuestion.id,
                type: currentQuestionType,
                subject: currentQuestionType === 'professional' ? 'computer' : null,
                index: parseInt(document.getElementById('questionIndex').value) || 1,
                title: document.getElementById('questionTitle').value.trim(),
                content: document.getElementById('questionContent').value.trim(),
                answer: document.getElementById('questionAnswer')?.value.trim() || '',
                difficulty: document.getElementById('questionDifficulty').value,
                isActive: true,
                usageCount: selectedQuestion.usageCount || 0
            };

            // 验证数据
            if (!formData.title) {
                showToast('请输入题目标题', 'error');
                return;
            }

            if (!formData.content) {
                showToast('请输入题目内容', 'error');
                return;
            }

            try {
                showToast('正在保存...', 'info');

                // 模拟保存（实际应该调用API）
                if (selectedQuestion.id) {
                    // 更新现有题目
                    const index = questions.findIndex(q => q.id === selectedQuestion.id);
                    if (index !== -1) {
                        questions[index] = { ...questions[index], ...formData };
                    }
                } else {
                    // 添加新题目
                    formData.id = `${currentQuestionType}_${Date.now()}`;
                    questions.push(formData);
                }

                // 重新渲染
                renderQuestionList();
                updateStats();
                clearEditor();

                showToast('保存成功', 'success');
            } catch (error) {
                console.error('保存失败:', error);
                showToast('保存失败: ' + error.message, 'error');
            }
        }

        // 删除题目
        function deleteQuestion() {
            if (!selectedQuestion || !selectedQuestion.id) return;

            if (confirm(`确定要删除题目"${selectedQuestion.title}"吗？`)) {
                questions = questions.filter(q => q.id !== selectedQuestion.id);
                renderQuestionList();
                updateStats();
                clearEditor();
                showToast('删除成功', 'success');
            }
        }

        // 清空编辑器
        function clearEditor() {
            const editorArea = document.getElementById('editorArea');
            editorArea.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">✏️</div>
                    <h3>选择题目进行编辑</h3>
                    <p>从左侧列表中选择一个题目进行编辑，或点击"添加题目"创建新题目</p>
                </div>
            `;

            selectedQuestion = null;
            isEditing = false;

            // 清除选中状态
            document.querySelectorAll('.question-item').forEach(item => {
                item.classList.remove('selected');
            });
        }

        // 刷新题目
        function refreshQuestions() {
            loadQuestions();
        }

        // 过滤题目
        function filterQuestions(searchText) {
            const filteredQuestions = questions.filter(q =>
                q.title.toLowerCase().includes(searchText.toLowerCase()) ||
                q.content.toLowerCase().includes(searchText.toLowerCase()) ||
                q.index.toString().includes(searchText)
            );

            // 临时保存原始数据
            const originalQuestions = questions;
            questions = filteredQuestions;
            renderQuestionList();
            questions = originalQuestions;
        }

        // 更新统计信息
        function updateStats() {
            const totalCount = questions.length;
            const translationCount = questions.filter(q => q.type === 'translation').length;
            const professionalCount = questions.filter(q => q.type === 'professional').length;

            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('translationCount').textContent = translationCount;
            document.getElementById('professionalCount').textContent = professionalCount;
        }

        // 工具函数
        function truncateText(text, maxLength) {
            if (!text) return '';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function getDifficultyText(difficulty) {
            const map = { 'easy': '简单', 'medium': '中等', 'hard': '困难' };
            return map[difficulty] || '未知';
        }

        function showToast(message, type = 'info') {
            // 移除现有的toast
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 3秒后自动移除
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
