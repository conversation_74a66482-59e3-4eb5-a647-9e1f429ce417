/**
 * StudentService - 学生数据服务
 * 负责学生相关的业务逻辑和数据操作
 */

class StudentService {
    constructor() {
        this.dbService = window.databaseService;
        this.currentStudent = null;
        this.eventListeners = new Map();
    }

    // ==================== 学生管理 ====================

    /**
     * 创建新学生
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Student>} 学生对象
     */
    async createStudent(studentNumber) {
        try {
            // 验证学生编号
            if (!studentNumber || studentNumber.trim() === '') {
                throw new Error('学生编号不能为空');
            }

            // 检查学生是否已存在
            const existingResult = await this.dbService.getStudent(studentNumber);
            if (existingResult.success && existingResult.data) {
                throw new Error('学生编号已存在');
            }

            // 创建学生对象
            const student = Student.create(studentNumber);
            
            // 验证学生数据
            const validation = student.validate();
            if (!validation.isValid) {
                throw new Error(validation.errors.join(', '));
            }

            // 保存到数据库
            const result = await this.dbService.createStudent(student.toDatabase());
            if (!result.success) {
                throw new Error(result.error.message);
            }

            // 更新学生ID
            student.id = result.data.id;
            
            // 触发事件
            this.emit('studentCreated', student);
            
            return student;
        } catch (error) {
            console.error('创建学生失败:', error);
            throw error;
        }
    }

    /**
     * 获取学生信息
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Student|null>} 学生对象
     */
    async getStudent(studentNumber) {
        try {
            const result = await this.dbService.getStudent(studentNumber);
            if (!result.success) {
                if (result.error.code === 'NOT_FOUND') {
                    return null;
                }
                throw new Error(result.error.message);
            }

            return Student.fromDatabase(result.data);
        } catch (error) {
            console.error('获取学生信息失败:', error);
            throw error;
        }
    }

    /**
     * 更新学生信息
     * @param {Student} student - 学生对象
     * @returns {Promise<Student>} 更新后的学生对象
     */
    async updateStudent(student) {
        try {
            // 验证学生数据
            const validation = student.validate();
            if (!validation.isValid) {
                throw new Error(validation.errors.join(', '));
            }

            // 更新数据库
            const result = await this.dbService.updateStudent(
                student.studentNumber, 
                student.toDatabase()
            );
            
            if (!result.success) {
                throw new Error(result.error.message);
            }

            // 触发事件
            this.emit('studentUpdated', student);
            
            return student;
        } catch (error) {
            console.error('更新学生信息失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有学生
     * @param {Object} options - 查询选项
     * @returns {Promise<Student[]>} 学生列表
     */
    async getStudents(options = {}) {
        try {
            const {
                page = 1,
                limit = 10,
                status = null,
                department = null
            } = options;

            const result = await this.dbService.getStudents({
                page,
                limit,
                status,
                department
            });

            if (!result.success) {
                throw new Error(result.error.message);
            }

            // 转换为Student对象
            const students = result.data.students.map(studentData =>
                Student.fromDatabase(studentData)
            );

            return {
                students,
                pagination: result.data.pagination
            };
        } catch (error) {
            console.error('获取学生列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取或创建学生
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Student>} 学生对象
     */
    async getOrCreateStudent(studentNumber) {
        try {
            let student = await this.getStudent(studentNumber);
            if (!student) {
                student = await this.createStudent(studentNumber);
            }
            return student;
        } catch (error) {
            console.error('获取或创建学生失败:', error);
            throw error;
        }
    }

    // ==================== 当前学生管理 ====================

    /**
     * 设置当前学生
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Student>} 当前学生对象
     */
    async setCurrentStudent(studentNumber) {
        try {
            const student = await this.getOrCreateStudent(studentNumber);
            this.currentStudent = student;
            
            // 触发事件
            this.emit('currentStudentChanged', student);
            
            return student;
        } catch (error) {
            console.error('设置当前学生失败:', error);
            throw error;
        }
    }

    /**
     * 获取当前学生
     * @returns {Student|null} 当前学生对象
     */
    getCurrentStudent() {
        return this.currentStudent;
    }

    /**
     * 清除当前学生
     */
    clearCurrentStudent() {
        const previousStudent = this.currentStudent;
        this.currentStudent = null;
        
        if (previousStudent) {
            this.emit('currentStudentCleared', previousStudent);
        }
    }

    // ==================== 学生进度管理 ====================

    /**
     * 保存学生进度
     * @param {string} studentNumber - 学生编号
     * @param {number} stepNumber - 步骤编号
     * @param {Object} stepData - 步骤数据
     * @returns {Promise<void>}
     */
    async saveProgress(studentNumber, stepNumber, stepData) {
        try {
            const student = await this.getStudent(studentNumber);
            if (!student) {
                throw new Error('学生不存在');
            }

            // 更新步骤数据
            student.updateStepData(stepNumber, stepData);
            
            // 保存到数据库
            await this.updateStudent(student);
            
            // 更新当前学生（如果是当前学生）
            if (this.currentStudent && this.currentStudent.studentNumber === studentNumber) {
                this.currentStudent = student;
            }

            // 触发事件
            this.emit('progressSaved', { student, stepNumber, stepData });
        } catch (error) {
            console.error('保存学生进度失败:', error);
            throw error;
        }
    }

    /**
     * 获取学生进度
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Object>} 学生进度数据
     */
    async getProgress(studentNumber) {
        try {
            const student = await this.getStudent(studentNumber);
            if (!student) {
                throw new Error('学生不存在');
            }

            return {
                studentNumber: student.studentNumber,
                currentStep: student.currentStep,
                stepData: student.stepData,
                totalDuration: student.getTotalDuration(),
                completedSteps: student.getCompletedStepsCount(),
                progressPercentage: student.getProgressPercentage(),
                isCompleted: student.isExamCompleted()
            };
        } catch (error) {
            console.error('获取学生进度失败:', error);
            throw error;
        }
    }

    // ==================== 考试流程管理 ====================

    /**
     * 开始考试
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Student>} 学生对象
     */
    async startExam(studentNumber) {
        try {
            const student = await this.getOrCreateStudent(studentNumber);
            
            // 如果考试已经开始，直接返回
            if (student.startTime) {
                return student;
            }

            // 设置开始时间
            student.startTime = new Date().toISOString();
            student.status = 'active';
            
            // 保存到数据库
            await this.updateStudent(student);
            
            // 设置为当前学生
            this.currentStudent = student;
            
            // 触发事件
            this.emit('examStarted', student);
            
            return student;
        } catch (error) {
            console.error('开始考试失败:', error);
            throw error;
        }
    }

    /**
     * 完成考试
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Student>} 学生对象
     */
    async completeExam(studentNumber) {
        try {
            const student = await this.getStudent(studentNumber);
            if (!student) {
                throw new Error('学生不存在');
            }

            // 设置考试完成状态
            student.status = 'completed';
            
            // 保存到数据库
            await this.updateStudent(student);
            
            // 触发事件
            this.emit('examCompleted', student);
            
            return student;
        } catch (error) {
            console.error('完成考试失败:', error);
            throw error;
        }
    }

    /**
     * 切换到下一步
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Student>} 学生对象
     */
    async nextStep(studentNumber) {
        try {
            const student = await this.getStudent(studentNumber);
            if (!student) {
                throw new Error('学生不存在');
            }

            if (!student.canGoToNextStep()) {
                throw new Error('无法进入下一步');
            }

            // 完成当前步骤
            const currentStepData = student.getCurrentStepData();
            if (currentStepData && currentStepData.status === 'active') {
                student.completeStep(student.currentStep);
            }

            // 进入下一步
            const nextStepNumber = student.currentStep + 1;
            student.startStep(nextStepNumber);
            
            // 保存到数据库
            await this.updateStudent(student);
            
            // 更新当前学生
            if (this.currentStudent && this.currentStudent.studentNumber === studentNumber) {
                this.currentStudent = student;
            }

            // 触发事件
            this.emit('stepChanged', { student, previousStep: nextStepNumber - 1, currentStep: nextStepNumber });
            
            return student;
        } catch (error) {
            console.error('切换到下一步失败:', error);
            throw error;
        }
    }

    /**
     * 返回上一步
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Student>} 学生对象
     */
    async previousStep(studentNumber) {
        try {
            const student = await this.getStudent(studentNumber);
            if (!student) {
                throw new Error('学生不存在');
            }

            if (!student.canGoToPreviousStep()) {
                throw new Error('无法返回上一步');
            }

            // 返回上一步
            const previousStepNumber = student.currentStep - 1;
            student.currentStep = previousStepNumber;
            
            // 保存到数据库
            await this.updateStudent(student);
            
            // 更新当前学生
            if (this.currentStudent && this.currentStudent.studentNumber === studentNumber) {
                this.currentStudent = student;
            }

            // 触发事件
            this.emit('stepChanged', { student, previousStep: previousStepNumber + 1, currentStep: previousStepNumber });
            
            return student;
        } catch (error) {
            console.error('返回上一步失败:', error);
            throw error;
        }
    }

    // ==================== 事件管理 ====================

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     */
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 重置学生数据
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Student>} 重置后的学生对象
     */
    async resetStudent(studentNumber) {
        try {
            const student = await this.getStudent(studentNumber);
            if (!student) {
                throw new Error('学生不存在');
            }

            // 重置学生数据
            student.reset();
            
            // 保存到数据库
            await this.updateStudent(student);
            
            // 触发事件
            this.emit('studentReset', student);
            
            return student;
        } catch (error) {
            console.error('重置学生数据失败:', error);
            throw error;
        }
    }

    /**
     * 获取学生统计信息
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Object>} 统计信息
     */
    async getStudentStats(studentNumber) {
        try {
            const student = await this.getStudent(studentNumber);
            if (!student) {
                throw new Error('学生不存在');
            }

            return {
                studentNumber: student.studentNumber,
                totalDuration: student.getTotalDuration(),
                completedSteps: student.getCompletedStepsCount(),
                progressPercentage: student.getProgressPercentage(),
                isCompleted: student.isExamCompleted(),
                stepData: student.stepData
            };
        } catch (error) {
            console.error('获取学生统计失败:', error);
            throw error;
        }
    }
}

// 创建单例实例
const studentService = new StudentService();

// 导出服务实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = studentService;
} else {
    window.StudentService = StudentService;
    window.studentService = studentService;
}
