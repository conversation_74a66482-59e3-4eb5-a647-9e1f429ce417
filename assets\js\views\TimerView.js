/**
 * TimerView - 计时器显示组件
 * 负责计时器数字显示、状态指示、控制按钮、时间警告提示
 */

class TimerView extends BaseView {
    constructor(container) {
        super(container);
        
        // 视图状态
        this.state = {
            isRunning: false,
            isPaused: false,
            remainingTime: 0,
            duration: 0,
            status: 'stopped',
            showWarnings: true
        };

        // 警告状态
        this.warningStates = {
            oneMinuteWarned: false,
            thirtySecondsWarned: false,
            tenSecondsWarned: false
        };

        this.template = this.getTimerTemplate();
        this.styles = this.getTimerStyles();
    }

    /**
     * 获取计时器模板
     * @returns {string} HTML模板
     */
    getTimerTemplate() {
        return `
            <div class="timer-container">
                <div class="timer-display">
                    <div class="time-digits" data-element="timeDigits">
                        <span class="minutes" data-element="minutes">05</span>
                        <span class="separator">:</span>
                        <span class="seconds" data-element="seconds">00</span>
                    </div>
                    <div class="timer-status">
                        <span class="status-text" data-element="statusText">已停止</span>
                        <div class="status-indicator" data-element="statusIndicator"></div>
                    </div>
                </div>

                <div class="timer-controls" data-element="timerControls">
                    <button class="timer-btn start-btn" data-action="startTimer" data-element="startBtn">
                        <i class="icon-play"></i>
                        <span>开始</span>
                    </button>
                    <button class="timer-btn pause-btn" data-action="pauseTimer" data-element="pauseBtn" style="display: none;">
                        <i class="icon-pause"></i>
                        <span>暂停</span>
                    </button>
                    <button class="timer-btn resume-btn" data-action="resumeTimer" data-element="resumeBtn" style="display: none;">
                        <i class="icon-play"></i>
                        <span>继续</span>
                    </button>
                    <button class="timer-btn reset-btn" data-action="resetTimer" data-element="resetBtn">
                        <i class="icon-reset"></i>
                        <span>重置</span>
                    </button>
                </div>

                <div class="timer-progress" data-element="timerProgress">
                    <div class="progress-bar">
                        <div class="progress-fill" data-element="progressFill" style="width: 0%"></div>
                    </div>
                    <div class="time-info">
                        <span class="elapsed-time" data-element="elapsedTime">已用时: 00:00</span>
                        <span class="remaining-time" data-element="remainingTime">剩余: 05:00</span>
                    </div>
                </div>

                <!-- 时间警告容器 -->
                <div class="timer-warnings" data-element="timerWarnings"></div>
            </div>
        `;
    }

    /**
     * 获取计时器样式
     * @returns {string} CSS样式
     */
    getTimerStyles() {
        return `
            .timer-container {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
                background: #fff;
                border-radius: 0.5rem;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            /* 计时器显示 */
            .timer-display {
                text-align: center;
                padding: 1rem;
                background: #f8fafc;
                border-radius: 0.5rem;
                border: 2px solid #e2e8f0;
            }

            .time-digits {
                font-family: 'Courier New', monospace;
                font-size: 3rem;
                font-weight: 700;
                color: #1e293b;
                margin-bottom: 0.5rem;
                transition: all 0.3s ease;
            }

            .time-digits.digit-change {
                transform: scale(1.05);
                color: #3b82f6;
            }

            .time-digits .separator {
                animation: blink 1s infinite;
            }

            @keyframes blink {
                0%, 50% { opacity: 1; }
                51%, 100% { opacity: 0.3; }
            }

            .timer-status {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                font-size: 0.875rem;
            }

            .status-text {
                font-weight: 500;
                color: #64748b;
            }

            .status-indicator {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #94a3b8;
                transition: background 0.3s ease;
            }

            .status-indicator.status-running {
                background: #22c55e;
                animation: pulse 2s infinite;
            }

            .status-indicator.status-paused {
                background: #f59e0b;
            }

            .status-indicator.status-stopped {
                background: #94a3b8;
            }

            .status-indicator.status-completed {
                background: #ef4444;
                animation: flash 0.5s infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            @keyframes flash {
                0%, 100% { opacity: 1; }
                50% { opacity: 0; }
            }

            /* 计时器控制按钮 */
            .timer-controls {
                display: flex;
                gap: 0.5rem;
                justify-content: center;
            }

            .timer-btn {
                display: flex;
                align-items: center;
                gap: 0.25rem;
                padding: 0.5rem 1rem;
                border: none;
                border-radius: 0.375rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
                font-size: 0.875rem;
            }

            .start-btn, .resume-btn {
                background: #22c55e;
                color: #fff;
            }

            .start-btn:hover, .resume-btn:hover {
                background: #16a34a;
            }

            .pause-btn {
                background: #f59e0b;
                color: #fff;
            }

            .pause-btn:hover {
                background: #d97706;
            }

            .reset-btn {
                background: #6b7280;
                color: #fff;
            }

            .reset-btn:hover {
                background: #4b5563;
            }

            .timer-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .timer-btn i {
                font-size: 1rem;
            }

            /* 进度条 */
            .timer-progress {
                margin-top: 0.5rem;
            }

            .progress-bar {
                width: 100%;
                height: 8px;
                background: #e2e8f0;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 0.5rem;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #22c55e, #16a34a);
                transition: width 1s ease-in-out;
                border-radius: 4px;
            }

            .progress-fill.warning {
                background: linear-gradient(90deg, #f59e0b, #d97706);
            }

            .progress-fill.critical {
                background: linear-gradient(90deg, #ef4444, #dc2626);
            }

            .time-info {
                display: flex;
                justify-content: space-between;
                font-size: 0.75rem;
                color: #64748b;
            }

            /* 时间警告 */
            .timer-warnings {
                position: relative;
                min-height: 2rem;
            }

            .timer-warning {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                padding: 0.5rem;
                border-radius: 0.375rem;
                font-weight: 500;
                text-align: center;
                animation: slideDown 0.3s ease-out;
            }

            .timer-warning.warning {
                background: #fef3c7;
                color: #92400e;
                border: 1px solid #f59e0b;
            }

            .timer-warning.urgent {
                background: #fee2e2;
                color: #991b1b;
                border: 1px solid #ef4444;
            }

            .timer-warning.critical {
                background: #fecaca;
                color: #7f1d1d;
                border: 1px solid #dc2626;
                animation: shake 0.5s ease-in-out infinite;
            }

            .timer-warning.timeout {
                background: #1f2937;
                color: #fff;
                border: 1px solid #374151;
                animation: flash 0.5s ease-in-out infinite;
            }

            @keyframes slideDown {
                from {
                    transform: translateY(-100%);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }

            /* 闪烁效果 */
            .timer-display.blinking {
                animation: displayBlink 0.5s ease-in-out infinite;
            }

            @keyframes displayBlink {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.3; }
            }

            /* 时间到达效果 */
            .timer-display.time-up {
                border-color: #ef4444;
                background: #fef2f2;
            }

            .timer-display.time-up .time-digits {
                color: #dc2626;
                animation: timeUpPulse 1s ease-in-out infinite;
            }

            @keyframes timeUpPulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.1); }
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .timer-container {
                    padding: 0.75rem;
                }

                .time-digits {
                    font-size: 2rem;
                }

                .timer-controls {
                    flex-direction: column;
                    gap: 0.25rem;
                }

                .timer-btn {
                    justify-content: center;
                    padding: 0.75rem;
                }

                .time-info {
                    flex-direction: column;
                    gap: 0.25rem;
                    text-align: center;
                }
            }

            @media (max-width: 480px) {
                .time-digits {
                    font-size: 1.5rem;
                }

                .timer-btn span {
                    display: none;
                }

                .timer-btn {
                    padding: 0.5rem;
                    min-width: 40px;
                    justify-content: center;
                }
            }
        `;
    }

    /**
     * 初始化视图
     */
    async onInitialize() {
        // 设置初始状态
        this.updateTimerDisplay({
            remainingTime: 300, // 5分钟
            duration: 300,
            status: 'stopped'
        });

        // 监听全局事件
        this.setupGlobalListeners();
    }

    /**
     * 设置全局监听器
     */
    setupGlobalListeners() {
        if (this.eventBus) {
            // 监听计时器控制器事件
            this.eventBus.on('TimerController.started', (data) => {
                this.handleTimerStarted(data);
            });

            this.eventBus.on('TimerController.paused', (data) => {
                this.handleTimerPaused(data);
            });

            this.eventBus.on('TimerController.resumed', (data) => {
                this.handleTimerResumed(data);
            });

            this.eventBus.on('TimerController.stopped', (data) => {
                this.handleTimerStopped(data);
            });

            this.eventBus.on('TimerController.reset', (data) => {
                this.handleTimerReset(data);
            });

            this.eventBus.on('TimerController.tick', (data) => {
                this.handleTimerTick(data);
            });

            this.eventBus.on('TimerController.timeUp', (data) => {
                this.handleTimeUp(data);
            });

            this.eventBus.on('TimerController.timeWarning', (data) => {
                this.handleTimeWarning(data);
            });
        }
    }

    // ==================== 动作处理方法 ====================

    /**
     * 处理开始计时器动作
     */
    handleStartTimerAction() {
        this.emit('startTimerRequested');
    }

    /**
     * 处理暂停计时器动作
     */
    handlePauseTimerAction() {
        this.emit('pauseTimerRequested');
    }

    /**
     * 处理恢复计时器动作
     */
    handleResumeTimerAction() {
        this.emit('resumeTimerRequested');
    }

    /**
     * 处理重置计时器动作
     */
    handleResetTimerAction() {
        this.emit('resetTimerRequested');
    }

    // ==================== 计时器显示更新 ====================

    /**
     * 更新计时器显示
     * @param {Object} timerData - 计时器数据
     */
    updateTimerDisplay(timerData) {
        const {
            remainingTime,
            duration,
            elapsedTime,
            status,
            isRunning,
            isPaused,
            progressPercentage
        } = timerData;

        // 更新状态
        this.state = {
            ...this.state,
            remainingTime: remainingTime || 0,
            duration: duration || 0,
            status: status || 'stopped',
            isRunning: isRunning || false,
            isPaused: isPaused || false
        };

        // 更新时间显示
        this.updateTimeDisplay(remainingTime || 0);

        // 更新状态显示
        this.updateStatusDisplay(status || 'stopped');

        // 更新控制按钮
        this.updateControlButtons();

        // 更新进度条
        this.updateProgress(progressPercentage || 0, remainingTime || 0);

        // 更新时间信息
        this.updateTimeInfo(elapsedTime || 0, remainingTime || 0);

        // 检查时间警告
        this.checkTimeWarning(remainingTime || 0);
    }

    /**
     * 更新时间显示
     * @param {number} seconds - 剩余秒数
     */
    updateTimeDisplay(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;

        if (this.elements.minutes) {
            this.elements.minutes.textContent = minutes.toString().padStart(2, '0');
        }

        if (this.elements.seconds) {
            this.elements.seconds.textContent = secs.toString().padStart(2, '0');
        }

        // 添加数字变化动画
        this.animateDigitChange();
    }

    /**
     * 更新状态显示
     * @param {string} status - 计时器状态
     */
    updateStatusDisplay(status) {
        const statusMap = {
            'running': { text: '计时中', class: 'status-running' },
            'paused': { text: '已暂停', class: 'status-paused' },
            'stopped': { text: '已停止', class: 'status-stopped' },
            'completed': { text: '时间到', class: 'status-completed' }
        };

        const statusInfo = statusMap[status] || statusMap['stopped'];

        if (this.elements.statusText) {
            this.elements.statusText.textContent = statusInfo.text;
        }

        if (this.elements.statusIndicator) {
            this.elements.statusIndicator.className = `status-indicator ${statusInfo.class}`;
        }
    }

    /**
     * 更新控制按钮
     */
    updateControlButtons() {
        const { isRunning, isPaused } = this.state;

        // 开始按钮
        if (this.elements.startBtn) {
            this.elements.startBtn.style.display = (!isRunning && !isPaused) ? 'flex' : 'none';
        }

        // 暂停按钮
        if (this.elements.pauseBtn) {
            this.elements.pauseBtn.style.display = (isRunning && !isPaused) ? 'flex' : 'none';
        }

        // 继续按钮
        if (this.elements.resumeBtn) {
            this.elements.resumeBtn.style.display = isPaused ? 'flex' : 'none';
        }

        // 重置按钮始终可用
        if (this.elements.resetBtn) {
            this.elements.resetBtn.disabled = false;
        }
    }

    /**
     * 更新进度条
     * @param {number} percentage - 进度百分比
     * @param {number} remainingTime - 剩余时间
     */
    updateProgress(percentage, remainingTime) {
        if (this.elements.progressFill) {
            this.elements.progressFill.style.width = `${percentage}%`;

            // 根据剩余时间改变进度条颜色
            this.elements.progressFill.classList.remove('warning', 'critical');

            if (remainingTime <= 60 && remainingTime > 30) {
                this.elements.progressFill.classList.add('warning');
            } else if (remainingTime <= 30) {
                this.elements.progressFill.classList.add('critical');
            }
        }
    }

    /**
     * 更新时间信息
     * @param {number} elapsedTime - 已用时间
     * @param {number} remainingTime - 剩余时间
     */
    updateTimeInfo(elapsedTime, remainingTime) {
        if (this.elements.elapsedTime) {
            this.elements.elapsedTime.textContent = `已用时: ${this.formatTime(elapsedTime)}`;
        }

        if (this.elements.remainingTime) {
            this.elements.remainingTime.textContent = `剩余: ${this.formatTime(remainingTime)}`;
        }
    }

    // ==================== 事件处理 ====================

    /**
     * 处理计时器启动
     * @param {Object} data - 计时器数据
     */
    handleTimerStarted(data) {
        this.updateTimerDisplay(data);
        this.resetWarningStates();
        this.hideTimeUpEffect();
    }

    /**
     * 处理计时器暂停
     * @param {Object} data - 计时器数据
     */
    handleTimerPaused(data) {
        this.updateTimerDisplay(data);
        this.stopBlinkingEffect();
    }

    /**
     * 处理计时器恢复
     * @param {Object} data - 计时器数据
     */
    handleTimerResumed(data) {
        this.updateTimerDisplay(data);
    }

    /**
     * 处理计时器停止
     * @param {Object} data - 计时器数据
     */
    handleTimerStopped(data) {
        this.updateTimerDisplay(data);
        this.clearWarnings();
        this.hideTimeUpEffect();
    }

    /**
     * 处理计时器重置
     * @param {Object} data - 计时器数据
     */
    handleTimerReset(data) {
        this.updateTimerDisplay(data);
        this.resetWarningStates();
        this.hideTimeUpEffect();
    }

    /**
     * 处理计时器滴答
     * @param {Object} data - 计时器数据
     */
    handleTimerTick(data) {
        this.updateTimerDisplay(data);
    }

    /**
     * 处理时间到达
     * @param {Object} data - 时间数据
     */
    handleTimeUp(data) {
        this.showTimeUpEffect();
        this.showWarning('时间到！', 'timeout');
        this.emit('timeUpNotification', data);
    }

    /**
     * 处理时间警告
     * @param {Object} data - 警告数据
     */
    handleTimeWarning(data) {
        const { type, remainingTime, message } = data;

        let level = 'warning';
        if (type === 'thirtySeconds') {
            level = 'urgent';
        } else if (type === 'tenSeconds') {
            level = 'critical';
        }

        this.showWarning(message, level);
    }

    // ==================== 动画效果 ====================

    /**
     * 数字变化动画
     */
    animateDigitChange() {
        if (this.elements.timeDigits) {
            this.elements.timeDigits.classList.add('digit-change');

            setTimeout(() => {
                this.elements.timeDigits.classList.remove('digit-change');
            }, 300);
        }
    }

    /**
     * 开始闪烁效果
     */
    startBlinkingEffect() {
        if (this.elements.timerDisplay) {
            this.elements.timerDisplay.classList.add('blinking');
        }
    }

    /**
     * 停止闪烁效果
     */
    stopBlinkingEffect() {
        if (this.elements.timerDisplay) {
            this.elements.timerDisplay.classList.remove('blinking');
        }
    }

    /**
     * 显示时间到达效果
     */
    showTimeUpEffect() {
        if (this.elements.timerDisplay) {
            this.elements.timerDisplay.classList.add('time-up');
        }

        this.startBlinkingEffect();
    }

    /**
     * 隐藏时间到达效果
     */
    hideTimeUpEffect() {
        if (this.elements.timerDisplay) {
            this.elements.timerDisplay.classList.remove('time-up');
        }

        this.stopBlinkingEffect();
    }

    // ==================== 时间警告 ====================

    /**
     * 检查时间警告
     * @param {number} remainingTime - 剩余时间
     */
    checkTimeWarning(remainingTime) {
        // 1分钟警告
        if (remainingTime <= 60 && remainingTime > 30 && !this.warningStates.oneMinuteWarned) {
            this.warningStates.oneMinuteWarned = true;
            this.showWarning('还剩1分钟', 'warning');
        }

        // 30秒警告
        if (remainingTime <= 30 && remainingTime > 10 && !this.warningStates.thirtySecondsWarned) {
            this.warningStates.thirtySecondsWarned = true;
            this.showWarning('还剩30秒', 'urgent');
        }

        // 10秒警告
        if (remainingTime <= 10 && remainingTime > 0 && !this.warningStates.tenSecondsWarned) {
            this.warningStates.tenSecondsWarned = true;
            this.showWarning('还剩10秒', 'critical');
            this.startBlinkingEffect();
        }

        // 时间到达
        if (remainingTime === 0) {
            this.showWarning('时间到！', 'timeout');
            this.showTimeUpEffect();
        }
    }

    /**
     * 显示警告
     * @param {string} message - 警告消息
     * @param {string} level - 警告级别
     */
    showWarning(message, level) {
        if (!this.state.showWarnings || !this.elements.timerWarnings) {
            return;
        }

        // 创建警告元素
        const warning = document.createElement('div');
        warning.className = `timer-warning ${level}`;
        warning.textContent = message;

        // 清除之前的警告
        this.elements.timerWarnings.innerHTML = '';

        // 添加新警告
        this.elements.timerWarnings.appendChild(warning);

        // 自动移除警告
        setTimeout(() => {
            if (warning.parentNode) {
                warning.parentNode.removeChild(warning);
            }
        }, level === 'timeout' ? 5000 : 3000);

        // 触发警告事件
        this.emit('warningShown', { message, level });
    }

    /**
     * 清除所有警告
     */
    clearWarnings() {
        if (this.elements.timerWarnings) {
            this.elements.timerWarnings.innerHTML = '';
        }

        this.stopBlinkingEffect();
        this.hideTimeUpEffect();
    }

    /**
     * 重置警告状态
     */
    resetWarningStates() {
        this.warningStates = {
            oneMinuteWarned: false,
            thirtySecondsWarned: false,
            tenSecondsWarned: false
        };

        this.clearWarnings();
    }

    // ==================== 工具方法 ====================

    /**
     * 格式化时间显示
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(seconds) {
        if (seconds < 0) seconds = 0;

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }

    /**
     * 设置计时器时长
     * @param {number} duration - 时长（秒）
     */
    setDuration(duration) {
        this.updateTimerDisplay({
            duration,
            remainingTime: duration,
            status: 'stopped',
            isRunning: false,
            isPaused: false,
            progressPercentage: 0
        });
    }

    /**
     * 设置警告开关
     * @param {boolean} enabled - 是否启用警告
     */
    setWarningsEnabled(enabled) {
        this.state.showWarnings = enabled;

        if (!enabled) {
            this.clearWarnings();
        }
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getCurrentState() {
        return {
            ...this.state,
            warningStates: { ...this.warningStates }
        };
    }

    /**
     * 重置视图状态
     */
    resetState() {
        this.state = {
            isRunning: false,
            isPaused: false,
            remainingTime: 0,
            duration: 0,
            status: 'stopped',
            showWarnings: true
        };

        this.resetWarningStates();
        this.hideTimeUpEffect();
        this.updateControlButtons();
    }

    /**
     * 销毁视图
     */
    onDestroy() {
        this.clearWarnings();
        this.hideTimeUpEffect();
        this.resetState();
    }
}

// 导出TimerView类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TimerView;
} else {
    window.TimerView = TimerView;
}
