"""
API路由处理 - 为前端提供基本的API接口
"""

from flask import Blueprint, jsonify, request
import json
import os
from datetime import datetime

# 创建API蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api')

# 数据文件路径
DATA_DIR = 'assets/data'
STUDENTS_FILE = os.path.join(DATA_DIR, 'students.json')
SETTINGS_FILE = os.path.join(DATA_DIR, 'settings.json')
QUESTIONS_FILE = os.path.join(DATA_DIR, 'questions.json')

# 确保数据目录存在
os.makedirs(DATA_DIR, exist_ok=True)

def load_json_file(filepath, default=None):
    """加载JSON文件"""
    if default is None:
        default = {}
    try:
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        return default
    except Exception as e:
        print(f"加载文件失败 {filepath}: {e}")
        return default

def save_json_file(filepath, data):
    """保存JSON文件"""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存文件失败 {filepath}: {e}")
        return False

@api_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'success': True,
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '2.0.0'
    })

@api_bp.route('/settings', methods=['GET'])
def get_settings():
    """获取系统设置"""
    default_settings = {
        'systemName': '研究生复试流程控制系统',
        'version': '2.0.0',
        'language': 'zh-CN',
        'theme': 'default',
        'examSettings': {
            'totalSteps': 5,
            'autoSave': True,
            'autoSaveInterval': 30,
            'allowPause': True,
            'allowSkip': False,
            'showProgress': True,
            'showTimer': True
        },
        'timeSettings': {
            'step1Time': 120,  # 中文自我介绍 2分钟
            'step2Time': 120,  # 英文自我介绍 2分钟
            'step3Time': 300,  # 英文翻译 5分钟
            'step4Time': 600,  # 专业课问答 10分钟
            'step5Time': 480,  # 综合面试 8分钟
            'warningTime': 30,
            'overtimeAllowed': False,
            'overtimeLimit': 60
        },
        'scoringSettings': {
            'maxScore': 100,
            'passingScore': 60,
            'stepWeights': {
                'step1': 0.15,
                'step2': 0.15,
                'step3': 0.20,
                'step4': 0.30,
                'step5': 0.20
            },
            'autoCalculate': True,
            'showScores': False
        },
        'uiSettings': {
            'showLeftPanel': True,
            'showRightPanel': True,
            'leftPanelWidth': 20,
            'rightPanelWidth': 20,
            'fontSize': 'medium',
            'compactMode': False,
            'animations': True,
            'sounds': False
        }
    }
    
    settings = load_json_file(SETTINGS_FILE, default_settings)
    return jsonify({
        'success': True,
        'settings': settings
    })

@api_bp.route('/settings', methods=['POST'])
def update_settings():
    """更新系统设置"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的数据'}), 400
        
        # 加载现有设置
        settings = load_json_file(SETTINGS_FILE, {})
        
        # 更新设置
        settings.update(data)
        settings['updatedAt'] = datetime.now().isoformat()
        
        # 保存设置
        if save_json_file(SETTINGS_FILE, settings):
            return jsonify({
                'success': True,
                'message': '设置已更新',
                'settings': settings
            })
        else:
            return jsonify({'success': False, 'message': '保存设置失败'}), 500
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/students', methods=['GET'])
def get_students():
    """获取学生列表"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        status = request.args.get('status', '')
        
        # 加载学生数据
        students_data = load_json_file(STUDENTS_FILE, {'students': []})
        students = students_data.get('students', [])
        
        # 过滤状态
        if status:
            students = [s for s in students if s.get('examStatus') == status]
        
        # 分页
        total = len(students)
        start = (page - 1) * limit
        end = start + limit
        students_page = students[start:end]
        
        return jsonify({
            'success': True,
            'students': students_page,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total,
                'pages': (total + limit - 1) // limit
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/students', methods=['POST'])
def create_student():
    """创建学生"""
    try:
        data = request.get_json()
        if not data or not data.get('studentNumber'):
            return jsonify({'success': False, 'message': '学号不能为空'}), 400
        
        # 加载现有学生数据
        students_data = load_json_file(STUDENTS_FILE, {'students': []})
        students = students_data.get('students', [])
        
        # 检查学号是否已存在
        if any(s.get('studentNumber') == data['studentNumber'] for s in students):
            return jsonify({'success': False, 'message': '学号已存在'}), 400
        
        # 创建新学生
        new_student = {
            'id': len(students) + 1,
            'studentNumber': data['studentNumber'],
            'name': data.get('name', ''),
            'gender': data.get('gender', ''),
            'age': data.get('age'),
            'phone': data.get('phone', ''),
            'email': data.get('email', ''),
            'major': data.get('major', ''),
            'examStatus': 'pending',
            'currentStep': 1,
            'totalSteps': 5,
            'createdAt': datetime.now().isoformat(),
            'updatedAt': datetime.now().isoformat()
        }
        
        students.append(new_student)
        students_data['students'] = students
        
        # 保存数据
        if save_json_file(STUDENTS_FILE, students_data):
            return jsonify({
                'success': True,
                'message': '学生创建成功',
                'student': new_student
            })
        else:
            return jsonify({'success': False, 'message': '保存学生数据失败'}), 500
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/students/<student_number>', methods=['GET'])
def get_student(student_number):
    """获取单个学生信息"""
    try:
        students_data = load_json_file(STUDENTS_FILE, {'students': []})
        students = students_data.get('students', [])
        
        student = next((s for s in students if s.get('studentNumber') == student_number), None)
        
        if student:
            return jsonify({
                'success': True,
                'student': student
            })
        else:
            return jsonify({'success': False, 'message': '学生不存在'}), 404
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/questions', methods=['GET'])
def get_questions():
    """获取题目列表"""
    try:
        question_type = request.args.get('type', '')
        subject = request.args.get('subject', '')
        
        # 默认题目数据
        default_questions = {
            'translation': [
                {
                    'id': 1,
                    'type': 'translation',
                    'content': 'The rapid development of artificial intelligence has transformed many industries.',
                    'difficulty': 'medium'
                },
                {
                    'id': 2,
                    'type': 'translation',
                    'content': 'Climate change poses significant challenges to global sustainability.',
                    'difficulty': 'medium'
                }
            ],
            'professional': {
                'computer': [
                    {
                        'id': 3,
                        'type': 'professional',
                        'subject': 'computer',
                        'content': '请解释面向对象编程的三大特性。',
                        'difficulty': 'medium'
                    }
                ],
                'math': [
                    {
                        'id': 4,
                        'type': 'professional',
                        'subject': 'math',
                        'content': '请解释微积分的基本定理。',
                        'difficulty': 'hard'
                    }
                ]
            }
        }
        
        questions_data = load_json_file(QUESTIONS_FILE, default_questions)
        
        if question_type == 'translation':
            questions = questions_data.get('translation', [])
        elif question_type == 'professional' and subject:
            questions = questions_data.get('professional', {}).get(subject, [])
        else:
            questions = []
            
        return jsonify({
            'success': True,
            'questions': questions
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# 错误处理
@api_bp.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'message': 'API接口不存在'}), 404

@api_bp.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'message': '服务器内部错误'}), 500
