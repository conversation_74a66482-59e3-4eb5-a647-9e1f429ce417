/**
 * 事件管理类 - 管理所有事件绑定和处理
 * 遵循单一职责原则，专门负责事件的统一管理
 */
class EventManager extends BaseClass {
    constructor() {
        super('EventManager');
        this.eventHandlers = new Map();
        this.delegatedEvents = new Map();
        this.customEvents = new Map();
        this.throttledHandlers = new Map();
        this.debouncedHandlers = new Map();
    }

    /**
     * 初始化事件管理器
     * @returns {Promise<boolean>} 初始化结果
     */
    async initialize() {
        try {
            this.setupGlobalEventHandlers();
            this.setInitialized(true);
            this.logger.info('EventManager initialized successfully');
            return true;
        } catch (error) {
            this.handleError(error, 'initialize');
            return false;
        }
    }

    /**
     * 设置全局事件处理器
     */
    setupGlobalEventHandlers() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            this.emit('global:error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });

        // 未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.emit('global:unhandledRejection', {
                reason: event.reason,
                promise: event.promise
            });
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.emit('page:visibilityChange', {
                hidden: document.hidden,
                visibilityState: document.visibilityState
            });
        });
    }

    /**
     * 添加事件监听器
     * @param {string|Element} target - 目标元素或选择器
     * @param {string} eventType - 事件类型
     * @param {Function} handler - 事件处理函数
     * @param {Object} options - 选项
     * @returns {string} 事件ID
     */
    on(target, eventType, handler, options = {}) {
        const eventId = this.generateId('event');
        const element = typeof target === 'string' ? document.querySelector(target) : target;
        
        if (!element) {
            this.logger.warn(`Target element not found: ${target}`);
            return null;
        }

        const wrappedHandler = this.wrapHandler(handler, options);
        
        // 存储事件信息
        this.eventHandlers.set(eventId, {
            element,
            eventType,
            handler: wrappedHandler,
            originalHandler: handler,
            options
        });

        // 添加事件监听器
        element.addEventListener(eventType, wrappedHandler, options);
        
        this.logger.debug(`Event listener added: ${eventType} on ${target}`);
        return eventId;
    }

    /**
     * 移除事件监听器
     * @param {string} eventId - 事件ID
     * @returns {boolean} 是否成功移除
     */
    off(eventId) {
        const eventInfo = this.eventHandlers.get(eventId);
        if (!eventInfo) {
            this.logger.warn(`Event not found: ${eventId}`);
            return false;
        }

        const { element, eventType, handler, options } = eventInfo;
        element.removeEventListener(eventType, handler, options);
        this.eventHandlers.delete(eventId);
        
        this.logger.debug(`Event listener removed: ${eventId}`);
        return true;
    }

    /**
     * 事件委托
     * @param {string|Element} container - 容器元素
     * @param {string} selector - 目标选择器
     * @param {string} eventType - 事件类型
     * @param {Function} handler - 事件处理函数
     * @returns {string} 委托ID
     */
    delegate(container, selector, eventType, handler) {
        const delegateId = this.generateId('delegate');
        const containerElement = typeof container === 'string' ? document.querySelector(container) : container;
        
        if (!containerElement) {
            this.logger.warn(`Container element not found: ${container}`);
            return null;
        }

        const delegateHandler = (event) => {
            const target = event.target.closest(selector);
            if (target && containerElement.contains(target)) {
                handler.call(target, event);
            }
        };

        this.delegatedEvents.set(delegateId, {
            container: containerElement,
            selector,
            eventType,
            handler: delegateHandler,
            originalHandler: handler
        });

        containerElement.addEventListener(eventType, delegateHandler);
        
        this.logger.debug(`Event delegation added: ${eventType} for ${selector} in ${container}`);
        return delegateId;
    }

    /**
     * 移除事件委托
     * @param {string} delegateId - 委托ID
     * @returns {boolean} 是否成功移除
     */
    undelegate(delegateId) {
        const delegateInfo = this.delegatedEvents.get(delegateId);
        if (!delegateInfo) {
            this.logger.warn(`Delegate not found: ${delegateId}`);
            return false;
        }

        const { container, eventType, handler } = delegateInfo;
        container.removeEventListener(eventType, handler);
        this.delegatedEvents.delete(delegateId);
        
        this.logger.debug(`Event delegation removed: ${delegateId}`);
        return true;
    }

    /**
     * 触发自定义事件
     * @param {string} eventName - 事件名称
     * @param {*} data - 事件数据
     * @param {Element} target - 目标元素
     */
    emit(eventName, data = null, target = document) {
        const customEvent = new CustomEvent(eventName, {
            detail: data,
            bubbles: true,
            cancelable: true
        });

        target.dispatchEvent(customEvent);
        this.logger.debug(`Custom event emitted: ${eventName}`, data);
    }

    /**
     * 监听自定义事件
     * @param {string} eventName - 事件名称
     * @param {Function} handler - 事件处理函数
     * @param {Element} target - 目标元素
     * @returns {string} 事件ID
     */
    listen(eventName, handler, target = document) {
        return this.on(target, eventName, handler);
    }

    /**
     * 包装事件处理函数
     * @param {Function} handler - 原始处理函数
     * @param {Object} options - 选项
     * @returns {Function} 包装后的处理函数
     */
    wrapHandler(handler, options) {
        let wrappedHandler = handler;

        // 节流处理
        if (options.throttle) {
            const throttleKey = `throttle_${this.generateId()}`;
            wrappedHandler = this.throttle(wrappedHandler, options.throttle);
            this.throttledHandlers.set(throttleKey, wrappedHandler);
        }

        // 防抖处理
        if (options.debounce) {
            const debounceKey = `debounce_${this.generateId()}`;
            wrappedHandler = this.debounce(wrappedHandler, options.debounce);
            this.debouncedHandlers.set(debounceKey, wrappedHandler);
        }

        // 一次性事件
        if (options.once) {
            const originalHandler = wrappedHandler;
            wrappedHandler = function(event) {
                originalHandler.call(this, event);
                event.target.removeEventListener(event.type, wrappedHandler);
            };
        }

        return wrappedHandler;
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 节流后的函数
     */
    throttle(func, delay) {
        let lastCall = 0;
        return function(...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, delay) {
        let timeoutId;
        return function(...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    /**
     * 清理所有事件
     */
    cleanup() {
        // 清理普通事件监听器
        for (const [eventId, eventInfo] of this.eventHandlers) {
            this.off(eventId);
        }

        // 清理委托事件
        for (const [delegateId] of this.delegatedEvents) {
            this.undelegate(delegateId);
        }

        // 清理节流和防抖处理器
        this.throttledHandlers.clear();
        this.debouncedHandlers.clear();

        this.logger.info('All events cleaned up');
    }

    /**
     * 获取事件统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalEvents: this.eventHandlers.size,
            delegatedEvents: this.delegatedEvents.size,
            throttledHandlers: this.throttledHandlers.size,
            debouncedHandlers: this.debouncedHandlers.size
        };
    }

    /**
     * 销毁事件管理器
     */
    destroy() {
        this.cleanup();
        super.destroy();
    }
}

// 创建全局实例
const eventManager = new EventManager();

// 导出
window.EventManager = EventManager;
window.eventManager = eventManager;
