/**
 * 步骤导航组件
 * 支持多步骤表单，包含计时器功能和表单验证
 */

/**
 * 初始化步骤导航
 * @param {Object} config 配置参数
 * @param {String|Element} config.container 容器选择器或元素
 * @param {Function} config.onStepChange 步骤变化回调
 * @param {Function} config.onComplete 完成回调
 * @returns {Object} 步骤导航实例
 */
function initStepper(options = {}) {
    // 默认选项
    const defaults = {
        container: '.stepper-container',
        onStepChange: null
    };
    
    const settings = { ...defaults, ...options };
    
    // 获取容器和步骤元素
    const container = document.querySelector(settings.container);
    if (!container) {
        console.error('Stepper容器未找到');
        return;
    }
    
    // 获取步骤指示器和步骤面板
    const stepIndicators = container.querySelectorAll('.modern-step-indicator, .step-indicator');
    const stepPanes = container.querySelectorAll('.modern-step-pane, .step-pane');
    
    // 计时相关变量
    let timer = null;
    let timeRemaining = 0;
    let timerDisplay = document.getElementById('timer');
    
    // 当前步骤
    let currentStep = 1;
    
    // 格式化时间为mm:ss格式
    function formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    // 更新计时器显示
    function updateTimerDisplay() {
        if (timerDisplay) {
            timerDisplay.textContent = formatTime(timeRemaining);
        }
    }
    
    // 更新步骤显示
    function updateSteps() {
        // 更新指示器
        stepIndicators.forEach((indicator, index) => {
            if (index + 1 === currentStep) {
                indicator.classList.add('active');
                indicator.classList.remove('completed');
            } else if (index + 1 < currentStep) {
                indicator.classList.add('completed');
                indicator.classList.remove('active');
            } else {
                indicator.classList.remove('active', 'completed');
            }
        });
        
        // 更新面板
        stepPanes.forEach((pane, index) => {
            if (index + 1 === currentStep) {
                pane.classList.add('active');
            } else {
                pane.classList.remove('active');
            }
        });
    }
    
    // 开始计时
    function startTimer() {
        if (timer) clearInterval(timer);
        
        timer = setInterval(() => {
            if (timeRemaining > 0) {
                timeRemaining--;
                updateTimerDisplay();
            } else {
                // 时间到
                clearInterval(timer);
                alert('时间到！');
            }
        }, 1000);
    }
    
    // 停止计时
    function stopTimer() {
        if (timer) clearInterval(timer);
        timer = null;
    }
    
    // 设置剩余时间
    function setTimeRemaining(seconds) {
        timeRemaining = seconds;
        updateTimerDisplay();
    }
    
    // 下一步
    function next(triggerCallback = true) {
        if (currentStep < stepPanes.length) {
            currentStep++;
            updateSteps();
            
            // 触发回调
            if (triggerCallback && typeof settings.onStepChange === 'function') {
                settings.onStepChange(currentStep, stepPanes.length);
            }
        }
    }
    
    // 上一步
    function prev() {
        if (currentStep > 1) {
            currentStep--;
            updateSteps();
            
            // 触发回调
            if (typeof settings.onStepChange === 'function') {
                settings.onStepChange(currentStep, stepPanes.length);
            }
        }
    }
    
    // 重置步骤
    function reset() {
        currentStep = 1;
        updateSteps();
        
        // 触发回调
        if (typeof settings.onStepChange === 'function') {
            settings.onStepChange(currentStep, stepPanes.length);
        }
    }
    
    // 初始化
    updateSteps();
    if (timerDisplay) {
        timerDisplay.textContent = formatTime(timeRemaining);
    }
    
    // 返回控制接口
    return {
        next,
        prev,
        reset,
        startTimer,
        stopTimer,
        setTimeRemaining,
        get currentStep() {
            return currentStep;
        }
    };
}