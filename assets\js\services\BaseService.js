/**
 * BaseService - 服务基类
 * 提供所有服务的通用功能和接口
 */

class BaseService {
    constructor() {
        this.eventBus = null;
        this.isInitialized = false;
        this.isStarted = false;
        this.cache = new Map();
        this.config = {};
    }

    /**
     * 初始化服务
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }

        try {
            await this.onInitialize();
            this.isInitialized = true;
            this.emit('initialized');
        } catch (error) {
            console.error(`${this.constructor.name} 初始化失败:`, error);
            throw error;
        }
    }

    /**
     * 子类重写此方法进行具体初始化
     * @returns {Promise<void>}
     */
    async onInitialize() {
        // 子类实现
    }

    /**
     * 启动服务
     * @returns {Promise<void>}
     */
    async start() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        if (this.isStarted) {
            return;
        }

        try {
            await this.onStart();
            this.isStarted = true;
            this.emit('started');
        } catch (error) {
            console.error(`${this.constructor.name} 启动失败:`, error);
            throw error;
        }
    }

    /**
     * 子类重写此方法进行具体启动
     * @returns {Promise<void>}
     */
    async onStart() {
        // 子类实现
    }

    /**
     * 停止服务
     * @returns {Promise<void>}
     */
    async stop() {
        if (!this.isStarted) {
            return;
        }

        try {
            await this.onStop();
            this.isStarted = false;
            this.emit('stopped');
        } catch (error) {
            console.error(`${this.constructor.name} 停止失败:`, error);
        }
    }

    /**
     * 子类重写此方法进行具体停止
     * @returns {Promise<void>}
     */
    async onStop() {
        // 子类实现
    }

    /**
     * 设置事件总线
     * @param {EventBus} eventBus - 事件总线实例
     */
    setEventBus(eventBus) {
        this.eventBus = eventBus;
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     */
    emit(event, data) {
        if (this.eventBus) {
            this.eventBus.emit(`${this.constructor.name}.${event}`, data);
        }
    }

    /**
     * 监听事件
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (this.eventBus) {
            this.eventBus.on(event, callback);
        }
    }

    /**
     * HTTP请求方法
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<any>} 响应数据
     */
    async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const config = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }

            return await response.text();

        } catch (error) {
            console.error(`${this.constructor.name} 请求失败:`, error);
            this.emit('requestError', { url, error });
            throw error;
        }
    }

    /**
     * GET请求
     * @param {string} url - 请求URL
     * @param {Object} params - 查询参数
     * @returns {Promise<any>} 响应数据
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl);
    }

    /**
     * POST请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @returns {Promise<any>} 响应数据
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @returns {Promise<any>} 响应数据
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     * @param {string} url - 请求URL
     * @returns {Promise<any>} 响应数据
     */
    async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }

    /**
     * 缓存数据
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {number} ttl - 生存时间（毫秒）
     */
    setCache(key, value, ttl = 300000) { // 默认5分钟
        const expiry = Date.now() + ttl;
        this.cache.set(key, { value, expiry });
    }

    /**
     * 获取缓存数据
     * @param {string} key - 缓存键
     * @returns {any} 缓存值
     */
    getCache(key) {
        const cached = this.cache.get(key);
        if (!cached) {
            return null;
        }

        if (Date.now() > cached.expiry) {
            this.cache.delete(key);
            return null;
        }

        return cached.value;
    }

    /**
     * 清除缓存
     * @param {string} key - 缓存键，不提供则清除所有
     */
    clearCache(key = null) {
        if (key) {
            this.cache.delete(key);
        } else {
            this.cache.clear();
        }
    }

    /**
     * 验证数据
     * @param {any} data - 要验证的数据
     * @param {Object} rules - 验证规则
     * @returns {Object} 验证结果
     */
    validate(data, rules) {
        const errors = [];

        for (const [field, rule] of Object.entries(rules)) {
            const value = data[field];

            if (rule.required && (value === undefined || value === null || value === '')) {
                errors.push(`${field} 是必需的`);
                continue;
            }

            if (value !== undefined && value !== null) {
                if (rule.type && typeof value !== rule.type) {
                    errors.push(`${field} 类型必须是 ${rule.type}`);
                }

                if (rule.min !== undefined && value < rule.min) {
                    errors.push(`${field} 不能小于 ${rule.min}`);
                }

                if (rule.max !== undefined && value > rule.max) {
                    errors.push(`${field} 不能大于 ${rule.max}`);
                }

                if (rule.pattern && !rule.pattern.test(value)) {
                    errors.push(`${field} 格式不正确`);
                }
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handleError(error, context = 'unknown') {
        const errorInfo = {
            service: this.constructor.name,
            context,
            error: {
                message: error.message,
                code: error.code || 'UNKNOWN_ERROR',
                stack: error.stack
            },
            timestamp: new Date().toISOString()
        };

        console.error(`${this.constructor.name} 错误 [${context}]:`, error);
        this.emit('error', errorInfo);
    }

    /**
     * 获取服务信息
     * @returns {Object} 服务信息
     */
    getInfo() {
        return {
            name: this.constructor.name,
            isInitialized: this.isInitialized,
            isStarted: this.isStarted,
            cacheSize: this.cache.size
        };
    }

    /**
     * 检查服务是否健康
     * @returns {boolean} 是否健康
     */
    isHealthy() {
        return this.isInitialized && this.isStarted;
    }

    /**
     * 销毁服务
     */
    async destroy() {
        try {
            await this.stop();
            this.clearCache();
            this.eventBus = null;
            this.isInitialized = false;
            this.emit('destroyed');
        } catch (error) {
            console.error(`${this.constructor.name} 销毁失败:`, error);
        }
    }
}

// 导出BaseService类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BaseService;
} else {
    window.BaseService = BaseService;
}
