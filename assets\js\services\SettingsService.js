/**
 * SettingsService - 系统设置服务
 * 负责系统设置的管理和持久化
 */

class SettingsService extends BaseService {
    constructor() {
        super();
        this.dbService = null;
        this.settings = null;
        this.defaultSettings = {
            timeSettings: {
                chineseTime: 2,      // 中文自我介绍时间（分钟）
                englishTime: 2,      // 英文自我介绍时间（分钟）
                translationTime: 5,  // 英文翻译时间（分钟）
                professionalTime: 10, // 专业问题时间（分钟）
                comprehensiveTime: 8  // 综合问答时间（分钟）
            },
            systemSettings: {
                debugMode: false,    // 调试模式
                autoSave: true,      // 自动保存
                theme: 'light',      // 主题
                language: 'zh-CN'    // 语言
            },
            uiSettings: {
                fontSize: 'medium',  // 字体大小
                showAnimations: true, // 显示动画
                compactMode: false   // 紧凑模式
            },
            examSettings: {
                autoStartTimer: true,    // 自动开始计时
                showQuestionNumbers: true, // 显示题目编号
                allowStepBack: true,     // 允许返回上一步
                confirmBeforeNext: false  // 下一步前确认
            },
            dataSettings: {
                backupInterval: 24,  // 备份间隔（小时）
                maxRecords: 1000,    // 最大记录数
                autoCleanup: true    // 自动清理
            }
        };
        this.changeListeners = new Set();
    }

    /**
     * 初始化服务
     */
    async onInitialize() {
        this.dbService = window.databaseService;
        await this.loadSettings();
    }

    /**
     * 启动服务
     */
    async onStart() {
        // 启动逻辑
    }

    // ==================== 设置管理 ====================

    /**
     * 加载设置
     * @returns {Promise<Object>} 设置对象
     */
    async loadSettings() {
        try {
            const result = await this.dbService.getSettings();
            
            if (result.success && result.data) {
                this.settings = this.mergeWithDefaults(result.data);
            } else {
                // 如果没有设置，使用默认设置
                this.settings = { ...this.defaultSettings };
                await this.saveSettings();
            }

            this.emit('settingsLoaded', this.settings);
            return this.settings;
        } catch (error) {
            console.error('加载设置失败:', error);
            this.settings = { ...this.defaultSettings };
            return this.settings;
        }
    }

    /**
     * 保存设置
     * @returns {Promise<Object>} 保存后的设置
     */
    async saveSettings() {
        try {
            const result = await this.dbService.saveSettings(this.settings);
            
            if (!result.success) {
                throw new Error(result.error.message);
            }

            this.emit('settingsSaved', this.settings);
            this.notifyChangeListeners();
            
            return this.settings;
        } catch (error) {
            console.error('保存设置失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有设置
     * @returns {Object} 设置对象
     */
    getSettings() {
        return this.settings ? { ...this.settings } : { ...this.defaultSettings };
    }

    /**
     * 获取分类设置
     * @param {string} category - 设置分类
     * @returns {Object} 分类设置
     */
    getCategorySettings(category) {
        const settings = this.getSettings();
        return settings[category] || {};
    }

    /**
     * 获取单个设置值
     * @param {string} category - 设置分类
     * @param {string} key - 设置键
     * @returns {any} 设置值
     */
    getSetting(category, key) {
        const categorySettings = this.getCategorySettings(category);
        return categorySettings[key];
    }

    /**
     * 更新设置
     * @param {Object} newSettings - 新设置
     * @returns {Promise<Object>} 更新后的设置
     */
    async updateSettings(newSettings) {
        try {
            // 深度合并设置
            this.settings = this.deepMerge(this.settings, newSettings);
            
            // 保存到数据库
            await this.saveSettings();
            
            this.emit('settingsUpdated', {
                oldSettings: this.settings,
                newSettings: this.settings,
                changes: newSettings
            });

            return this.settings;
        } catch (error) {
            console.error('更新设置失败:', error);
            throw error;
        }
    }

    /**
     * 更新分类设置
     * @param {string} category - 设置分类
     * @param {Object} categorySettings - 分类设置
     * @returns {Promise<Object>} 更新后的设置
     */
    async updateCategorySettings(category, categorySettings) {
        const updates = { [category]: categorySettings };
        return await this.updateSettings(updates);
    }

    /**
     * 更新单个设置
     * @param {string} category - 设置分类
     * @param {string} key - 设置键
     * @param {any} value - 设置值
     * @returns {Promise<Object>} 更新后的设置
     */
    async updateSetting(category, key, value) {
        const updates = {
            [category]: {
                [key]: value
            }
        };
        return await this.updateSettings(updates);
    }

    /**
     * 重置设置
     * @param {string} category - 设置分类（可选，不提供则重置所有）
     * @returns {Promise<Object>} 重置后的设置
     */
    async resetSettings(category = null) {
        try {
            if (category) {
                // 重置特定分类
                this.settings[category] = { ...this.defaultSettings[category] };
            } else {
                // 重置所有设置
                this.settings = { ...this.defaultSettings };
            }

            await this.saveSettings();
            
            this.emit('settingsReset', {
                category,
                settings: this.settings
            });

            return this.settings;
        } catch (error) {
            console.error('重置设置失败:', error);
            throw error;
        }
    }

    // ==================== 时间设置专用方法 ====================

    /**
     * 获取时间设置
     * @returns {Object} 时间设置
     */
    getTimeSettings() {
        return this.getCategorySettings('timeSettings');
    }

    /**
     * 更新时间设置
     * @param {Object} timeSettings - 时间设置
     * @returns {Promise<Object>} 更新后的设置
     */
    async updateTimeSettings(timeSettings) {
        return await this.updateCategorySettings('timeSettings', timeSettings);
    }

    /**
     * 获取步骤时间限制
     * @param {number} stepNumber - 步骤编号
     * @returns {number} 时间限制（分钟）
     */
    getStepTimeLimit(stepNumber) {
        const timeSettings = this.getTimeSettings();
        const stepTimeMap = {
            1: timeSettings.chineseTime,
            2: timeSettings.englishTime,
            3: timeSettings.translationTime,
            4: timeSettings.professionalTime,
            5: timeSettings.comprehensiveTime
        };
        return stepTimeMap[stepNumber] || 5; // 默认5分钟
    }

    // ==================== 系统设置专用方法 ====================

    /**
     * 获取系统设置
     * @returns {Object} 系统设置
     */
    getSystemSettings() {
        return this.getCategorySettings('systemSettings');
    }

    /**
     * 更新系统设置
     * @param {Object} systemSettings - 系统设置
     * @returns {Promise<Object>} 更新后的设置
     */
    async updateSystemSettings(systemSettings) {
        return await this.updateCategorySettings('systemSettings', systemSettings);
    }

    /**
     * 切换调试模式
     * @returns {Promise<boolean>} 新的调试模式状态
     */
    async toggleDebugMode() {
        const currentMode = this.getSetting('systemSettings', 'debugMode');
        await this.updateSetting('systemSettings', 'debugMode', !currentMode);
        return !currentMode;
    }

    // ==================== UI设置专用方法 ====================

    /**
     * 获取UI设置
     * @returns {Object} UI设置
     */
    getUISettings() {
        return this.getCategorySettings('uiSettings');
    }

    /**
     * 更新UI设置
     * @param {Object} uiSettings - UI设置
     * @returns {Promise<Object>} 更新后的设置
     */
    async updateUISettings(uiSettings) {
        return await this.updateCategorySettings('uiSettings', uiSettings);
    }

    // ==================== 变更监听 ====================

    /**
     * 添加设置变更监听器
     * @param {Function} listener - 监听器函数
     */
    addChangeListener(listener) {
        this.changeListeners.add(listener);
    }

    /**
     * 移除设置变更监听器
     * @param {Function} listener - 监听器函数
     */
    removeChangeListener(listener) {
        this.changeListeners.delete(listener);
    }

    /**
     * 通知变更监听器
     */
    notifyChangeListeners() {
        this.changeListeners.forEach(listener => {
            try {
                listener(this.settings);
            } catch (error) {
                console.error('设置变更监听器错误:', error);
            }
        });
    }

    // ==================== 工具方法 ====================

    /**
     * 与默认设置合并
     * @param {Object} settings - 设置对象
     * @returns {Object} 合并后的设置
     */
    mergeWithDefaults(settings) {
        return this.deepMerge(this.defaultSettings, settings);
    }

    /**
     * 深度合并对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @returns {Object} 合并后的对象
     */
    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(result[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    }

    /**
     * 验证设置
     * @param {Object} settings - 设置对象
     * @returns {Object} 验证结果
     */
    validateSettings(settings) {
        const errors = [];

        // 验证时间设置
        if (settings.timeSettings) {
            const timeSettings = settings.timeSettings;
            const timeFields = ['chineseTime', 'englishTime', 'translationTime', 'professionalTime', 'comprehensiveTime'];
            
            timeFields.forEach(field => {
                if (timeSettings[field] !== undefined) {
                    if (typeof timeSettings[field] !== 'number' || timeSettings[field] < 1 || timeSettings[field] > 60) {
                        errors.push(`${field} 必须是1-60之间的数字`);
                    }
                }
            });
        }

        // 验证系统设置
        if (settings.systemSettings) {
            const systemSettings = settings.systemSettings;
            
            if (systemSettings.theme && !['light', 'dark'].includes(systemSettings.theme)) {
                errors.push('主题必须是 light 或 dark');
            }
            
            if (systemSettings.language && !['zh-CN', 'en-US'].includes(systemSettings.language)) {
                errors.push('语言必须是 zh-CN 或 en-US');
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 导出设置
     * @returns {string} 设置的JSON字符串
     */
    exportSettings() {
        return JSON.stringify(this.settings, null, 2);
    }

    /**
     * 导入设置
     * @param {string} settingsJson - 设置的JSON字符串
     * @returns {Promise<Object>} 导入后的设置
     */
    async importSettings(settingsJson) {
        try {
            const importedSettings = JSON.parse(settingsJson);
            
            // 验证设置
            const validation = this.validateSettings(importedSettings);
            if (!validation.isValid) {
                throw new Error(`设置验证失败: ${validation.errors.join(', ')}`);
            }

            // 更新设置
            await this.updateSettings(importedSettings);
            
            this.emit('settingsImported', this.settings);
            return this.settings;
        } catch (error) {
            console.error('导入设置失败:', error);
            throw error;
        }
    }
}

// 创建全局实例
const settingsService = new SettingsService();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SettingsService, settingsService };
} else {
    window.SettingsService = SettingsService;
    window.settingsService = settingsService;
}
