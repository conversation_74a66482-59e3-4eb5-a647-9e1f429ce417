# 研究生复试系统 - API接口文档

## API架构概述

API层是系统的数据访问接口，负责处理前端请求、数据验证、业务逻辑执行和数据库操作。采用RESTful设计风格，提供统一的接口规范和错误处理机制。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Frontend     │───►│   API Gateway   │───►│    Database     │
│   (前端请求)     │◄───│   (接口处理)     │◄───│   (数据存储)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  HTTP Requests  │    │  API Endpoints  │    │  SQL Queries    │
│  - GET/POST     │    │ - 路由处理       │    │  - CRUD操作     │
│  - PUT/DELETE   │    │ - 数据验证       │    │  - 事务管理     │
│  - Headers      │    │ - 错误处理       │    │  - 索引优化     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## API设计原则

### 1. RESTful设计
遵循REST架构风格，使用标准HTTP方法：
- **GET**: 获取资源
- **POST**: 创建资源
- **PUT**: 更新资源
- **DELETE**: 删除资源

### 2. 统一响应格式
所有API响应都使用统一的JSON格式：
```json
{
  "success": true,
  "data": {},
  "error": null,
  "timestamp": "2024-12-09T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 3. 分层错误处理
```json
{
  "success": false,
  "data": null,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "数据验证失败",
    "details": {
      "field": "studentNumber",
      "reason": "学生编号不能为空"
    }
  },
  "timestamp": "2024-12-09T10:30:00Z"
}
```

## 学生管理API

### 基础路径: `/api/students`

#### 创建学生
```http
POST /api/students
Content-Type: application/json

{
  "studentNumber": "2024001",
  "name": "张三",
  "department": "计算机科学与技术"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "studentNumber": "2024001",
    "name": "张三",
    "department": "计算机科学与技术",
    "currentStep": 1,
    "status": "active",
    "startTime": "2024-12-09T10:30:00Z",
    "createdAt": "2024-12-09T10:30:00Z"
  },
  "timestamp": "2024-12-09T10:30:00Z"
}
```

#### 获取学生信息
```http
GET /api/students/{studentNumber}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "studentNumber": "2024001",
    "currentStep": 3,
    "status": "active",
    "startTime": "2024-12-09T10:30:00Z",
    "stepData": {
      "1": {
        "stepName": "中文自我介绍",
        "startTime": "2024-12-09T10:30:00Z",
        "endTime": "2024-12-09T10:32:00Z",
        "duration": 120,
        "status": "completed"
      },
      "2": {
        "stepName": "英文自我介绍",
        "startTime": "2024-12-09T10:32:00Z",
        "endTime": "2024-12-09T10:34:00Z",
        "duration": 120,
        "status": "completed"
      },
      "3": {
        "stepName": "英文翻译",
        "startTime": "2024-12-09T10:34:00Z",
        "status": "active"
      }
    }
  }
}
```

#### 更新学生状态
```http
PUT /api/students/{studentNumber}
Content-Type: application/json

{
  "currentStep": 4,
  "status": "active",
  "stepData": {
    "3": {
      "endTime": "2024-12-09T10:39:00Z",
      "duration": 300,
      "status": "completed",
      "questions": [
        {
          "id": "translation_5",
          "type": "translation",
          "index": 5,
          "selectedAt": "2024-12-09T10:35:00Z"
        }
      ]
    }
  }
}
```

#### 获取所有学生
```http
GET /api/students?page=1&limit=10&status=active
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `status`: 状态筛选 (active/completed/paused)
- `department`: 院系筛选

## 题目管理API

### 基础路径: `/api/questions`

#### 获取题目列表
```http
GET /api/questions/{type}
```

**路径参数:**
- `type`: 题目类型 (translation/professional)

**响应示例:**
```json
{
  "success": true,
  "data": {
    "questions": [
      {
        "id": "translation_1",
        "type": "translation",
        "index": 1,
        "title": "翻译题1",
        "content": {
          "english": "Hello, world!",
          "chinese": "你好，世界！",
          "difficulty": "easy"
        },
        "isActive": true,
        "createdAt": "2024-12-09T10:00:00Z"
      }
    ],
    "total": 20,
    "type": "translation"
  }
}
```

#### 获取指定题目
```http
GET /api/questions/{type}/{index}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": "translation_5",
    "type": "translation",
    "index": 5,
    "title": "翻译题5",
    "content": {
      "english": "The quick brown fox jumps over the lazy dog.",
      "chinese": "敏捷的棕色狐狸跳过懒惰的狗。",
      "difficulty": "medium",
      "tags": ["动物", "形容词"]
    },
    "isActive": true
  }
}
```

#### 获取已使用题目
```http
GET /api/questions/{type}/used
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "usedQuestions": [
      {
        "questionId": "translation_2",
        "questionIndex": 2,
        "studentNumber": "2024001",
        "usedAt": "2024-12-09T10:35:00Z"
      },
      {
        "questionId": "translation_7",
        "questionIndex": 7,
        "studentNumber": "2024002",
        "usedAt": "2024-12-09T10:40:00Z"
      }
    ],
    "usedNumbers": [2, 7],
    "type": "translation"
  }
}
```

#### 获取题目统计
```http
GET /api/questions/{type}/stats
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "type": "translation",
    "total": 20,
    "used": 5,
    "available": 15,
    "usageRate": 25,
    "usedNumbers": [1, 3, 5, 8, 12],
    "availableNumbers": [2, 4, 6, 7, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20]
  }
}
```

#### 标记题目为已使用
```http
POST /api/questions/mark-used
Content-Type: application/json

{
  "questionId": "translation_5",
  "questionType": "translation",
  "questionIndex": 5,
  "studentNumber": "2024001",
  "examRecordId": 1,
  "stepNumber": 3
}
```

#### 创建题目
```http
POST /api/questions
Content-Type: application/json

{
  "type": "translation",
  "index": 21,
  "title": "翻译题21",
  "content": {
    "english": "Artificial Intelligence is transforming our world.",
    "chinese": "人工智能正在改变我们的世界。",
    "difficulty": "hard",
    "tags": ["科技", "AI"]
  }
}
```

#### 更新题目
```http
PUT /api/questions/{questionId}
Content-Type: application/json

{
  "title": "翻译题21（修订版）",
  "content": {
    "english": "Artificial Intelligence is rapidly transforming our world.",
    "chinese": "人工智能正在快速改变我们的世界。",
    "difficulty": "hard",
    "tags": ["科技", "AI", "变革"]
  }
}
```

## 考试记录API

### 基础路径: `/api/exam-records`

#### 创建考试记录
```http
POST /api/exam-records
Content-Type: application/json

{
  "studentId": 1,
  "studentNumber": "2024001",
  "startTime": "2024-12-09T10:30:00Z"
}
```

#### 获取考试记录列表
```http
GET /api/exam-records?page=1&limit=10&status=completed&date=2024-12-09
```

**查询参数:**
- `page`: 页码
- `limit`: 每页数量
- `status`: 状态筛选 (in_progress/completed/cancelled)
- `date`: 日期筛选 (YYYY-MM-DD)
- `studentNumber`: 学生编号筛选

**响应示例:**
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "studentNumber": "2024001",
        "startTime": "2024-12-09T10:30:00Z",
        "endTime": "2024-12-09T10:55:00Z",
        "totalDuration": 1500,
        "status": "completed",
        "stepRecords": [
          {
            "stepNumber": 1,
            "stepName": "中文自我介绍",
            "duration": 120,
            "status": "completed"
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

#### 获取学生考试记录
```http
GET /api/exam-records/student/{studentNumber}
```

#### 更新考试记录
```http
PUT /api/exam-records/{recordId}
Content-Type: application/json

{
  "endTime": "2024-12-09T10:55:00Z",
  "status": "completed",
  "totalDuration": 1500
}
```

## 步骤记录API

### 基础路径: `/api/step-records`

#### 创建步骤记录
```http
POST /api/step-records
Content-Type: application/json

{
  "examRecordId": 1,
  "stepNumber": 3,
  "stepName": "英文翻译",
  "startTime": "2024-12-09T10:34:00Z"
}
```

#### 完成步骤记录
```http
PUT /api/step-records/{stepId}/complete
Content-Type: application/json

{
  "endTime": "2024-12-09T10:39:00Z",
  "questionsData": [
    {
      "id": "translation_5",
      "type": "translation",
      "index": 5,
      "selectedAt": "2024-12-09T10:35:00Z",
      "content": {
        "english": "The quick brown fox jumps over the lazy dog.",
        "chinese": "敏捷的棕色狐狸跳过懒惰的狗。"
      }
    }
  ]
}
```

## 系统设置API

### 基础路径: `/api/settings`

#### 获取所有设置
```http
GET /api/settings
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "timeSettings": {
      "chineseTime": 2,
      "englishTime": 2,
      "translationTime": 5,
      "professionalTime": 10,
      "comprehensiveTime": 8
    },
    "systemSettings": {
      "debugMode": false,
      "autoSave": true,
      "theme": "light"
    },
    "uiSettings": {
      "fontSize": "medium",
      "showAnimations": true
    }
  }
}
```

#### 获取分类设置
```http
GET /api/settings/{category}
```

**路径参数:**
- `category`: 设置分类 (time/system/ui/exam/data)

#### 更新设置
```http
PUT /api/settings/{category}/{key}
Content-Type: application/json

{
  "value": 6
}
```

#### 批量更新设置
```http
PUT /api/settings
Content-Type: application/json

{
  "timeSettings": {
    "translationTime": 6,
    "professionalTime": 12
  },
  "systemSettings": {
    "debugMode": true
  }
}
```

#### 重置设置
```http
POST /api/settings/reset
```

## 数据管理API

### 基础路径: `/api/data`

#### 备份数据
```http
POST /api/data/backup
Content-Type: application/json

{
  "includeQuestions": true,
  "includeRecords": true,
  "includeSettings": true
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "backupId": "backup_20241209_103000",
    "filename": "interview_system_backup_20241209_103000.sql",
    "size": 1024000,
    "createdAt": "2024-12-09T10:30:00Z"
  }
}
```

#### 恢复数据
```http
POST /api/data/restore
Content-Type: application/json

{
  "backupFile": "interview_system_backup_20241209_103000.sql",
  "overwriteExisting": true
}
```

#### 清理数据
```http
POST /api/data/cleanup
Content-Type: application/json

{
  "cleanupType": "old_records",
  "retentionDays": 30,
  "dryRun": false
}
```

#### 重置所有数据
```http
POST /api/data/reset
Content-Type: application/json

{
  "confirmationCode": "RESET_ALL_DATA",
  "keepQuestions": true,
  "keepSettings": true
}
```

## 统计查询API

### 基础路径: `/api/stats`

#### 获取系统统计
```http
GET /api/stats/system
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "totalStudents": 150,
    "activeExams": 5,
    "completedExams": 145,
    "totalQuestions": 40,
    "usedQuestions": 25,
    "systemUptime": 86400,
    "databaseSize": "2.5MB"
  }
}
```

#### 获取考试统计
```http
GET /api/stats/exam?period=today&groupBy=hour
```

**查询参数:**
- `period`: 时间段 (today/week/month/year)
- `groupBy`: 分组方式 (hour/day/week/month)

#### 获取题目统计
```http
GET /api/stats/questions
```

## 工具API

### 基础路径: `/api/utils`

#### 健康检查
```http
GET /api/health
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "database": "connected",
    "uptime": 86400,
    "version": "2.0.0",
    "timestamp": "2024-12-09T10:30:00Z"
  }
}
```

#### 获取系统信息
```http
GET /api/info
```

#### 自定义查询
```http
POST /api/query
Content-Type: application/json

{
  "sql": "SELECT COUNT(*) as total FROM students WHERE status = ?",
  "params": ["active"]
}
```

#### 批量操作
```http
POST /api/batch
Content-Type: application/json

{
  "operations": [
    {
      "method": "POST",
      "endpoint": "/api/students",
      "data": {"studentNumber": "2024003"}
    },
    {
      "method": "PUT",
      "endpoint": "/api/students/2024003",
      "data": {"currentStep": 2}
    }
  ]
}
```

## 错误代码定义

### 通用错误 (1000-1999)
- `1000`: `UNKNOWN_ERROR` - 未知错误
- `1001`: `INVALID_REQUEST` - 请求格式错误
- `1002`: `MISSING_PARAMETER` - 缺少必需参数
- `1003`: `INVALID_PARAMETER` - 参数值无效
- `1004`: `UNAUTHORIZED` - 未授权访问
- `1005`: `FORBIDDEN` - 禁止访问
- `1006`: `RATE_LIMITED` - 请求频率限制

### 学生相关错误 (2000-2999)
- `2001`: `STUDENT_NOT_FOUND` - 学生不存在
- `2002`: `STUDENT_ALREADY_EXISTS` - 学生已存在
- `2003`: `INVALID_STUDENT_NUMBER` - 学生编号格式错误
- `2004`: `STUDENT_EXAM_IN_PROGRESS` - 学生考试进行中
- `2005`: `STUDENT_EXAM_COMPLETED` - 学生考试已完成

### 题目相关错误 (3000-3999)
- `3001`: `QUESTION_NOT_FOUND` - 题目不存在
- `3002`: `QUESTION_ALREADY_USED` - 题目已被使用
- `3003`: `NO_AVAILABLE_QUESTIONS` - 没有可用题目
- `3004`: `INVALID_QUESTION_TYPE` - 题目类型无效
- `3005`: `QUESTION_INDEX_CONFLICT` - 题目编号冲突

### 考试相关错误 (4000-4999)
- `4001`: `EXAM_NOT_STARTED` - 考试未开始
- `4002`: `EXAM_ALREADY_COMPLETED` - 考试已完成
- `4003`: `INVALID_STEP_TRANSITION` - 无效的步骤切换
- `4004`: `STEP_NOT_COMPLETED` - 步骤未完成
- `4005`: `TIMER_NOT_RUNNING` - 计时器未运行

### 数据库错误 (5000-5999)
- `5001`: `DATABASE_CONNECTION_ERROR` - 数据库连接错误
- `5002`: `DATABASE_QUERY_ERROR` - 数据库查询错误
- `5003`: `DATABASE_CONSTRAINT_ERROR` - 数据库约束错误
- `5004`: `DATABASE_TRANSACTION_ERROR` - 数据库事务错误
- `5005`: `DATABASE_BACKUP_ERROR` - 数据库备份错误

## API版本控制

### 版本策略
- 使用URL路径版本控制: `/api/v1/`, `/api/v2/`
- 向后兼容性保证
- 废弃API的渐进式迁移

### 版本信息
```http
GET /api/version
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "currentVersion": "2.0.0",
    "supportedVersions": ["1.0", "2.0"],
    "deprecatedVersions": [],
    "apiDocumentation": "https://docs.example.com/api/v2"
  }
}
```

## 安全和认证

### API密钥认证
```http
Authorization: Bearer your-api-key-here
```

### 请求签名
```http
X-Signature: sha256=calculated-signature
X-Timestamp: 1702123456
```

### 速率限制
- 每分钟最多100个请求
- 超出限制返回429状态码
- 响应头包含限制信息

API层提供了完整的数据访问接口，支持系统的所有功能需求，并确保了数据的安全性、一致性和可扩展性。
