/**
 * ViewManager - 视图管理器
 * 负责管理所有视图组件的创建、初始化、生命周期和销毁
 */

class ViewManager {
    constructor() {
        this.views = new Map();
        this.viewInstances = new Map();
        this.eventBus = null;
        this.isInitialized = false;
        this.isStarted = false;
        
        // 视图配置
        this.viewConfigs = new Map();
        
        // 视图状态
        this.viewStates = new Map();
        
        // 视图依赖关系
        this.viewDependencies = new Map();
    }

    /**
     * 初始化视图管理器
     * @param {EventBus} eventBus - 事件总线
     * @returns {Promise<void>}
     */
    async initialize(eventBus) {
        if (this.isInitialized) {
            console.warn('ViewManager 已经初始化');
            return;
        }

        try {
            console.log('🎨 初始化视图管理器...');
            
            this.eventBus = eventBus;
            
            // 1. 注册视图类
            this.registerViewClasses();
            
            // 2. 设置视图配置
            this.setupViewConfigs();
            
            // 3. 设置事件监听
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ ViewManager 初始化完成');
            
        } catch (error) {
            console.error('❌ ViewManager 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 启动视图管理器
     * @returns {Promise<void>}
     */
    async start() {
        if (!this.isInitialized) {
            throw new Error('ViewManager 未初始化');
        }

        if (this.isStarted) {
            console.warn('ViewManager 已经启动');
            return;
        }

        try {
            console.log('🎯 启动视图管理器...');
            
            // 1. 创建核心视图
            await this.createCoreViews();
            
            // 2. 初始化视图
            await this.initializeViews();
            
            this.isStarted = true;
            console.log('✅ ViewManager 启动完成');
            
        } catch (error) {
            console.error('❌ ViewManager 启动失败:', error);
            throw error;
        }
    }

    /**
     * 停止视图管理器
     * @returns {Promise<void>}
     */
    async stop() {
        if (!this.isStarted) {
            return;
        }

        try {
            console.log('🛑 停止视图管理器...');
            
            // 销毁所有视图
            await this.destroyAllViews();
            
            this.isStarted = false;
            console.log('✅ ViewManager 已停止');
            
        } catch (error) {
            console.error('❌ ViewManager 停止失败:', error);
        }
    }

    // ==================== 视图注册 ====================

    /**
     * 注册视图类
     */
    registerViewClasses() {
        // 注册基础视图类
        if (window.BaseView) {
            this.views.set('BaseView', window.BaseView);
        }
        
        // 注册业务视图类
        if (window.ExamView) {
            this.views.set('ExamView', window.ExamView);
        }
        
        if (window.QuestionView) {
            this.views.set('QuestionView', window.QuestionView);
        }
        
        if (window.TimerView) {
            this.views.set('TimerView', window.TimerView);
        }
        
        console.log(`已注册 ${this.views.size} 个视图类`);
    }

    /**
     * 设置视图配置
     */
    setupViewConfigs() {
        // 主考试视图配置
        this.viewConfigs.set('examView', {
            className: 'ExamView',
            container: '#app',
            autoCreate: true,
            singleton: true,
            dependencies: ['questionView', 'timerView']
        });
        
        // 题目视图配置
        this.viewConfigs.set('questionView', {
            className: 'QuestionView',
            container: '.question-container',
            autoCreate: false,
            singleton: true,
            dependencies: []
        });
        
        // 计时器视图配置
        this.viewConfigs.set('timerView', {
            className: 'TimerView',
            container: '.timer-container',
            autoCreate: false,
            singleton: true,
            dependencies: []
        });
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 监听视图相关事件
        this.eventBus.on('view.create', (data) => {
            this.handleViewCreateRequest(data);
        });
        
        this.eventBus.on('view.destroy', (data) => {
            this.handleViewDestroyRequest(data);
        });
        
        this.eventBus.on('view.show', (data) => {
            this.handleViewShowRequest(data);
        });
        
        this.eventBus.on('view.hide', (data) => {
            this.handleViewHideRequest(data);
        });
    }

    // ==================== 视图创建和管理 ====================

    /**
     * 创建核心视图
     */
    async createCoreViews() {
        // 创建需要自动创建的视图
        for (const [viewName, config] of this.viewConfigs) {
            if (config.autoCreate) {
                await this.createView(viewName);
            }
        }
    }

    /**
     * 创建视图
     * @param {string} viewName - 视图名称
     * @param {Object} options - 创建选项
     * @returns {Promise<Object>} 视图实例
     */
    async createView(viewName, options = {}) {
        const config = this.viewConfigs.get(viewName);
        if (!config) {
            throw new Error(`视图配置不存在: ${viewName}`);
        }

        // 检查是否为单例且已存在
        if (config.singleton && this.viewInstances.has(viewName)) {
            return this.viewInstances.get(viewName);
        }

        try {
            // 获取视图类
            const ViewClass = this.views.get(config.className);
            if (!ViewClass) {
                throw new Error(`视图类不存在: ${config.className}`);
            }

            // 获取容器元素
            const container = this.getContainer(config.container, options.container);
            if (!container) {
                throw new Error(`容器元素不存在: ${config.container}`);
            }

            // 创建视图实例
            const viewInstance = new ViewClass(container);
            
            // 设置事件总线
            if (viewInstance.setEventBus) {
                viewInstance.setEventBus(this.eventBus);
            }

            // 初始化视图
            if (viewInstance.initialize) {
                await viewInstance.initialize();
            }

            // 存储视图实例
            this.viewInstances.set(viewName, viewInstance);
            this.viewStates.set(viewName, 'created');

            console.log(`✅ 视图创建成功: ${viewName}`);
            
            // 触发视图创建事件
            this.eventBus.emit('viewManager.viewCreated', {
                viewName,
                viewInstance,
                timestamp: new Date().toISOString()
            });

            return viewInstance;

        } catch (error) {
            console.error(`❌ 视图创建失败: ${viewName}`, error);
            this.viewStates.set(viewName, 'error');
            throw error;
        }
    }

    /**
     * 获取容器元素
     * @param {string} selector - 选择器
     * @param {string} fallback - 备用选择器
     * @returns {Element} 容器元素
     */
    getContainer(selector, fallback = null) {
        let container = document.querySelector(selector);
        
        if (!container && fallback) {
            container = document.querySelector(fallback);
        }
        
        if (!container && selector === '#app') {
            // 如果是主容器且不存在，创建一个
            container = document.createElement('div');
            container.id = 'app';
            document.body.appendChild(container);
        }
        
        return container;
    }

    /**
     * 初始化视图
     */
    async initializeViews() {
        for (const [viewName, viewInstance] of this.viewInstances) {
            try {
                if (viewInstance.onInitialize) {
                    await viewInstance.onInitialize();
                }
                this.viewStates.set(viewName, 'initialized');
                console.log(`✅ 视图初始化成功: ${viewName}`);
            } catch (error) {
                console.error(`❌ 视图初始化失败: ${viewName}`, error);
                this.viewStates.set(viewName, 'error');
            }
        }
    }

    /**
     * 初始化主视图
     */
    async initializeMainView() {
        // 确保主视图存在
        if (!this.viewInstances.has('examView')) {
            await this.createView('examView');
        }
        
        const examView = this.viewInstances.get('examView');
        if (examView && examView.render) {
            await examView.render();
        }
    }

    // ==================== 视图操作 ====================

    /**
     * 获取视图实例
     * @param {string} viewName - 视图名称
     * @returns {Object} 视图实例
     */
    getView(viewName) {
        return this.viewInstances.get(viewName);
    }

    /**
     * 显示视图
     * @param {string} viewName - 视图名称
     * @param {Object} data - 显示数据
     */
    async showView(viewName, data = {}) {
        const viewInstance = this.viewInstances.get(viewName);
        if (!viewInstance) {
            console.warn(`视图不存在: ${viewName}`);
            return;
        }

        try {
            if (viewInstance.show) {
                await viewInstance.show(data);
            } else if (viewInstance.container) {
                viewInstance.container.style.display = 'block';
            }
            
            this.viewStates.set(viewName, 'visible');
            
            this.eventBus.emit('viewManager.viewShown', {
                viewName,
                data,
                timestamp: new Date().toISOString()
            });
            
        } catch (error) {
            console.error(`显示视图失败: ${viewName}`, error);
        }
    }

    /**
     * 隐藏视图
     * @param {string} viewName - 视图名称
     */
    async hideView(viewName) {
        const viewInstance = this.viewInstances.get(viewName);
        if (!viewInstance) {
            console.warn(`视图不存在: ${viewName}`);
            return;
        }

        try {
            if (viewInstance.hide) {
                await viewInstance.hide();
            } else if (viewInstance.container) {
                viewInstance.container.style.display = 'none';
            }
            
            this.viewStates.set(viewName, 'hidden');
            
            this.eventBus.emit('viewManager.viewHidden', {
                viewName,
                timestamp: new Date().toISOString()
            });
            
        } catch (error) {
            console.error(`隐藏视图失败: ${viewName}`, error);
        }
    }

    /**
     * 销毁视图
     * @param {string} viewName - 视图名称
     */
    async destroyView(viewName) {
        const viewInstance = this.viewInstances.get(viewName);
        if (!viewInstance) {
            console.warn(`视图不存在: ${viewName}`);
            return;
        }

        try {
            // 调用视图的销毁方法
            if (viewInstance.destroy) {
                await viewInstance.destroy();
            } else if (viewInstance.onDestroy) {
                await viewInstance.onDestroy();
            }
            
            // 从管理器中移除
            this.viewInstances.delete(viewName);
            this.viewStates.delete(viewName);
            
            console.log(`✅ 视图销毁成功: ${viewName}`);
            
            this.eventBus.emit('viewManager.viewDestroyed', {
                viewName,
                timestamp: new Date().toISOString()
            });
            
        } catch (error) {
            console.error(`销毁视图失败: ${viewName}`, error);
        }
    }

    /**
     * 销毁所有视图
     */
    async destroyAllViews() {
        const viewNames = Array.from(this.viewInstances.keys());
        
        for (const viewName of viewNames) {
            await this.destroyView(viewName);
        }
    }

    // ==================== 事件处理 ====================

    /**
     * 处理视图创建请求
     */
    async handleViewCreateRequest(data) {
        const { viewName, options } = data;
        try {
            await this.createView(viewName, options);
        } catch (error) {
            console.error('处理视图创建请求失败:', error);
        }
    }

    /**
     * 处理视图销毁请求
     */
    async handleViewDestroyRequest(data) {
        const { viewName } = data;
        await this.destroyView(viewName);
    }

    /**
     * 处理视图显示请求
     */
    async handleViewShowRequest(data) {
        const { viewName, viewData } = data;
        await this.showView(viewName, viewData);
    }

    /**
     * 处理视图隐藏请求
     */
    async handleViewHideRequest(data) {
        const { viewName } = data;
        await this.hideView(viewName);
    }

    // ==================== 工具方法 ====================

    /**
     * 获取所有视图状态
     * @returns {Object} 视图状态
     */
    getViewStates() {
        return Object.fromEntries(this.viewStates);
    }

    /**
     * 获取视图列表
     * @returns {Array} 视图列表
     */
    getViewList() {
        return Array.from(this.viewInstances.keys());
    }

    /**
     * 检查视图是否存在
     * @param {string} viewName - 视图名称
     * @returns {boolean} 是否存在
     */
    hasView(viewName) {
        return this.viewInstances.has(viewName);
    }

    /**
     * 检查视图是否可见
     * @param {string} viewName - 视图名称
     * @returns {boolean} 是否可见
     */
    isViewVisible(viewName) {
        return this.viewStates.get(viewName) === 'visible';
    }

    /**
     * 获取健康状态
     * @returns {Object} 健康状态
     */
    getHealthStatus() {
        const totalViews = this.viewInstances.size;
        const errorViews = Array.from(this.viewStates.values()).filter(state => state === 'error').length;
        
        return {
            isHealthy: errorViews === 0,
            totalViews,
            errorViews,
            viewStates: this.getViewStates()
        };
    }

    /**
     * 检查是否健康
     * @returns {boolean} 是否健康
     */
    isHealthy() {
        return this.getHealthStatus().isHealthy;
    }
}

// 导出ViewManager类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ViewManager;
} else {
    window.ViewManager = ViewManager;
}
