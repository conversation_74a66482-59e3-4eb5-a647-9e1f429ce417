/**
 * 日志管理器
 * 提供统一的日志记录功能
 */
class Logger {
    constructor() {
        this.logLevel = 'info'; // debug, info, warn, error
        this.enableConsole = true;
        this.logs = [];
    }

    /**
     * 设置日志级别
     * @param {string} level - 日志级别
     */
    setLevel(level) {
        this.logLevel = level;
    }

    /**
     * 记录调试信息
     * @param {string} message - 日志消息
     * @param {any} data - 附加数据
     */
    debug(message, data = null) {
        this._log('debug', message, data);
    }

    /**
     * 记录信息
     * @param {string} message - 日志消息
     * @param {any} data - 附加数据
     */
    info(message, data = null) {
        this._log('info', message, data);
    }

    /**
     * 记录警告
     * @param {string} message - 日志消息
     * @param {any} data - 附加数据
     */
    warn(message, data = null) {
        this._log('warn', message, data);
    }

    /**
     * 记录错误
     * @param {string} message - 日志消息
     * @param {any} data - 附加数据
     */
    error(message, data = null) {
        this._log('error', message, data);
    }

    /**
     * 内部日志记录方法
     * @private
     */
    _log(level, message, data) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            data
        };

        this.logs.push(logEntry);

        if (this.enableConsole && this._shouldLog(level)) {
            const emoji = this._getEmoji(level);
            const formattedMessage = `${emoji} ${message}`;
            
            switch (level) {
                case 'debug':
                    console.debug(formattedMessage, data || '');
                    break;
                case 'info':
                    console.info(formattedMessage, data || '');
                    break;
                case 'warn':
                    console.warn(formattedMessage, data || '');
                    break;
                case 'error':
                    console.error(formattedMessage, data || '');
                    break;
            }
        }
    }

    /**
     * 判断是否应该记录该级别的日志
     * @private
     */
    _shouldLog(level) {
        const levels = ['debug', 'info', 'warn', 'error'];
        const currentLevelIndex = levels.indexOf(this.logLevel);
        const logLevelIndex = levels.indexOf(level);
        return logLevelIndex >= currentLevelIndex;
    }

    /**
     * 获取日志级别对应的emoji
     * @private
     */
    _getEmoji(level) {
        const emojis = {
            debug: '🐛',
            info: '📝',
            warn: '⚠️',
            error: '❌'
        };
        return emojis[level] || '📝';
    }

    /**
     * 获取所有日志
     */
    getLogs() {
        return this.logs;
    }

    /**
     * 清空日志
     */
    clear() {
        this.logs = [];
    }

    /**
     * 导出日志为JSON字符串
     */
    export() {
        return JSON.stringify(this.logs, null, 2);
    }
}

// 创建全局日志实例
if (!window.logger) {
    window.logger = new Logger();
}

// 导出Logger类
window.Logger = Logger;
