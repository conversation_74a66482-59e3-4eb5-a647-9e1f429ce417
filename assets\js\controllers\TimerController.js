/**
 * TimerController - 计时器控制器
 * 负责计时器生命周期管理、时间显示更新、时间到达处理
 */

class TimerController extends BaseController {
    constructor() {
        super();
        
        // 控制器状态
        this.state = {
            isRunning: false,
            isPaused: false,
            duration: 0,
            remainingTime: 0,
            startTime: null,
            pauseTime: null,
            currentStepNumber: null,
            timerMode: 'countdown' // countdown, countup
        };

        // 计时器实例
        this.timer = null;
        this.tickInterval = 1000; // 1秒
        
        // 时间警告配置
        this.warningConfig = {
            oneMinute: 60,
            thirtySeconds: 30,
            tenSeconds: 10,
            timeUp: 0
        };

        // 警告状态
        this.warningStates = {
            oneMinuteWarned: false,
            thirtySecondsWarned: false,
            tenSecondsWarned: false,
            timeUpWarned: false
        };
    }

    /**
     * 初始化控制器
     */
    async onInitialize() {
        // 监听全局事件
        this.setupGlobalListeners();

        this.log('info', 'TimerController 初始化完成');
    }

    /**
     * 设置全局监听器
     */
    setupGlobalListeners() {
        // 监听考试控制器事件
        this.listenGlobal('ExamController.resetTimer', (data) => {
            this.resetTimer(data.duration, data.stepNumber);
        });

        this.listenGlobal('ExamController.stopTimer', () => {
            this.stopTimer();
        });

        this.listenGlobal('ExamController.stopAllTimers', () => {
            this.stopTimer();
        });

        // 监听题目控制器事件
        this.listenGlobal('QuestionController.autoStartTimer', (data) => {
            this.startTimer();
        });

        // 监听UI事件
        this.listenGlobal('UI.startTimerRequested', () => {
            this.startTimer();
        });

        this.listenGlobal('UI.pauseTimerRequested', () => {
            this.pauseTimer();
        });

        this.listenGlobal('UI.resumeTimerRequested', () => {
            this.resumeTimer();
        });

        this.listenGlobal('UI.resetTimerRequested', () => {
            this.resetTimer();
        });

        this.listenGlobal('UI.stopTimerRequested', () => {
            this.stopTimer();
        });
    }

    // ==================== 计时器控制 ====================

    /**
     * 启动计时器
     * @param {number} duration - 计时时长（秒）
     * @param {number} stepNumber - 步骤编号
     * @returns {Promise<Object>} 启动结果
     */
    async startTimer(duration = null, stepNumber = null) {
        return this.executeOperation(async () => {
            // 如果已经在运行，先停止
            if (this.state.isRunning) {
                this.stopTimer();
            }

            // 设置时长和步骤
            if (duration !== null) {
                this.updateState({ 
                    duration, 
                    remainingTime: duration,
                    currentStepNumber: stepNumber 
                });
            }

            // 验证启动条件
            const conditions = [
                () => this.state.duration > 0,
                () => this.state.remainingTime > 0,
                () => !this.state.isRunning
            ];

            if (!this.validateConditions(conditions, 'startTimer')) {
                throw new Error('计时器启动条件不满足');
            }

            // 重置警告状态
            this.resetWarningStates();

            // 更新状态
            this.updateState({
                isRunning: true,
                isPaused: false,
                startTime: new Date().toISOString()
            });

            // 启动计时器
            this.timer = setInterval(() => {
                this.tick();
            }, this.tickInterval);

            // 触发启动事件
            this.emit('started', this.getTimerState());

            this.log('info', `计时器启动: ${this.state.duration}秒`);

            return this.getTimerState();
        }, 'startTimer');
    }

    /**
     * 暂停计时器
     * @returns {Promise<Object>} 暂停结果
     */
    async pauseTimer() {
        return this.executeOperation(async () => {
            if (!this.state.isRunning || this.state.isPaused) {
                throw new Error('计时器未运行或已暂停');
            }

            // 停止计时器
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }

            // 更新状态
            this.updateState({
                isPaused: true,
                pauseTime: new Date().toISOString()
            });

            // 触发暂停事件
            this.emit('paused', this.getTimerState());

            this.log('info', '计时器已暂停');

            return this.getTimerState();
        }, 'pauseTimer');
    }

    /**
     * 恢复计时器
     * @returns {Promise<Object>} 恢复结果
     */
    async resumeTimer() {
        return this.executeOperation(async () => {
            if (!this.state.isRunning || !this.state.isPaused) {
                throw new Error('计时器未暂停');
            }

            // 更新状态
            this.updateState({
                isPaused: false,
                pauseTime: null
            });

            // 重新启动计时器
            this.timer = setInterval(() => {
                this.tick();
            }, this.tickInterval);

            // 触发恢复事件
            this.emit('resumed', this.getTimerState());

            this.log('info', '计时器已恢复');

            return this.getTimerState();
        }, 'resumeTimer');
    }

    /**
     * 重置计时器
     * @param {number} newDuration - 新的时长（可选）
     * @param {number} stepNumber - 步骤编号（可选）
     * @returns {Promise<Object>} 重置结果
     */
    async resetTimer(newDuration = null, stepNumber = null) {
        return this.executeOperation(async () => {
            // 停止当前计时器
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }

            // 重置警告状态
            this.resetWarningStates();

            // 设置新时长
            const duration = newDuration !== null ? newDuration : this.state.duration;

            // 更新状态
            this.updateState({
                isRunning: false,
                isPaused: false,
                duration,
                remainingTime: duration,
                startTime: null,
                pauseTime: null,
                currentStepNumber: stepNumber !== null ? stepNumber : this.state.currentStepNumber
            });

            // 触发重置事件
            this.emit('reset', this.getTimerState());

            this.log('info', `计时器已重置: ${duration}秒`);

            return this.getTimerState();
        }, 'resetTimer');
    }

    /**
     * 停止计时器
     * @returns {Promise<Object>} 停止结果
     */
    async stopTimer() {
        return this.executeOperation(async () => {
            // 停止计时器
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }

            // 重置警告状态
            this.resetWarningStates();

            // 更新状态
            this.updateState({
                isRunning: false,
                isPaused: false,
                startTime: null,
                pauseTime: null
            });

            // 触发停止事件
            this.emit('stopped', this.getTimerState());

            this.log('info', '计时器已停止');

            return this.getTimerState();
        }, 'stopTimer');
    }

    // ==================== 计时器核心逻辑 ====================

    /**
     * 计时器滴答
     */
    tick() {
        if (!this.state.isRunning || this.state.isPaused) {
            return;
        }

        if (this.state.timerMode === 'countdown') {
            // 倒计时模式
            if (this.state.remainingTime > 0) {
                this.updateState({ remainingTime: this.state.remainingTime - 1 });
                
                // 检查时间警告
                this.checkTimeWarnings();
                
                // 触发滴答事件
                this.emit('tick', this.getTimerState());
            } else {
                // 时间到达
                this.handleTimeUp();
            }
        } else {
            // 正计时模式
            const elapsed = this.state.duration - this.state.remainingTime + 1;
            this.updateState({ remainingTime: this.state.duration - elapsed });
            
            // 触发滴答事件
            this.emit('tick', this.getTimerState());
        }
    }

    /**
     * 处理时间到达
     */
    handleTimeUp() {
        // 停止计时器
        this.stopTimer();

        // 更新状态
        this.updateState({ remainingTime: 0 });

        // 触发时间到达事件
        this.emit('timeUp', {
            stepNumber: this.state.currentStepNumber,
            duration: this.state.duration,
            timestamp: new Date().toISOString()
        });

        this.log('warn', '时间到达！');
    }

    /**
     * 检查时间警告
     */
    checkTimeWarnings() {
        const remaining = this.state.remainingTime;

        // 1分钟警告
        if (remaining <= this.warningConfig.oneMinute && !this.warningStates.oneMinuteWarned) {
            this.warningStates.oneMinuteWarned = true;
            this.emit('timeWarning', {
                type: 'oneMinute',
                remainingTime: remaining,
                message: '还剩1分钟'
            });
        }

        // 30秒警告
        if (remaining <= this.warningConfig.thirtySeconds && !this.warningStates.thirtySecondsWarned) {
            this.warningStates.thirtySecondsWarned = true;
            this.emit('timeWarning', {
                type: 'thirtySeconds',
                remainingTime: remaining,
                message: '还剩30秒'
            });
        }

        // 10秒警告
        if (remaining <= this.warningConfig.tenSeconds && !this.warningStates.tenSecondsWarned) {
            this.warningStates.tenSecondsWarned = true;
            this.emit('timeWarning', {
                type: 'tenSeconds',
                remainingTime: remaining,
                message: '还剩10秒'
            });
        }
    }

    /**
     * 重置警告状态
     */
    resetWarningStates() {
        this.warningStates = {
            oneMinuteWarned: false,
            thirtySecondsWarned: false,
            tenSecondsWarned: false,
            timeUpWarned: false
        };
    }

    // ==================== 状态管理 ====================

    /**
     * 获取计时器状态
     * @returns {Object} 计时器状态
     */
    getTimerState() {
        return {
            isRunning: this.state.isRunning,
            isPaused: this.state.isPaused,
            duration: this.state.duration,
            remainingTime: this.state.remainingTime,
            elapsedTime: this.state.duration - this.state.remainingTime,
            startTime: this.state.startTime,
            pauseTime: this.state.pauseTime,
            currentStepNumber: this.state.currentStepNumber,
            timerMode: this.state.timerMode,
            progressPercentage: this.getProgressPercentage(),
            formattedTime: this.formatTime(this.state.remainingTime),
            formattedElapsed: this.formatTime(this.state.duration - this.state.remainingTime),
            status: this.getTimerStatus()
        };
    }

    /**
     * 获取计时器状态文本
     * @returns {string} 状态文本
     */
    getTimerStatus() {
        if (!this.state.isRunning) {
            return 'stopped';
        } else if (this.state.isPaused) {
            return 'paused';
        } else {
            return 'running';
        }
    }

    /**
     * 获取进度百分比
     * @returns {number} 进度百分比 (0-100)
     */
    getProgressPercentage() {
        if (this.state.duration === 0) {
            return 0;
        }
        
        const elapsed = this.state.duration - this.state.remainingTime;
        return Math.round((elapsed / this.state.duration) * 100);
    }

    /**
     * 格式化时间显示
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(seconds) {
        if (seconds < 0) seconds = 0;
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }

    /**
     * 设置计时器时长
     * @param {number} duration - 时长（秒）
     * @returns {Promise<Object>} 设置结果
     */
    async setDuration(duration) {
        return this.executeOperation(async () => {
            if (duration <= 0) {
                throw new Error('时长必须大于0');
            }

            // 如果计时器正在运行，先停止
            if (this.state.isRunning) {
                await this.stopTimer();
            }

            // 更新时长
            this.updateState({
                duration,
                remainingTime: duration
            });

            // 触发时长变更事件
            this.emit('durationChanged', this.getTimerState());

            return this.getTimerState();
        }, 'setDuration');
    }

    /**
     * 设置计时器模式
     * @param {string} mode - 计时模式 ('countdown' | 'countup')
     */
    setTimerMode(mode) {
        if (!['countdown', 'countup'].includes(mode)) {
            throw new Error('无效的计时器模式');
        }

        this.updateState({ timerMode: mode });
        this.emit('modeChanged', { mode, state: this.getTimerState() });
    }

    /**
     * 配置时间警告
     * @param {Object} config - 警告配置
     */
    configureWarnings(config) {
        this.warningConfig = { ...this.warningConfig, ...config };
        this.emit('warningConfigChanged', this.warningConfig);
    }

    // ==================== 工具方法 ====================

    /**
     * 检查计时器是否健康
     * @returns {boolean} 是否健康
     */
    isTimerHealthy() {
        return !this.hasErrors() && (
            !this.state.isRunning || 
            (this.state.isRunning && this.timer !== null)
        );
    }

    /**
     * 获取计时器统计信息
     * @returns {Object} 统计信息
     */
    getTimerStats() {
        return {
            isRunning: this.state.isRunning,
            isPaused: this.state.isPaused,
            duration: this.state.duration,
            remainingTime: this.state.remainingTime,
            elapsedTime: this.state.duration - this.state.remainingTime,
            progressPercentage: this.getProgressPercentage(),
            currentStepNumber: this.state.currentStepNumber,
            timerMode: this.state.timerMode,
            warningStates: { ...this.warningStates }
        };
    }

    /**
     * 销毁控制器
     */
    onDestroy() {
        // 停止计时器
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        // 重置状态
        this.resetWarningStates();

        this.log('info', 'TimerController 已销毁');
    }
}

// 导出TimerController类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TimerController;
} else {
    window.TimerController = TimerController;
}
