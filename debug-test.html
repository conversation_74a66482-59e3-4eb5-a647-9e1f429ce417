<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript 调试测试</title>
</head>
<body>
    <h1>JavaScript 类加载测试</h1>
    <div id="test-results"></div>

    <!-- 按照正确顺序加载脚本 -->
    <!-- 工具类 -->
    <script src="assets/js/utils/utils.js"></script>
    <script src="assets/js/utils/logger.js"></script>
    <script src="assets/js/utils/baseClass.js"></script>

    <!-- 核心类 -->
    <script src="assets/js/core/database.js"></script>
    <script src="assets/js/core/timer.js"></script>
    <script src="assets/js/core/draggableTimer.js"></script>

    <!-- UI类 -->
    <script src="assets/js/ui/ui.js"></script>
    <script src="assets/js/ui/eventManager.js"></script>

    <!-- 管理器类 -->
    <script src="assets/js/buttonManager.js"></script>

    <!-- 业务逻辑类 -->
    <script src="assets/js/business/Student.js"></script>
    <script src="assets/js/business/ExamStep.js"></script>
    <script src="assets/js/business/Question.js"></script>
    <script src="assets/js/business/Settings.js"></script>

    <!-- 主控制器 -->
    <script src="assets/js/core/ExamSystem.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('test-results');
            const results = [];

            // 测试各个类是否正确加载
            const classesToTest = [
                'Logger',
                'BaseClass',
                'Database',
                'Timer',
                'DraggableTimer',
                'UI',
                'EventManager',
                'ButtonManager',
                'Student',
                'ExamStep',
                'Question',
                'Settings',
                'ExamSystem'
            ];

            classesToTest.forEach(className => {
                if (window[className]) {
                    results.push(`✅ ${className} - 已加载`);
                    
                    // 测试实例化
                    try {
                        if (className === 'ExamSystem') {
                            const instance = new window[className]();
                            results.push(`✅ ${className} - 可以实例化`);
                            
                            // 测试log方法
                            if (typeof instance.log === 'function') {
                                results.push(`✅ ${className} - log方法可用`);
                                instance.log('测试日志消息');
                            } else {
                                results.push(`❌ ${className} - log方法不可用`);
                            }
                        }
                    } catch (error) {
                        results.push(`❌ ${className} - 实例化失败: ${error.message}`);
                    }
                } else {
                    results.push(`❌ ${className} - 未加载`);
                }
            });

            // 测试全局实例
            if (window.logger) {
                results.push('✅ window.logger - 全局日志实例可用');
            } else {
                results.push('❌ window.logger - 全局日志实例不可用');
            }

            // 显示结果
            resultsDiv.innerHTML = '<pre>' + results.join('\n') + '</pre>';
            
            console.log('=== 类加载测试结果 ===');
            results.forEach(result => console.log(result));
        });
    </script>
</body>
</html>
