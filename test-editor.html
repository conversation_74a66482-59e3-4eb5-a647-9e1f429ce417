<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库编辑器测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 题库编辑器功能测试</h1>
        <p>测试新版MVC框架的题库编辑器各项功能</p>

        <!-- API测试 -->
        <div class="test-section">
            <h3>📡 API接口测试</h3>
            <button class="btn btn-primary" onclick="testHealthAPI()">测试健康检查</button>
            <button class="btn btn-primary" onclick="testQuestionsAPI()">测试题目接口</button>
            <button class="btn btn-success" onclick="testCreateQuestion()">测试创建题目</button>
            <button class="btn btn-warning" onclick="testUpdateQuestion()">测试更新题目</button>
            <div id="apiResults"></div>
        </div>

        <!-- 前端组件测试 -->
        <div class="test-section">
            <h3>🎨 前端组件测试</h3>
            <button class="btn btn-primary" onclick="testModels()">测试数据模型</button>
            <button class="btn btn-primary" onclick="testServices()">测试服务层</button>
            <button class="btn btn-success" onclick="testViewComponents()">测试视图组件</button>
            <div id="componentResults"></div>
        </div>

        <!-- 功能集成测试 -->
        <div class="test-section">
            <h3>🔧 功能集成测试</h3>
            <button class="btn btn-primary" onclick="testFullWorkflow()">完整工作流测试</button>
            <button class="btn btn-success" onclick="openNewEditor()">打开新版编辑器</button>
            <button class="btn btn-warning" onclick="compareWithOld()">与旧版对比</button>
            <div id="integrationResults"></div>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
            <h3>📋 测试日志</h3>
            <button class="btn btn-warning" onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log-container"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            container.innerHTML += `<div class="result ${className}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }

        // API测试
        async function testHealthAPI() {
            log('开始测试健康检查API...');
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                if (data.success) {
                    showResult('apiResults', '✅ 健康检查API正常: ' + JSON.stringify(data), 'success');
                    log('健康检查API测试成功', 'success');
                } else {
                    showResult('apiResults', '❌ 健康检查API失败: ' + JSON.stringify(data), 'error');
                    log('健康检查API测试失败', 'error');
                }
            } catch (error) {
                showResult('apiResults', '❌ 健康检查API错误: ' + error.message, 'error');
                log('健康检查API测试错误: ' + error.message, 'error');
            }
        }

        async function testQuestionsAPI() {
            log('开始测试题目API...');
            try {
                // 测试获取翻译题
                const translationResponse = await fetch('/api/questions?type=translation');
                const translationData = await translationResponse.json();
                
                if (translationData.success) {
                    showResult('apiResults', `✅ 翻译题API正常，获取到${translationData.questions.length}道题目`, 'success');
                    log(`翻译题API测试成功，获取${translationData.questions.length}道题目`, 'success');
                } else {
                    showResult('apiResults', '❌ 翻译题API失败: ' + JSON.stringify(translationData), 'error');
                    log('翻译题API测试失败', 'error');
                }

                // 测试获取专业题
                const professionalResponse = await fetch('/api/questions?type=professional&subject=computer');
                const professionalData = await professionalResponse.json();
                
                if (professionalData.success) {
                    showResult('apiResults', `✅ 专业题API正常，获取到${professionalData.questions.length}道题目`, 'success');
                    log(`专业题API测试成功，获取${professionalData.questions.length}道题目`, 'success');
                } else {
                    showResult('apiResults', '❌ 专业题API失败: ' + JSON.stringify(professionalData), 'error');
                    log('专业题API测试失败', 'error');
                }
            } catch (error) {
                showResult('apiResults', '❌ 题目API错误: ' + error.message, 'error');
                log('题目API测试错误: ' + error.message, 'error');
            }
        }

        async function testCreateQuestion() {
            log('开始测试创建题目API...');
            try {
                const newQuestion = {
                    type: 'translation',
                    index: 999,
                    title: '测试翻译题',
                    content: 'This is a test translation question.',
                    answer: '这是一个测试翻译题目。',
                    difficulty: 'easy',
                    isActive: true,
                    tags: ['测试'],
                    notes: '这是一个测试题目'
                };

                const response = await fetch('/api/questions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(newQuestion)
                });

                const data = await response.json();
                if (data.success) {
                    showResult('apiResults', '✅ 创建题目API正常: ' + data.message, 'success');
                    log('创建题目API测试成功', 'success');
                } else {
                    showResult('apiResults', '❌ 创建题目API失败: ' + data.message, 'error');
                    log('创建题目API测试失败', 'error');
                }
            } catch (error) {
                showResult('apiResults', '❌ 创建题目API错误: ' + error.message, 'error');
                log('创建题目API测试错误: ' + error.message, 'error');
            }
        }

        async function testUpdateQuestion() {
            log('开始测试更新题目API...');
            try {
                const updateData = {
                    title: '更新后的测试翻译题',
                    content: 'This is an updated test translation question.',
                    difficulty: 'medium'
                };

                const response = await fetch('/api/questions/translation_999', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const data = await response.json();
                if (data.success) {
                    showResult('apiResults', '✅ 更新题目API正常: ' + data.message, 'success');
                    log('更新题目API测试成功', 'success');
                } else {
                    showResult('apiResults', '❌ 更新题目API失败: ' + data.message, 'error');
                    log('更新题目API测试失败', 'error');
                }
            } catch (error) {
                showResult('apiResults', '❌ 更新题目API错误: ' + error.message, 'error');
                log('更新题目API测试错误: ' + error.message, 'error');
            }
        }

        // 前端组件测试
        function testModels() {
            log('开始测试数据模型...');
            try {
                // 这里需要加载相应的模型文件才能测试
                showResult('componentResults', '⚠️ 数据模型测试需要加载相应的JS文件', 'info');
                log('数据模型测试需要完整的前端环境', 'info');
            } catch (error) {
                showResult('componentResults', '❌ 数据模型测试错误: ' + error.message, 'error');
                log('数据模型测试错误: ' + error.message, 'error');
            }
        }

        function testServices() {
            log('开始测试服务层...');
            try {
                showResult('componentResults', '⚠️ 服务层测试需要加载相应的JS文件', 'info');
                log('服务层测试需要完整的前端环境', 'info');
            } catch (error) {
                showResult('componentResults', '❌ 服务层测试错误: ' + error.message, 'error');
                log('服务层测试错误: ' + error.message, 'error');
            }
        }

        function testViewComponents() {
            log('开始测试视图组件...');
            try {
                showResult('componentResults', '⚠️ 视图组件测试需要加载相应的JS文件', 'info');
                log('视图组件测试需要完整的前端环境', 'info');
            } catch (error) {
                showResult('componentResults', '❌ 视图组件测试错误: ' + error.message, 'error');
                log('视图组件测试错误: ' + error.message, 'error');
            }
        }

        // 功能集成测试
        async function testFullWorkflow() {
            log('开始完整工作流测试...');
            
            // 1. 测试API健康检查
            await testHealthAPI();
            
            // 2. 测试获取题目列表
            await testQuestionsAPI();
            
            // 3. 测试创建题目
            await testCreateQuestion();
            
            // 4. 测试更新题目
            await testUpdateQuestion();
            
            showResult('integrationResults', '✅ 完整工作流测试完成', 'success');
            log('完整工作流测试完成', 'success');
        }

        function openNewEditor() {
            log('尝试打开新版题库编辑器...');
            try {
                window.open('/question-editor.html', '_blank');
                showResult('integrationResults', '✅ 新版编辑器页面已在新窗口打开', 'success');
                log('新版编辑器页面已打开', 'success');
            } catch (error) {
                showResult('integrationResults', '❌ 打开新版编辑器失败: ' + error.message, 'error');
                log('打开新版编辑器失败: ' + error.message, 'error');
            }
        }

        function compareWithOld() {
            log('对比新旧版本功能...');
            const comparison = `
            功能对比：
            ✅ 新版：MVC架构，模块化设计
            ❌ 旧版：jQuery混合架构
            
            ✅ 新版：响应式三栏布局
            ❌ 旧版：固定表格布局
            
            ✅ 新版：实时搜索和过滤
            ❌ 旧版：基础搜索功能
            
            ✅ 新版：完整的API接口
            ❌ 旧版：有限的后端支持
            
            ✅ 新版：现代化UI设计
            ❌ 旧版：传统界面设计
            `;
            
            showResult('integrationResults', comparison, 'info');
            log('功能对比完成', 'info');
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('题库编辑器测试页面已加载');
            log('点击上方按钮开始测试各项功能');
        });
    </script>
</body>
</html>
