# 研究生复试系统 - 数据库接口文档

## 数据库架构

本系统使用SQLite数据库，采用标准的关系型数据库设计，确保数据一致性和完整性。

## 数据表结构

### 1. students (学生表)
```sql
CREATE TABLE students (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_number VARCHAR(20) UNIQUE NOT NULL,
    current_step INTEGER DEFAULT 1,
    start_time DATETIME,
    status VARCHAR(20) DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明：**
- `id`: 主键，自增
- `student_number`: 学生编号，唯一标识
- `current_step`: 当前考试步骤 (1-5)
- `start_time`: 考试开始时间
- `status`: 考试状态 ('active', 'completed', 'paused')
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 2. exam_records (考试记录表)
```sql
CREATE TABLE exam_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    student_number VARCHAR(20) NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    current_step INTEGER DEFAULT 1,
    total_duration INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'in_progress',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id)
);
```

**字段说明：**
- `id`: 主键，自增
- `student_id`: 学生ID，外键
- `student_number`: 学生编号（冗余字段，便于查询）
- `start_time`: 考试开始时间
- `end_time`: 考试结束时间
- `current_step`: 当前步骤
- `total_duration`: 总用时（秒）
- `status`: 记录状态 ('in_progress', 'completed', 'cancelled')

### 3. step_records (步骤记录表)
```sql
CREATE TABLE step_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    exam_record_id INTEGER NOT NULL,
    step_number INTEGER NOT NULL,
    step_name VARCHAR(100) NOT NULL,
    start_time DATETIME,
    end_time DATETIME,
    duration INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending',
    questions_data TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (exam_record_id) REFERENCES exam_records(id),
    UNIQUE(exam_record_id, step_number)
);
```

**字段说明：**
- `id`: 主键，自增
- `exam_record_id`: 考试记录ID，外键
- `step_number`: 步骤编号 (1-5)
- `step_name`: 步骤名称
- `start_time`: 步骤开始时间
- `end_time`: 步骤结束时间
- `duration`: 步骤用时（秒）
- `status`: 步骤状态 ('pending', 'active', 'completed', 'skipped')
- `questions_data`: 题目数据（JSON格式）

### 4. questions (题目表)
```sql
CREATE TABLE questions (
    id VARCHAR(50) PRIMARY KEY,
    type VARCHAR(20) NOT NULL,
    question_index INTEGER NOT NULL,
    title VARCHAR(200),
    question_data TEXT NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(type, question_index)
);
```

**字段说明：**
- `id`: 主键，题目唯一标识
- `type`: 题目类型 ('translation', 'professional')
- `question_index`: 题目编号
- `title`: 题目标题
- `question_data`: 题目内容（JSON格式）
- `is_active`: 是否启用

### 5. question_usage (题目使用记录表)
```sql
CREATE TABLE question_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    question_id VARCHAR(50) NOT NULL,
    question_type VARCHAR(20) NOT NULL,
    question_index INTEGER NOT NULL,
    student_id INTEGER NOT NULL,
    student_number VARCHAR(20) NOT NULL,
    used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    exam_record_id INTEGER,
    step_number INTEGER,
    FOREIGN KEY (question_id) REFERENCES questions(id),
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (exam_record_id) REFERENCES exam_records(id)
);
```

**字段说明：**
- `id`: 主键，自增
- `question_id`: 题目ID，外键
- `question_type`: 题目类型
- `question_index`: 题目编号
- `student_id`: 使用学生ID
- `student_number`: 学生编号
- `used_at`: 使用时间
- `exam_record_id`: 关联的考试记录
- `step_number`: 使用的步骤

### 6. settings (系统设置表)
```sql
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category VARCHAR(50) NOT NULL,
    key VARCHAR(100) NOT NULL,
    value TEXT NOT NULL,
    data_type VARCHAR(20) DEFAULT 'string',
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category, key)
);
```

**字段说明：**
- `id`: 主键，自增
- `category`: 设置分类 ('time', 'system', 'ui')
- `key`: 设置键名
- `value`: 设置值
- `data_type`: 数据类型 ('string', 'number', 'boolean', 'json')
- `description`: 设置描述

## 数据库接口API

### 基础CRUD操作

#### 1. 创建记录
```typescript
interface CreateRequest<T> {
  table: string;
  data: T;
}

interface CreateResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}
```

#### 2. 查询记录
```typescript
interface QueryRequest {
  table: string;
  conditions?: Record<string, any>;
  orderBy?: string;
  limit?: number;
  offset?: number;
}

interface QueryResponse<T> {
  success: boolean;
  data?: T[];
  total?: number;
  error?: string;
}
```

#### 3. 更新记录
```typescript
interface UpdateRequest<T> {
  table: string;
  id: string | number;
  data: Partial<T>;
}

interface UpdateResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}
```

#### 4. 删除记录
```typescript
interface DeleteRequest {
  table: string;
  id: string | number;
}

interface DeleteResponse {
  success: boolean;
  error?: string;
}
```

### 专用数据库操作

#### 学生相关操作

##### 创建学生
```sql
-- API: POST /api/students
INSERT INTO students (student_number, start_time, status) 
VALUES (?, CURRENT_TIMESTAMP, 'active');
```

##### 获取学生信息
```sql
-- API: GET /api/students/:studentNumber
SELECT * FROM students WHERE student_number = ?;
```

##### 更新学生状态
```sql
-- API: PUT /api/students/:studentNumber
UPDATE students 
SET current_step = ?, status = ?, updated_at = CURRENT_TIMESTAMP 
WHERE student_number = ?;
```

#### 考试记录操作

##### 创建考试记录
```sql
-- API: POST /api/exam-records
INSERT INTO exam_records (student_id, student_number, start_time, status)
VALUES (?, ?, CURRENT_TIMESTAMP, 'in_progress');
```

##### 获取考试记录列表
```sql
-- API: GET /api/exam-records
SELECT er.*, s.student_number 
FROM exam_records er
JOIN students s ON er.student_id = s.id
ORDER BY er.created_at DESC;
```

##### 获取学生考试记录
```sql
-- API: GET /api/exam-records/student/:studentNumber
SELECT er.*, sr.step_number, sr.step_name, sr.duration, sr.status as step_status
FROM exam_records er
LEFT JOIN step_records sr ON er.id = sr.exam_record_id
JOIN students s ON er.student_id = s.id
WHERE s.student_number = ?
ORDER BY sr.step_number;
```

#### 步骤记录操作

##### 创建步骤记录
```sql
-- API: POST /api/step-records
INSERT INTO step_records (exam_record_id, step_number, step_name, start_time, status)
VALUES (?, ?, ?, CURRENT_TIMESTAMP, 'active');
```

##### 完成步骤记录
```sql
-- API: PUT /api/step-records/:id/complete
UPDATE step_records 
SET end_time = CURRENT_TIMESTAMP, 
    duration = (strftime('%s', CURRENT_TIMESTAMP) - strftime('%s', start_time)),
    status = 'completed',
    questions_data = ?
WHERE id = ?;
```

#### 题目相关操作

##### 获取题目列表
```sql
-- API: GET /api/questions/:type
SELECT * FROM questions 
WHERE type = ? AND is_active = 1 
ORDER BY question_index;
```

##### 获取指定题目
```sql
-- API: GET /api/questions/:type/:index
SELECT * FROM questions 
WHERE type = ? AND question_index = ? AND is_active = 1;
```

##### 获取已使用题目
```sql
-- API: GET /api/questions/:type/used
SELECT DISTINCT question_index 
FROM question_usage 
WHERE question_type = ?
ORDER BY question_index;
```

##### 标记题目为已使用
```sql
-- API: POST /api/questions/mark-used
INSERT INTO question_usage (
    question_id, question_type, question_index, 
    student_id, student_number, exam_record_id, step_number
) VALUES (?, ?, ?, ?, ?, ?, ?);
```

##### 获取题目使用统计
```sql
-- API: GET /api/questions/:type/stats
SELECT 
    COUNT(*) as total,
    COUNT(DISTINCT qu.question_index) as used,
    (COUNT(*) - COUNT(DISTINCT qu.question_index)) as available
FROM questions q
LEFT JOIN question_usage qu ON q.id = qu.question_id
WHERE q.type = ? AND q.is_active = 1;
```

#### 设置相关操作

##### 获取设置
```sql
-- API: GET /api/settings
SELECT category, key, value, data_type FROM settings;
```

##### 获取分类设置
```sql
-- API: GET /api/settings/:category
SELECT key, value, data_type FROM settings WHERE category = ?;
```

##### 更新设置
```sql
-- API: PUT /api/settings/:category/:key
INSERT OR REPLACE INTO settings (category, key, value, data_type, updated_at)
VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP);
```

## 数据库初始化

### 初始化脚本
```sql
-- 创建所有表
-- (表结构如上所示)

-- 插入默认设置
INSERT INTO settings (category, key, value, data_type, description) VALUES
('time', 'chineseTime', '2', 'number', '中文自我介绍时间（分钟）'),
('time', 'englishTime', '2', 'number', '英文自我介绍时间（分钟）'),
('time', 'translationTime', '5', 'number', '英文翻译时间（分钟）'),
('time', 'professionalTime', '10', 'number', '专业课问答时间（分钟）'),
('time', 'comprehensiveTime', '8', 'number', '综合面试时间（分钟）'),
('system', 'debugMode', 'false', 'boolean', '调试模式'),
('system', 'autoSave', 'true', 'boolean', '自动保存'),
('ui', 'theme', 'light', 'string', '界面主题'),
('ui', 'fontSize', 'medium', 'string', '字体大小');

-- 插入示例题目数据
INSERT INTO questions (id, type, question_index, title, question_data) VALUES
('translation_1', 'translation', 1, '翻译题1', '{"english": "Hello World", "chinese": "你好世界"}'),
('translation_2', 'translation', 2, '翻译题2', '{"english": "Good Morning", "chinese": "早上好"}'),
('translation_3', 'translation', 3, '翻译题3', '{"english": "Thank you", "chinese": "谢谢"}'),
('professional_1', 'professional', 1, '专业题1', '{"question": "请介绍一下你的专业背景"}'),
('professional_2', 'professional', 2, '专业题2', '{"question": "你对这个专业的理解"}'),
('professional_3', 'professional', 3, '专业题3', '{"question": "你的研究方向和兴趣"}');
```

## 数据库连接配置

### 连接参数
```typescript
interface DatabaseConfig {
  type: 'sqlite';
  database: string;
  synchronize: boolean;
  logging: boolean;
  entities: string[];
}

const config: DatabaseConfig = {
  type: 'sqlite',
  database: 'assets/data/interview_system.db',
  synchronize: false,
  logging: process.env.NODE_ENV === 'development',
  entities: ['src/models/*.ts']
};
```

### 连接池配置
```typescript
interface ConnectionPoolConfig {
  max: number;
  min: number;
  idle: number;
  acquire: number;
  evict: number;
}

const poolConfig: ConnectionPoolConfig = {
  max: 10,
  min: 2,
  idle: 30000,
  acquire: 60000,
  evict: 1000
};
```

## 数据备份与恢复

### 备份策略
```sql
-- 每日备份
.backup assets/data/backups/interview_system_$(date +%Y%m%d).db

-- 导出数据
.mode csv
.output assets/data/exports/students.csv
SELECT * FROM students;
.output assets/data/exports/exam_records.csv
SELECT * FROM exam_records;
```

### 恢复策略
```sql
-- 从备份恢复
.restore assets/data/backups/interview_system_20241209.db

-- 导入数据
.mode csv
.import assets/data/exports/students.csv students
```

## 性能优化

### 索引策略
```sql
-- 学生表索引
CREATE INDEX idx_students_number ON students(student_number);
CREATE INDEX idx_students_status ON students(status);

-- 考试记录索引
CREATE INDEX idx_exam_records_student ON exam_records(student_id);
CREATE INDEX idx_exam_records_time ON exam_records(start_time);

-- 题目使用记录索引
CREATE INDEX idx_question_usage_type ON question_usage(question_type);
CREATE INDEX idx_question_usage_student ON question_usage(student_number);
CREATE INDEX idx_question_usage_time ON question_usage(used_at);

-- 设置表索引
CREATE INDEX idx_settings_category ON settings(category);
```

### 查询优化
```sql
-- 使用EXPLAIN QUERY PLAN分析查询性能
EXPLAIN QUERY PLAN 
SELECT * FROM exam_records er
JOIN students s ON er.student_id = s.id
WHERE s.student_number = ?;

-- 使用合适的数据类型和约束
-- 避免SELECT *，只查询需要的字段
-- 使用LIMIT限制结果集大小
```

这个数据库接口文档定义了完整的数据结构和操作接口，确保数据的一致性和系统的可维护性。
