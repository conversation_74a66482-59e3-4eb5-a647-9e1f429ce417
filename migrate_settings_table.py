#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库迁移脚本 - 将settings表的时间字段从INTEGER改为REAL
"""

import sqlite3
import os

DATABASE = 'interview_system.db'

def migrate_settings_table():
    """迁移settings表，将时间字段从INTEGER改为REAL"""
    
    if not os.path.exists(DATABASE):
        print(f"数据库文件 {DATABASE} 不存在，无需迁移")
        return
    
    print(f"开始迁移数据库 {DATABASE} 的settings表...")
    
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # 检查settings表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'")
        if not cursor.fetchone():
            print("settings表不存在，无需迁移")
            conn.close()
            return
        
        # 获取当前settings表的数据
        cursor.execute("SELECT * FROM settings")
        current_data = cursor.fetchall()
        print(f"找到 {len(current_data)} 条设置记录")
        
        # 获取表结构信息
        cursor.execute("PRAGMA table_info(settings)")
        columns_info = cursor.fetchall()
        print("当前表结构:")
        for col in columns_info:
            print(f"  {col[1]} {col[2]} (默认值: {col[4]})")
        
        # 检查是否需要迁移（检查第一个时间字段的类型）
        chinese_intro_type = None
        for col in columns_info:
            if col[1] == 'chinese_intro':
                chinese_intro_type = col[2]
                break
        
        if chinese_intro_type == 'REAL':
            print("表结构已经是REAL类型，无需迁移")
            conn.close()
            return
        
        print(f"检测到时间字段类型为 {chinese_intro_type}，需要迁移到REAL类型")
        
        # 创建新的settings表（临时表）
        print("创建新的settings表结构...")
        cursor.execute('''
        CREATE TABLE settings_new (
            id INTEGER PRIMARY KEY CHECK (id = 1),
            chinese_intro REAL NOT NULL DEFAULT 2.0,
            english_intro REAL NOT NULL DEFAULT 2.0,
            translation REAL NOT NULL DEFAULT 5.0,
            professional REAL NOT NULL DEFAULT 10.0,
            comprehensive REAL NOT NULL DEFAULT 5.0
        )
        ''')
        
        # 复制数据到新表
        print("复制数据到新表...")
        for row in current_data:
            cursor.execute('''
            INSERT INTO settings_new (id, chinese_intro, english_intro, translation, professional, comprehensive)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                row[0],  # id
                float(row[1]),  # chinese_intro - 转换为浮点数
                float(row[2]),  # english_intro - 转换为浮点数
                float(row[3]),  # translation - 转换为浮点数
                float(row[4]),  # professional - 转换为浮点数
                float(row[5])   # comprehensive - 转换为浮点数
            ))
        
        # 删除旧表
        print("删除旧的settings表...")
        cursor.execute("DROP TABLE settings")
        
        # 重命名新表
        print("重命名新表为settings...")
        cursor.execute("ALTER TABLE settings_new RENAME TO settings")
        
        # 验证迁移结果
        cursor.execute("SELECT * FROM settings")
        migrated_data = cursor.fetchall()
        print(f"迁移完成，新表包含 {len(migrated_data)} 条记录")
        
        # 显示新表结构
        cursor.execute("PRAGMA table_info(settings)")
        new_columns_info = cursor.fetchall()
        print("新表结构:")
        for col in new_columns_info:
            print(f"  {col[1]} {col[2]} (默认值: {col[4]})")
        
        # 显示迁移后的数据
        print("迁移后的数据:")
        for row in migrated_data:
            print(f"  ID: {row[0]}, 中文自我介绍: {row[1]}分钟, 英文自我介绍: {row[2]}分钟, "
                  f"英文翻译: {row[3]}分钟, 专业课问答: {row[4]}分钟, 综合面试: {row[5]}分钟")
        
        conn.commit()
        conn.close()
        
        print("✅ settings表迁移成功！现在支持小数时间设置了。")
        
    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    migrate_settings_table()
