/**
 * Application - 应用程序主类
 * 作为整个系统的入口点，管理应用生命周期和全局状态
 */

class Application {
    constructor(config = {}) {
        this.config = config;
        this.isInitialized = false;
        this.isRunning = false;
        this.startTime = null;
        
        // 应用状态
        this.state = {
            currentStudent: null,
            currentStep: 1,
            examInProgress: false,
            systemReady: false
        };
        
        // 组件引用
        this.systemManager = null;
        this.configManager = null;
        this.eventBus = null;
        
        // 生命周期钩子
        this.hooks = {
            beforeInit: [],
            afterInit: [],
            beforeStart: [],
            afterStart: [],
            beforeStop: [],
            afterStop: []
        };
        
        // 绑定方法
        this.handleBeforeUnload = this.handleBeforeUnload.bind(this);
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    /**
     * 初始化应用
     * @param {Object} options - 初始化选项
     * @returns {Promise<void>}
     */
    async initialize(options = {}) {
        if (this.isInitialized) {
            console.warn('应用已经初始化');
            return;
        }

        try {
            console.log('🚀 初始化面试系统应用...');
            
            // 执行初始化前钩子
            await this.executeHooks('beforeInit');
            
            // 1. 初始化系统管理器
            await this.initializeSystemManager();
            
            // 2. 设置全局事件监听
            this.setupGlobalEventListeners();
            
            // 3. 设置应用级事件监听
            this.setupApplicationEventListeners();
            
            // 4. 初始化应用状态
            await this.initializeApplicationState();
            
            // 5. 设置错误处理
            this.setupErrorHandling();
            
            this.isInitialized = true;
            this.state.systemReady = true;
            
            console.log('✅ 应用初始化完成');
            
            // 执行初始化后钩子
            await this.executeHooks('afterInit');
            
            // 触发初始化完成事件
            this.eventBus.emit('app.initialized', {
                timestamp: new Date().toISOString(),
                config: this.configManager.getAll(),
                state: this.state
            });
            
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this.handleError(error, 'initialization');
            throw error;
        }
    }

    /**
     * 启动应用
     * @returns {Promise<void>}
     */
    async start() {
        if (!this.isInitialized) {
            throw new Error('应用未初始化，请先调用 initialize()');
        }

        if (this.isRunning) {
            console.warn('应用已经在运行');
            return;
        }

        try {
            console.log('🎯 启动面试系统应用...');
            
            // 执行启动前钩子
            await this.executeHooks('beforeStart');
            
            // 1. 启动系统管理器
            await this.systemManager.start();
            
            // 2. 加载应用数据
            await this.loadApplicationData();
            
            // 3. 初始化用户界面
            await this.initializeUserInterface();
            
            // 4. 恢复应用状态
            await this.restoreApplicationState();
            
            this.isRunning = true;
            this.startTime = new Date();
            
            console.log('✅ 应用启动完成');
            
            // 执行启动后钩子
            await this.executeHooks('afterStart');
            
            // 触发启动完成事件
            this.eventBus.emit('app.started', {
                timestamp: this.startTime.toISOString(),
                state: this.state
            });
            
        } catch (error) {
            console.error('❌ 应用启动失败:', error);
            this.handleError(error, 'startup');
            throw error;
        }
    }

    /**
     * 停止应用
     * @returns {Promise<void>}
     */
    async stop() {
        if (!this.isRunning) {
            return;
        }

        try {
            console.log('🛑 停止面试系统应用...');
            
            // 执行停止前钩子
            await this.executeHooks('beforeStop');
            
            // 1. 保存应用状态
            await this.saveApplicationState();
            
            // 2. 停止系统管理器
            await this.systemManager.stop();
            
            // 3. 清理资源
            this.cleanup();
            
            this.isRunning = false;
            
            console.log('✅ 应用已停止');
            
            // 执行停止后钩子
            await this.executeHooks('afterStop');
            
            // 触发停止事件
            this.eventBus.emit('app.stopped', {
                timestamp: new Date().toISOString(),
                uptime: Date.now() - this.startTime.getTime()
            });
            
        } catch (error) {
            console.error('❌ 应用停止失败:', error);
            this.handleError(error, 'shutdown');
        }
    }

    /**
     * 重启应用
     * @returns {Promise<void>}
     */
    async restart() {
        console.log('🔄 重启面试系统应用...');
        await this.stop();
        await this.start();
    }

    // ==================== 初始化方法 ====================

    /**
     * 初始化系统管理器
     */
    async initializeSystemManager() {
        if (!window.systemManager) {
            throw new Error('SystemManager 未找到');
        }
        
        this.systemManager = window.systemManager;
        await this.systemManager.initialize(this.config);
        
        // 获取组件引用
        this.configManager = this.systemManager.getManager('ConfigManager');
        this.eventBus = this.systemManager.getManager('EventBus');
    }

    /**
     * 设置全局事件监听
     */
    setupGlobalEventListeners() {
        // 页面卸载前保存状态
        window.addEventListener('beforeunload', this.handleBeforeUnload);
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
        
        // 全局错误处理
        window.addEventListener('error', this.handleError);
        window.addEventListener('unhandledrejection', this.handleError);
    }

    /**
     * 设置应用级事件监听
     */
    setupApplicationEventListeners() {
        // 监听系统事件
        this.eventBus.on('system.error', (data) => {
            this.handleSystemError(data);
        });
        
        this.eventBus.on('system.warning', (data) => {
            this.handleSystemWarning(data);
        });
        
        // 监听考试事件
        this.eventBus.on('exam.started', (data) => {
            this.handleExamStarted(data);
        });
        
        this.eventBus.on('exam.completed', (data) => {
            this.handleExamCompleted(data);
        });
        
        // 监听学生事件
        this.eventBus.on('student.selected', (data) => {
            this.handleStudentSelected(data);
        });
        
        // 监听步骤变更事件
        this.eventBus.on('step.changed', (data) => {
            this.handleStepChanged(data);
        });
    }

    /**
     * 初始化应用状态
     */
    async initializeApplicationState() {
        // 从配置中获取初始状态
        const savedState = this.configManager.get('app.lastState');
        if (savedState) {
            this.state = { ...this.state, ...savedState };
        }
        
        // 检查是否有进行中的考试
        try {
            const examService = this.systemManager.getService('ExamService');
            if (examService) {
                const currentExam = await examService.getCurrentExam();
                if (currentExam) {
                    this.state.currentStudent = currentExam.studentNumber;
                    this.state.currentStep = currentExam.currentStep;
                    this.state.examInProgress = true;
                }
            }
        } catch (error) {
            console.warn('⚠️ 检查当前考试状态失败:', error);
        }
    }

    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        // 设置全局错误处理策略
        this.errorHandlingStrategy = this.configManager.get('app.errorHandling', 'log');
        
        // 设置错误恢复机制
        this.setupErrorRecovery();
    }

    // ==================== 应用数据管理 ====================

    /**
     * 加载应用数据
     */
    async loadApplicationData() {
        try {
            // 加载系统设置
            const settingsService = this.systemManager.getService('SettingsService');
            if (settingsService) {
                const settings = await settingsService.getSettings();
                this.eventBus.emit('app.settingsLoaded', settings);
            }
            
            // 加载学生数据
            const studentService = this.systemManager.getService('StudentService');
            if (studentService) {
                const students = await studentService.getStudents({ limit: 10 });
                this.eventBus.emit('app.studentsLoaded', students);
            }
            
        } catch (error) {
            console.warn('⚠️ 加载应用数据失败:', error);
        }
    }

    /**
     * 初始化用户界面
     */
    async initializeUserInterface() {
        // 设置主题
        const theme = this.configManager.get('ui.theme', 'light');
        document.documentElement.setAttribute('data-theme', theme);
        
        // 设置字体大小
        const fontSize = this.configManager.get('ui.fontSize', 'medium');
        document.documentElement.setAttribute('data-font-size', fontSize);
        
        // 初始化主视图
        const viewManager = this.systemManager.getManager('ViewManager');
        if (viewManager) {
            await viewManager.initializeMainView();
        }
        
        // 显示欢迎信息
        this.showWelcomeMessage();
    }

    /**
     * 恢复应用状态
     */
    async restoreApplicationState() {
        if (this.state.examInProgress && this.state.currentStudent) {
            // 恢复考试状态
            const examController = this.systemManager.getController('ExamController');
            if (examController) {
                await examController.restoreExamState(this.state.currentStudent);
            }
        }
    }

    /**
     * 保存应用状态
     */
    async saveApplicationState() {
        try {
            // 保存当前状态到配置
            this.configManager.set('app.lastState', {
                currentStudent: this.state.currentStudent,
                currentStep: this.state.currentStep,
                examInProgress: this.state.examInProgress,
                timestamp: new Date().toISOString()
            });
            
            // 保存到本地存储
            localStorage.setItem('interview_app_state', JSON.stringify({
                ...this.state,
                timestamp: new Date().toISOString()
            }));
            
        } catch (error) {
            console.warn('⚠️ 保存应用状态失败:', error);
        }
    }

    // ==================== 事件处理 ====================

    /**
     * 处理页面卸载前事件
     */
    handleBeforeUnload(event) {
        if (this.state.examInProgress) {
            event.preventDefault();
            event.returnValue = '考试正在进行中，确定要离开吗？';
            return event.returnValue;
        }
        
        // 保存应用状态
        this.saveApplicationState();
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停某些操作
            this.eventBus.emit('app.hidden');
        } else {
            // 页面显示时恢复操作
            this.eventBus.emit('app.visible');
        }
    }

    /**
     * 处理错误
     */
    handleError(error, context = 'unknown') {
        console.error(`应用错误 [${context}]:`, error);
        
        // 记录错误
        this.eventBus.emit('app.error', {
            error: error.toString(),
            context,
            timestamp: new Date().toISOString(),
            state: this.state
        });
        
        // 根据错误处理策略执行相应操作
        this.executeErrorHandling(error, context);
    }

    /**
     * 处理系统错误
     */
    handleSystemError(data) {
        console.error('系统错误:', data);
        
        // 可以在这里实现错误恢复逻辑
        if (data.severity === 'critical') {
            this.showErrorDialog('系统发生严重错误，建议重启应用');
        }
    }

    /**
     * 处理系统警告
     */
    handleSystemWarning(data) {
        console.warn('系统警告:', data);
        
        // 显示警告提示
        this.showWarningToast(data.message);
    }

    /**
     * 处理考试开始
     */
    handleExamStarted(data) {
        this.state.examInProgress = true;
        this.state.currentStudent = data.studentNumber;
        this.state.currentStep = 1;
        
        console.log(`考试开始: 学生 ${data.studentNumber}`);
    }

    /**
     * 处理考试完成
     */
    handleExamCompleted(data) {
        this.state.examInProgress = false;
        this.state.currentStudent = null;
        this.state.currentStep = 1;
        
        console.log(`考试完成: 学生 ${data.studentNumber}`);
    }

    /**
     * 处理学生选择
     */
    handleStudentSelected(data) {
        this.state.currentStudent = data.studentNumber;
        console.log(`选择学生: ${data.studentNumber}`);
    }

    /**
     * 处理步骤变更
     */
    handleStepChanged(data) {
        this.state.currentStep = data.stepNumber;
        console.log(`步骤变更: ${data.stepNumber}`);
    }

    // ==================== 工具方法 ====================

    /**
     * 添加生命周期钩子
     */
    addHook(hookName, callback) {
        if (this.hooks[hookName]) {
            this.hooks[hookName].push(callback);
        }
    }

    /**
     * 执行生命周期钩子
     */
    async executeHooks(hookName) {
        const hooks = this.hooks[hookName] || [];
        for (const hook of hooks) {
            try {
                await hook();
            } catch (error) {
                console.error(`执行${hookName}钩子失败:`, error);
            }
        }
    }

    /**
     * 获取应用状态
     */
    getState() {
        return { ...this.state };
    }

    /**
     * 更新应用状态
     */
    setState(newState) {
        const oldState = { ...this.state };
        this.state = { ...this.state, ...newState };
        
        this.eventBus.emit('app.stateChanged', {
            oldState,
            newState: this.state,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 获取应用信息
     */
    getInfo() {
        return {
            name: this.configManager.get('app.name'),
            version: this.configManager.get('app.version'),
            isInitialized: this.isInitialized,
            isRunning: this.isRunning,
            startTime: this.startTime,
            uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0,
            state: this.state
        };
    }

    /**
     * 显示欢迎信息
     */
    showWelcomeMessage() {
        const appName = this.configManager.get('app.name');
        const version = this.configManager.get('app.version');
        
        console.log(`%c${appName} v${version}`, 'color: #2563eb; font-size: 16px; font-weight: bold;');
        console.log('系统已就绪，可以开始使用');
    }

    /**
     * 显示错误对话框
     */
    showErrorDialog(message) {
        // 这里可以调用UI组件显示错误对话框
        alert(message);
    }

    /**
     * 显示警告提示
     */
    showWarningToast(message) {
        // 这里可以调用UI组件显示警告提示
        console.warn(message);
    }

    /**
     * 设置错误恢复机制
     */
    setupErrorRecovery() {
        // 实现错误恢复逻辑
    }

    /**
     * 执行错误处理
     */
    executeErrorHandling(error, context) {
        // 根据配置执行错误处理策略
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 移除事件监听器
        window.removeEventListener('beforeunload', this.handleBeforeUnload);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        window.removeEventListener('error', this.handleError);
        window.removeEventListener('unhandledrejection', this.handleError);
    }

    /**
     * 销毁应用
     */
    async destroy() {
        await this.stop();
        this.cleanup();
        this.isInitialized = false;
        console.log('✅ 应用已销毁');
    }
}

// 导出Application类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Application;
} else {
    window.Application = Application;
}
