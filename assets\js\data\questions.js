/**
 * 考试题目数据
 * 包含翻译题、专业题等各类题目
 */

// 翻译题库
const translationQuestions = [
    {
        id: 1,
        type: 'translation',
        category: 'chinese_to_english',
        question: '人工智能正在改变我们的生活方式。',
        answer: 'Artificial intelligence is changing our way of life.',
        difficulty: 'medium',
        keywords: ['人工智能', 'artificial intelligence', '改变', 'change', '生活方式', 'way of life']
    },
    {
        id: 2,
        type: 'translation',
        category: 'chinese_to_english',
        question: '可持续发展是当今世界面临的重要挑战。',
        answer: 'Sustainable development is an important challenge facing the world today.',
        difficulty: 'medium',
        keywords: ['可持续发展', 'sustainable development', '挑战', 'challenge', '面临', 'facing']
    },
    {
        id: 3,
        type: 'translation',
        category: 'chinese_to_english',
        question: '教育是社会进步的基石。',
        answer: 'Education is the cornerstone of social progress.',
        difficulty: 'easy',
        keywords: ['教育', 'education', '基石', 'cornerstone', '社会进步', 'social progress']
    },
    {
        id: 4,
        type: 'translation',
        category: 'english_to_chinese',
        question: 'Climate change poses a significant threat to global food security.',
        answer: '气候变化对全球粮食安全构成重大威胁。',
        difficulty: 'medium',
        keywords: ['climate change', '气候变化', 'threat', '威胁', 'food security', '粮食安全']
    },
    {
        id: 5,
        type: 'translation',
        category: 'english_to_chinese',
        question: 'Innovation drives economic growth and competitiveness.',
        answer: '创新推动经济增长和竞争力。',
        difficulty: 'medium',
        keywords: ['innovation', '创新', 'economic growth', '经济增长', 'competitiveness', '竞争力']
    }
];

// 专业题库
const professionalQuestions = [
    {
        id: 101,
        type: 'professional',
        category: 'computer_science',
        question: '请解释什么是面向对象编程，并说明其主要特征。',
        answer: '面向对象编程是一种编程范式，主要特征包括：1)封装：将数据和方法封装在类中；2)继承：子类可以继承父类的属性和方法；3)多态：同一接口可以有不同的实现；4)抽象：通过抽象类和接口定义规范。',
        difficulty: 'medium',
        keywords: ['面向对象', '封装', '继承', '多态', '抽象']
    },
    {
        id: 102,
        type: 'professional',
        category: 'computer_science',
        question: '什么是数据结构？请举例说明几种常见的数据结构。',
        answer: '数据结构是计算机存储、组织数据的方式。常见的数据结构包括：1)数组：线性存储结构；2)链表：动态存储结构；3)栈：后进先出(LIFO)；4)队列：先进先出(FIFO)；5)树：层次结构；6)图：网络结构。',
        difficulty: 'easy',
        keywords: ['数据结构', '数组', '链表', '栈', '队列', '树', '图']
    },
    {
        id: 103,
        type: 'professional',
        category: 'mathematics',
        question: '请解释什么是概率论，并说明其在实际生活中的应用。',
        answer: '概率论是研究随机现象规律性的数学分支。实际应用包括：1)统计分析：数据分析和预测；2)风险评估：金融投资决策；3)质量控制：产品质量管理；4)机器学习：算法优化；5)保险精算：保费计算。',
        difficulty: 'medium',
        keywords: ['概率论', '随机现象', '统计分析', '风险评估', '机器学习']
    }
];

// 综合题库
const comprehensiveQuestions = [
    {
        id: 201,
        type: 'comprehensive',
        category: 'analysis',
        question: '请分析当前人工智能发展的趋势，并谈谈你对未来发展的看法。',
        answer: '当前AI发展趋势：1)深度学习技术成熟；2)应用场景不断扩展；3)算力和数据支撑增强；4)产业化程度提高。未来发展：1)通用人工智能(AGI)；2)人机协作深化；3)伦理和安全问题；4)跨学科融合。',
        difficulty: 'hard',
        keywords: ['人工智能', '深度学习', '通用人工智能', '人机协作', '伦理安全']
    }
];

// 题目配置
const questionConfig = {
    translation: {
        timeLimit: 300, // 5分钟
        totalQuestions: translationQuestions.length,
        categories: ['chinese_to_english', 'english_to_chinese']
    },
    professional: {
        timeLimit: 600, // 10分钟
        totalQuestions: professionalQuestions.length,
        categories: ['computer_science', 'mathematics', 'physics', 'chemistry']
    },
    comprehensive: {
        timeLimit: 900, // 15分钟
        totalQuestions: comprehensiveQuestions.length,
        categories: ['analysis', 'discussion', 'case_study']
    }
};

// 导出数据
window.questionsData = {
    translation: translationQuestions,
    professional: professionalQuestions,
    comprehensive: comprehensiveQuestions,
    config: questionConfig
};

// 题目管理工具函数
window.questionUtils = {
    /**
     * 根据类型获取题目
     */
    getQuestionsByType(type) {
        return window.questionsData[type] || [];
    },

    /**
     * 随机获取题目
     */
    getRandomQuestion(type, excludeIds = []) {
        const questions = this.getQuestionsByType(type);
        const availableQuestions = questions.filter(q => !excludeIds.includes(q.id));
        
        if (availableQuestions.length === 0) {
            return null;
        }
        
        const randomIndex = Math.floor(Math.random() * availableQuestions.length);
        return availableQuestions[randomIndex];
    },

    /**
     * 根据难度获取题目
     */
    getQuestionsByDifficulty(type, difficulty) {
        const questions = this.getQuestionsByType(type);
        return questions.filter(q => q.difficulty === difficulty);
    },

    /**
     * 获取题目配置
     */
    getConfig(type) {
        return window.questionsData.config[type] || {};
    }
};
