# 研究生复试系统 - Controller业务逻辑文档

## Controller层架构概述

Controller层是MVC架构的核心，负责协调Model和View之间的交互，处理业务逻辑，响应用户操作，并管理应用程序的状态流转。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     View        │───►│   Controller    │───►│    Service      │
│   (用户交互)     │◄───│   (业务逻辑)     │◄───│   (数据访问)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  UI Components  │    │   Controllers   │    │     Models      │
│  - 按钮点击      │    │ - 状态管理       │    │  - 数据验证      │
│  - 表单提交      │    │ - 流程控制       │    │  - 业务规则      │
│  - 页面导航      │    │ - 事件处理       │    │  - 数据转换      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Controller设计原则

### 1. 单一职责原则
每个Controller只负责一个特定的业务领域：
- **ExamController**: 考试流程控制
- **QuestionController**: 题目管理控制
- **TimerController**: 计时器控制
- **StudentController**: 学生管理控制
- **SettingsController**: 系统设置控制

### 2. 依赖倒置原则
Controller依赖于Service接口，而不是具体实现：
```javascript
class ExamController {
    constructor(studentService, questionService, timerService) {
        this.studentService = studentService;
        this.questionService = questionService;
        this.timerService = timerService;
    }
}
```

### 3. 开闭原则
Controller对扩展开放，对修改封闭：
```javascript
// 可以通过插件机制扩展功能
class ExamController {
    addPlugin(plugin) {
        this.plugins.push(plugin);
    }
}
```

## ExamController - 考试流程控制器

### 职责范围
- 考试生命周期管理
- 步骤流转控制
- 状态同步协调
- 异常情况处理

### 核心方法

#### 考试初始化
```javascript
async initializeExam(studentNumber) {
    // 1. 验证学生编号
    // 2. 创建或获取学生
    // 3. 初始化考试状态
    // 4. 加载系统设置
    // 5. 准备UI界面
    // 6. 触发初始化完成事件
}
```

#### 开始考试
```javascript
async startExam(studentNumber) {
    // 1. 检查考试前置条件
    // 2. 创建考试记录
    // 3. 设置当前学生
    // 4. 进入第一步
    // 5. 初始化计时器
    // 6. 更新UI状态
}
```

#### 步骤切换
```javascript
async changeStep(direction) {
    // direction: 'next' | 'previous'
    // 1. 验证切换条件
    // 2. 保存当前步骤数据
    // 3. 停止当前计时器
    // 4. 更新学生状态
    // 5. 切换到目标步骤
    // 6. 重置计时器
    // 7. 更新UI显示
}
```

#### 完成考试
```javascript
async completeExam() {
    // 1. 保存最后步骤数据
    // 2. 停止所有计时器
    // 3. 生成考试记录
    // 4. 更新学生状态
    // 5. 显示完成界面
    // 6. 清理资源
}
```

### 状态管理
```javascript
class ExamController {
    constructor() {
        this.state = {
            currentStudent: null,
            currentStep: 1,
            isExamActive: false,
            isTimerRunning: false,
            examStartTime: null,
            stepStartTime: null
        };
    }

    updateState(newState) {
        const oldState = { ...this.state };
        this.state = { ...this.state, ...newState };
        this.emit('stateChanged', { oldState, newState: this.state });
    }
}
```

### 事件处理
```javascript
// 监听Service层事件
studentService.on('stepChanged', (data) => {
    this.handleStepChanged(data);
});

timerService.on('timeUp', () => {
    this.handleTimeUp();
});

// 监听View层事件
view.on('nextStepClicked', () => {
    this.changeStep('next');
});

view.on('previousStepClicked', () => {
    this.changeStep('previous');
});
```

## QuestionController - 题目管理控制器

### 职责范围
- 题目抽取流程控制
- 题目状态管理
- 抽题动画协调
- 题目网格更新

### 核心方法

#### 抽取题目
```javascript
async drawQuestion(type, method = 'random') {
    // method: 'random' | 'specific'
    // 1. 验证抽题条件
    // 2. 获取可用题目
    // 3. 执行抽题逻辑
    // 4. 播放抽题动画
    // 5. 显示题目内容
    // 6. 更新题目状态
    // 7. 保存抽题记录
}
```

#### 显示题目网格
```javascript
async showQuestionGrid(type) {
    // 1. 获取题目统计
    // 2. 生成网格数据
    // 3. 渲染网格界面
    // 4. 绑定交互事件
    // 5. 显示统计信息
}
```

#### 更新题目状态
```javascript
async updateQuestionStatus(type, questionNumber, status) {
    // 1. 验证状态变更
    // 2. 更新数据库
    // 3. 刷新缓存
    // 4. 更新UI显示
    // 5. 触发状态变更事件
}
```

### 抽题流程控制
```javascript
class QuestionController {
    async executeDrawProcess(type) {
        try {
            // 1. 前置检查
            await this.validateDrawConditions(type);
            
            // 2. 显示抽题界面
            await this.showDrawInterface(type);
            
            // 3. 执行抽题动画
            const selectedNumber = await this.playDrawAnimation(type);
            
            // 4. 获取题目内容
            const question = await this.loadQuestionContent(type, selectedNumber);
            
            // 5. 显示题目
            await this.displayQuestion(question);
            
            // 6. 更新状态
            await this.updateQuestionStatus(type, selectedNumber, 'used');
            
            // 7. 自动开始计时
            await this.startStepTimer();
            
        } catch (error) {
            await this.handleDrawError(error);
        }
    }
}
```

## TimerController - 计时器控制器

### 职责范围
- 计时器生命周期管理
- 时间显示更新
- 时间到达处理
- 计时器状态同步

### 核心方法

#### 启动计时器
```javascript
async startTimer(stepNumber, duration) {
    // 1. 验证计时器状态
    // 2. 设置计时器参数
    // 3. 开始倒计时
    // 4. 更新UI显示
    // 5. 触发启动事件
}
```

#### 暂停/恢复计时器
```javascript
async pauseTimer() {
    // 1. 保存当前时间
    // 2. 停止计时器
    // 3. 更新状态
    // 4. 更新UI显示
}

async resumeTimer() {
    // 1. 恢复计时器
    // 2. 继续倒计时
    // 3. 更新状态
    // 4. 更新UI显示
}
```

#### 重置计时器
```javascript
async resetTimer(newDuration) {
    // 1. 停止当前计时器
    // 2. 重置时间
    // 3. 更新显示
    // 4. 触发重置事件
}
```

### 时间管理
```javascript
class TimerController {
    constructor() {
        this.timers = new Map(); // 支持多个计时器
        this.currentTimer = null;
    }

    createTimer(id, duration, callbacks) {
        const timer = {
            id,
            duration,
            remaining: duration,
            isRunning: false,
            isPaused: false,
            startTime: null,
            callbacks
        };
        
        this.timers.set(id, timer);
        return timer;
    }

    tick(timerId) {
        const timer = this.timers.get(timerId);
        if (!timer || !timer.isRunning) return;

        timer.remaining--;
        
        // 触发tick回调
        if (timer.callbacks.onTick) {
            timer.callbacks.onTick(timer.remaining);
        }

        // 检查时间到达
        if (timer.remaining <= 0) {
            this.handleTimeUp(timerId);
        }
    }
}
```

## StudentController - 学生管理控制器

### 职责范围
- 学生切换流程
- 学生数据管理
- 进度保存恢复
- 学生状态同步

### 核心方法

#### 切换学生
```javascript
async switchStudent(newStudentNumber) {
    // 1. 保存当前学生数据
    // 2. 验证新学生编号
    // 3. 加载新学生数据
    // 4. 恢复考试状态
    // 5. 更新UI显示
    // 6. 触发切换事件
}
```

#### 保存学生进度
```javascript
async saveProgress() {
    // 1. 收集当前状态
    // 2. 验证数据完整性
    // 3. 保存到数据库
    // 4. 更新缓存
    // 5. 触发保存事件
}
```

#### 恢复学生状态
```javascript
async restoreStudentState(studentNumber) {
    // 1. 从数据库加载数据
    // 2. 验证数据有效性
    // 3. 恢复考试状态
    // 4. 恢复计时器状态
    // 5. 更新UI显示
}
```

## SettingsController - 系统设置控制器

### 职责范围
- 设置界面管理
- 设置数据验证
- 设置变更处理
- 设置同步更新

### 核心方法

#### 加载设置
```javascript
async loadSettings() {
    // 1. 从数据库加载设置
    // 2. 应用默认值
    // 3. 验证设置有效性
    // 4. 更新UI显示
}
```

#### 保存设置
```javascript
async saveSettings(newSettings) {
    // 1. 验证设置数据
    // 2. 检查变更影响
    // 3. 保存到数据库
    // 4. 应用新设置
    // 5. 通知相关模块
}
```

#### 重置设置
```javascript
async resetSettings() {
    // 1. 确认重置操作
    // 2. 恢复默认设置
    // 3. 保存到数据库
    // 4. 重新加载系统
}
```

## Controller间协调机制

### 事件总线
```javascript
class ControllerCoordinator {
    constructor() {
        this.eventBus = new EventBus();
        this.controllers = new Map();
    }

    registerController(name, controller) {
        this.controllers.set(name, controller);
        controller.setEventBus(this.eventBus);
    }

    // 协调考试流程
    async coordinateExamFlow(action, data) {
        switch (action) {
            case 'startExam':
                await this.examController.startExam(data.studentNumber);
                await this.timerController.initializeTimer(1);
                break;
                
            case 'drawQuestion':
                await this.questionController.drawQuestion(data.type);
                await this.timerController.startTimer();
                break;
                
            case 'nextStep':
                await this.examController.changeStep('next');
                await this.timerController.resetTimer();
                break;
        }
    }
}
```

### 状态同步
```javascript
// 全局状态管理
class GlobalStateManager {
    constructor() {
        this.state = {
            currentStudent: null,
            currentStep: 1,
            examStatus: 'idle',
            timerStatus: 'stopped',
            questionStatus: {}
        };
        
        this.subscribers = [];
    }

    updateState(updates) {
        const oldState = { ...this.state };
        this.state = { ...this.state, ...updates };
        
        this.notifySubscribers(oldState, this.state);
    }

    subscribe(callback) {
        this.subscribers.push(callback);
    }
}
```

## 错误处理和异常管理

### 统一错误处理
```javascript
class ControllerErrorHandler {
    static async handleError(error, context) {
        console.error(`Controller错误 [${context}]:`, error);
        
        // 根据错误类型处理
        switch (error.code) {
            case 'STUDENT_NOT_FOUND':
                await this.handleStudentNotFound(error);
                break;
                
            case 'QUESTION_NOT_AVAILABLE':
                await this.handleQuestionNotAvailable(error);
                break;
                
            case 'TIMER_ERROR':
                await this.handleTimerError(error);
                break;
                
            default:
                await this.handleGenericError(error);
        }
    }
}
```

### 业务规则验证
```javascript
class BusinessRuleValidator {
    static validateExamStart(student) {
        const rules = [
            () => student.status !== 'completed',
            () => student.currentStep <= 5,
            () => !student.isExamCompleted()
        ];
        
        return rules.every(rule => rule());
    }

    static validateStepChange(currentStep, direction) {
        if (direction === 'next') {
            return currentStep < 5;
        } else if (direction === 'previous') {
            return currentStep > 1;
        }
        return false;
    }
}
```

## 性能优化策略

### 1. 懒加载
```javascript
// 按需加载Controller
class ControllerLoader {
    static async loadController(name) {
        if (!this.controllers.has(name)) {
            const controller = await import(`./controllers/${name}Controller.js`);
            this.controllers.set(name, new controller.default());
        }
        return this.controllers.get(name);
    }
}
```

### 2. 缓存机制
```javascript
// Controller级别的缓存
class ControllerCache {
    constructor() {
        this.cache = new Map();
        this.ttl = 5 * 60 * 1000; // 5分钟
    }

    set(key, value) {
        this.cache.set(key, {
            value,
            timestamp: Date.now()
        });
    }

    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        
        if (Date.now() - item.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }
        
        return item.value;
    }
}
```

### 3. 批量操作
```javascript
// 批量处理用户操作
class BatchProcessor {
    constructor() {
        this.queue = [];
        this.processing = false;
    }

    addOperation(operation) {
        this.queue.push(operation);
        this.scheduleProcess();
    }

    async scheduleProcess() {
        if (this.processing) return;
        
        this.processing = true;
        await this.processQueue();
        this.processing = false;
    }
}
```

Controller层是整个MVC架构的核心协调者，负责将用户交互转换为业务操作，协调各个Service的调用，并确保系统状态的一致性和业务规则的正确执行。
