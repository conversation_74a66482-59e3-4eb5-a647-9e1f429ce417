/**
 * DatabaseService - 数据库访问服务
 * 负责所有数据库操作的统一接口
 */

class DatabaseService {
    constructor() {
        this.baseUrl = '/api';
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }

    /**
     * 发送HTTP请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 响应数据
     */
    async request(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: this.defaultHeaders,
                ...options
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
            }

            return {
                success: true,
                data: data,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Database request failed:', error);
            return {
                success: false,
                error: {
                    code: 'DATABASE_ERROR',
                    message: error.message,
                    details: error
                },
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * GET请求
     * @param {string} endpoint - API端点
     * @returns {Promise<Object>} 响应数据
     */
    async get(endpoint) {
        return this.request(`${this.baseUrl}${endpoint}`, {
            method: 'GET'
        });
    }

    /**
     * POST请求
     * @param {string} endpoint - API端点
     * @param {Object} data - 请求数据
     * @returns {Promise<Object>} 响应数据
     */
    async post(endpoint, data) {
        return this.request(`${this.baseUrl}${endpoint}`, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     * @param {string} endpoint - API端点
     * @param {Object} data - 请求数据
     * @returns {Promise<Object>} 响应数据
     */
    async put(endpoint, data) {
        return this.request(`${this.baseUrl}${endpoint}`, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     * @param {string} endpoint - API端点
     * @returns {Promise<Object>} 响应数据
     */
    async delete(endpoint) {
        return this.request(`${this.baseUrl}${endpoint}`, {
            method: 'DELETE'
        });
    }

    // ==================== 学生相关操作 ====================

    /**
     * 创建学生
     * @param {Object} studentData - 学生数据
     * @returns {Promise<Object>} 创建结果
     */
    async createStudent(studentData) {
        return this.post('/students', studentData);
    }

    /**
     * 获取学生信息
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Object>} 学生信息
     */
    async getStudent(studentNumber) {
        return this.get(`/students/${studentNumber}`);
    }

    /**
     * 更新学生信息
     * @param {string} studentNumber - 学生编号
     * @param {Object} updateData - 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateStudent(studentNumber, updateData) {
        return this.put(`/students/${studentNumber}`, updateData);
    }

    /**
     * 获取所有学生
     * @returns {Promise<Object>} 学生列表
     */
    async getAllStudents() {
        return this.get('/students');
    }

    // ==================== 考试记录相关操作 ====================

    /**
     * 创建考试记录
     * @param {Object} recordData - 考试记录数据
     * @returns {Promise<Object>} 创建结果
     */
    async createExamRecord(recordData) {
        return this.post('/exam-records', recordData);
    }

    /**
     * 获取考试记录
     * @param {number} recordId - 记录ID
     * @returns {Promise<Object>} 考试记录
     */
    async getExamRecord(recordId) {
        return this.get(`/exam-records/${recordId}`);
    }

    /**
     * 获取学生考试记录
     * @param {string} studentNumber - 学生编号
     * @returns {Promise<Object>} 学生考试记录
     */
    async getStudentExamRecord(studentNumber) {
        return this.get(`/exam-records/student/${studentNumber}`);
    }

    /**
     * 获取所有考试记录
     * @returns {Promise<Object>} 考试记录列表
     */
    async getAllExamRecords() {
        return this.get('/exam-records');
    }

    /**
     * 更新考试记录
     * @param {number} recordId - 记录ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateExamRecord(recordId, updateData) {
        return this.put(`/exam-records/${recordId}`, updateData);
    }

    // ==================== 步骤记录相关操作 ====================

    /**
     * 创建步骤记录
     * @param {Object} stepData - 步骤记录数据
     * @returns {Promise<Object>} 创建结果
     */
    async createStepRecord(stepData) {
        return this.post('/step-records', stepData);
    }

    /**
     * 更新步骤记录
     * @param {number} stepId - 步骤记录ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateStepRecord(stepId, updateData) {
        return this.put(`/step-records/${stepId}`, updateData);
    }

    /**
     * 完成步骤记录
     * @param {number} stepId - 步骤记录ID
     * @param {Object} completionData - 完成数据
     * @returns {Promise<Object>} 完成结果
     */
    async completeStepRecord(stepId, completionData) {
        return this.put(`/step-records/${stepId}/complete`, completionData);
    }

    // ==================== 题目相关操作 ====================

    /**
     * 获取题目列表
     * @param {string} type - 题目类型
     * @returns {Promise<Object>} 题目列表
     */
    async getQuestions(type) {
        return this.get(`/questions/${type}`);
    }

    /**
     * 获取指定题目
     * @param {string} type - 题目类型
     * @param {number} index - 题目编号
     * @returns {Promise<Object>} 题目信息
     */
    async getQuestion(type, index) {
        return this.get(`/questions/${type}/${index}`);
    }

    /**
     * 获取已使用题目
     * @param {string} type - 题目类型
     * @returns {Promise<Object>} 已使用题目列表
     */
    async getUsedQuestions(type) {
        return this.get(`/questions/${type}/used`);
    }

    /**
     * 获取题目统计
     * @param {string} type - 题目类型
     * @returns {Promise<Object>} 题目统计
     */
    async getQuestionStats(type) {
        return this.get(`/questions/${type}/stats`);
    }

    /**
     * 标记题目为已使用
     * @param {Object} usageData - 使用数据
     * @returns {Promise<Object>} 标记结果
     */
    async markQuestionAsUsed(usageData) {
        return this.post('/questions/mark-used', usageData);
    }

    /**
     * 创建题目
     * @param {Object} questionData - 题目数据
     * @returns {Promise<Object>} 创建结果
     */
    async createQuestion(questionData) {
        return this.post('/questions', questionData);
    }

    /**
     * 更新题目
     * @param {string} questionId - 题目ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateQuestion(questionId, updateData) {
        return this.put(`/questions/${questionId}`, updateData);
    }

    // ==================== 设置相关操作 ====================

    /**
     * 获取所有设置
     * @returns {Promise<Object>} 设置数据
     */
    async getSettings() {
        return this.get('/settings');
    }

    /**
     * 获取分类设置
     * @param {string} category - 设置分类
     * @returns {Promise<Object>} 分类设置
     */
    async getCategorySettings(category) {
        return this.get(`/settings/${category}`);
    }

    /**
     * 更新设置
     * @param {string} category - 设置分类
     * @param {string} key - 设置键
     * @param {any} value - 设置值
     * @returns {Promise<Object>} 更新结果
     */
    async updateSetting(category, key, value) {
        return this.put(`/settings/${category}/${key}`, { value });
    }

    /**
     * 批量更新设置
     * @param {Object} settings - 设置数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateSettings(settings) {
        return this.put('/settings', settings);
    }

    /**
     * 重置设置
     * @returns {Promise<Object>} 重置结果
     */
    async resetSettings() {
        return this.post('/settings/reset', {});
    }

    // ==================== 数据管理操作 ====================

    /**
     * 备份数据
     * @returns {Promise<Object>} 备份结果
     */
    async backupData() {
        return this.post('/data/backup', {});
    }

    /**
     * 恢复数据
     * @param {string} backupFile - 备份文件
     * @returns {Promise<Object>} 恢复结果
     */
    async restoreData(backupFile) {
        return this.post('/data/restore', { backupFile });
    }

    /**
     * 清理数据
     * @param {Object} cleanupOptions - 清理选项
     * @returns {Promise<Object>} 清理结果
     */
    async cleanupData(cleanupOptions) {
        return this.post('/data/cleanup', cleanupOptions);
    }

    /**
     * 重置所有数据
     * @returns {Promise<Object>} 重置结果
     */
    async resetAllData() {
        return this.post('/data/reset', {});
    }

    // ==================== 统计查询操作 ====================

    /**
     * 获取系统统计
     * @returns {Promise<Object>} 系统统计
     */
    async getSystemStats() {
        return this.get('/stats/system');
    }

    /**
     * 获取考试统计
     * @returns {Promise<Object>} 考试统计
     */
    async getExamStats() {
        return this.get('/stats/exam');
    }

    /**
     * 获取题目统计
     * @returns {Promise<Object>} 题目统计
     */
    async getAllQuestionStats() {
        return this.get('/stats/questions');
    }

    // ==================== 工具方法 ====================

    /**
     * 测试数据库连接
     * @returns {Promise<Object>} 连接测试结果
     */
    async testConnection() {
        return this.get('/health');
    }

    /**
     * 获取数据库信息
     * @returns {Promise<Object>} 数据库信息
     */
    async getDatabaseInfo() {
        return this.get('/info');
    }

    /**
     * 执行自定义查询
     * @param {string} sql - SQL查询
     * @param {Array} params - 查询参数
     * @returns {Promise<Object>} 查询结果
     */
    async customQuery(sql, params = []) {
        return this.post('/query', { sql, params });
    }

    /**
     * 批量操作
     * @param {Array} operations - 操作列表
     * @returns {Promise<Object>} 批量操作结果
     */
    async batchOperation(operations) {
        return this.post('/batch', { operations });
    }

    /**
     * 事务操作
     * @param {Array} operations - 事务操作列表
     * @returns {Promise<Object>} 事务结果
     */
    async transaction(operations) {
        return this.post('/transaction', { operations });
    }

    // ==================== 错误处理和重试机制 ====================

    /**
     * 带重试的请求
     * @param {Function} requestFn - 请求函数
     * @param {number} maxRetries - 最大重试次数
     * @param {number} delay - 重试延迟（毫秒）
     * @returns {Promise<Object>} 请求结果
     */
    async requestWithRetry(requestFn, maxRetries = 3, delay = 1000) {
        let lastError;

        for (let i = 0; i <= maxRetries; i++) {
            try {
                const result = await requestFn();
                if (result.success) {
                    return result;
                }
                lastError = result.error;
            } catch (error) {
                lastError = error;
            }

            if (i < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
            }
        }

        return {
            success: false,
            error: {
                code: 'MAX_RETRIES_EXCEEDED',
                message: `请求失败，已重试${maxRetries}次`,
                details: lastError
            },
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 检查网络连接
     * @returns {Promise<boolean>} 是否连接正常
     */
    async checkConnection() {
        try {
            const result = await this.testConnection();
            return result.success;
        } catch (error) {
            return false;
        }
    }

    /**
     * 获取错误信息
     * @param {Object} error - 错误对象
     * @returns {string} 用户友好的错误信息
     */
    getErrorMessage(error) {
        if (!error) return '未知错误';

        const errorMessages = {
            'DATABASE_ERROR': '数据库操作失败',
            'CONNECTION_ERROR': '网络连接失败',
            'VALIDATION_ERROR': '数据验证失败',
            'NOT_FOUND': '数据不存在',
            'DUPLICATE_ERROR': '数据已存在',
            'PERMISSION_ERROR': '权限不足',
            'TIMEOUT_ERROR': '请求超时'
        };

        return errorMessages[error.code] || error.message || '操作失败';
    }
}

// 创建单例实例
const databaseService = new DatabaseService();

// 导出服务实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = databaseService;
} else {
    window.DatabaseService = DatabaseService;
    window.databaseService = databaseService;
}
