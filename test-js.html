<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>JavaScript 文件加载测试</h1>
    <div id="test-results"></div>

    <!-- 按照index.html中的顺序加载脚本 -->
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <!-- 数据文件 -->
    <script src="assets/js/data/questions.js"></script>
    
    <!-- 工具类 -->
    <script src="assets/js/utils/utils.js"></script>
    <script src="assets/js/utils/logger.js"></script>
    <script src="assets/js/utils/baseClass.js"></script>

    <!-- 核心类 -->
    <script src="assets/js/core/database.js"></script>
    <script src="assets/js/core/timer.js"></script>
    <script src="assets/js/core/draggableTimer.js"></script>

    <!-- UI类 -->
    <script src="assets/js/ui/ui.js"></script>
    <script src="assets/js/ui/eventManager.js"></script>
    
    <!-- 业务逻辑类 -->
    <script src="assets/js/business/Student.js"></script>
    <script src="assets/js/business/ExamStep.js"></script>
    <script src="assets/js/business/Question.js"></script>
    <script src="assets/js/business/Settings.js"></script>
    
    <!-- 主控制器 -->
    <script src="assets/js/core/ExamSystem.js"></script>
    
    <!-- 应用入口 -->
    <script src="assets/js/app.js"></script>

    <script>
        // 测试脚本
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        // 测试各个类是否正确定义
        function runTests() {
            addTestResult('开始JavaScript测试...', 'info');

            // 测试工具类
            try {
                if (typeof Logger !== 'undefined') {
                    addTestResult('✓ Logger 类已定义', 'success');
                } else {
                    addTestResult('✗ Logger 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ Logger 测试失败: ' + e.message, 'error');
            }

            try {
                if (typeof BaseClass !== 'undefined') {
                    addTestResult('✓ BaseClass 类已定义', 'success');
                } else {
                    addTestResult('✗ BaseClass 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ BaseClass 测试失败: ' + e.message, 'error');
            }

            try {
                if (typeof Utils !== 'undefined') {
                    addTestResult('✓ Utils 类已定义', 'success');
                } else {
                    addTestResult('✗ Utils 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ Utils 测试失败: ' + e.message, 'error');
            }

            // 测试核心类
            try {
                if (typeof Database !== 'undefined') {
                    addTestResult('✓ Database 类已定义', 'success');
                } else {
                    addTestResult('✗ Database 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ Database 测试失败: ' + e.message, 'error');
            }

            // 测试UI类
            try {
                if (typeof UI !== 'undefined') {
                    addTestResult('✓ UI 类已定义', 'success');
                } else {
                    addTestResult('✗ UI 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ UI 测试失败: ' + e.message, 'error');
            }

            try {
                if (typeof EventManager !== 'undefined') {
                    addTestResult('✓ EventManager 类已定义', 'success');
                } else {
                    addTestResult('✗ EventManager 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ EventManager 测试失败: ' + e.message, 'error');
            }

            // 测试业务类
            try {
                if (typeof Student !== 'undefined') {
                    addTestResult('✓ Student 类已定义', 'success');
                } else {
                    addTestResult('✗ Student 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ Student 测试失败: ' + e.message, 'error');
            }

            try {
                if (typeof ExamStep !== 'undefined') {
                    addTestResult('✓ ExamStep 类已定义', 'success');
                } else {
                    addTestResult('✗ ExamStep 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ ExamStep 测试失败: ' + e.message, 'error');
            }

            try {
                if (typeof Question !== 'undefined') {
                    addTestResult('✓ Question 类已定义', 'success');
                } else {
                    addTestResult('✗ Question 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ Question 测试失败: ' + e.message, 'error');
            }

            try {
                if (typeof Settings !== 'undefined') {
                    addTestResult('✓ Settings 类已定义', 'success');
                } else {
                    addTestResult('✗ Settings 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ Settings 测试失败: ' + e.message, 'error');
            }

            // 测试主控制器
            try {
                if (typeof ExamSystem !== 'undefined') {
                    addTestResult('✓ ExamSystem 类已定义', 'success');
                } else {
                    addTestResult('✗ ExamSystem 类未定义', 'error');
                }
            } catch (e) {
                addTestResult('✗ ExamSystem 测试失败: ' + e.message, 'error');
            }

            // 测试全局变量
            try {
                if (typeof questions !== 'undefined') {
                    addTestResult('✓ questions 数据已加载', 'success');
                } else {
                    addTestResult('✗ questions 数据未加载', 'error');
                }
            } catch (e) {
                addTestResult('✗ questions 测试失败: ' + e.message, 'error');
            }

            addTestResult('测试完成！', 'info');
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
