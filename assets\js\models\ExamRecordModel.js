/**
 * ExamRecordModel - 考试记录数据模型
 * 管理考试记录相关的数据结构和验证
 */

class ExamRecordModel extends BaseModel {
    constructor(data = {}) {
        super(data);
        
        // 基本信息
        this.studentNumber = data.studentNumber || '';
        this.examId = data.examId || '';
        this.examType = data.examType || 'interview'; // interview, written, oral
        
        // 时间信息
        this.startTime = data.startTime || null;
        this.endTime = data.endTime || null;
        this.duration = data.duration || 0; // 总用时（秒）
        
        // 步骤信息
        this.currentStep = data.currentStep || 1;
        this.totalSteps = data.totalSteps || 5;
        this.completedSteps = data.completedSteps || [];
        this.stepRecords = data.stepRecords || {};
        
        // 状态信息
        this.status = data.status || 'pending'; // pending, in_progress, completed, cancelled, paused
        this.progress = data.progress || 0; // 进度百分比
        
        // 成绩信息
        this.scores = data.scores || {};
        this.totalScore = data.totalScore || null;
        this.maxScore = data.maxScore || 100;
        this.passed = data.passed || null;
        this.grade = data.grade || '';
        
        // 题目信息
        this.questions = data.questions || {};
        this.answers = data.answers || {};
        
        // 评价信息
        this.comments = data.comments || {};
        this.feedback = data.feedback || '';
        this.recommendations = data.recommendations || [];
        
        // 考官信息
        this.examiners = data.examiners || [];
        this.examRoom = data.examRoom || '';
        
        // 附加信息
        this.notes = data.notes || '';
        this.tags = data.tags || [];
        this.metadata = data.metadata || {};
    }

    /**
     * 验证属性是否有效
     * @param {string} property - 属性名
     * @returns {boolean} 是否有效
     */
    isValidProperty(property) {
        const validProperties = [
            'studentNumber', 'examId', 'examType', 'startTime', 'endTime', 'duration',
            'currentStep', 'totalSteps', 'completedSteps', 'stepRecords',
            'status', 'progress', 'scores', 'totalScore', 'maxScore', 'passed', 'grade',
            'questions', 'answers', 'comments', 'feedback', 'recommendations',
            'examiners', 'examRoom', 'notes', 'tags', 'metadata'
        ];
        return validProperties.includes(property);
    }

    /**
     * 自定义验证
     * @returns {Object} 验证结果
     */
    customValidate() {
        const errors = [];

        // 学号验证
        if (!this.studentNumber || this.studentNumber.trim() === '') {
            errors.push('学号不能为空');
        }

        // 考试类型验证
        const validTypes = ['interview', 'written', 'oral'];
        if (!validTypes.includes(this.examType)) {
            errors.push('考试类型无效');
        }

        // 状态验证
        const validStatuses = ['pending', 'in_progress', 'completed', 'cancelled', 'paused'];
        if (!validStatuses.includes(this.status)) {
            errors.push('考试状态无效');
        }

        // 步骤验证
        if (this.currentStep < 1 || this.currentStep > this.totalSteps) {
            errors.push('当前步骤超出范围');
        }

        // 进度验证
        if (this.progress < 0 || this.progress > 100) {
            errors.push('进度必须在0-100之间');
        }

        // 分数验证
        if (this.totalScore !== null && (this.totalScore < 0 || this.totalScore > this.maxScore)) {
            errors.push('总分超出范围');
        }

        // 时间验证
        if (this.startTime && this.endTime) {
            const start = new Date(this.startTime);
            const end = new Date(this.endTime);
            if (end <= start) {
                errors.push('结束时间必须晚于开始时间');
            }
        }

        return { errors };
    }

    /**
     * 开始考试
     */
    startExam() {
        this.status = 'in_progress';
        this.startTime = new Date().toISOString();
        this.currentStep = 1;
        this.progress = 0;
        this.touch();
    }

    /**
     * 完成考试
     */
    completeExam() {
        this.status = 'completed';
        this.endTime = new Date().toISOString();
        this.currentStep = this.totalSteps;
        this.progress = 100;
        this.calculateDuration();
        this.calculateTotalScore();
        this.touch();
    }

    /**
     * 暂停考试
     */
    pauseExam() {
        if (this.status === 'in_progress') {
            this.status = 'paused';
            this.touch();
        }
    }

    /**
     * 恢复考试
     */
    resumeExam() {
        if (this.status === 'paused') {
            this.status = 'in_progress';
            this.touch();
        }
    }

    /**
     * 取消考试
     */
    cancelExam() {
        this.status = 'cancelled';
        this.endTime = new Date().toISOString();
        this.calculateDuration();
        this.touch();
    }

    /**
     * 进入下一步
     */
    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.currentStep++;
            this.updateProgress();
            this.touch();
        }
    }

    /**
     * 返回上一步
     */
    previousStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateProgress();
            this.touch();
        }
    }

    /**
     * 完成当前步骤
     * @param {Object} stepData - 步骤数据
     */
    completeCurrentStep(stepData = {}) {
        const stepNumber = this.currentStep;
        
        // 记录步骤完成
        if (!this.completedSteps.includes(stepNumber)) {
            this.completedSteps.push(stepNumber);
        }

        // 保存步骤记录
        this.stepRecords[stepNumber] = {
            ...stepData,
            completedAt: new Date().toISOString(),
            stepNumber
        };

        this.updateProgress();
        this.touch();
    }

    /**
     * 设置步骤分数
     * @param {number} stepNumber - 步骤编号
     * @param {number} score - 分数
     */
    setStepScore(stepNumber, score) {
        this.scores[`step${stepNumber}`] = score;
        this.calculateTotalScore();
        this.touch();
    }

    /**
     * 设置步骤题目
     * @param {number} stepNumber - 步骤编号
     * @param {Object} question - 题目对象
     */
    setStepQuestion(stepNumber, question) {
        this.questions[`step${stepNumber}`] = question;
        this.touch();
    }

    /**
     * 设置步骤答案
     * @param {number} stepNumber - 步骤编号
     * @param {string} answer - 答案
     */
    setStepAnswer(stepNumber, answer) {
        this.answers[`step${stepNumber}`] = answer;
        this.touch();
    }

    /**
     * 设置步骤评价
     * @param {number} stepNumber - 步骤编号
     * @param {string} comment - 评价
     */
    setStepComment(stepNumber, comment) {
        this.comments[`step${stepNumber}`] = comment;
        this.touch();
    }

    /**
     * 计算总用时
     */
    calculateDuration() {
        if (this.startTime && this.endTime) {
            const start = new Date(this.startTime);
            const end = new Date(this.endTime);
            this.duration = Math.floor((end - start) / 1000);
        }
    }

    /**
     * 计算总分
     */
    calculateTotalScore() {
        const scores = Object.values(this.scores).filter(score => typeof score === 'number');
        if (scores.length === 0) {
            this.totalScore = null;
            return;
        }

        this.totalScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        
        // 判断是否通过
        this.passed = this.totalScore >= (this.maxScore * 0.6); // 60%及格
    }

    /**
     * 更新进度
     */
    updateProgress() {
        this.progress = Math.round((this.completedSteps.length / this.totalSteps) * 100);
    }

    /**
     * 获取格式化的用时
     * @returns {string} 格式化的用时
     */
    getFormattedDuration() {
        if (!this.duration) {
            return '00:00:00';
        }

        const hours = Math.floor(this.duration / 3600);
        const minutes = Math.floor((this.duration % 3600) / 60);
        const seconds = this.duration % 60;

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    /**
     * 获取状态显示文本
     * @returns {string} 状态文本
     */
    getStatusText() {
        const statusMap = {
            'pending': '待开始',
            'in_progress': '进行中',
            'completed': '已完成',
            'cancelled': '已取消',
            'paused': '已暂停'
        };
        return statusMap[this.status] || '未知状态';
    }

    /**
     * 获取成绩等级
     * @returns {string} 成绩等级
     */
    getGradeLevel() {
        if (this.totalScore === null) {
            return '未评分';
        }

        const percentage = (this.totalScore / this.maxScore) * 100;
        
        if (percentage >= 90) return 'A';
        if (percentage >= 80) return 'B';
        if (percentage >= 70) return 'C';
        if (percentage >= 60) return 'D';
        return 'F';
    }

    /**
     * 是否正在进行中
     * @returns {boolean} 是否进行中
     */
    isInProgress() {
        return this.status === 'in_progress';
    }

    /**
     * 是否已完成
     * @returns {boolean} 是否已完成
     */
    isCompleted() {
        return this.status === 'completed';
    }

    /**
     * 是否已取消
     * @returns {boolean} 是否已取消
     */
    isCancelled() {
        return this.status === 'cancelled';
    }

    /**
     * 是否已暂停
     * @returns {boolean} 是否已暂停
     */
    isPaused() {
        return this.status === 'paused';
    }

    /**
     * 添加考官
     * @param {string} examiner - 考官姓名
     */
    addExaminer(examiner) {
        if (!this.examiners.includes(examiner)) {
            this.examiners.push(examiner);
            this.touch();
        }
    }

    /**
     * 添加推荐意见
     * @param {string} recommendation - 推荐意见
     */
    addRecommendation(recommendation) {
        this.recommendations.push({
            content: recommendation,
            createdAt: new Date().toISOString()
        });
        this.touch();
    }
}

// 导出ExamRecordModel类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExamRecordModel;
} else {
    window.ExamRecordModel = ExamRecordModel;
}
