#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据管理API - 处理数据备份、恢复、清理等操作
"""

from flask import Blueprint, request, send_file
from .base import (
    APIResponse, APIError, ErrorCodes, api_route,
    validate_json, db_manager
)
import os
import json
import time
import shutil
import sqlite3
from datetime import datetime, timedelta

data_bp = Blueprint('data', __name__, url_prefix='/api/data')

BACKUP_DIR = 'assets/data/backups'
DATABASE_PATH = 'assets/data/interview_system.db'

# 确保备份目录存在
os.makedirs(BACKUP_DIR, exist_ok=True)

@data_bp.route('/backup', methods=['POST'])
@api_route(['POST'])
def backup_data():
    """备份数据"""
    data = validate_json() if request.is_json else {}
    
    include_questions = data.get('includeQuestions', True)
    include_records = data.get('includeRecords', True)
    include_settings = data.get('includeSettings', True)
    
    if not any([include_questions, include_records, include_settings]):
        raise APIError(
            ErrorCodes.INVALID_REQUEST,
            "至少需要选择一种数据类型进行备份"
        )
    
    # 生成备份文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_id = f"backup_{timestamp}"
    backup_filename = f"interview_system_backup_{timestamp}.sql"
    backup_path = os.path.join(BACKUP_DIR, backup_filename)
    
    try:
        # 创建备份
        create_backup_file(backup_path, include_questions, include_records, include_settings)
        
        # 获取文件大小
        file_size = os.path.getsize(backup_path)
        
        # 记录备份信息
        backup_info = {
            "backupId": backup_id,
            "filename": backup_filename,
            "size": file_size,
            "includeQuestions": include_questions,
            "includeRecords": include_records,
            "includeSettings": include_settings,
            "createdAt": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 保存备份记录到数据库
        save_backup_record(backup_info)
        
        return APIResponse.success(backup_info, "数据备份成功")
        
    except Exception as e:
        # 清理失败的备份文件
        if os.path.exists(backup_path):
            os.remove(backup_path)
        
        raise APIError(
            ErrorCodes.DATABASE_BACKUP_ERROR,
            f"数据备份失败: {str(e)}"
        )

@data_bp.route('/restore', methods=['POST'])
@api_route(['POST'])
def restore_data():
    """恢复数据"""
    data = validate_json()
    
    if 'backupFile' not in data:
        raise APIError(
            ErrorCodes.MISSING_PARAMETER,
            "缺少必需字段: backupFile"
        )
    
    backup_file = data['backupFile']
    overwrite_existing = data.get('overwriteExisting', False)
    
    backup_path = os.path.join(BACKUP_DIR, backup_file)
    
    if not os.path.exists(backup_path):
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            f"备份文件不存在: {backup_file}"
        )
    
    try:
        # 如果需要覆盖，先备份当前数据
        if overwrite_existing:
            current_backup_path = os.path.join(BACKUP_DIR, f"pre_restore_backup_{int(time.time())}.db")
            shutil.copy2(DATABASE_PATH, current_backup_path)
        
        # 执行恢复
        restore_from_backup(backup_path, overwrite_existing)
        
        return APIResponse.success({
            "backupFile": backup_file,
            "restoredAt": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "overwriteExisting": overwrite_existing
        }, "数据恢复成功")
        
    except Exception as e:
        raise APIError(
            ErrorCodes.DATABASE_BACKUP_ERROR,
            f"数据恢复失败: {str(e)}"
        )

@data_bp.route('/cleanup', methods=['POST'])
@api_route(['POST'])
def cleanup_data():
    """清理数据"""
    data = validate_json()
    
    cleanup_type = data.get('cleanupType', 'old_records')
    retention_days = data.get('retentionDays', 30)
    dry_run = data.get('dryRun', False)
    
    if cleanup_type not in ['old_records', 'unused_questions', 'old_backups']:
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            f"无效的清理类型: {cleanup_type}"
        )
    
    try:
        if cleanup_type == 'old_records':
            result = cleanup_old_records(retention_days, dry_run)
        elif cleanup_type == 'unused_questions':
            result = cleanup_unused_questions(dry_run)
        elif cleanup_type == 'old_backups':
            result = cleanup_old_backups(retention_days, dry_run)
        
        return APIResponse.success(result, "数据清理完成" if not dry_run else "数据清理预览完成")
        
    except Exception as e:
        raise APIError(
            ErrorCodes.DATABASE_QUERY_ERROR,
            f"数据清理失败: {str(e)}"
        )

@data_bp.route('/reset', methods=['POST'])
@api_route(['POST'])
def reset_all_data():
    """重置所有数据"""
    data = validate_json()
    
    confirmation_code = data.get('confirmationCode')
    keep_questions = data.get('keepQuestions', True)
    keep_settings = data.get('keepSettings', True)
    
    if confirmation_code != 'RESET_ALL_DATA':
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            "确认码错误，请输入 'RESET_ALL_DATA'"
        )
    
    try:
        # 创建重置前备份
        backup_path = os.path.join(BACKUP_DIR, f"pre_reset_backup_{int(time.time())}.db")
        shutil.copy2(DATABASE_PATH, backup_path)
        
        # 执行重置
        reset_result = reset_database(keep_questions, keep_settings)
        
        return APIResponse.success({
            "resetAt": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "keepQuestions": keep_questions,
            "keepSettings": keep_settings,
            "backupCreated": backup_path,
            **reset_result
        }, "数据重置成功")
        
    except Exception as e:
        raise APIError(
            ErrorCodes.DATABASE_QUERY_ERROR,
            f"数据重置失败: {str(e)}"
        )

@data_bp.route('/export', methods=['POST'])
@api_route(['POST'])
def export_data():
    """导出数据"""
    data = validate_json() if request.is_json else {}
    
    export_format = data.get('format', 'json')
    include_questions = data.get('includeQuestions', True)
    include_records = data.get('includeRecords', True)
    include_settings = data.get('includeSettings', True)
    
    if export_format not in ['json', 'csv']:
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            f"不支持的导出格式: {export_format}"
        )
    
    try:
        # 生成导出文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_filename = f"interview_system_export_{timestamp}.{export_format}"
        export_path = os.path.join(BACKUP_DIR, export_filename)
        
        if export_format == 'json':
            export_to_json(export_path, include_questions, include_records, include_settings)
        elif export_format == 'csv':
            export_to_csv(export_path, include_questions, include_records, include_settings)
        
        file_size = os.path.getsize(export_path)
        
        return APIResponse.success({
            "filename": export_filename,
            "format": export_format,
            "size": file_size,
            "downloadUrl": f"/api/data/download/{export_filename}",
            "createdAt": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }, "数据导出成功")
        
    except Exception as e:
        raise APIError(
            ErrorCodes.DATABASE_QUERY_ERROR,
            f"数据导出失败: {str(e)}"
        )

@data_bp.route('/download/<filename>', methods=['GET'])
@api_route(['GET'])
def download_file(filename):
    """下载文件"""
    file_path = os.path.join(BACKUP_DIR, filename)
    
    if not os.path.exists(file_path):
        raise APIError(
            ErrorCodes.INVALID_PARAMETER,
            f"文件不存在: {filename}"
        )
    
    return send_file(file_path, as_attachment=True, download_name=filename)

@data_bp.route('/backups', methods=['GET'])
@api_route(['GET'])
def list_backups():
    """获取备份列表"""
    backups = []
    
    # 从文件系统获取备份文件
    if os.path.exists(BACKUP_DIR):
        for filename in os.listdir(BACKUP_DIR):
            if filename.endswith(('.sql', '.db', '.json')):
                file_path = os.path.join(BACKUP_DIR, filename)
                file_stat = os.stat(file_path)
                
                backups.append({
                    "filename": filename,
                    "size": file_stat.st_size,
                    "createdAt": datetime.fromtimestamp(file_stat.st_ctime).strftime("%Y-%m-%d %H:%M:%S"),
                    "modifiedAt": datetime.fromtimestamp(file_stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                })
    
    # 按创建时间倒序排列
    backups.sort(key=lambda x: x['createdAt'], reverse=True)
    
    return APIResponse.success({
        "backups": backups,
        "total": len(backups),
        "backupDir": BACKUP_DIR
    })

def create_backup_file(backup_path, include_questions, include_records, include_settings):
    """创建备份文件"""
    conn = sqlite3.connect(DATABASE_PATH)
    
    with open(backup_path, 'w', encoding='utf-8') as f:
        # 写入备份头信息
        f.write(f"-- Interview System Database Backup\n")
        f.write(f"-- Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"-- Include Questions: {include_questions}\n")
        f.write(f"-- Include Records: {include_records}\n")
        f.write(f"-- Include Settings: {include_settings}\n\n")
        
        # 备份表结构
        for line in conn.iterdump():
            if line.startswith('CREATE TABLE'):
                f.write(line + '\n')
        
        f.write('\n')
        
        # 备份数据
        tables_to_backup = []
        
        if include_questions:
            tables_to_backup.extend(['translation_questions', 'professional_questions'])
        
        if include_records:
            tables_to_backup.extend(['students', 'exam_records', 'student_questions'])
        
        if include_settings:
            tables_to_backup.append('settings')
        
        for table in tables_to_backup:
            cursor = conn.execute(f"SELECT * FROM {table}")
            columns = [description[0] for description in cursor.description]
            
            for row in cursor:
                values = []
                for value in row:
                    if value is None:
                        values.append('NULL')
                    elif isinstance(value, str):
                        values.append(f"'{value.replace('\'', '\'\'')}'")
                    else:
                        values.append(str(value))
                
                f.write(f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(values)});\n")
    
    conn.close()

def restore_from_backup(backup_path, overwrite_existing):
    """从备份恢复数据"""
    if overwrite_existing:
        # 重新创建数据库
        if os.path.exists(DATABASE_PATH):
            os.remove(DATABASE_PATH)
    
    # 执行备份SQL
    conn = sqlite3.connect(DATABASE_PATH)
    
    with open(backup_path, 'r', encoding='utf-8') as f:
        sql_script = f.read()
        conn.executescript(sql_script)
    
    conn.close()

def cleanup_old_records(retention_days, dry_run):
    """清理旧记录"""
    cutoff_date = (datetime.now() - timedelta(days=retention_days)).strftime('%Y-%m-%d')
    
    # 查找要删除的记录
    old_records = db_manager.execute_query(
        "SELECT COUNT(*) as count FROM exam_records WHERE exam_date < ?",
        (cutoff_date,),
        fetch_one=True
    )
    
    count = old_records['count'] if old_records else 0
    
    if not dry_run and count > 0:
        # 删除旧的考试记录
        db_manager.execute_query(
            "DELETE FROM exam_records WHERE exam_date < ?",
            (cutoff_date,)
        )
        
        # 删除相关的学生题目记录
        db_manager.execute_query(
            "DELETE FROM student_questions WHERE created_at < ?",
            (cutoff_date,)
        )
    
    return {
        "cleanupType": "old_records",
        "retentionDays": retention_days,
        "recordsFound": count,
        "recordsDeleted": count if not dry_run else 0,
        "dryRun": dry_run
    }

def cleanup_unused_questions(dry_run):
    """清理未使用的题目"""
    # 这里可以实现清理逻辑
    # 目前返回示例数据
    return {
        "cleanupType": "unused_questions",
        "questionsFound": 0,
        "questionsDeleted": 0,
        "dryRun": dry_run
    }

def cleanup_old_backups(retention_days, dry_run):
    """清理旧备份"""
    cutoff_time = time.time() - (retention_days * 24 * 3600)
    deleted_count = 0
    
    if os.path.exists(BACKUP_DIR):
        for filename in os.listdir(BACKUP_DIR):
            file_path = os.path.join(BACKUP_DIR, filename)
            if os.path.isfile(file_path) and os.path.getctime(file_path) < cutoff_time:
                if not dry_run:
                    os.remove(file_path)
                deleted_count += 1
    
    return {
        "cleanupType": "old_backups",
        "retentionDays": retention_days,
        "backupsFound": deleted_count,
        "backupsDeleted": deleted_count if not dry_run else 0,
        "dryRun": dry_run
    }

def reset_database(keep_questions, keep_settings):
    """重置数据库"""
    tables_to_clear = ['students', 'exam_records', 'student_questions']
    
    if not keep_questions:
        tables_to_clear.extend(['translation_questions', 'professional_questions'])
    
    if not keep_settings:
        tables_to_clear.append('settings')
    
    cleared_counts = {}
    for table in tables_to_clear:
        count_result = db_manager.execute_query(
            f"SELECT COUNT(*) as count FROM {table}",
            fetch_one=True
        )
        cleared_counts[table] = count_result['count'] if count_result else 0
        
        db_manager.execute_query(f"DELETE FROM {table}")
    
    return {
        "clearedTables": tables_to_clear,
        "clearedCounts": cleared_counts
    }

def export_to_json(export_path, include_questions, include_records, include_settings):
    """导出为JSON格式"""
    export_data = {
        "exportInfo": {
            "timestamp": datetime.now().isoformat(),
            "includeQuestions": include_questions,
            "includeRecords": include_records,
            "includeSettings": include_settings
        }
    }
    
    if include_questions:
        export_data["questions"] = {
            "translation": db_manager.execute_query("SELECT * FROM translation_questions", fetch_all=True),
            "professional": db_manager.execute_query("SELECT * FROM professional_questions", fetch_all=True)
        }
    
    if include_records:
        export_data["records"] = {
            "students": db_manager.execute_query("SELECT * FROM students", fetch_all=True),
            "examRecords": db_manager.execute_query("SELECT * FROM exam_records", fetch_all=True),
            "studentQuestions": db_manager.execute_query("SELECT * FROM student_questions", fetch_all=True)
        }
    
    if include_settings:
        export_data["settings"] = db_manager.execute_query("SELECT * FROM settings", fetch_all=True)
    
    with open(export_path, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, ensure_ascii=False, indent=2)

def export_to_csv(export_path, include_questions, include_records, include_settings):
    """导出为CSV格式"""
    import csv
    
    # CSV导出需要分别处理每个表
    # 这里简化实现，实际可以创建多个CSV文件或使用ZIP打包
    with open(export_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Export Info'])
        writer.writerow(['Timestamp', datetime.now().isoformat()])
        writer.writerow(['Include Questions', include_questions])
        writer.writerow(['Include Records', include_records])
        writer.writerow(['Include Settings', include_settings])

def save_backup_record(backup_info):
    """保存备份记录到数据库"""
    try:
        db_manager.execute_query(
            """INSERT INTO backup_records 
               (backup_id, filename, size, created_at, backup_info)
               VALUES (?, ?, ?, ?, ?)""",
            (
                backup_info['backupId'],
                backup_info['filename'],
                backup_info['size'],
                backup_info['createdAt'],
                json.dumps(backup_info)
            )
        )
    except:
        # 如果备份记录表不存在，忽略错误
        pass
