# 研究生复试管理系统

一个基于Flask和Bootstrap的研究生复试流程管理系统，支持面试流程控制、题库管理、计时器功能和随机抽题。

## 核心功能

- **完整的面试流程管理**：支持五个面试环节的流程控制与计时管理
- **试题库管理**：提供翻译题目和专业课题目的管理，支持添加、编辑、删除操作
- **随机抽题功能**：面试过程中可随机抽取试题，并记录到考生记录中
- **考生记录**：记录每个考生的面试情况，包括抽取的题目和当前面试阶段
- **自定义时间设置**：可根据需要调整各环节的面试时间长度
- **现代化UI界面**：基于Bootstrap 5的响应式界面设计，多主题支持

## 系统架构

- **前端**：HTML5/CSS3/JavaScript(ES6+)，Bootstrap 5.3
- **后端**：Python Flask框架
- **数据库**：SQLite (interview_system.db)

## 面试环节

系统支持以下五个面试环节：

1. **中文自我介绍**（默认2分钟）：考生用中文进行自我介绍
2. **英文自我介绍**（默认2分钟）：考生用英文进行自我介绍
3. **英文翻译**（默认5分钟）：随机抽取英文段落进行翻译
4. **专业课问答**（默认10分钟）：随机抽取专业问题进行回答
5. **综合面试**（默认5分钟）：考官综合提问

## 开始使用

### 安装依赖

```bash
pip install -r requirements.txt
```

### 启动系统

Windows环境：
```
start_server.bat
```

或使用Python直接启动：
```
python app.py
```

### 访问系统

启动服务器后，在浏览器中访问：
```
http://localhost:5000
```

## 系统功能导航

1. **主页面 (index.html)**：面试流程控制和考生记录
2. **题库管理 (editor.html)**：翻译题目和专业课题目管理
3. **面试记录**：可查看历史考生面试情况

## 数据库结构

系统使用SQLite数据库(interview_system.db)，包含以下表：

- **translation_questions**：存储翻译题目
- **professional_questions**：存储专业课题目
- **exam_records**：存储考生面试记录
- **settings**：存储系统设置，如各环节的时间长度

## 主要文件结构

```
├── app.py              # 主应用程序
├── questions.py        # 题库管理API模块
├── index.html          # 主界面
├── editor.html         # 题库管理界面
├── interview_system.db # 数据库文件
├── requirements.txt    # 依赖列表
├── start_server.bat    # Windows启动脚本
├── src/
│   ├── database.js     # 前端数据库操作
│   ├── script.js       # 主脚本
│   ├── stepper.js      # 步骤导航组件
│   ├── steps.js        # 步骤控制逻辑
│   ├── timer.js        # 计时器组件
│   ├── interview.html  # 面试界面模板
│   └── ...             # 其他前端资源
└── css/
    ├── style.css       # 主样式
    ├── editor.css      # 编辑器样式
    └── progress.css    # 进度条样式
```

## 自定义设置

您可以通过系统设置修改以下内容：

1. 各环节的面试时间长度
2. 界面主题样式
3. 抽题数量设置

## 系统要求

- Python 3.6+
- 现代浏览器（Chrome、Firefox、Edge等）
- 显示分辨率 1280x720 或更高

## 版权信息

版权所有 © 2025 王文通  
联系方式: <EMAIL> 