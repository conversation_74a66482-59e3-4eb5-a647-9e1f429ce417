/**
 * 按钮事件管理器 - 遵循单一职责原则
 * 负责管理所有按钮的事件绑定和处理
 */
class ButtonManager {
    constructor() {
        this.eventHandlers = new Map();
        this.boundElements = new Set();
        
        this.init();
    }
    
    init() {
        // 等待DOM加载完成后绑定事件
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.bindAllEvents());
        } else {
            this.bindAllEvents();
        }
        
        // 监听动态添加的元素
        this.observeNewElements();
    }
    
    /**
     * 绑定所有按钮事件
     */
    bindAllEvents() {
        // 步骤导航按钮
        this.bindStepNavigation();
        
        // 考试控制按钮
        this.bindExamControls();
        
        // 题目操作按钮
        this.bindQuestionControls();
        
        // 设置相关按钮
        this.bindSettingsControls();
        
        // 通用按钮
        this.bindGenericButtons();
        
        console.log('ButtonManager: All events bound successfully');
    }
    
    /**
     * 绑定步骤导航相关按钮
     */
    bindStepNavigation() {
        // 下一步按钮 - 更新为实际的HTML ID
        this.bindButton('#next-step-btn, #nextBtn, .next-btn', (e) => {
            e.preventDefault();
            this.handleNextStep();
        });

        // 上一步按钮
        this.bindButton('#prev-step-btn, #prevBtn, .prev-btn', (e) => {
            e.preventDefault();
            this.handlePrevStep();
        });

        // 步骤指示器点击
        this.bindButton('.step-item', (e) => {
            // 获取step-item元素，可能点击的是子元素
            const stepItem = e.target.closest('.step-item');
            if (stepItem) {
                const stepNumber = parseInt(stepItem.dataset.step);
                if (stepNumber) {
                    this.handleStepClick(stepNumber);
                }
            }
        });
    }
    
    /**
     * 绑定考试控制按钮
     */
    bindExamControls() {
        // 计时器相关按钮
        this.bindButton('#start-timer-btn, #pause-timer-btn, #reset-timer-btn', (e) => {
            e.preventDefault();
            this.handleTimerControl(e.target);
        });

        // 抽题按钮
        this.bindButton('#draw-translation-question, #draw-professional-question', (e) => {
            e.preventDefault();
            this.handleDrawQuestion(e.target);
        });

        // 考试记录按钮
        this.bindButton('#exam-records', (e) => {
            e.preventDefault();
            this.handleExamRecords();
        });

        // 下一名考生按钮
        this.bindButton('#next-student', (e) => {
            e.preventDefault();
            this.handleNextStudent();
        });

        // 重置考试按钮
        this.bindButton('#resetExamBtn, .reset-exam-btn', (e) => {
            e.preventDefault();
            this.handleResetExam();
        });

        // 设置按钮
        this.bindButton('#settings-btn', (e) => {
            // Bootstrap modal会自动处理，不需要阻止默认行为
        });
    }
    
    /**
     * 绑定题目操作按钮
     */
    bindQuestionControls() {
        // 题目选择按钮
        this.bindButton('.question-nav-btn', (e) => {
            const questionIndex = parseInt(e.target.dataset.questionIndex);
            if (questionIndex !== undefined) {
                this.handleQuestionNavigation(questionIndex);
            }
        });
        
        // 标记题目按钮
        this.bindButton('.mark-question-btn', (e) => {
            const questionIndex = parseInt(e.target.dataset.questionIndex);
            if (questionIndex !== undefined) {
                this.handleMarkQuestion(questionIndex);
            }
        });
        
        // 清除答案按钮
        this.bindButton('.clear-answer-btn', (e) => {
            const questionIndex = parseInt(e.target.dataset.questionIndex);
            if (questionIndex !== undefined) {
                this.handleClearAnswer(questionIndex);
            }
        });
    }
    
    /**
     * 绑定设置相关按钮
     */
    bindSettingsControls() {
        // 保存设置按钮
        this.bindButton('#saveSettingsBtn, .save-settings-btn', (e) => {
            e.preventDefault();
            this.handleSaveSettings();
        });
        
        // 重置设置按钮
        this.bindButton('#resetSettingsBtn, .reset-settings-btn', (e) => {
            e.preventDefault();
            this.handleResetSettings();
        });
        
        // 主题切换按钮
        this.bindButton('.theme-toggle-btn', (e) => {
            this.handleThemeToggle();
        });
    }
    
    /**
     * 绑定通用按钮
     */
    bindGenericButtons() {
        // 模态框关闭按钮
        this.bindButton('.modal .close, .modal-close', (e) => {
            const modal = e.target.closest('.modal');
            if (modal) {
                this.handleCloseModal(modal);
            }
        });
        
        // 确认按钮
        this.bindButton('.confirm-btn', (e) => {
            this.handleConfirm(e.target);
        });
        
        // 取消按钮
        this.bindButton('.cancel-btn', (e) => {
            this.handleCancel(e.target);
        });
    }
    
    /**
     * 绑定单个按钮事件
     * @param {string} selector - 选择器
     * @param {Function} handler - 事件处理函数
     * @param {Object} options - 事件选项
     */
    bindButton(selector, handler, options = {}) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (!this.boundElements.has(element)) {
                const wrappedHandler = this.wrapHandler(handler, element);
                element.addEventListener('click', wrappedHandler, options);
                
                this.eventHandlers.set(element, {
                    selector,
                    handler: wrappedHandler,
                    originalHandler: handler,
                    options
                });
                
                this.boundElements.add(element);
            }
        });
    }
    
    /**
     * 包装事件处理函数
     * @param {Function} handler - 原始处理函数
     * @param {Element} element - 目标元素
     * @returns {Function} 包装后的处理函数
     */
    wrapHandler(handler, element) {
        return (e) => {
            try {
                // 防止重复点击
                if (element.disabled || element.classList.contains('disabled')) {
                    return;
                }
                
                // 添加加载状态
                const originalText = element.textContent;
                element.classList.add('loading');
                
                // 执行处理函数
                const result = handler(e);
                
                // 如果返回Promise，等待完成
                if (result instanceof Promise) {
                    result.finally(() => {
                        element.classList.remove('loading');
                    });
                } else {
                    element.classList.remove('loading');
                }
                
            } catch (error) {
                console.error('Button handler error:', error);
                element.classList.remove('loading');
            }
        };
    }
    
    /**
     * 观察新添加的元素
     */
    observeNewElements() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.bindNewElements(node);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * 为新添加的元素绑定事件
     * @param {Element} container - 容器元素
     */
    bindNewElements(container) {
        // 只为新元素绑定事件，避免重复绑定
        const newButtons = container.querySelectorAll('button, .btn, [role="button"]');
        newButtons.forEach(button => {
            if (!this.boundElements.has(button)) {
                // 根据按钮的类名或ID绑定相应事件
                this.bindButtonIfNeeded(button);
            }
        });
    }

    /**
     * 根据按钮特征绑定相应事件
     * @param {Element} button - 按钮元素
     */
    bindButtonIfNeeded(button) {
        // 检查按钮是否匹配已知的选择器模式
        const selectors = [
            '#nextBtn, .next-btn',
            '#prevBtn, .prev-btn',
            '.step-indicator',
            '#startExamBtn, .start-exam-btn',
            '#pauseExamBtn, .pause-exam-btn',
            '#endExamBtn, .end-exam-btn',
            '#submitBtn, .submit-btn',
            '.question-nav-btn',
            '.mark-question-btn',
            '.clear-answer-btn',
            '#saveSettingsBtn, .save-settings-btn',
            '#resetSettingsBtn, .reset-settings-btn',
            '.theme-toggle-btn',
            '.modal .close, .modal-close',
            '.confirm-btn',
            '.cancel-btn'
        ];

        // 检查按钮是否匹配任何选择器
        for (const selector of selectors) {
            if (button.matches(selector)) {
                // 这里可以根据选择器绑定相应的处理函数
                // 为了简化，暂时不重复绑定
                break;
            }
        }
    }
    
    // 事件处理方法
    handleNextStep() {
        console.log('Next step button clicked');
        if (window.examSystem && window.examSystem.nextStep) {
            window.examSystem.nextStep();
        } else {
            console.warn('ExamSystem not available or nextStep method not found');
        }
    }

    handlePrevStep() {
        console.log('Previous step button clicked');
        if (window.examSystem && window.examSystem.previousStep) {
            window.examSystem.previousStep();
        } else {
            console.warn('ExamSystem not available or previousStep method not found');
        }
    }

    handleStartTimer(button) {
        console.log('Start timer button clicked:', button.id);
        if (window.examSystem && window.examSystem.startTimer) {
            window.examSystem.startTimer(button.id);
        } else {
            console.warn('ExamSystem not available or startTimer method not found');
        }
    }

    handleTimerControl(button) {
        // 确保获取到正确的按钮元素（可能点击的是子元素）
        const actualButton = button.closest('button') || button;
        const buttonId = actualButton.id;

        console.log('Timer control button clicked:', buttonId);

        if (!window.examSystem) {
            console.warn('ExamSystem not available');
            return;
        }

        switch (buttonId) {
            case 'start-timer-btn':
                // 开始当前步骤的计时器
                if (window.examSystem.startCurrentStepTimer) {
                    window.examSystem.startCurrentStepTimer();
                } else {
                    console.warn('startCurrentStepTimer method not found');
                }
                break;
            case 'pause-timer-btn':
                // 暂停计时器
                if (window.examSystem.timer && window.examSystem.timer.pause) {
                    window.examSystem.timer.pause();
                } else {
                    console.warn('Timer pause method not found');
                }
                break;
            case 'reset-timer-btn':
                // 重置计时器
                if (window.examSystem && window.examSystem.resetCurrentStepTimer) {
                    window.examSystem.resetCurrentStepTimer();
                } else if (window.examSystem.timer && window.examSystem.timer.stop) {
                    window.examSystem.timer.stop();
                    console.log('计时器停止并重置');
                } else {
                    console.warn('Timer reset method not found');
                }
                break;
            default:
                console.warn('Unknown timer control button:', button.id);
        }
    }

    handleDrawQuestion(button) {
        console.log('Draw question button clicked:', button.id);
        if (window.examSystem && window.examSystem.drawQuestion) {
            window.examSystem.drawQuestion(button.id);
        } else {
            console.warn('ExamSystem not available or drawQuestion method not found');
        }
    }

    handleExamRecords() {
        console.log('Exam records button clicked');
        if (window.examSystem && window.examSystem.showExamRecords) {
            window.examSystem.showExamRecords();
        } else {
            console.warn('ExamSystem not available or showExamRecords method not found');
        }
    }

    handleNextStudent() {
        console.log('Next student button clicked');
        if (window.examSystem && window.examSystem.nextStudent) {
            window.examSystem.nextStudent();
        } else {
            console.warn('ExamSystem not available or nextStudent method not found');
        }
    }

    handleResetExam() {
        console.log('Reset exam button clicked');
        if (window.examSystem && window.examSystem.resetExam) {
            window.examSystem.resetExam();
        } else {
            console.warn('ExamSystem not available or resetExam method not found');
        }
    }
    
    handleStepClick(stepNumber) {
        if (window.examSystem && window.examSystem.goToStep) {
            window.examSystem.goToStep(stepNumber);
        }
    }
    
    handleStartExam() {
        if (window.examSystem && window.examSystem.startExam) {
            window.examSystem.startExam();
        }
    }
    
    handlePauseExam() {
        if (window.examSystem && window.examSystem.pauseExam) {
            window.examSystem.pauseExam();
        }
    }
    
    handleEndExam() {
        if (window.examSystem && window.examSystem.endExam) {
            window.examSystem.endExam();
        }
    }
    
    handleSubmitAnswers() {
        if (window.examSystem && window.examSystem.submitAnswers) {
            window.examSystem.submitAnswers();
        }
    }
    
    handleQuestionNavigation(questionIndex) {
        if (window.examSystem && window.examSystem.goToQuestion) {
            window.examSystem.goToQuestion(questionIndex);
        }
    }
    
    handleMarkQuestion(questionIndex) {
        if (window.examSystem && window.examSystem.markQuestion) {
            window.examSystem.markQuestion(questionIndex);
        }
    }
    
    handleClearAnswer(questionIndex) {
        if (window.examSystem && window.examSystem.clearAnswer) {
            window.examSystem.clearAnswer(questionIndex);
        }
    }
    
    handleSaveSettings() {
        if (window.examSystem && window.examSystem.saveSettings) {
            window.examSystem.saveSettings();
        }
    }
    
    handleResetSettings() {
        if (window.examSystem && window.examSystem.resetSettings) {
            window.examSystem.resetSettings();
        }
    }
    
    handleThemeToggle() {
        document.body.classList.toggle('dark-theme');
        localStorage.setItem('theme', document.body.classList.contains('dark-theme') ? 'dark' : 'light');
    }
    
    handleCloseModal(modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
    }
    
    handleConfirm(button) {
        const action = button.dataset.action;
        if (action && window.examSystem && window.examSystem[action]) {
            window.examSystem[action]();
        }
    }
    
    handleCancel(button) {
        const modal = button.closest('.modal');
        if (modal) {
            this.handleCloseModal(modal);
        }
    }
    
    /**
     * 销毁管理器
     */
    destroy() {
        this.eventHandlers.forEach((info, element) => {
            element.removeEventListener('click', info.handler, info.options);
        });
        
        this.eventHandlers.clear();
        this.boundElements.clear();
    }
}

// 创建全局实例
window.buttonManager = new ButtonManager();

// 导出类
window.ButtonManager = ButtonManager;
