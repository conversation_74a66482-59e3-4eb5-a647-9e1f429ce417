/**
 * 考试系统应用入口文件
 * 负责系统的启动和全局错误处理
 */

// 全局应用对象
window.ExamApp = {
    system: null,
    isReady: false,
    startTime: Date.now(),
    
    /**
     * 应用初始化
     */
    async init() {
        try {
            console.log('🚀 考试系统启动中...');
            
            // 检查浏览器兼容性
            if (!this.checkBrowserCompatibility()) {
                this.showCompatibilityError();
                return false;
            }
            
            // 显示加载界面
            this.showLoadingScreen();
            
            // 创建并初始化考试系统
            this.system = new ExamSystem();
            const success = await this.system.initialize();

            if (success) {
                // 将考试系统实例暴露到全局作用域，供ButtonManager使用
                window.examSystem = this.system;

                this.isReady = true;
                this.hideLoadingScreen();
                this.showWelcomeMessage();
                console.log('✅ 考试系统启动成功');
                return true;
            } else {
                throw new Error('考试系统初始化失败');
            }
            
        } catch (error) {
            console.error('❌ 考试系统启动失败:', error);
            this.showErrorScreen(error);
            return false;
        }
    },
    
    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        const requiredFeatures = [
            'localStorage',
            'sessionStorage',
            'Promise',
            'fetch',
            'Map',
            'Set',
            'Symbol'
        ];
        
        for (const feature of requiredFeatures) {
            if (!(feature in window)) {
                console.error(`浏览器不支持 ${feature}`);
                return false;
            }
        }
        
        // 检查ES6支持
        try {
            eval('const test = () => {}; class Test {}');
        } catch (e) {
            console.error('浏览器不支持ES6语法');
            return false;
        }
        
        return true;
    },
    
    /**
     * 显示兼容性错误
     */
    showCompatibilityError() {
        document.body.innerHTML = `
            <div class="error-screen">
                <div class="error-content">
                    <h1>🚫 浏览器不兼容</h1>
                    <p>您的浏览器版本过低，无法运行此考试系统。</p>
                    <p>请使用以下浏览器的最新版本：</p>
                    <ul>
                        <li>Chrome 60+</li>
                        <li>Firefox 55+</li>
                        <li>Safari 11+</li>
                        <li>Edge 79+</li>
                    </ul>
                </div>
            </div>
        `;
    },
    
    /**
     * 显示加载界面
     */
    showLoadingScreen() {
        // 创建toast容器（如果不存在）
        this.createToastContainer();

        // 显示初始加载toast
        this.showLoadingToast('考试系统加载中...', '正在初始化系统组件');

        // 模拟加载进度
        this.simulateLoadingProgress();
    },
    
    /**
     * 模拟加载进度
     */
    simulateLoadingProgress() {
        const steps = [
            { text: '正在初始化系统组件...', delay: 300 },
            { text: '正在加载数据库...', delay: 500 },
            { text: '正在初始化界面...', delay: 400 },
            { text: '正在加载题库...', delay: 600 },
            { text: '系统准备就绪...', delay: 300 }
        ];

        let currentStep = 0;
        const showNextStep = () => {
            if (currentStep < steps.length) {
                const step = steps[currentStep];
                this.updateLoadingToast(step.text);
                currentStep++;
                setTimeout(showNextStep, step.delay);
            } else {
                // 所有步骤完成后，稍等一下再隐藏toast
                setTimeout(() => {
                    this.hideLoadingToast();
                }, 500);
            }
        };

        // 开始显示步骤
        setTimeout(showNextStep, 200);
    },
    
    /**
     * 隐藏加载界面
     */
    hideLoadingScreen() {
        // 隐藏加载toast
        this.hideLoadingToast();
    },
    
    /**
     * 显示欢迎消息
     */
    showWelcomeMessage() {
        const loadTime = Date.now() - this.startTime;
        console.log(`🎉 系统加载完成，耗时 ${loadTime}ms`);

        // 显示成功toast
        this.showSuccessToast('考试系统已就绪', `系统加载完成，耗时 ${loadTime}ms`);
    },

    /**
     * 创建toast容器
     */
    createToastContainer() {
        if (!document.getElementById('toast-container')) {
            const toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }
    },

    /**
     * 显示加载toast
     */
    showLoadingToast(title, message) {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;

        const toastId = 'loading-toast';

        // 如果已存在加载toast，先移除
        const existingToast = document.getElementById(toastId);
        if (existingToast) {
            existingToast.remove();
        }

        const toastHTML = `
            <div id="${toastId}" class="toast show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="false">
                <div class="toast-header bg-primary text-white">
                    <div class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></div>
                    <strong class="me-auto">${title}</strong>
                </div>
                <div class="toast-body" id="loading-toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        this.currentLoadingToast = document.getElementById(toastId);
    },

    /**
     * 更新加载toast内容
     */
    updateLoadingToast(message) {
        const toastBody = document.getElementById('loading-toast-body');
        if (toastBody) {
            toastBody.textContent = message;
        }
    },

    /**
     * 隐藏加载toast
     */
    hideLoadingToast() {
        const loadingToast = document.getElementById('loading-toast');
        if (loadingToast) {
            loadingToast.classList.remove('show');
            setTimeout(() => {
                loadingToast.remove();
            }, 300);
        }
    },

    /**
     * 显示成功toast
     */
    showSuccessToast(title, message) {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;

        const toastId = 'success-toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
                <div class="toast-header bg-success text-white">
                    <i class="bi bi-check-circle me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);

        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();

        // 自动移除toast元素
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    },
    
    /**
     * 显示错误界面
     */
    showErrorScreen(error) {
        this.hideLoadingScreen();
        
        const errorHTML = `
            <div class="error-screen">
                <div class="error-content">
                    <h1>⚠️ 系统启动失败</h1>
                    <p>考试系统在启动过程中遇到错误：</p>
                    <div class="error-details">
                        <code>${error.message}</code>
                    </div>
                    <div class="error-actions">
                        <button onclick="location.reload()" class="btn btn-primary">
                            重新加载
                        </button>
                        <button onclick="ExamApp.showDebugInfo()" class="btn btn-secondary">
                            查看详情
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('afterbegin', errorHTML);
    },
    
    /**
     * 显示调试信息
     */
    showDebugInfo() {
        const debugInfo = {
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            url: location.href,
            localStorage: !!window.localStorage,
            sessionStorage: !!window.sessionStorage,
            indexedDB: !!window.indexedDB
        };
        
        console.log('🔍 调试信息:', debugInfo);
        alert('调试信息已输出到控制台，请按F12查看');
    },
    
    /**
     * 全局错误处理
     */
    handleGlobalError(error, source, lineno, colno, errorObj) {
        console.error('全局错误:', {
            message: error,
            source: source,
            line: lineno,
            column: colno,
            error: errorObj
        });
        
        // 如果系统已初始化，使用系统的错误处理
        if (this.isReady && this.system) {
            this.system.handleError(errorObj || new Error(error), '全局错误');
        }
        
        return true; // 阻止默认错误处理
    },
    
    /**
     * 处理未捕获的Promise拒绝
     */
    handleUnhandledRejection(event) {
        console.error('未处理的Promise拒绝:', event.reason);
        
        if (this.isReady && this.system) {
            this.system.handleError(event.reason, '未处理的Promise拒绝');
        }
        
        event.preventDefault(); // 阻止默认处理
    },
    
    /**
     * 应用销毁
     */
    destroy() {
        if (this.system) {
            this.system.destroy();
            this.system = null;
        }
        
        this.isReady = false;
        console.log('🔄 考试系统已销毁');
    }
};

// 设置全局错误处理
window.addEventListener('error', (event) => {
    ExamApp.handleGlobalError(
        event.message,
        event.filename,
        event.lineno,
        event.colno,
        event.error
    );
});

window.addEventListener('unhandledrejection', (event) => {
    ExamApp.handleUnhandledRejection(event);
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    ExamApp.destroy();
});

// DOM加载完成后启动应用
document.addEventListener('DOMContentLoaded', async () => {
    await ExamApp.init();
    initializeSidebarControls(); // 初始化侧边栏控制
});

// 如果DOM已经加载完成，立即启动
if (document.readyState === 'loading') {
    // DOM还在加载中，等待DOMContentLoaded事件
} else {
    // DOM已经加载完成，立即启动
    setTimeout(async () => {
        await ExamApp.init();
        initializeSidebarControls(); // 初始化侧边栏控制
    }, 0);
}

// 侧边栏控制功能
function initializeSidebarControls() {
    const threeColumnLayout = document.querySelector('.three-column-layout');
    const leftSidebar = document.getElementById('leftSidebar');
    const rightSidebar = document.getElementById('rightSidebar');

    // 左侧栏控制按钮
    const leftToggleBtn = document.getElementById('leftToggleBtn');
    const leftMinimizeBtn = document.getElementById('leftMinimizeBtn');

    // 右侧栏控制按钮
    const rightToggleBtn = document.getElementById('rightToggleBtn');
    const rightMinimizeBtn = document.getElementById('rightMinimizeBtn');

    // 中间内容区域控制按钮
    const showLeftBtn = document.getElementById('showLeftBtn');
    const showRightBtn = document.getElementById('showRightBtn');
    const fullscreenBtn = document.getElementById('fullscreenBtn');

    // 左侧栏隐藏/显示
    leftToggleBtn?.addEventListener('click', function() {
        const isHidden = threeColumnLayout.classList.contains('left-hidden');

        if (isHidden) {
            threeColumnLayout.classList.remove('left-hidden');
            leftToggleBtn.innerHTML = '<i class="bi bi-chevron-left"></i>';
            leftToggleBtn.title = '隐藏左侧栏';
            showLeftBtn.style.display = 'none';
        } else {
            threeColumnLayout.classList.remove('left-minimized');
            threeColumnLayout.classList.add('left-hidden');
            leftSidebar.classList.remove('minimized');
            leftToggleBtn.innerHTML = '<i class="bi bi-chevron-right"></i>';
            leftToggleBtn.title = '显示左侧栏';
            showLeftBtn.style.display = 'flex';
        }
    });

    // 左侧栏最小化/恢复
    leftMinimizeBtn?.addEventListener('click', function() {
        const isMinimized = threeColumnLayout.classList.contains('left-minimized');

        if (isMinimized) {
            threeColumnLayout.classList.remove('left-minimized');
            leftSidebar.classList.remove('minimized');
            leftMinimizeBtn.innerHTML = '<i class="bi bi-dash-square"></i>';
            leftMinimizeBtn.title = '最小化左侧栏';
        } else {
            threeColumnLayout.classList.remove('left-hidden');
            threeColumnLayout.classList.add('left-minimized');
            leftSidebar.classList.add('minimized');
            leftMinimizeBtn.innerHTML = '<i class="bi bi-plus-square"></i>';
            leftMinimizeBtn.title = '恢复左侧栏';
            showLeftBtn.style.display = 'none';
        }
    });

    // 右侧栏隐藏/显示
    rightToggleBtn?.addEventListener('click', function() {
        const isHidden = threeColumnLayout.classList.contains('right-hidden');

        if (isHidden) {
            threeColumnLayout.classList.remove('right-hidden');
            rightToggleBtn.innerHTML = '<i class="bi bi-chevron-right"></i>';
            rightToggleBtn.title = '隐藏右侧栏';
            showRightBtn.style.display = 'none';
        } else {
            threeColumnLayout.classList.remove('right-minimized');
            threeColumnLayout.classList.add('right-hidden');
            rightSidebar.classList.remove('minimized');
            rightToggleBtn.innerHTML = '<i class="bi bi-chevron-left"></i>';
            rightToggleBtn.title = '显示右侧栏';
            showRightBtn.style.display = 'flex';
        }
    });

    // 右侧栏最小化/恢复
    rightMinimizeBtn?.addEventListener('click', function() {
        const isMinimized = threeColumnLayout.classList.contains('right-minimized');

        if (isMinimized) {
            threeColumnLayout.classList.remove('right-minimized');
            rightSidebar.classList.remove('minimized');
            rightMinimizeBtn.innerHTML = '<i class="bi bi-dash-square"></i>';
            rightMinimizeBtn.title = '最小化右侧栏';
        } else {
            threeColumnLayout.classList.remove('right-hidden');
            threeColumnLayout.classList.add('right-minimized');
            rightSidebar.classList.add('minimized');
            rightMinimizeBtn.innerHTML = '<i class="bi bi-plus-square"></i>';
            rightMinimizeBtn.title = '恢复右侧栏';
            showRightBtn.style.display = 'none';
        }
    });

    // 显示左侧栏
    showLeftBtn?.addEventListener('click', function() {
        threeColumnLayout.classList.remove('left-hidden');
        leftToggleBtn.innerHTML = '<i class="bi bi-chevron-left"></i>';
        leftToggleBtn.title = '隐藏左侧栏';
        showLeftBtn.style.display = 'none';
    });

    // 显示右侧栏
    showRightBtn?.addEventListener('click', function() {
        threeColumnLayout.classList.remove('right-hidden');
        rightToggleBtn.innerHTML = '<i class="bi bi-chevron-right"></i>';
        rightToggleBtn.title = '隐藏右侧栏';
        showRightBtn.style.display = 'none';
    });

    // 全屏模式
    fullscreenBtn?.addEventListener('click', function() {
        const isFullscreen = threeColumnLayout.classList.contains('fullscreen-mode');

        if (isFullscreen) {
            threeColumnLayout.classList.remove('fullscreen-mode');
            fullscreenBtn.innerHTML = '<i class="bi bi-arrows-fullscreen"></i>';
            fullscreenBtn.title = '全屏模式';
            showLeftBtn.style.display = 'none';
            showRightBtn.style.display = 'none';
        } else {
            threeColumnLayout.classList.add('fullscreen-mode');
            fullscreenBtn.innerHTML = '<i class="bi bi-fullscreen-exit"></i>';
            fullscreenBtn.title = '退出全屏';
            showLeftBtn.style.display = 'flex';
            showRightBtn.style.display = 'flex';
        }
    });

    // 键盘快捷键支持
    document.addEventListener('keydown', function(e) {
        // Ctrl + 1: 切换左侧栏
        if (e.ctrlKey && e.key === '1') {
            e.preventDefault();
            leftToggleBtn?.click();
        }

        // Ctrl + 2: 切换右侧栏
        if (e.ctrlKey && e.key === '2') {
            e.preventDefault();
            rightToggleBtn?.click();
        }

        // Ctrl+Shift+1: 最小化左侧栏
        if (e.ctrlKey && e.shiftKey && e.key === '1') {
            e.preventDefault();
            leftMinimizeBtn?.click();
        }

        // Ctrl+Shift+2: 最小化右侧栏
        if (e.ctrlKey && e.shiftKey && e.key === '2') {
            e.preventDefault();
            rightMinimizeBtn?.click();
        }

        // F11: 全屏模式
        if (e.key === 'F11') {
            e.preventDefault();
            fullscreenBtn?.click();
        }
    });

    // 最小化按钮功能绑定
    function bindMinimizedButtons() {
        // 最小化计时器按钮
        const minimizedStartTimer = document.getElementById('minimizedStartTimer');
        const minimizedResetTimer = document.getElementById('minimizedResetTimer');
        const startTimerBtn = document.getElementById('start-timer-btn');
        const resetTimerBtn = document.getElementById('reset-timer-btn');

        minimizedStartTimer?.addEventListener('click', () => startTimerBtn?.click());
        minimizedResetTimer?.addEventListener('click', () => resetTimerBtn?.click());

        // 最小化题目操作按钮
        const minimizedDrawQuestion = document.getElementById('minimizedDrawQuestion');
        const drawQuestionBtn = document.getElementById('draw-question-btn');

        minimizedDrawQuestion?.addEventListener('click', () => drawQuestionBtn?.click());

        // 最小化流程控制按钮
        const minimizedPrevStep = document.getElementById('minimizedPrevStep');
        const minimizedNextStep = document.getElementById('minimizedNextStep');
        const prevStepBtn = document.getElementById('prev-step-btn');
        const nextStepBtn = document.getElementById('next-step-btn');

        minimizedPrevStep?.addEventListener('click', () => prevStepBtn?.click());
        minimizedNextStep?.addEventListener('click', () => nextStepBtn?.click());

        // 最小化系统操作按钮
        const minimizedExamRecord = document.getElementById('minimizedExamRecord');
        const minimizedNextStudent = document.getElementById('minimizedNextStudent');
        const minimizedSettings = document.getElementById('minimizedSettings');
        const examRecordBtn = document.getElementById('exam-record-btn');
        const nextStudentBtn = document.getElementById('next-student-btn');
        const settingsBtn = document.getElementById('settings-btn');

        minimizedExamRecord?.addEventListener('click', () => examRecordBtn?.click());
        minimizedNextStudent?.addEventListener('click', () => nextStudentBtn?.click());
        minimizedSettings?.addEventListener('click', () => settingsBtn?.click());

        // 最小化流程步骤点击
        const minimizedSteps = document.querySelectorAll('.minimized-step');
        minimizedSteps.forEach(step => {
            step.addEventListener('click', function() {
                const stepNumber = parseInt(this.dataset.step);
                if (stepNumber) {
                    // 获取当前步骤
                    const activeStep = document.querySelector('.step-item.active');
                    let currentStep = 1;
                    if (activeStep) {
                        const stepNumberElement = activeStep.querySelector('.step-number');
                        if (stepNumberElement) {
                            currentStep = parseInt(stepNumberElement.textContent) || 1;
                        }
                    }

                    if (stepNumber !== currentStep) {
                        // 使用考试系统的步骤切换方法
                        if (window.examSystem && window.examSystem.goToStep) {
                            window.examSystem.goToStep(stepNumber);
                        }
                    }
                }
            });
        });
    }

    // 更新最小化流程步骤状态
    function updateMinimizedSteps() {
        const minimizedSteps = document.querySelectorAll('.minimized-step');

        // 从DOM中获取当前步骤信息
        const activeStep = document.querySelector('.step-item.active');
        let currentStep = 1;
        if (activeStep) {
            const stepNumber = activeStep.querySelector('.step-number');
            if (stepNumber) {
                currentStep = parseInt(stepNumber.textContent) || 1;
            }
        }

        const totalSteps = 5;
        const currentStepIndex = currentStep - 1;

        console.log(`[MinimizedSteps] 更新最小化步骤状态: ${currentStep}/${totalSteps}`);

        // 更新步骤状态
        minimizedSteps.forEach((step, index) => {
            step.classList.remove('active', 'completed');

            if (index < currentStepIndex) {
                step.classList.add('completed');
            } else if (index === currentStepIndex) {
                step.classList.add('active');
            }
        });

        // 更新最小化进度指示器
        updateMinimizedProgress(currentStep, totalSteps);
    }

    // 更新最小化进度指示器
    function updateMinimizedProgress(currentStep, totalSteps) {
        const progressText = document.querySelector('.minimized-progress-text');
        const progressFill = document.querySelector('.minimized-progress-fill');

        if (progressText) {
            progressText.textContent = `${currentStep}/${totalSteps}`;
        }

        if (progressFill) {
            const percentage = (currentStep / totalSteps) * 100;
            progressFill.style.width = `${percentage}%`;
        }
    }

    // 更新展开状态的进度指示器
    function updateExpandedProgress(currentStep, totalSteps) {
        console.log(`[Progress] 更新展开进度: ${currentStep}/${totalSteps}`);

        // 查找进度指示器容器
        const progressIndicator = document.querySelector('.progress-indicator');
        if (!progressIndicator) {
            console.warn('[Progress] 未找到进度指示器容器');
            return;
        }

        // 强制重新创建完整的HTML结构，确保进度条显示
        const percentage = (currentStep / totalSteps) * 100;
        progressIndicator.innerHTML = `
            <div class="progress-text">进度: ${currentStep}/${totalSteps}</div>
            <div class="progress-bar-container">
                <div class="progress-bar" style="width: ${percentage}%"></div>
            </div>
        `;

        console.log(`[Progress] 进度指示器已重建: ${currentStep}/${totalSteps} (${percentage}%)`);
        console.log(`[Progress] HTML结构: ${progressIndicator.innerHTML}`);
    }

    // 更新最小化按钮状态
    function updateMinimizedButtons() {
        const minimizedStartTimer = document.getElementById('minimizedStartTimer');
        const minimizedPrevStep = document.getElementById('minimizedPrevStep');
        const minimizedNextStep = document.getElementById('minimizedNextStep');

        // 更新计时器按钮
        if (minimizedStartTimer) {
            if (window.timerState && window.timerState.isRunning) {
                minimizedStartTimer.innerHTML = '<i class="bi bi-pause-fill"></i>';
                minimizedStartTimer.title = '暂停计时';
            } else {
                minimizedStartTimer.innerHTML = '<i class="bi bi-play-fill"></i>';
                minimizedStartTimer.title = '开始计时';
            }
        }

        // 更新流程控制按钮
        if (minimizedPrevStep) {
            const currentStepIndex = window.stepsModule ? window.stepsModule.getCurrentStepIndex() : 0;
            minimizedPrevStep.disabled = currentStepIndex <= 0;
            minimizedPrevStep.style.opacity = currentStepIndex <= 0 ? '0.5' : '1';
        }

        if (minimizedNextStep) {
            const currentStepIndex = window.stepsModule ? window.stepsModule.getCurrentStepIndex() : 0;
            const totalSteps = window.stepsModule ? window.stepsModule.getTotalSteps() : 5;
            minimizedNextStep.disabled = currentStepIndex >= totalSteps - 1;
            minimizedNextStep.style.opacity = currentStepIndex >= totalSteps - 1 ? '0.5' : '1';
        }
    }

    // 初始化最小化功能
    bindMinimizedButtons();

    // 初始化进度显示
    setTimeout(() => {
        // 从DOM中获取当前步骤信息
        const activeStep = document.querySelector('.step-item.active');
        let currentStep = 1;
        if (activeStep) {
            const stepNumber = activeStep.querySelector('.step-number');
            if (stepNumber) {
                currentStep = parseInt(stepNumber.textContent) || 1;
            }
        }

        const totalSteps = 5;
        console.log(`[Progress] 初始化进度显示: ${currentStep}/${totalSteps}`);
        updateExpandedProgress(currentStep, totalSteps);
        updateMinimizedSteps();
    }, 1000);

    // 暴露函数到全局作用域
    window.updateMinimizedSteps = updateMinimizedSteps;
    window.updateMinimizedButtons = updateMinimizedButtons;
    window.updateExpandedProgress = updateExpandedProgress;
}
