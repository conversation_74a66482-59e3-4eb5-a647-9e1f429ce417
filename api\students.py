#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
学生管理API - 处理学生相关的所有操作
"""

from flask import Blueprint, request
from .base import (
    APIResponse, APIError, ErrorCodes, api_route, 
    validate_json, validate_required_fields, validate_student_number,
    validate_step_number, validate_pagination, db_manager
)
import json
import time

students_bp = Blueprint('students', __name__, url_prefix='/api/students')

@students_bp.route('', methods=['POST'])
@api_route(['POST'])
def create_student():
    """创建学生"""
    data = validate_json()
    validate_required_fields(data, ['studentNumber'])
    
    student_number = validate_student_number(data['studentNumber'])
    name = data.get('name', '')
    department = data.get('department', '')
    
    # 检查学生是否已存在
    existing_student = db_manager.execute_query(
        "SELECT * FROM students WHERE student_number = ?",
        (student_number,),
        fetch_one=True
    )
    
    if existing_student:
        raise APIError(
            ErrorCodes.STUDENT_ALREADY_EXISTS,
            f"学生编号 {student_number} 已存在"
        )
    
    # 创建学生记录
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    student_id = db_manager.execute_query(
        """INSERT INTO students (student_number, name, exam_date, current_step, completed)
           VALUES (?, ?, ?, 1, 0)""",
        (student_number, name, current_time)
    )
    
    # 创建考试记录
    exam_record_id = db_manager.execute_query(
        """INSERT INTO exam_records (student_number, exam_date, current_step, status)
           VALUES (?, ?, 1, '准备中')""",
        (student_number, current_time)
    )
    
    # 返回创建的学生信息
    student_data = {
        "id": student_id,
        "studentNumber": student_number,
        "name": name,
        "department": department,
        "currentStep": 1,
        "status": "active",
        "startTime": current_time,
        "createdAt": current_time,
        "examRecordId": exam_record_id
    }
    
    return APIResponse.success(student_data, "学生创建成功")

@students_bp.route('/<student_number>', methods=['GET'])
@api_route(['GET'])
def get_student(student_number):
    """获取学生信息"""
    student_number = validate_student_number(student_number)
    
    # 获取学生基本信息
    student = db_manager.execute_query(
        "SELECT * FROM students WHERE student_number = ?",
        (student_number,),
        fetch_one=True
    )
    
    if not student:
        raise APIError(
            ErrorCodes.STUDENT_NOT_FOUND,
            f"学生编号 {student_number} 不存在"
        )
    
    # 获取考试记录
    exam_record = db_manager.execute_query(
        "SELECT * FROM exam_records WHERE student_number = ? ORDER BY created_at DESC LIMIT 1",
        (student_number,),
        fetch_one=True
    )
    
    # 获取学生题目记录
    student_questions = db_manager.execute_query(
        "SELECT * FROM student_questions WHERE student_id = ? ORDER BY created_at",
        (student['id'],),
        fetch_all=True
    )
    
    # 构建步骤数据
    step_data = {}
    for i in range(1, 6):
        step_data[str(i)] = {
            "stepName": get_step_name(i),
            "status": "pending" if i > student['current_step'] else ("completed" if i < student['current_step'] else "active")
        }
    
    # 添加题目信息到步骤数据
    for question in student_questions:
        step_num = get_step_by_question_type(question['question_type'])
        if step_num and str(step_num) in step_data:
            if 'questions' not in step_data[str(step_num)]:
                step_data[str(step_num)]['questions'] = []
            
            step_data[str(step_num)]['questions'].append({
                "id": f"{question['question_type']}_{question['question_index']}",
                "type": question['question_type'],
                "index": question['question_index'],
                "selectedAt": question['created_at']
            })
    
    student_data = {
        "id": student['id'],
        "studentNumber": student['student_number'],
        "name": student.get('name', ''),
        "currentStep": student['current_step'],
        "status": "completed" if student['completed'] else "active",
        "startTime": student.get('exam_date'),
        "stepData": step_data,
        "examRecord": dict(exam_record) if exam_record else None
    }
    
    return APIResponse.success(student_data)

@students_bp.route('/<student_number>', methods=['PUT'])
@api_route(['PUT'])
def update_student(student_number):
    """更新学生状态"""
    student_number = validate_student_number(student_number)
    data = validate_json()
    
    # 检查学生是否存在
    student = db_manager.execute_query(
        "SELECT * FROM students WHERE student_number = ?",
        (student_number,),
        fetch_one=True
    )
    
    if not student:
        raise APIError(
            ErrorCodes.STUDENT_NOT_FOUND,
            f"学生编号 {student_number} 不存在"
        )
    
    # 准备更新数据
    update_fields = []
    update_values = []
    
    if 'currentStep' in data:
        current_step = validate_step_number(data['currentStep'])
        update_fields.append("current_step = ?")
        update_values.append(current_step)
    
    if 'status' in data:
        if data['status'] == 'completed':
            update_fields.append("completed = ?")
            update_values.append(1)
    
    if 'name' in data:
        update_fields.append("name = ?")
        update_values.append(data['name'])
    
    # 执行更新
    if update_fields:
        update_values.append(student_number)
        db_manager.execute_query(
            f"UPDATE students SET {', '.join(update_fields)} WHERE student_number = ?",
            update_values
        )
    
    # 处理步骤数据更新
    if 'stepData' in data:
        for step_num, step_info in data['stepData'].items():
            if 'questions' in step_info:
                for question_data in step_info['questions']:
                    # 检查题目记录是否已存在
                    existing_question = db_manager.execute_query(
                        """SELECT * FROM student_questions 
                           WHERE student_id = ? AND question_type = ? AND question_index = ?""",
                        (student['id'], question_data['type'], question_data['index']),
                        fetch_one=True
                    )
                    
                    if not existing_question:
                        # 创建新的题目记录
                        db_manager.execute_query(
                            """INSERT INTO student_questions 
                               (student_id, question_type, question_id, question_index, question_data, created_at)
                               VALUES (?, ?, ?, ?, ?, ?)""",
                            (
                                student['id'],
                                question_data['type'],
                                question_data.get('id', 0),
                                question_data['index'],
                                json.dumps(question_data),
                                question_data.get('selectedAt', time.strftime("%Y-%m-%d %H:%M:%S"))
                            )
                        )
    
    # 更新考试记录
    if 'currentStep' in data or 'status' in data:
        exam_status = "已完成" if data.get('status') == 'completed' else "进行中"
        db_manager.execute_query(
            "UPDATE exam_records SET current_step = ?, status = ? WHERE student_number = ?",
            (data.get('currentStep', student['current_step']), exam_status, student_number)
        )
    
    # 返回更新后的学生信息
    return get_student(student_number)

@students_bp.route('', methods=['GET'])
@api_route(['GET'])
def get_students():
    """获取学生列表"""
    page = request.args.get('page', 1)
    limit = request.args.get('limit', 10)
    status = request.args.get('status')
    department = request.args.get('department')
    
    page, limit = validate_pagination(page, limit)
    offset = (page - 1) * limit
    
    # 构建查询条件
    where_conditions = []
    query_params = []
    
    if status:
        if status == 'active':
            where_conditions.append("completed = 0")
        elif status == 'completed':
            where_conditions.append("completed = 1")
    
    if department:
        where_conditions.append("department = ?")
        query_params.append(department)
    
    where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
    
    # 获取总数
    total_query = f"SELECT COUNT(*) as total FROM students{where_clause}"
    total_result = db_manager.execute_query(total_query, query_params, fetch_one=True)
    total = total_result['total'] if total_result else 0
    
    # 获取学生列表
    students_query = f"""
        SELECT s.*, er.status as exam_status, er.exam_date as exam_start_time
        FROM students s
        LEFT JOIN exam_records er ON s.student_number = er.student_number
        {where_clause}
        ORDER BY s.created_at DESC
        LIMIT ? OFFSET ?
    """
    query_params.extend([limit, offset])
    students = db_manager.execute_query(students_query, query_params, fetch_all=True)
    
    # 格式化学生数据
    students_data = []
    for student in students:
        students_data.append({
            "id": student['id'],
            "studentNumber": student['student_number'],
            "name": student.get('name', ''),
            "currentStep": student['current_step'],
            "status": "completed" if student['completed'] else "active",
            "examStatus": student.get('exam_status', '未开始'),
            "startTime": student.get('exam_start_time'),
            "createdAt": student.get('created_at')
        })
    
    # 计算分页信息
    total_pages = (total + limit - 1) // limit
    
    response_data = {
        "students": students_data,
        "pagination": {
            "page": page,
            "limit": limit,
            "total": total,
            "totalPages": total_pages
        }
    }
    
    return APIResponse.success(response_data)

def get_step_name(step_number):
    """获取步骤名称"""
    step_names = {
        1: "中文自我介绍",
        2: "英文自我介绍", 
        3: "英文翻译",
        4: "专业课问答",
        5: "综合面试"
    }
    return step_names.get(step_number, f"步骤{step_number}")

def get_step_by_question_type(question_type):
    """根据题目类型获取步骤编号"""
    type_to_step = {
        'translation': 3,
        'professional': 4
    }
    return type_to_step.get(question_type)
