/**
 * Settings Model - 系统设置数据模型
 * 负责系统设置相关的数据结构定义和基础操作
 */

class Settings {
    /**
     * 构造函数
     * @param {Object} data - 设置数据
     */
    constructor(data = {}) {
        // 时间设置
        this.timeSettings = {
            chineseTime: 2,
            englishTime: 2,
            translationTime: 5,
            professionalTime: 10,
            comprehensiveTime: 8,
            ...data.timeSettings
        };

        // 系统设置
        this.systemSettings = {
            debugMode: false,
            autoSave: true,
            theme: 'light',
            language: 'zh-CN',
            ...data.systemSettings
        };

        // UI设置
        this.uiSettings = {
            fontSize: 'medium',
            showAnimations: true,
            showNotifications: true,
            compactMode: false,
            ...data.uiSettings
        };

        // 考试设置
        this.examSettings = {
            allowPreviousStep: true,
            autoNextStep: false,
            showTimer: true,
            showProgress: true,
            enableQuestionDraw: true,
            ...data.examSettings
        };

        // 数据设置
        this.dataSettings = {
            autoBackup: true,
            backupInterval: 300, // 5分钟
            maxRecords: 1000,
            dataRetentionDays: 30,
            ...data.dataSettings
        };

        this.createdAt = data.createdAt || null;
        this.updatedAt = data.updatedAt || null;
    }

    /**
     * 获取步骤时间设置
     * @param {number} stepNumber - 步骤编号
     * @returns {number} 时间（分钟）
     */
    getStepTime(stepNumber) {
        const timeMap = {
            1: this.timeSettings.chineseTime,
            2: this.timeSettings.englishTime,
            3: this.timeSettings.translationTime,
            4: this.timeSettings.professionalTime,
            5: this.timeSettings.comprehensiveTime
        };
        return timeMap[stepNumber] || 0;
    }

    /**
     * 设置步骤时间
     * @param {number} stepNumber - 步骤编号
     * @param {number} time - 时间（分钟）
     */
    setStepTime(stepNumber, time) {
        const timeMap = {
            1: 'chineseTime',
            2: 'englishTime',
            3: 'translationTime',
            4: 'professionalTime',
            5: 'comprehensiveTime'
        };
        
        const key = timeMap[stepNumber];
        if (key && time > 0) {
            this.timeSettings[key] = time;
            this.updatedAt = new Date().toISOString();
        }
    }

    /**
     * 获取步骤名称
     * @param {number} stepNumber - 步骤编号
     * @returns {string} 步骤名称
     */
    getStepName(stepNumber) {
        const time = this.getStepTime(stepNumber);
        const nameMap = {
            1: `中文自我介绍（${time}分钟）`,
            2: `英文自我介绍（${time}分钟）`,
            3: `英文翻译（${time}分钟）`,
            4: `专业课问答（${time}分钟）`,
            5: `综合面试（${time}分钟）`
        };
        return nameMap[stepNumber] || '未知步骤';
    }

    /**
     * 获取所有步骤名称
     * @returns {Object} 步骤名称映射
     */
    getAllStepNames() {
        const names = {};
        for (let i = 1; i <= 5; i++) {
            names[i] = this.getStepName(i);
        }
        return names;
    }

    /**
     * 获取总考试时间
     * @returns {number} 总时间（分钟）
     */
    getTotalExamTime() {
        return Object.values(this.timeSettings).reduce((total, time) => total + time, 0);
    }

    /**
     * 更新时间设置
     * @param {Object} timeSettings - 时间设置
     */
    updateTimeSettings(timeSettings) {
        this.timeSettings = {
            ...this.timeSettings,
            ...timeSettings
        };
        this.updatedAt = new Date().toISOString();
    }

    /**
     * 更新系统设置
     * @param {Object} systemSettings - 系统设置
     */
    updateSystemSettings(systemSettings) {
        this.systemSettings = {
            ...this.systemSettings,
            ...systemSettings
        };
        this.updatedAt = new Date().toISOString();
    }

    /**
     * 更新UI设置
     * @param {Object} uiSettings - UI设置
     */
    updateUISettings(uiSettings) {
        this.uiSettings = {
            ...this.uiSettings,
            ...uiSettings
        };
        this.updatedAt = new Date().toISOString();
    }

    /**
     * 更新考试设置
     * @param {Object} examSettings - 考试设置
     */
    updateExamSettings(examSettings) {
        this.examSettings = {
            ...this.examSettings,
            ...examSettings
        };
        this.updatedAt = new Date().toISOString();
    }

    /**
     * 更新数据设置
     * @param {Object} dataSettings - 数据设置
     */
    updateDataSettings(dataSettings) {
        this.dataSettings = {
            ...this.dataSettings,
            ...dataSettings
        };
        this.updatedAt = new Date().toISOString();
    }

    /**
     * 重置为默认设置
     */
    resetToDefaults() {
        this.timeSettings = {
            chineseTime: 2,
            englishTime: 2,
            translationTime: 5,
            professionalTime: 10,
            comprehensiveTime: 8
        };

        this.systemSettings = {
            debugMode: false,
            autoSave: true,
            theme: 'light',
            language: 'zh-CN'
        };

        this.uiSettings = {
            fontSize: 'medium',
            showAnimations: true,
            showNotifications: true,
            compactMode: false
        };

        this.examSettings = {
            allowPreviousStep: true,
            autoNextStep: false,
            showTimer: true,
            showProgress: true,
            enableQuestionDraw: true
        };

        this.dataSettings = {
            autoBackup: true,
            backupInterval: 300,
            maxRecords: 1000,
            dataRetentionDays: 30
        };

        this.updatedAt = new Date().toISOString();
    }

    /**
     * 获取设置分类列表
     * @returns {Array} 设置分类
     */
    getCategories() {
        return [
            {
                key: 'timeSettings',
                name: '时间设置',
                description: '各步骤的时间限制设置'
            },
            {
                key: 'systemSettings',
                name: '系统设置',
                description: '系统基础功能设置'
            },
            {
                key: 'uiSettings',
                name: 'UI设置',
                description: '用户界面相关设置'
            },
            {
                key: 'examSettings',
                name: '考试设置',
                description: '考试流程相关设置'
            },
            {
                key: 'dataSettings',
                name: '数据设置',
                description: '数据管理相关设置'
            }
        ];
    }

    /**
     * 获取设置项定义
     * @returns {Object} 设置项定义
     */
    getSettingsDefinition() {
        return {
            timeSettings: {
                chineseTime: { type: 'number', min: 1, max: 60, unit: '分钟', label: '中文自我介绍时间' },
                englishTime: { type: 'number', min: 1, max: 60, unit: '分钟', label: '英文自我介绍时间' },
                translationTime: { type: 'number', min: 1, max: 60, unit: '分钟', label: '英文翻译时间' },
                professionalTime: { type: 'number', min: 1, max: 60, unit: '分钟', label: '专业课问答时间' },
                comprehensiveTime: { type: 'number', min: 1, max: 60, unit: '分钟', label: '综合面试时间' }
            },
            systemSettings: {
                debugMode: { type: 'boolean', label: '调试模式' },
                autoSave: { type: 'boolean', label: '自动保存' },
                theme: { type: 'select', options: ['light', 'dark'], label: '界面主题' },
                language: { type: 'select', options: ['zh-CN', 'en-US'], label: '界面语言' }
            },
            uiSettings: {
                fontSize: { type: 'select', options: ['small', 'medium', 'large'], label: '字体大小' },
                showAnimations: { type: 'boolean', label: '显示动画效果' },
                showNotifications: { type: 'boolean', label: '显示通知' },
                compactMode: { type: 'boolean', label: '紧凑模式' }
            },
            examSettings: {
                allowPreviousStep: { type: 'boolean', label: '允许返回上一步' },
                autoNextStep: { type: 'boolean', label: '自动进入下一步' },
                showTimer: { type: 'boolean', label: '显示计时器' },
                showProgress: { type: 'boolean', label: '显示进度条' },
                enableQuestionDraw: { type: 'boolean', label: '启用题目抽取' }
            },
            dataSettings: {
                autoBackup: { type: 'boolean', label: '自动备份' },
                backupInterval: { type: 'number', min: 60, max: 3600, unit: '秒', label: '备份间隔' },
                maxRecords: { type: 'number', min: 100, max: 10000, label: '最大记录数' },
                dataRetentionDays: { type: 'number', min: 1, max: 365, unit: '天', label: '数据保留天数' }
            }
        };
    }

    /**
     * 转换为数据库格式
     * @returns {Array} 数据库格式的设置数组
     */
    toDatabase() {
        const settings = [];
        
        // 时间设置
        Object.entries(this.timeSettings).forEach(([key, value]) => {
            settings.push({
                category: 'time',
                key,
                value: value.toString(),
                data_type: 'number'
            });
        });

        // 系统设置
        Object.entries(this.systemSettings).forEach(([key, value]) => {
            settings.push({
                category: 'system',
                key,
                value: value.toString(),
                data_type: typeof value
            });
        });

        // UI设置
        Object.entries(this.uiSettings).forEach(([key, value]) => {
            settings.push({
                category: 'ui',
                key,
                value: value.toString(),
                data_type: typeof value
            });
        });

        // 考试设置
        Object.entries(this.examSettings).forEach(([key, value]) => {
            settings.push({
                category: 'exam',
                key,
                value: value.toString(),
                data_type: typeof value
            });
        });

        // 数据设置
        Object.entries(this.dataSettings).forEach(([key, value]) => {
            settings.push({
                category: 'data',
                key,
                value: value.toString(),
                data_type: typeof value
            });
        });

        return settings;
    }

    /**
     * 转换为API格式
     * @returns {Object} API格式的数据
     */
    toAPI() {
        return {
            timeSettings: this.timeSettings,
            systemSettings: this.systemSettings,
            uiSettings: this.uiSettings,
            examSettings: this.examSettings,
            dataSettings: this.dataSettings,
            stepNames: this.getAllStepNames(),
            totalExamTime: this.getTotalExamTime(),
            categories: this.getCategories(),
            definition: this.getSettingsDefinition(),
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    /**
     * 验证设置数据
     * @returns {Object} 验证结果
     */
    validate() {
        const errors = [];
        const definition = this.getSettingsDefinition();

        // 验证时间设置
        Object.entries(this.timeSettings).forEach(([key, value]) => {
            const def = definition.timeSettings[key];
            if (def) {
                if (value < def.min || value > def.max) {
                    errors.push(`${def.label}必须在${def.min}-${def.max}之间`);
                }
            }
        });

        // 验证其他设置
        ['systemSettings', 'uiSettings', 'examSettings', 'dataSettings'].forEach(category => {
            Object.entries(this[category]).forEach(([key, value]) => {
                const def = definition[category][key];
                if (def) {
                    if (def.type === 'number' && (typeof value !== 'number' || isNaN(value))) {
                        errors.push(`${def.label}必须是有效数字`);
                    } else if (def.type === 'boolean' && typeof value !== 'boolean') {
                        errors.push(`${def.label}必须是布尔值`);
                    } else if (def.type === 'select' && !def.options.includes(value)) {
                        errors.push(`${def.label}必须是有效选项`);
                    }
                }
            });
        });

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 克隆设置对象
     * @returns {Settings} 克隆的设置对象
     */
    clone() {
        return new Settings(this.toAPI());
    }

    /**
     * 从数据库数据创建Settings实例
     * @param {Array} dbData - 数据库设置数组
     * @returns {Settings} Settings实例
     */
    static fromDatabase(dbData) {
        const data = {
            timeSettings: {},
            systemSettings: {},
            uiSettings: {},
            examSettings: {},
            dataSettings: {}
        };

        dbData.forEach(setting => {
            const category = setting.category;
            const key = setting.key;
            let value = setting.value;

            // 类型转换
            if (setting.data_type === 'number') {
                value = parseFloat(value);
            } else if (setting.data_type === 'boolean') {
                value = value === 'true';
            }

            // 分类映射
            const categoryMap = {
                'time': 'timeSettings',
                'system': 'systemSettings',
                'ui': 'uiSettings',
                'exam': 'examSettings',
                'data': 'dataSettings'
            };

            const mappedCategory = categoryMap[category];
            if (mappedCategory && data[mappedCategory]) {
                data[mappedCategory][key] = value;
            }
        });

        return new Settings(data);
    }

    /**
     * 从API数据创建Settings实例
     * @param {Object} apiData - API数据
     * @returns {Settings} Settings实例
     */
    static fromAPI(apiData) {
        return new Settings(apiData);
    }

    /**
     * 创建默认设置
     * @returns {Settings} 默认设置实例
     */
    static createDefault() {
        return new Settings();
    }
}

// 导出Settings类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Settings;
} else {
    window.Settings = Settings;
}
