/* 
 * 研究生复试流程控制系统
 * 版权所有© 2025 王文通
 * 联系方式：<EMAIL>
 */

/* 全局样式 */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 0.25rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Microsoft YaHei', sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.4;
    padding: 10px 0;
    margin: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 20px);
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    flex-shrink: 0;
}

header h1 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.6rem;
}

.actions {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 8px 12px;
    border-radius: var(--border-radius);
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.btn:hover {
    opacity: 0.9;
}

.settings-btn {
    background-color: var(--primary-color);
    color: white;
}

.record-btn {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.manager-btn {
    background-color: var(--dark-color);
    color: white;
    text-decoration: none;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.success-btn {
    background-color: var(--success-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.warning-btn {
    background-color: var(--warning-color);
    color: var(--dark-color);
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.danger-btn {
    background-color: var(--danger-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.back-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

/* 学生信息样式 */
.student-info-container {
    background-color: var(--light-color);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    flex-shrink: 0;
}

.student-info {
    font-weight: bold;
    font-size: 1rem;
}

/* 步骤样式 */
.step {
    display: none;
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: var(--box-shadow);
    flex: 1;
    overflow-y: auto;
}

.step.active {
    display: flex;
    flex-direction: column;
}

.step h2 {
    color: var(--primary-color);
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
    flex-shrink: 0;
}

.sub-step {
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f9f9f9;
    border-radius: var(--border-radius);
    flex-shrink: 0;
}

.sub-step h3 {
    color: var(--dark-color);
    margin-top: 0;
    font-size: 1.1rem;
    margin-bottom: 10px;
}

/* 计时器样式 */
.timer-group {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.time-limit {
    margin: 0;
    font-weight: bold;
}

.timer-display {
    background-color: var(--dark-color);
    color: white;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 5px;
}

.timer-display.hidden {
    display: none;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.final-buttons {
    display: flex;
    gap: 10px;
}

/* 翻译和专业题相关样式 */
.translation-control, .professional-questions {
    margin: 20px 0;
}

.remaining-questions {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: var(--border-radius);
}

.remaining-questions h4 {
    color: var(--dark-color);
    margin-top: 0;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 模态框样式优化 */
.modal {
    z-index: 1055 !important;
}

.modal-backdrop {
    background-color: transparent !important;
    pointer-events: none !important;
}

.modal-dialog {
    z-index: 1060 !important;
    position: relative;
    pointer-events: auto !important;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5) !important;
}

.modal-content {
    border-radius: 8px;
    overflow: hidden;
    z-index: 1061 !important;
    position: relative;
    pointer-events: auto !important;
    background-color: white !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.modal-content * {
    pointer-events: auto !important;
}

/* 时间到模态框专用样式 */
.time-up-modal {
    z-index: 1055 !important;
}

.time-up-modal + .modal-backdrop,
.time-up-modal ~ .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.5) !important;
    pointer-events: none !important;
}

.time-up-modal .modal-dialog {
    z-index: 1060 !important;
    position: relative;
    pointer-events: auto !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.time-up-modal .modal-content {
    border-radius: 8px;
    overflow: hidden;
    z-index: 1061 !important;
    position: relative;
    pointer-events: auto !important;
    background-color: white !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.modal-header {
    background-color: var(--primary-color);
    color: white;
    border-bottom: none;
}

.modal-header h5 {
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-body {
    padding: 20px;
}

.settings-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* 确保输入框可见 */
.form-control, input[type="number"] {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    position: static !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .timer-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .button-group {
        flex-direction: column;
        gap: 15px;
    }
    
    .actions {
        width: 100%;
        justify-content: space-between;
    }
}

/* 动画效果 */
.step {
    transition: var(--transition);
}

.step.active {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 拖拽计时器样式 */
.draggable-timer {
    width: 200px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    user-select: none;
}

.timer-header {
    background: #f8f9fa;
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    border-radius: 8px 8px 0 0;
    cursor: grab;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.timer-header:active {
    cursor: grabbing;
}

.timer-title {
    font-size: 12px;
    font-weight: 600;
    color: #333;
}

.timer-controls {
    display: flex;
    gap: 4px;
}

.timer-controls button {
    width: 16px;
    height: 16px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 12px;
    line-height: 1;
    color: #666;
    border-radius: 2px;
}

.timer-controls button:hover {
    background: #e9ecef;
    color: #333;
}

.draggable-timer .timer-body {
    padding: 12px;
}

.draggable-timer .timer-display {
    text-align: center;
    margin-bottom: 12px;
}

.draggable-timer .time-text {
    font-size: 24px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    color: #333;
}

.timer-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.timer-buttons button {
    padding: 4px 8px;
    font-size: 11px;
    border: 1px solid #ddd;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.timer-buttons button:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.timer-buttons .btn-start {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.timer-buttons .btn-start:hover {
    background: #218838;
}

.timer-buttons .btn-pause {
    background: #ffc107;
    color: #212529;
    border-color: #ffc107;
}

.timer-buttons .btn-pause:hover {
    background: #e0a800;
}

.timer-buttons .btn-reset {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.timer-buttons .btn-reset:hover {
    background: #c82333;
}

/* 题目网格容器样式 */
.question-grid-container {
    margin: 20px 0;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.question-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.question-grid-title {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.question-grid-legend {
    display: flex;
    gap: 15px;
    align-items: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #6c757d;
}

.legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid;
}

.legend-dot.available {
    background: #ffffff;
    border-color: #dee2e6;
}

.legend-dot.used {
    background: #d4edda;
    border-color: #28a745;
}

.legend-dot.selected {
    background: #007bff;
    border-color: #007bff;
}

/* 题目网格样式 */
.question-numbers-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 10px;
    margin: 15px 0;
}

.question-number {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: #ffffff;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.question-number:hover {
    border-color: #007bff;
    background: #e3f2fd;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
}

.question-number.available {
    background: #ffffff;
    border-color: #dee2e6;
    color: #495057;
}

.question-number.used {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.question-number.selected {
    background: #007bff;
    border-color: #007bff;
    color: white;
    animation: pulse 1.5s infinite;
    box-shadow: 0 0 20px rgba(0, 123, 255, 0.5);
}

.question-number-text {
    font-size: 14px;
    font-weight: 600;
}

.question-status-icon {
    position: absolute;
    top: -3px;
    right: -3px;
    font-size: 10px;
    opacity: 0.8;
}

.question-grid-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
    font-size: 12px;
    color: #6c757d;
}

/* 题目占位符样式 */
.question-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    text-align: center;
    color: #6c757d;
}

.question-placeholder-icon {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 15px;
}

.question-placeholder-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #495057;
}

.question-placeholder-hint {
    font-size: 14px;
    color: #6c757d;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 响应式设计 - 适应不同分辨率 */

/* 大屏幕优化 (1920px+) */
@media (min-width: 1920px) {
    .container {
        max-width: 1600px;
        padding: 20px;
    }

    header h1 {
        font-size: 1.8rem;
    }

    .step {
        padding: 20px;
    }

    .question-numbers-grid {
        grid-template-columns: repeat(12, 1fr);
        gap: 12px;
    }

    .question-number {
        width: 55px;
        height: 55px;
        font-size: 15px;
    }
}

/* 中等屏幕优化 (1366px - 1919px) */
@media (min-width: 1366px) and (max-width: 1919px) {
    .container {
        max-width: 1200px;
        padding: 18px;
    }

    .question-numbers-grid {
        grid-template-columns: repeat(10, 1fr);
        gap: 10px;
    }

    .question-number {
        width: 50px;
        height: 50px;
        font-size: 14px;
    }
}

/* 小屏幕优化 (1024px - 1365px) */
@media (min-width: 1024px) and (max-width: 1365px) {
    body {
        padding: 5px 0;
        line-height: 1.3;
    }

    .container {
        max-width: 1000px;
        padding: 12px;
        min-height: calc(100vh - 10px);
    }

    header {
        padding-bottom: 10px;
        margin-bottom: 10px;
    }

    header h1 {
        font-size: 1.4rem;
    }

    .student-info-container {
        padding: 6px 10px;
        margin-bottom: 10px;
    }

    .student-info {
        font-size: 0.95rem;
    }

    .step {
        padding: 12px;
        margin-bottom: 10px;
    }

    .step h2 {
        font-size: 1.2rem;
        margin-bottom: 12px;
        padding-bottom: 6px;
    }

    .sub-step {
        padding: 10px;
        margin-bottom: 12px;
    }

    .sub-step h3 {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .question-numbers-grid {
        grid-template-columns: repeat(10, 1fr);
        gap: 8px;
        margin: 12px 0;
    }

    .question-number {
        width: 45px;
        height: 45px;
        font-size: 13px;
    }

    .question-grid-container {
        margin: 15px 0;
        padding: 15px;
    }

    .question-placeholder {
        padding: 30px 15px;
    }

    .question-placeholder-icon {
        font-size: 40px;
        margin-bottom: 12px;
    }

    .question-placeholder-text {
        font-size: 15px;
        margin-bottom: 6px;
    }

    .question-placeholder-hint {
        font-size: 13px;
    }

    .btn {
        padding: 6px 10px;
        font-size: 0.85rem;
    }

    .timer-group {
        gap: 10px;
        margin-bottom: 15px;
    }

    .timer-display {
        padding: 6px 12px;
        font-size: 0.9rem;
    }
}

/* 平板屏幕优化 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    body {
        padding: 5px;
        line-height: 1.2;
    }

    .container {
        max-width: 100%;
        padding: 10px;
        margin: 0 5px;
        min-height: calc(100vh - 10px);
    }

    header {
        flex-direction: column;
        gap: 10px;
        padding-bottom: 8px;
        margin-bottom: 8px;
    }

    header h1 {
        font-size: 1.3rem;
    }

    .actions {
        flex-wrap: wrap;
        gap: 8px;
    }

    .student-info {
        font-size: 0.9rem;
    }

    .step {
        padding: 10px;
        margin-bottom: 8px;
    }

    .step h2 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .question-numbers-grid {
        grid-template-columns: repeat(8, 1fr);
        gap: 6px;
        margin: 10px 0;
    }

    .question-number {
        width: 40px;
        height: 40px;
        font-size: 12px;
    }

    .question-grid-container {
        margin: 12px 0;
        padding: 12px;
    }

    .question-grid-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .question-grid-legend {
        gap: 10px;
    }

    .btn {
        padding: 5px 8px;
        font-size: 0.8rem;
    }
}

/* 手机屏幕优化 (最大767px) */
@media (max-width: 767px) {
    body {
        padding: 2px;
        line-height: 1.2;
    }

    .container {
        max-width: 100%;
        padding: 8px;
        margin: 0 2px;
        min-height: calc(100vh - 4px);
        border-radius: 4px;
    }

    header {
        flex-direction: column;
        gap: 8px;
        padding-bottom: 6px;
        margin-bottom: 6px;
    }

    header h1 {
        font-size: 1.2rem;
        text-align: center;
    }

    .actions {
        flex-wrap: wrap;
        gap: 6px;
        justify-content: center;
    }

    .student-info-container {
        padding: 5px 8px;
        margin-bottom: 8px;
    }

    .student-info {
        font-size: 0.85rem;
        text-align: center;
    }

    .step {
        padding: 8px;
        margin-bottom: 6px;
    }

    .step h2 {
        font-size: 1rem;
        margin-bottom: 8px;
        text-align: center;
    }

    .sub-step {
        padding: 8px;
        margin-bottom: 8px;
    }

    .sub-step h3 {
        font-size: 0.95rem;
        margin-bottom: 6px;
    }

    .question-numbers-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 4px;
        margin: 8px 0;
    }

    .question-number {
        width: 35px;
        height: 35px;
        font-size: 11px;
    }

    .question-grid-container {
        margin: 10px 0;
        padding: 10px;
    }

    .question-grid-header {
        flex-direction: column;
        align-items: center;
        gap: 6px;
    }

    .question-grid-title {
        font-size: 0.9rem;
        text-align: center;
    }

    .question-grid-legend {
        gap: 8px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .legend-item {
        font-size: 11px;
    }

    .question-grid-stats {
        flex-direction: column;
        gap: 4px;
        text-align: center;
        font-size: 11px;
    }

    .question-placeholder {
        padding: 20px 10px;
    }

    .question-placeholder-icon {
        font-size: 32px;
        margin-bottom: 10px;
    }

    .question-placeholder-text {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .question-placeholder-hint {
        font-size: 12px;
    }

    .btn {
        padding: 4px 6px;
        font-size: 0.75rem;
    }

    .timer-group {
        flex-direction: column;
        gap: 8px;
        margin-bottom: 10px;
        align-items: center;
    }

    .timer-display {
        padding: 5px 10px;
        font-size: 0.85rem;
    }

    .button-group {
        flex-direction: column;
        gap: 8px;
        margin-top: 15px;
    }

    .final-buttons {
        flex-direction: column;
        gap: 6px;
    }
}

/* 模态框美化样式 */
.modal-content {
    border-radius: 12px !important;
    overflow: hidden;
}

.modal-header {
    border-radius: 12px 12px 0 0 !important;
}

.modal-footer {
    border-radius: 0 0 12px 12px !important;
}

/* 设置模态框特殊样式 */
.settings-section {
    border-left: 4px solid var(--bs-primary);
    padding-left: 1rem;
    margin-bottom: 1.5rem;
}

.section-title {
    font-weight: 600;
    color: var(--bs-dark);
    margin-bottom: 0.75rem;
}

.bg-light-blue {
    background-color: rgba(13, 110, 253, 0.05) !important;
}

/* 考试记录模态框样式 */
.record-list {
    max-height: 400px;
    overflow-y: auto;
}

.record-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.record-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.record-status {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

.record-status.completed {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.record-status.in-progress {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

/* 确认对话框美化 */
.modal-dialog-centered {
    min-height: calc(100vh - 3rem);
}

.modal-dialog-centered .modal-content {
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}