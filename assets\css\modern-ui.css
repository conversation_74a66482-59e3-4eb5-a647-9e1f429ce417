/**
 * 现代化UI样式 - 研究生复试流程控制系统
 * 版权所有© 2025 王文通
 * 联系方式：<EMAIL>
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 主色调 - 学术蓝 */
    --primary-500: #2563eb;
    --primary-400: #3b82f6;
    --primary-600: #1d4ed8;
    --primary-700: #1e40af;
    --primary-300: #60a5fa;
    --primary-200: #93c5fd;
    --primary-100: #bfdbfe;
    --primary-50: #dbeafe;
    --primary-25: #eff6ff;

    /* 辅助色 - 优化考试环境 */
    --success-500: #059669;
    --success-400: #10b981;
    --success-600: #047857;
    --success-300: #34d399;
    --success-100: #a7f3d0;
    --success-50: #d1fae5;

    --warning-500: #f59e0b;
    --warning-400: #fbbf24;
    --warning-600: #d97706;
    --warning-300: #fcd34d;
    --warning-100: #fef3c7;
    --warning-50: #fffbeb;

    --error-500: #dc2626;
    --error-400: #ef4444;
    --error-600: #b91c1c;
    --error-300: #f87171;
    --error-100: #fecaca;
    --error-50: #fef2f2;

    --info-500: #0284c7;
    --info-400: #0ea5e9;
    --info-600: #0369a1;
    --info-300: #38bdf8;
    --info-100: #bae6fd;
    --info-50: #e0f2fe;
    
    /* 中性色 - 考试环境优化 */
    --gray-900: #0f172a;
    --gray-800: #1e293b;
    --gray-700: #334155;
    --gray-600: #475569;
    --gray-500: #64748b;
    --gray-400: #94a3b8;
    --gray-300: #cbd5e1;
    --gray-200: #e2e8f0;
    --gray-100: #f1f5f9;
    --gray-50: #f8fafc;

    /* 背景色 - 护眼配色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-accent: #eff6ff;

    /* 基础颜色 */
    --white: #ffffff;
    --black: #000000;
    
    /* 渐变色 - 考试系统专用 */
    --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --gradient-secondary: linear-gradient(135deg, #475569 0%, #334155 100%);
    --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-error: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    --gradient-info: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);

    /* 特殊效果渐变 */
    --gradient-academic: linear-gradient(135deg, #1e40af 0%, #2563eb 50%, #3b82f6 100%);
    --gradient-gold: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    --gradient-bg: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    
    /* 间距系统 */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-10: 40px;
    --space-12: 48px;
    --space-16: 64px;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
    
    /* 圆角系统 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 20px;
    --radius-full: 9999px;
    
    /* 字体系统 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* 过渡效果 */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    
    /* Z-index层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ===== 全局重置和基础样式 ===== */
* {
    box-sizing: border-box;
}

html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-900);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== 现代化页面布局系统 ===== */
.modern-page-layout {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    background: var(--gradient-bg);
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* ===== 容器样式 ===== */
.modern-container {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background: transparent;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 页面头部区域 */
.modern-page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 100;
    padding: var(--space-3) var(--space-6);
    flex-shrink: 0;
    width: 100%;
}

.modern-page-header .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* 页面底部区域 */
.modern-page-footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--gray-200);
    position: sticky;
    bottom: 0;
    z-index: 100;
    padding: var(--space-4) 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.modern-page-footer .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

/* 操作区域布局优化 */
.modern-action-toolbar {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.modern-action-group {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2);
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    backdrop-filter: blur(10px);
}

.modern-action-group.primary {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border-color: var(--primary-200);
}

.modern-action-group.secondary {
    background: rgba(248, 250, 252, 0.9);
    border-color: var(--gray-300);
}

/* 主要操作按钮 */
.modern-primary-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

/* 次要操作按钮 */
.modern-secondary-actions {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

/* 操作按钮增强 */
.modern-action-btn {
    position: relative;
    overflow: hidden;
}

.modern-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.modern-action-btn:hover::before {
    left: 100%;
}

/* 快捷键提示 */
.modern-shortcut-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    margin-top: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: rgba(0, 0, 0, 0.05);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

.modern-shortcut-key {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-sm);
    padding: 2px 6px;
    font-family: monospace;
    font-size: var(--font-size-xs);
    font-weight: 600;
}

/* ===== 视觉层次和信息架构增强 ===== */

/* 页面分隔线 */
.modern-section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--gray-300), transparent);
    margin: var(--space-8) 0;
    position: relative;
}

.modern-section-divider::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

/* 内容层次标识 */
.modern-content-level-1 {
    position: relative;
    padding-left: var(--space-4);
}

.modern-content-level-1::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.modern-content-level-2 {
    position: relative;
    padding-left: var(--space-3);
    margin-left: var(--space-4);
}

.modern-content-level-2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-300);
    border-radius: var(--radius-full);
}

/* 信息卡片层次 */
.modern-info-hierarchy {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.modern-info-primary {
    background: var(--gradient-subtle);
    border: 2px solid var(--primary-200);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
}

.modern-info-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-primary);
}

.modern-info-secondary {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    backdrop-filter: blur(10px);
}

.modern-info-tertiary {
    background: rgba(248, 250, 252, 0.6);
    border: 1px solid var(--gray-100);
    border-radius: var(--radius-md);
    padding: var(--space-4);
}

/* 视觉焦点元素 */
.modern-focus-element {
    position: relative;
    z-index: 10;
}

.modern-focus-element::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(45deg, var(--primary-200), var(--success-200));
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity var(--transition-base);
}

.modern-focus-element:hover::after {
    opacity: 0.3;
}

/* 内容组织网格 */
.modern-content-grid {
    display: grid;
    gap: var(--space-6);
}

.modern-content-grid.two-column {
    grid-template-columns: 1fr 1fr;
}

.modern-content-grid.three-column {
    grid-template-columns: 1fr 1fr 1fr;
}

.modern-content-grid.sidebar-layout {
    grid-template-columns: 300px 1fr;
}

/* 信息标签系统 */
.modern-info-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
}

.modern-info-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-3);
    background: var(--gray-100);
    color: var(--gray-700);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: 1px solid var(--gray-200);
}

.modern-info-tag.primary {
    background: var(--primary-100);
    color: var(--primary-700);
    border-color: var(--primary-200);
}

.modern-info-tag.success {
    background: var(--success-100);
    color: var(--success-700);
    border-color: var(--success-200);
}

.modern-info-tag.warning {
    background: var(--warning-100);
    color: var(--warning-700);
    border-color: var(--warning-200);
}

/* 主内容区域布局 */
.modern-main-layout {
    display: flex;
    flex-direction: column;
    gap: 0;
    flex: 1;
    min-height: 0;
    height: 100%;
    overflow: hidden;
}

/* 头部区域布局 */
.modern-header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-2) var(--space-4);
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
    border-bottom: 1px solid rgba(79, 70, 229, 0.1);
    flex-shrink: 0;
}

.modern-header-left {
    grid-area: left;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.modern-header-right {
    grid-area: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

/* 计时器区域 */
.modern-timer-area {
    grid-area: timer;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 步骤导航区域 */
.modern-stepper-area {
    grid-area: stepper;
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--radius-xl);
    padding: var(--space-4);
    border: 1px solid var(--gray-200);
}

/* 内容区域 */
.modern-content-area {
    grid-area: content;
    min-height: 0;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    border: 1px solid var(--gray-200);
}

/* 步骤面板布局 */
.modern-step-pane {
    display: none;
    animation: fadeIn 0.4s ease-in-out;
}

.modern-step-pane.active {
    display: block;
    animation: slideInRight 0.5s ease-out;
}

/* 内容卡片布局优化 */
.modern-content-card-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
}

/* 双列布局（适用于有题目展示的步骤） */
.modern-content-two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
    align-items: start;
}

/* 题目展示区域 */
.modern-question-display-area {
    background: var(--gradient-subtle);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.modern-question-display-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

/* 操作区域 */
.modern-action-area {
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    border: 1px solid var(--gray-200);
    backdrop-filter: blur(10px);
}

/* 表单区域优化 */
.modern-form-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    border: 1px solid var(--gray-200);
    margin-bottom: var(--space-4);
}

.modern-form-section:last-child {
    margin-bottom: 0;
}

/* 信息提示区域 */
.modern-info-section {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    border: 1px solid rgba(59, 130, 246, 0.2);
    margin-bottom: var(--space-4);
}

.modern-info-section .info-icon {
    color: var(--primary-500);
    font-size: var(--font-size-xl);
    margin-right: var(--space-3);
}

/* 内容区域的滚动条样式 */
.modern-content-area::-webkit-scrollbar {
    width: 8px;
}

.modern-content-area::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: var(--radius-full);
}

.modern-content-area::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-full);
}

.modern-content-area::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 头部装饰元素 */
.modern-header-decoration {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

.modern-header-decoration i {
    font-size: var(--font-size-lg);
    color: var(--primary-400);
}

/* ===== 现代化卡片组件 ===== */
.modern-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-base);
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.modern-card-header {
    background: var(--gradient-primary);
    color: white;
    padding: var(--space-6);
    border: none;
}

.modern-card-header h5 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.modern-card-body {
    padding: var(--space-6);
}

/* ===== 现代化按钮组件 ===== */
.modern-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1.5;
    border-radius: var(--radius-lg);
    border: none;
    cursor: pointer;
    transition: all var(--transition-base);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    min-height: 44px;
}

.modern-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 移除有问题的滑动效果，改用简单的hover效果 */
.modern-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    filter: brightness(1.05);
}

/* 主要按钮 */
.modern-btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.modern-btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.modern-btn-primary:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s ease;
}

.modern-btn-primary:focus {
    outline: none;
    box-shadow: var(--shadow-lg), 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* 成功按钮 */
.modern-btn-success {
    background: var(--gradient-success);
    color: white;
    box-shadow: var(--shadow-md);
}

.modern-btn-success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.modern-btn-success:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s ease;
}

.modern-btn-success:focus {
    outline: none;
    box-shadow: var(--shadow-lg), 0 0 0 3px rgba(16, 185, 129, 0.2);
}

/* 警告按钮 */
.modern-btn-warning {
    background: var(--gradient-warning);
    color: white;
    box-shadow: var(--shadow-md);
}

.modern-btn-warning:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.modern-btn-warning:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s ease;
}

.modern-btn-warning:focus {
    outline: none;
    box-shadow: var(--shadow-lg), 0 0 0 3px rgba(245, 158, 11, 0.2);
}

/* 次要按钮 */
.modern-btn-outline {
    background: white;
    color: var(--primary-500);
    border: 2px solid var(--primary-500);
    box-shadow: var(--shadow-sm);
}

.modern-btn-outline:hover:not(:disabled) {
    background: var(--primary-50);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.modern-btn-outline:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.1s ease;
}

.modern-btn-outline:focus {
    outline: none;
    box-shadow: var(--shadow-md), 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* ===== 现代化输入框 ===== */
.modern-form-group {
    margin-bottom: var(--space-6);
}

.modern-form-label {
    display: block;
    margin-bottom: var(--space-2);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
}

.modern-form-control {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--gray-900);
    background: white;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-sm);
}

.modern-form-control:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    transform: translateY(-1px);
}

.modern-form-control:hover {
    border-color: var(--gray-400);
}

/* ===== 加载动画 ===== */
.modern-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== 现代化步骤导航 ===== */
.modern-stepper {
    margin-bottom: var(--space-8);
}

.modern-step-indicators {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--space-8);
    position: relative;
    padding: 0 var(--space-4);
}

.modern-step-indicators::before {
    content: '';
    position: absolute;
    top: 24px;
    left: var(--space-4);
    right: var(--space-4);
    height: 3px;
    background: var(--gray-300);
    border-radius: var(--radius-full);
    z-index: 1;
}

.modern-step-indicators::after {
    content: '';
    position: absolute;
    top: 24px;
    left: var(--space-4);
    height: 3px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    z-index: 2;
    transition: width var(--transition-slow);
    width: 0%;
}

.modern-step-indicator {
    position: relative;
    z-index: 3;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 120px;
}

.modern-step-number {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-full);
    background: white;
    color: var(--gray-500);
    font-weight: 600;
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-3);
    border: 3px solid var(--gray-300);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-sm);
}

.modern-step-title {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    text-align: center;
    font-weight: 500;
    transition: all var(--transition-base);
    line-height: 1.3;
}

/* 激活状态 */
.modern-step-indicator.active .modern-step-number {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-500);
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.modern-step-indicator.active .modern-step-title {
    color: var(--primary-600);
    font-weight: 600;
}

/* 完成状态 */
.modern-step-indicator.completed .modern-step-number {
    background: var(--gradient-success);
    color: white;
    border-color: var(--success-500);
    box-shadow: var(--shadow-md);
}

.modern-step-indicator.completed .modern-step-title {
    color: var(--success-600);
    font-weight: 500;
}

/* 步骤内容面板 */
.modern-step-pane {
    display: none;
    animation: fadeInUp var(--transition-base);
}

.modern-step-pane.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 现代化计时器 ===== */
.modern-timer {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 2px solid var(--primary-200);
    border-radius: var(--radius-2xl);
    padding: var(--space-4) var(--space-6);
    box-shadow: var(--shadow-xl);
    z-index: var(--z-fixed);
    text-align: center;
    min-width: 200px;
    transition: all var(--transition-base);
}

.modern-timer:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-2xl);
}

.modern-timer-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-600);
    margin-bottom: var(--space-1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-timer-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    margin-bottom: var(--space-1);
}

.modern-timer-step {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    font-weight: 500;
}

/* 计时器警告状态 */
.modern-timer.warning {
    border-color: var(--warning-400);
    background: var(--gradient-warning);
    animation: pulse 1s infinite;
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.4);
}

.modern-timer.warning .modern-timer-value {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-weight: 700;
}

.modern-timer.warning .modern-timer-label {
    color: rgba(255, 255, 255, 0.9);
}

.modern-timer.warning .modern-timer-step {
    color: rgba(255, 255, 255, 0.8);
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(245, 158, 11, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(245, 158, 11, 0.6);
    }
}

/* 计时器危险状态 */
.modern-timer.danger {
    border-color: var(--error-400);
    background: var(--gradient-error);
    animation: shake 0.5s infinite;
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
}

.modern-timer.danger .modern-timer-value {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-weight: 700;
}

.modern-timer.danger .modern-timer-label {
    color: rgba(255, 255, 255, 0.9);
}

.modern-timer.danger .modern-timer-step {
    color: rgba(255, 255, 255, 0.8);
}

@keyframes shake {
    0%, 100% { transform: translateX(0) scale(1); }
    10% { transform: translateX(-3px) scale(1.02); }
    20% { transform: translateX(3px) scale(1.02); }
    30% { transform: translateX(-3px) scale(1.02); }
    40% { transform: translateX(3px) scale(1.02); }
    50% { transform: translateX(-2px) scale(1.01); }
    60% { transform: translateX(2px) scale(1.01); }
    70% { transform: translateX(-2px) scale(1.01); }
    80% { transform: translateX(2px) scale(1.01); }
    90% { transform: translateX(-1px) scale(1); }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .modern-container {
        margin: var(--space-4);
        padding: var(--space-4);
        border-radius: var(--radius-xl);
    }

    .modern-card-header,
    .modern-card-body {
        padding: var(--space-4);
    }

    .modern-btn {
        width: 100%;
        justify-content: center;
    }

    .modern-step-indicators {
        padding: 0;
    }

    .modern-step-title {
        display: none;
    }

    .modern-step-number {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }

    .modern-timer {
        top: var(--space-4);
        right: var(--space-4);
        left: var(--space-4);
        position: relative;
        margin-bottom: var(--space-6);
        padding: var(--space-4);
    }

    .modern-timer-value {
        font-size: var(--font-size-2xl);
    }

    .modern-question-grid {
        grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
        gap: var(--space-2);
    }

    .modern-question-number {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-sm);
    }

    .btn-toolbar {
        flex-direction: column;
        gap: var(--space-3);
    }

    .btn-group {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
        width: 100%;
    }
}

@media (max-width: 480px) {
    .modern-container {
        margin: var(--space-2);
        padding: var(--space-3);
    }

    .modern-card-header,
    .modern-card-body {
        padding: var(--space-3);
    }

    .modern-timer {
        padding: var(--space-3);
    }

    .modern-timer-value {
        font-size: var(--font-size-xl);
    }

    .modern-question-grid {
        grid-template-columns: repeat(auto-fill, minmax(45px, 1fr));
    }

    .modern-question-number {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-xs);
    }

    .modern-step-number {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-sm);
    }
}

/* ===== 现代化题目网格 ===== */
.modern-question-grid-container {
    margin-bottom: var(--space-6);
}

.modern-question-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.modern-question-number {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60px;
    height: 60px;
    background: white;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all var(--transition-base);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.modern-question-number::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left var(--transition-base);
}

.modern-question-number:hover::before {
    left: 100%;
}

.modern-question-number:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-400);
}

/* 选中状态 */
.modern-question-number.selected {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-500);
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg);
    animation: selectedPulse 2s ease-in-out infinite;
}

@keyframes selectedPulse {
    0%, 100% {
        box-shadow: var(--shadow-lg);
    }
    50% {
        box-shadow: 0 8px 25px -5px rgba(79, 70, 229, 0.4), 0 10px 10px -5px rgba(79, 70, 229, 0.04);
    }
}

/* 已使用状态 */
.modern-question-number.used {
    background: var(--gray-200);
    color: var(--gray-500);
    border-color: var(--gray-300);
    opacity: 0.7;
    text-decoration: line-through;
    cursor: not-allowed;
}

.modern-question-number.used:hover {
    transform: none;
    box-shadow: var(--shadow-sm);
}

/* 全局已使用状态 */
.modern-question-number.globally-used {
    background: var(--gray-100);
    color: var(--gray-400);
    border-color: var(--gray-200);
    opacity: 0.5;
    cursor: not-allowed;
}

/* 高亮动画状态 */
.modern-question-number.highlight {
    animation: questionHighlight 1s ease-in-out infinite alternate;
}

@keyframes questionHighlight {
    0% {
        transform: scale(1);
        background: white;
        border-color: var(--gray-300);
    }
    100% {
        transform: scale(1.1);
        background: var(--gradient-primary);
        color: white;
        border-color: var(--primary-500);
        box-shadow: var(--shadow-lg);
    }
}

/* ===== 现代化题目内容展示 ===== */
.modern-question-container {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-top: var(--space-4);
}

.modern-question-content {
    padding: var(--space-6);
    max-height: 400px;
    overflow-y: auto;
    line-height: 1.8;
}

.modern-question-content::-webkit-scrollbar {
    width: 6px;
}

.modern-question-content::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-full);
}

.modern-question-content::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--radius-full);
}

.modern-question-content::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

.modern-question-content p {
    margin-bottom: var(--space-4);
    color: var(--gray-800);
    font-size: var(--font-size-lg);
}

.modern-question-content img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin: var(--space-4) 0;
}

/* 题目占位符 */
.modern-question-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-12);
    color: var(--gray-500);
    text-align: center;
}

.modern-question-placeholder-icon {
    width: 64px;
    height: 64px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-4);
    font-size: var(--font-size-2xl);
}

.modern-question-placeholder-text {
    font-size: var(--font-size-lg);
    font-weight: 500;
    margin-bottom: var(--space-2);
}

.modern-question-placeholder-hint {
    font-size: var(--font-size-sm);
    color: var(--gray-400);
}

/* ===== 现代化加载状态 ===== */
.modern-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-3);
    padding: var(--space-6);
    color: var(--primary-600);
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--gray-200);
}

.modern-loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--primary-200);
    border-top: 3px solid var(--primary-500);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 现代化状态提示 ===== */
.modern-alert {
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-lg);
    border: 1px solid;
    margin-bottom: var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-weight: 500;
}

.modern-alert-info {
    background: var(--info-50, #EFF6FF);
    border-color: var(--info-200, #BFDBFE);
    color: var(--info-800, #1E40AF);
}

.modern-alert-success {
    background: var(--success-50, #ECFDF5);
    border-color: var(--success-200, #BBF7D0);
    color: var(--success-800, #166534);
}

.modern-alert-warning {
    background: var(--warning-50, #FFFBEB);
    border-color: var(--warning-200, #FED7AA);
    color: var(--warning-800, #9A3412);
}

.modern-alert-error {
    background: var(--error-50, #FEF2F2);
    border-color: var(--error-200, #FECACA);
    color: var(--error-800, #991B1B);
}

/* ===== 动画和过渡效果 ===== */

/* 页面加载动画 */
.modern-container {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 卡片出现动画 */
.modern-card {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 步骤切换动画 */
.modern-step-pane {
    animation: fadeIn 0.4s ease-in-out;
}

.modern-step-pane.active {
    animation: slideInRight 0.5s ease-out;
}

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 计时器出现动画 */
.modern-timer {
    animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 题目网格动画 */
.modern-question-number {
    animation: popIn 0.3s ease-out;
    animation-fill-mode: both;
}

.modern-question-number:nth-child(1) { animation-delay: 0.1s; }
.modern-question-number:nth-child(2) { animation-delay: 0.15s; }
.modern-question-number:nth-child(3) { animation-delay: 0.2s; }
.modern-question-number:nth-child(4) { animation-delay: 0.25s; }
.modern-question-number:nth-child(5) { animation-delay: 0.3s; }
.modern-question-number:nth-child(6) { animation-delay: 0.35s; }
.modern-question-number:nth-child(7) { animation-delay: 0.4s; }
.modern-question-number:nth-child(8) { animation-delay: 0.45s; }
.modern-question-number:nth-child(9) { animation-delay: 0.5s; }
.modern-question-number:nth-child(10) { animation-delay: 0.55s; }

@keyframes popIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 按钮点击波纹效果 */
.modern-btn {
    position: relative;
    overflow: hidden;
}

.modern-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.modern-btn:active::after {
    width: 300px;
    height: 300px;
}

/* 加载状态动画增强 */
.modern-loading {
    animation: fadeIn 0.3s ease-in-out;
}

.modern-loading-spinner {
    animation: spin 1s linear infinite, pulse 2s ease-in-out infinite alternate;
}

/* 警告状态动画增强 */
.modern-alert {
    animation: slideInDown 0.4s ease-out;
}

@keyframes slideInDown {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 题号网格样式 ===== */
.question-numbers-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: var(--space-2);
    padding: var(--space-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    margin-bottom: var(--space-4);
}

.question-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: white;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.question-number:hover {
    border-color: var(--primary-400);
    background: var(--primary-50);
    color: var(--primary-600);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.question-number.highlighting {
    background: var(--warning-500);
    border-color: var(--warning-600);
    color: white;
    animation: pulse 0.3s ease-in-out;
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4);
}

.question-number.selected {
    background: var(--success-500);
    border-color: var(--success-600);
    color: white;
    transform: scale(1.15);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    animation: selectedPulse 0.6s ease-in-out;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1.1);
    }
    50% {
        transform: scale(1.2);
    }
}

@keyframes selectedPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    }
    50% {
        transform: scale(1.15);
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
    100% {
        transform: scale(1.15);
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }
}

/* ===== 三栏布局样式 ===== */

/* 三栏布局容器 - 铺满浏览器 */
.three-column-layout {
    display: flex;
    flex-direction: row;
    gap: 0;
    height: 100%;
    width: 100%;
    flex: 1;
    overflow: hidden;
    background: var(--white);
    position: relative;
}

/* 三栏布局状态类 - 确保正确的宽度控制 */
.three-column-layout .left-sidebar {
    width: 20% !important;
    min-width: 20% !important;
}

.three-column-layout .right-sidebar {
    width: 20% !important;
    min-width: 20% !important;
}

.three-column-layout.left-hidden .left-sidebar {
    width: 0 !important;
    min-width: 0 !important;
    overflow: hidden;
    border-right: none;
}

.three-column-layout.right-hidden .right-sidebar {
    width: 0 !important;
    min-width: 0 !important;
    overflow: hidden;
    border-left: none;
}

.three-column-layout.left-minimized .left-sidebar {
    width: 60px !important;
    min-width: 60px !important;
}

.three-column-layout.right-minimized .right-sidebar {
    width: 60px !important;
    min-width: 60px !important;
}

.three-column-layout.fullscreen-mode .left-sidebar,
.three-column-layout.fullscreen-mode .right-sidebar {
    width: 0 !important;
    min-width: 0 !important;
    overflow: hidden;
    border: none;
}

/* 确保大屏幕下始终是水平三栏布局 */
@media (min-width: 1024px) {
    .three-column-layout {
        display: flex;
        flex-direction: row;
    }
}

/* 左侧边栏 - 流程节点 */
.left-sidebar {
    width: 20%;
    flex-shrink: 0;
    background: var(--gradient-bg);
    border-right: 1px solid var(--gray-200);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow-y: auto;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
}

/* 侧边栏控制按钮容器 */
.sidebar-controls {
    position: absolute;
    top: var(--space-2); /* 适当增加顶部距离 */
    right: var(--space-2); /* 适当增加右侧距离 */
    display: flex;
    gap: var(--space-1);
    z-index: 1000;
}

.sidebar-controls.right-controls {
    left: var(--space-2); /* 适当增加左侧距离 */
    right: auto;
}

/* 侧边栏控制按钮 */
.sidebar-toggle-btn,
.sidebar-minimize-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: var(--radius-sm);
    background: rgba(255, 255, 255, 0.9);
    color: var(--gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sidebar-toggle-btn:hover,
.sidebar-minimize-btn:hover {
    background: var(--white);
    color: var(--primary-600);
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.process-steps {
    padding: var(--space-2) var(--space-5) var(--space-5) var(--space-5); /* 减少顶部内边距，给进度条更多空间 */
}

.sidebar-header {
    margin-top: 0; /* 移除顶部边距，让进度条完全显示 */
    margin-bottom: var(--space-6);
    padding-right: var(--space-12); /* 增加右侧内边距避免被按钮遮挡 */
}

.sidebar-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.sidebar-title i {
    color: var(--primary-600);
    font-size: 1.2em;
}

/* 展开状态进度指示器 - 优化美观样式，与step-item对齐 */
.sidebar-header .progress-indicator {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    border-radius: var(--radius-lg); /* 与step-item保持一致 */
    padding: var(--space-4); /* 与step-item保持一致 */
    border: 1px solid var(--primary-200); /* 与step-item边框厚度一致 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* 与step-item阴影一致 */
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
    width: calc(100% + var(--space-12)); /* 补偿sidebar-header的padding-right */
    box-sizing: border-box;
    position: static;
    min-height: 70px;
    overflow: visible;
    transition: all 0.3s ease;
    margin: 0 calc(-1 * var(--space-12)) 0 0; /* 向右扩展，抵消padding-right的影响 */
}

.sidebar-header .progress-text {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-800);
    text-align: center;
    margin: 0;
    white-space: nowrap;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.sidebar-header .progress-indicator .progress-bar-container {
    width: 100% !important;
    height: 20px !important;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    position: relative !important;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(255, 255, 255, 0.5) !important;
    border: 1px solid #cbd5e1 !important;
    flex-shrink: 0 !important;
    margin-top: 0 !important;
    display: block !important;
}

.sidebar-header .progress-indicator .progress-bar {
    height: 100% !important;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%) !important;
    border-radius: 10px !important;
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    min-width: 12px !important;
    position: relative !important;
    display: block !important;
}

/* 添加进度条的光泽效果 */
.sidebar-header .progress-indicator .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), transparent);
    border-radius: 10px 10px 0 0;
}

/* 添加进度条动画效果 */
.sidebar-header .progress-indicator .progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.step-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    margin-top: var(--space-6); /* 增加顶部边距，给进度条留出空间 */
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    background: var(--bg-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.step-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gray-300);
    transition: all 0.3s ease;
}

.step-item:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-300);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
}

.step-item:hover::before {
    background: var(--gradient-gold);
    width: 5px;
}

.step-item.active {
    background: var(--bg-accent);
    border-color: var(--primary-400);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.2);
}

.step-item.active::before {
    background: var(--gradient-academic);
    width: 6px;
}

.step-item.completed {
    background: var(--success-50);
    border-color: var(--success-300);
}

.step-item.completed::before {
    background: var(--gradient-success);
    width: 5px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gray-200);
    color: var(--gray-700);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
    transition: all var(--transition-base);
    border: 2px solid var(--gray-300);
}

.step-item.active .step-number {
    background: var(--primary-500);
    color: var(--white);
}

.step-item.completed .step-number {
    background: var(--success-500);
    color: var(--white);
}

.step-content {
    flex: 1;
    min-width: 0;
}

.step-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    line-height: 1.3;
}

.step-duration {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--space-1);
    margin-bottom: var(--space-2);
}

.step-duration i {
    color: var(--gray-500);
}

.step-description {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    line-height: 1.4;
    font-style: italic;
}

/* 步骤状态图标控制 */
.step-status {
    position: relative;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-status i {
    position: absolute;
    font-size: 16px;
    transition: all 0.3s ease;
    opacity: 0;
    transform: scale(0.8);
    display: none; /* 默认隐藏所有图标 */
}

/* 默认状态：只显示pending图标 */
.step-status .pending-icon {
    display: block;
    opacity: 1;
    transform: scale(1);
    color: var(--gray-400);
}

/* 当前步骤：只显示current图标 */
.step-item.active .step-status .pending-icon,
.step-item.active .step-status .completed-icon {
    display: none;
}

.step-item.active .step-status .current-icon {
    display: block;
    opacity: 1;
    transform: scale(1);
    color: var(--primary-500);
}

/* 已完成步骤：只显示completed图标 */
.step-item.completed .step-status .pending-icon,
.step-item.completed .step-status .current-icon {
    display: none;
}

.step-item.completed .step-status .completed-icon {
    display: block;
    opacity: 1;
    transform: scale(1);
    color: var(--success-500);
}

.step-item.active .step-title {
    color: var(--primary-700);
}

.step-item.active .step-duration {
    color: var(--primary-600);
}

.step-item.active .step-description {
    color: var(--primary-500);
}

.step-item.completed .step-title {
    color: var(--success-700);
}

.step-item.completed .step-duration {
    color: var(--success-600);
}

/* 中间内容区域 */
.center-content {
    flex: 1;
    background: var(--white);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    position: relative;
    transition: all 0.3s ease;
}

/* 中间内容区域控制按钮 */
.content-controls {
    position: absolute;
    top: var(--space-2);
    right: var(--space-2);
    display: flex;
    gap: var(--space-1);
    z-index: 1000;
}

.content-control-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: var(--radius-md);
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-control-btn:hover {
    background: var(--primary-100);
    color: var(--primary-700);
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(37, 99, 235, 0.2);
}

/* 最小化状态样式 */
.left-sidebar.minimized .process-steps,
.right-sidebar.minimized .control-panel {
    display: none;
}

.left-sidebar.minimized .minimized-process-steps,
.right-sidebar.minimized .minimized-control-panel {
    display: flex;
}

.left-sidebar.minimized,
.right-sidebar.minimized {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: var(--space-2) var(--space-1);
    overflow-y: auto;
}

/* 最小化流程步骤样式 */
.minimized-process-steps {
    display: none;
    flex-direction: column;
    gap: var(--space-2);
    width: 100%;
    padding: var(--space-2) 0;
}

/* 最小化进度指示器 */
.minimized-progress-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: var(--space-3);
    padding: var(--space-2);
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--radius-md);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.minimized-progress-text {
    font-size: 10px;
    font-weight: var(--font-weight-bold);
    color: var(--primary-700);
    margin-bottom: var(--space-1);
    text-align: center;
}

.minimized-progress-bar {
    width: 40px;
    height: 3px;
    background: var(--primary-100);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.minimized-progress-fill {
    height: 100%;
    background: var(--gradient-academic);
    border-radius: var(--radius-full);
    transition: width 0.3s ease;
}

.minimized-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.minimized-step:hover {
    background: rgba(255, 255, 255, 0.1);
}

.minimized-step-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--gray-300);
    color: var(--gray-700);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-xs);
    margin-bottom: var(--space-1);
    transition: all 0.2s ease;
}

.minimized-step.active .minimized-step-number {
    background: var(--primary-500);
    color: var(--white);
}

.minimized-step.completed .minimized-step-number {
    background: var(--success-500);
    color: var(--white);
}

.minimized-step-time {
    font-size: 10px;
    color: var(--gray-600);
    text-align: center;
    line-height: 1.2;
}

.minimized-step-indicator {
    width: 2px;
    height: var(--space-2);
    background: var(--gray-300);
    margin: var(--space-1) 0;
}

.minimized-step.active .minimized-step-indicator {
    background: var(--primary-500);
}

.minimized-step.completed .minimized-step-indicator {
    background: var(--success-500);
}

.minimized-step:last-child .minimized-step-indicator {
    display: none;
}

/* 最小化控制面板样式 */
.minimized-control-panel {
    display: none;
    flex-direction: column;
    gap: var(--space-2);
    width: 100%;
    padding: var(--space-2) 0;
}

.minimized-control-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.minimized-control-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
}

.minimized-control-btn:hover {
    background: var(--primary-50);
    color: var(--primary-600);
    transform: scale(1.05);
    box-shadow: 0 2px 6px rgba(37, 99, 235, 0.2);
}

.minimized-control-btn.primary {
    background: var(--primary-500);
    color: var(--white);
}

.minimized-control-btn.primary:hover {
    background: var(--primary-600);
    transform: scale(1.05);
}

.minimized-control-btn.success {
    background: var(--success-500);
    color: var(--white);
}

.minimized-control-btn.success:hover {
    background: var(--success-600);
    transform: scale(1.05);
}

.minimized-control-divider {
    width: 30px;
    height: 1px;
    background: var(--gray-300);
    margin: var(--space-1) auto;
}

/* 响应式断点调整 */
@media (max-width: 1200px) and (min-width: 1024px) {
    .left-sidebar {
        width: 22%;
    }

    .right-sidebar {
        width: 22%;
    }
}

@media (max-width: 1024px) {
    .sidebar-controls {
        display: none; /* 在小屏幕上隐藏控制按钮，使用响应式布局 */
    }
}

.content-header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    flex-shrink: 0;
}

.content-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.content-body {
    flex: 1;
    padding: var(--space-6);
    overflow-y: auto;
    position: relative;
}

.step-content {
    display: none;
}

.step-content.active {
    display: block;
}

.step-description {
    margin-bottom: var(--space-6);
}

.step-description p {
    font-size: var(--font-size-lg);
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

/* 右侧边栏 - 控制面板 */
.right-sidebar {
    width: 20%;
    flex-shrink: 0;
    background: var(--gradient-bg);
    border-left: 1px solid var(--gray-200);
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow-y: auto;
    height: 100%;
    position: relative;
    z-index: 10;
    transition: all 0.3s ease;
}

.control-panel {
    padding: var(--space-6) var(--space-5) var(--space-5) var(--space-5); /* 增加顶部内边距避免被按钮遮挡 */
}

.panel-header {
    margin-bottom: var(--space-6);
}

.panel-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin: 0;
}

.panel-title i {
    color: var(--primary-600);
    font-size: 1.2em;
}

.control-section {
    margin-bottom: var(--space-6);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.control-section:last-child {
    margin-bottom: 0;
}

.section-header {
    background: var(--bg-tertiary);
    padding: var(--space-3) var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.section-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-700);
    margin: 0;
}

.section-header i {
    color: var(--gray-600);
    font-size: 1.1em;
}

.control-buttons {
    padding: var(--space-4);
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.group-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-3);
}

.action-btn {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-md);
    border: 2px solid transparent;
    background: var(--bg-primary);
    color: var(--gray-700);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    cursor: pointer;
    transition: all var(--transition-base);
    margin-bottom: var(--space-2);
    text-align: left;
    border: 1px solid var(--gray-300);
}

.action-btn:last-child {
    margin-bottom: 0;
}

.action-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.action-btn.primary {
    background: var(--primary-500);
    color: var(--white);
    border-color: var(--primary-500);
}

.action-btn.primary:hover:not(:disabled) {
    background: var(--primary-600);
    border-color: var(--primary-600);
}

.action-btn.success {
    background: var(--success-500);
    color: var(--white);
    border-color: var(--success-500);
}

.action-btn.success:hover:not(:disabled) {
    background: var(--success-600);
    border-color: var(--success-600);
}

.action-btn.warning {
    background: var(--warning-500);
    color: var(--white);
    border-color: var(--warning-500);
}

.action-btn.warning:hover:not(:disabled) {
    background: var(--warning-600);
    border-color: var(--warning-600);
}

.action-btn.info {
    background: var(--info-500);
    color: var(--white);
    border-color: var(--info-500);
}

.action-btn.info:hover:not(:disabled) {
    background: var(--info-600);
    border-color: var(--info-600);
}

.action-btn.secondary {
    background: var(--gray-500);
    color: var(--white);
    border-color: var(--gray-500);
}

.action-btn.secondary:hover:not(:disabled) {
    background: var(--gray-600);
    border-color: var(--gray-600);
}

.action-btn.outline {
    background: var(--white);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.action-btn.outline:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.action-btn.disabled,
.action-btn:disabled {
    background: var(--gray-200);
    color: var(--gray-400);
    border-color: var(--gray-200);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* ===== 动画效果优化 ===== */

/* 步骤切换动画 */
.step-transition {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 按钮加载状态 */
.action-btn.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.action-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 悬停效果增强 */
.step-item:hover {
    border-color: var(--primary-300);
    background: var(--primary-50);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.action-btn:hover:not(:disabled):not(.loading) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 焦点状态优化 */
.step-item:focus,
.action-btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* 激活状态动画 */
.step-item.active {
    animation: stepActivate 0.3s ease-out;
}

@keyframes stepActivate {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

/* 完成状态动画 */
.step-item.completed .step-number {
    animation: checkmark 0.5s ease-in-out;
}

@keyframes checkmark {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* 内容区域动画 */
.step-content {
    transition: all 0.3s ease-in-out;
}

.step-content.active {
    animation: slideInRight 0.4s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 题目网格动画 */
.question-number {
    transition: all 0.2s ease-in-out;
}

.question-number:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.question-number.selected {
    animation: questionPulse 1s infinite;
}

@keyframes questionPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 8px rgba(0, 123, 255, 0);
    }
}

/* 微交互动画 */
.action-btn:active:not(:disabled) {
    transform: translateY(0) scale(0.98);
}

.step-item:active {
    transform: translateY(0) scale(0.98);
}

/* 渐变背景动画 */
.step-item.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-100), var(--primary-50));
    border-radius: inherit;
    z-index: -1;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background: linear-gradient(135deg, var(--primary-100), var(--primary-50));
    }
    50% {
        background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    }
}

/* ===== 用户反馈和状态指示优化 ===== */

/* 成功状态指示 */
.status-success {
    color: var(--success-600);
    background: var(--success-50);
    border: 1px solid var(--success-200);
    border-radius: var(--radius-md);
    padding: var(--space-2) var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    animation: slideInDown 0.3s ease-out;
}

.status-success::before {
    content: '✓';
    font-weight: bold;
    color: var(--success-600);
}

/* 警告状态指示 */
.status-warning {
    color: var(--warning-600);
    background: var(--warning-50);
    border: 1px solid var(--warning-200);
    border-radius: var(--radius-md);
    padding: var(--space-2) var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    animation: slideInDown 0.3s ease-out;
}

.status-warning::before {
    content: '⚠';
    font-weight: bold;
    color: var(--warning-600);
}

/* 错误状态指示 */
.status-error {
    color: var(--error-600);
    background: var(--error-50);
    border: 1px solid var(--error-200);
    border-radius: var(--radius-md);
    padding: var(--space-2) var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    animation: slideInDown 0.3s ease-out;
}

.status-error::before {
    content: '✕';
    font-weight: bold;
    color: var(--error-600);
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 通用进度条 */
.generic-progress-bar {
    position: relative;
    height: 4px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.generic-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: inherit;
    transition: width 0.3s ease-out;
    width: var(--progress, 0%);
}

/* 加载状态指示 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
    z-index: 10;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--gray-300);
    border-top: 3px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: var(--white);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease-in-out;
    z-index: var(--z-tooltip);
}

.tooltip:hover::after {
    opacity: 1;
}

/* 徽章和标签 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    line-height: 1;
}

.badge.primary {
    background: var(--primary-100);
    color: var(--primary-700);
}

.badge.success {
    background: var(--success-100);
    color: var(--success-700);
}

.badge.warning {
    background: var(--warning-100);
    color: var(--warning-700);
}

.badge.error {
    background: var(--error-100);
    color: var(--error-700);
}

/* 步骤状态徽章 */
.step-item .step-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    opacity: 0;
    transform: scale(0);
    transition: all 0.2s ease-in-out;
}

.step-item.completed .step-badge {
    background: var(--success-500);
    color: var(--white);
    opacity: 1;
    transform: scale(1);
}

.step-item.active .step-badge {
    background: var(--primary-500);
    color: var(--white);
    opacity: 1;
    transform: scale(1);
}

/* 计时器状态指示 */
.timer-status {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.timer-status.running {
    color: var(--success-600);
}

.timer-status.paused {
    color: var(--warning-600);
}

.timer-status.stopped {
    color: var(--gray-600);
}

.timer-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

.timer-status.paused::before,
.timer-status.stopped::before {
    animation: none;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* ===== 高级视觉效果和微交互 ===== */

/* 玻璃态效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* 渐变边框效果 */
.gradient-border {
    position: relative;
    background: var(--white);
    border-radius: var(--radius-lg);
}

.gradient-border::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: var(--gradient-primary);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
}

/* 悬浮卡片效果 */
.floating-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
}

.floating-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 脉冲动画增强 */
.pulse-enhanced {
    animation: pulseEnhanced 2s infinite;
}

@keyframes pulseEnhanced {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(79, 70, 229, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
    }
}

/* 打字机效果 */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--primary-500);
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--primary-500); }
}

/* 波纹效果 */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* 磁性吸附效果 */
.magnetic {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magnetic:hover {
    transform: scale(1.02);
}

/* 彩虹边框动画 */
.rainbow-border {
    position: relative;
    background: var(--white);
    border-radius: var(--radius-lg);
}

.rainbow-border::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #ff0000, #ff7300, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000);
    background-size: 400%;
    border-radius: inherit;
    z-index: -1;
    animation: rainbow 3s linear infinite;
}

@keyframes rainbow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 粒子效果背景 */
.particle-bg {
    position: relative;
    overflow: hidden;
}

.particle-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
    animation: particleFloat 6s ease-in-out infinite;
    z-index: -1;
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(120deg); }
    66% { transform: translateY(5px) rotate(240deg); }
}

/* 霓虹灯效果 */
.neon-glow {
    color: var(--primary-500);
    text-shadow:
        0 0 5px var(--primary-500),
        0 0 10px var(--primary-500),
        0 0 15px var(--primary-500),
        0 0 20px var(--primary-500);
    animation: neonFlicker 2s infinite alternate;
}

@keyframes neonFlicker {
    0%, 100% {
        text-shadow:
            0 0 5px var(--primary-500),
            0 0 10px var(--primary-500),
            0 0 15px var(--primary-500),
            0 0 20px var(--primary-500);
    }
    50% {
        text-shadow:
            0 0 2px var(--primary-500),
            0 0 5px var(--primary-500),
            0 0 8px var(--primary-500),
            0 0 12px var(--primary-500);
    }
}

/* 3D翻转效果 */
.flip-card {
    perspective: 1000px;
    width: 100%;
    height: 200px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
}

.flip-card-back {
    transform: rotateY(180deg);
    background: var(--gradient-primary);
    color: var(--white);
}

/* 滚动指示器 */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gray-200);
    z-index: var(--z-fixed);
}

.scroll-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: var(--gradient-primary);
    width: var(--scroll-progress, 0%);
    transition: width 0.1s ease-out;
}

/* 呼吸灯效果 */
.breathing {
    animation: breathing 3s ease-in-out infinite;
}

@keyframes breathing {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* ===== 响应式设计优化 ===== */

/* 超大屏幕优化 (1920px+) */
@media (min-width: 1920px) {
    .modern-container {
        max-width: 1600px;
        padding: var(--space-8);
    }

    .modern-page-header {
        padding: var(--space-6) var(--space-8);
    }

    .modern-main-layout {
        gap: var(--space-8);
    }

    .modern-card {
        padding: var(--space-8);
    }

    .modern-card-header {
        padding: var(--space-6) var(--space-8);
    }

    .modern-card-body {
        padding: var(--space-8);
    }

    .question-numbers-grid {
        grid-template-columns: repeat(12, 1fr);
        gap: var(--space-4);
    }

    .question-number {
        width: 55px;
        height: 55px;
        font-size: var(--font-size-lg);
    }
}

/* 大屏幕优化 (1366px - 1919px) */
@media (min-width: 1366px) and (max-width: 1919px) {
    .modern-container {
        max-width: 1200px;
        padding: var(--space-6);
    }

    .modern-page-header {
        padding: var(--space-5) var(--space-6);
    }

    .modern-main-layout {
        gap: var(--space-6);
    }

    .question-numbers-grid {
        grid-template-columns: repeat(10, 1fr);
        gap: var(--space-3);
    }

    .question-number {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-base);
    }
}

/* 三栏布局响应式设计 */

/* 大屏幕优化三栏布局 */
@media (min-width: 1366px) {
    .three-column-layout {
        gap: var(--space-8);
    }

    .left-sidebar {
        width: 320px;
    }

    .right-sidebar {
        width: 340px;
    }
}

/* 中等屏幕优化 (1024px - 1365px) */
@media (min-width: 1024px) and (max-width: 1365px) {
    .three-column-layout {
        gap: var(--space-4);
        height: calc(100vh - 180px);
    }

    .left-sidebar {
        width: 240px;
    }

    .right-sidebar {
        width: 260px;
    }

    .process-steps {
        padding: var(--space-4);
    }

    .step-item {
        padding: var(--space-3);
        gap: var(--space-2);
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-base);
    }

    .step-title {
        font-size: var(--font-size-sm);
    }

    .step-duration {
        font-size: var(--font-size-xs);
    }

    .content-header {
        padding: var(--space-4) var(--space-5);
    }

    .content-title {
        font-size: var(--font-size-lg);
    }

    .content-body {
        padding: var(--space-5);
    }

    .action-buttons {
        padding: var(--space-4);
    }

    .action-btn {
        padding: var(--space-2) var(--space-3);
        font-size: var(--font-size-xs);
    }

    .modern-container {
        max-width: 1000px;
        padding: var(--space-4);
        min-height: calc(100vh - var(--space-4));
    }

    .modern-page-header {
        padding: var(--space-4) var(--space-4);
    }

    .modern-page-header h1 {
        font-size: var(--font-size-2xl);
    }

    .modern-main-layout {
        gap: var(--space-4);
        padding: var(--space-4);
    }

    .modern-header-section {
        padding: var(--space-3) 0;
        gap: var(--space-4);
    }

    .modern-stepper-area {
        padding: var(--space-3) 0;
    }

    .modern-step-indicators {
        gap: var(--space-2);
    }

    .modern-step-indicator {
        padding: var(--space-2) var(--space-3);
    }

    .modern-step-number {
        width: 28px;
        height: 28px;
        font-size: var(--font-size-sm);
    }

    .modern-step-title {
        font-size: var(--font-size-sm);
    }

    .modern-content-area {
        padding: var(--space-4) 0;
    }

    .modern-card {
        margin-bottom: var(--space-4);
    }

    .modern-card-header {
        padding: var(--space-4) var(--space-5);
    }

    .modern-card-header h5 {
        font-size: var(--font-size-lg);
    }

    .modern-card-body {
        padding: var(--space-5);
    }

    .modern-btn {
        padding: var(--space-2) var(--space-4);
        font-size: var(--font-size-sm);
    }

    .question-numbers-grid {
        grid-template-columns: repeat(10, 1fr);
        gap: var(--space-2);
        padding: var(--space-4);
    }

    .question-number {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-sm);
    }

    .modern-question-placeholder {
        padding: var(--space-8) var(--space-4);
    }

    .modern-question-placeholder-icon {
        font-size: 2.5rem;
        margin-bottom: var(--space-3);
    }

    .modern-question-placeholder-text {
        font-size: var(--font-size-lg);
        margin-bottom: var(--space-2);
    }

    .modern-question-placeholder-hint {
        font-size: var(--font-size-sm);
    }

    .draggable-timer {
        width: 200px;
    }

    .timer-value {
        font-size: var(--font-size-xl);
    }

    .step-name {
        font-size: var(--font-size-sm);
    }
}

/* 平板屏幕优化 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .three-column-layout {
        flex-direction: column;
        gap: var(--space-3);
        height: auto;
        padding: var(--space-4);
    }

    .left-sidebar {
        width: 100%;
        order: 1;
        border-right: none;
        border-bottom: 1px solid var(--gray-200);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }

    .center-content {
        width: 100%;
        order: 2;
        border-radius: 0;
    }

    .right-sidebar {
        width: 100%;
        order: 3;
        border-left: none;
        border-top: 1px solid var(--gray-200);
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    }

    /* 平板下的流程节点优化 */
    .step-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-3);
    }

    .step-item {
        padding: var(--space-3);
    }

    .step-item::before {
        display: none;
    }

    /* 平板下的控制面板优化 */
    .control-panel {
        padding: var(--space-4);
    }

    .control-section {
        margin-bottom: var(--space-4);
    }

    .control-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: var(--space-2);
        padding: var(--space-3);
    }

    .navigation-buttons {
        grid-template-columns: 1fr 1fr;
    }

    .left-sidebar {
        width: 100%;
        order: 1;
    }

    .center-content {
        order: 2;
        min-height: 400px;
    }

    .right-sidebar {
        width: 100%;
        order: 3;
    }

    .process-steps {
        padding: var(--space-3);
    }

    .step-list {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--space-2);
    }

    .step-item {
        flex: 1;
        min-width: 150px;
        padding: var(--space-2);
        gap: var(--space-2);
    }

    .step-number {
        width: 30px;
        height: 30px;
        font-size: var(--font-size-sm);
    }

    .step-title {
        font-size: var(--font-size-xs);
    }

    .step-duration {
        font-size: var(--font-size-xs);
    }

    .action-buttons {
        padding: var(--space-3);
    }

    .button-group {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-2);
        margin-bottom: var(--space-3);
    }

    .action-btn {
        flex: 1;
        min-width: 120px;
        margin-bottom: 0;
    }

    .modern-container {
        max-width: 100%;
        padding: var(--space-3);
        margin: 0 var(--space-2);
        min-height: calc(100vh - var(--space-4));
    }

    .modern-page-header {
        flex-direction: column;
        gap: var(--space-3);
        padding: var(--space-3);
        text-align: center;
    }

    .modern-page-header h1 {
        font-size: var(--font-size-xl);
    }

    .modern-main-layout {
        gap: var(--space-3);
        padding: var(--space-3);
    }

    .modern-header-section {
        flex-direction: column;
        gap: var(--space-3);
        padding: var(--space-2) 0;
        text-align: center;
    }

    .modern-header-left,
    .modern-header-right {
        justify-content: center;
    }

    .modern-stepper-area {
        padding: var(--space-2) 0;
    }

    .modern-step-indicators {
        flex-wrap: wrap;
        gap: var(--space-2);
        justify-content: center;
    }

    .modern-step-indicator {
        padding: var(--space-2);
        min-width: 120px;
    }

    .modern-step-number {
        width: 26px;
        height: 26px;
        font-size: var(--font-size-sm);
    }

    .modern-step-title {
        font-size: var(--font-size-xs);
    }

    .modern-content-area {
        padding: var(--space-3) 0;
    }

    .modern-card-header {
        padding: var(--space-3) var(--space-4);
    }

    .modern-card-header h5 {
        font-size: var(--font-size-base);
        text-align: center;
    }

    .modern-card-body {
        padding: var(--space-4);
    }

    .modern-btn {
        padding: var(--space-2) var(--space-3);
        font-size: var(--font-size-sm);
        width: 100%;
        margin-bottom: var(--space-2);
    }

    .question-numbers-grid {
        grid-template-columns: repeat(8, 1fr);
        gap: var(--space-2);
        padding: var(--space-3);
    }

    .question-number {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-sm);
    }

    .modern-question-grid-container {
        margin: var(--space-3) 0;
    }

    .modern-question-placeholder {
        padding: var(--space-6) var(--space-3);
    }

    .modern-question-placeholder-icon {
        font-size: 2rem;
        margin-bottom: var(--space-2);
    }

    .modern-question-placeholder-text {
        font-size: var(--font-size-base);
        margin-bottom: var(--space-2);
    }

    .modern-question-placeholder-hint {
        font-size: var(--font-size-sm);
    }

    .draggable-timer {
        width: 180px;
        position: fixed;
        top: var(--space-4);
        right: var(--space-4);
    }

    .timer-value {
        font-size: var(--font-size-lg);
    }

    .step-name {
        font-size: var(--font-size-xs);
    }

    /* 按钮组响应式 */
    .row .col-md-6 {
        margin-bottom: var(--space-2);
    }
}

/* 手机屏幕优化 (最大767px) */
@media (max-width: 767px) {
    .three-column-layout {
        flex-direction: column;
        gap: var(--space-2);
        height: auto;
        padding: var(--space-2);
    }

    /* 手机下的侧边栏布局 */
    .left-sidebar,
    .right-sidebar {
        width: 100%;
        border: none;
        border-radius: var(--radius-md);
        margin-bottom: var(--space-2);
    }

    .center-content {
        width: 100%;
        border-radius: var(--radius-md);
        margin-bottom: var(--space-2);
    }

    /* 手机下的流程节点优化 */
    .process-steps {
        padding: var(--space-3);
    }

    .sidebar-header {
        margin-bottom: var(--space-3);
    }

    .progress-indicator {
        padding: var(--space-2);
    }

    .step-list {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
    }

    .step-item {
        padding: var(--space-3);
        flex-direction: row;
        align-items: center;
    }

    .step-item::before {
        display: none;
    }

    .step-indicator {
        margin-right: var(--space-3);
    }

    .step-number {
        width: 32px;
        height: 32px;
        font-size: var(--font-size-sm);
    }

    .step-content {
        flex: 1;
    }

    .step-title {
        font-size: var(--font-size-sm);
        margin-bottom: var(--space-1);
    }

    .step-duration {
        font-size: var(--font-size-xs);
    }

    .step-description {
        display: none; /* 手机上隐藏描述文字 */
    }

    /* 手机下的控制面板优化 */
    .control-panel {
        padding: var(--space-3);
    }

    .panel-header {
        margin-bottom: var(--space-3);
    }

    .panel-title {
        font-size: var(--font-size-base);
    }

    .control-section {
        margin-bottom: var(--space-3);
    }

    .section-header {
        padding: var(--space-2) var(--space-3);
    }

    .section-title {
        font-size: var(--font-size-xs);
    }

    .control-buttons {
        padding: var(--space-2);
        gap: var(--space-2);
    }

    .control-btn {
        padding: var(--space-2) var(--space-3);
        font-size: var(--font-size-xs);
    }

    .control-btn kbd {
        display: none; /* 手机上隐藏快捷键提示 */
    }

    .navigation-buttons {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-2);
    }

    /* 手机下的中间内容优化 */
    .content-header {
        padding: var(--space-3);
    }

    .content-title {
        font-size: var(--font-size-base);
    }

    .content-body {
        padding: var(--space-3);
    }

    /* 手机下的顶部导航优化 */
    .modern-page-header {
        padding: var(--space-3) var(--space-4);
    }

    .modern-header-content h1 {
        font-size: var(--font-size-lg);
    }

    .modern-student-info {
        font-size: var(--font-size-sm);
    }

    /* 手机下的状态栏优化 */
    .modern-status-bar {
        padding: var(--space-2) var(--space-4);
        flex-wrap: wrap;
        gap: var(--space-2);
    }

    .modern-status-item {
        flex: 1;
        min-width: 120px;
    }

    .modern-timer-display {
        font-size: var(--font-size-lg);
    }
}

/* 超小屏幕优化 (最大480px) */
@media (max-width: 480px) {
    .three-column-layout {
        padding: var(--space-1);
        gap: var(--space-1);
    }

    /* 超小屏幕下的流程节点 */
    .process-steps {
        padding: var(--space-2);
    }

    .sidebar-title {
        font-size: var(--font-size-sm);
    }

    .step-list {
        gap: var(--space-1);
    }

    .step-item {
        padding: var(--space-2);
    }

    .step-number {
        width: 28px;
        height: 28px;
        font-size: var(--font-size-xs);
    }

    .step-title {
        font-size: var(--font-size-xs);
    }

    .step-duration {
        font-size: 10px;
    }

    /* 超小屏幕下的控制面板 */
    .control-panel {
        padding: var(--space-2);
    }

    .panel-title {
        font-size: var(--font-size-sm);
    }

    .control-section {
        margin-bottom: var(--space-2);
    }

    .control-buttons {
        padding: var(--space-1);
        gap: var(--space-1);
    }

    .control-btn {
        padding: var(--space-1) var(--space-2);
        font-size: 10px;
    }

    .navigation-buttons {
        gap: var(--space-1);
    }

    /* 超小屏幕下的顶部优化 */
    .modern-page-header {
        padding: var(--space-2) var(--space-3);
    }

    .modern-header-content h1 {
        font-size: var(--font-size-base);
    }

    .modern-student-info {
        font-size: var(--font-size-xs);
    }

    /* 超小屏幕下的状态栏 */
    .modern-status-bar {
        padding: var(--space-1) var(--space-3);
        flex-direction: column;
        align-items: stretch;
    }

    .modern-status-item {
        min-width: auto;
        text-align: center;
    }

    .modern-timer-display {
        font-size: var(--font-size-base);
    }
}

/* 横屏模式优化 */
@media (max-width: 1023px) and (orientation: landscape) {
    .three-column-layout {
        flex-direction: row;
        height: 100vh;
        padding: var(--space-2);
        gap: var(--space-2);
    }

    .left-sidebar {
        width: 280px;
        order: 1;
        border-right: 1px solid var(--gray-200);
        border-bottom: none;
        border-radius: var(--radius-lg) 0 0 var(--radius-lg);
    }

    .center-content {
        flex: 1;
        order: 2;
        border-radius: 0;
    }

    .right-sidebar {
        width: 300px;
        order: 3;
        border-left: 1px solid var(--gray-200);
        border-top: none;
        border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    }

    /* 横屏下的流程节点优化 */
    .step-list {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
    }

    .step-item {
        padding: var(--space-2);
    }

    .step-item::before {
        width: 3px;
    }

    /* 横屏下的控制面板优化 */
    .control-panel {
        padding: var(--space-3);
    }

    .control-section {
        margin-bottom: var(--space-3);
    }

    .control-buttons {
        padding: var(--space-2);
        gap: var(--space-2);
    }

    .control-btn {
        padding: var(--space-2) var(--space-3);
        font-size: var(--font-size-xs);
    }
}

/* 打印样式优化 */
@media print {
    .three-column-layout {
        flex-direction: column;
        gap: var(--space-4);
        background: white;
    }

    .left-sidebar,
    .right-sidebar {
        width: 100%;
        border: 1px solid #000;
        border-radius: 0;
        box-shadow: none;
        page-break-inside: avoid;
    }

    .center-content {
        width: 100%;
        border: 1px solid #000;
        border-radius: 0;
        page-break-inside: avoid;
    }

    .control-btn,
    .action-btn {
        display: none; /* 打印时隐藏按钮 */
    }

    .modern-page-header,
    .modern-status-bar {
        border-bottom: 2px solid #000;
    }

    .page-footer {
        border-top: 1px solid #000;
        background: white;
    }
}

    .left-sidebar {
        width: 100%;
        order: 1;
    }

    .center-content {
        order: 2;
        min-height: 300px;
    }

    .right-sidebar {
        width: 100%;
        order: 3;
    }

    .process-steps {
        padding: var(--space-2);
    }

    .step-list {
        flex-direction: column;
        gap: var(--space-1);
    }

    .step-item {
        padding: var(--space-2);
        gap: var(--space-2);
    }

    .step-number {
        width: 28px;
        height: 28px;
        font-size: var(--font-size-sm);
    }

    .step-title {
        font-size: var(--font-size-sm);
    }

    .step-duration {
        font-size: var(--font-size-xs);
    }

    .content-header {
        padding: var(--space-3);
    }

    .content-title {
        font-size: var(--font-size-base);
    }

    .content-body {
        padding: var(--space-3);
    }

    .action-buttons {
        padding: var(--space-2);
    }

    .button-group {
        margin-bottom: var(--space-2);
    }

    .action-btn {
        padding: var(--space-2);
        font-size: var(--font-size-xs);
        margin-bottom: var(--space-1);
    }

    .modern-container {
        max-width: 100%;
        padding: var(--space-2);
        margin: 0 var(--space-1);
        min-height: calc(100vh - var(--space-2));
        border-radius: var(--radius-md);
    }

    .modern-page-header {
        flex-direction: column;
        gap: var(--space-2);
        padding: var(--space-2);
        text-align: center;
    }

    .modern-page-header h1 {
        font-size: var(--font-size-lg);
        line-height: 1.2;
    }

    .modern-main-layout {
        gap: var(--space-2);
        padding: var(--space-2);
    }

    .modern-header-section {
        flex-direction: column;
        gap: var(--space-2);
        padding: var(--space-1) 0;
        text-align: center;
    }

    .modern-header-decoration {
        font-size: var(--font-size-sm);
        padding: var(--space-1) var(--space-2);
    }

    .modern-stepper-area {
        padding: var(--space-1) 0;
    }

    .modern-step-indicators {
        flex-direction: column;
        gap: var(--space-1);
        align-items: center;
    }

    .modern-step-indicator {
        padding: var(--space-1) var(--space-2);
        min-width: 100px;
        width: 100%;
        max-width: 200px;
    }

    .modern-step-number {
        width: 24px;
        height: 24px;
        font-size: var(--font-size-xs);
    }

    .modern-step-title {
        font-size: var(--font-size-xs);
        text-align: center;
    }

    .modern-content-area {
        padding: var(--space-2) 0;
    }

    .modern-card {
        margin-bottom: var(--space-2);
        border-radius: var(--radius-md);
    }

    .modern-card-header {
        padding: var(--space-2) var(--space-3);
    }

    .modern-card-header h5 {
        font-size: var(--font-size-sm);
        text-align: center;
        line-height: 1.3;
    }

    .modern-card-body {
        padding: var(--space-3);
    }

    .modern-card-body p {
        font-size: var(--font-size-sm);
        line-height: 1.4;
        text-align: center;
    }

    .modern-btn {
        padding: var(--space-1) var(--space-2);
        font-size: var(--font-size-xs);
        width: 100%;
        margin-bottom: var(--space-2);
        border-radius: var(--radius-sm);
    }

    .question-numbers-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: var(--space-1);
        padding: var(--space-2);
    }

    .question-number {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-xs);
        border-radius: var(--radius-sm);
    }

    .modern-question-grid-container {
        margin: var(--space-2) 0;
        padding: var(--space-2);
        border-radius: var(--radius-md);
    }

    .modern-question-grid-header {
        flex-direction: column;
        align-items: center;
        gap: var(--space-2);
        text-align: center;
    }

    .modern-question-grid-title {
        font-size: var(--font-size-sm);
    }

    .modern-question-grid-legend {
        gap: var(--space-2);
        flex-wrap: wrap;
        justify-content: center;
    }

    .legend-item {
        font-size: var(--font-size-xs);
    }

    .modern-question-grid-stats {
        flex-direction: column;
        gap: var(--space-1);
        text-align: center;
        font-size: var(--font-size-xs);
    }

    .modern-question-placeholder {
        padding: var(--space-4) var(--space-2);
    }

    .modern-question-placeholder-icon {
        font-size: 1.5rem;
        margin-bottom: var(--space-2);
    }

    .modern-question-placeholder-text {
        font-size: var(--font-size-sm);
        margin-bottom: var(--space-1);
    }

    .modern-question-placeholder-hint {
        font-size: var(--font-size-xs);
    }

    .draggable-timer {
        width: 150px;
        position: fixed;
        top: var(--space-2);
        right: var(--space-2);
        z-index: var(--z-fixed);
    }

    .timer-drag-handle {
        padding: var(--space-1) var(--space-2);
        font-size: var(--font-size-xs);
    }

    .timer-value {
        font-size: var(--font-size-base);
    }

    .step-name {
        font-size: var(--font-size-xs);
        text-align: center;
    }

    /* 按钮组完全垂直布局 */
    .row {
        flex-direction: column;
    }

    .row .col-md-6 {
        width: 100%;
        margin-bottom: var(--space-2);
    }

    /* 底部按钮区域 */
    .modern-footer-buttons {
        flex-direction: column;
        gap: var(--space-2);
        padding: var(--space-3);
    }

    .modern-footer-buttons .modern-btn {
        width: 100%;
    }

    /* 模态框响应式 */
    .modal-dialog {
        margin: var(--space-2);
        max-width: calc(100vw - var(--space-4));
    }

    .modal-content {
        border-radius: var(--radius-md);
    }

    .modal-header {
        padding: var(--space-2) var(--space-3);
    }

    .modal-body {
        padding: var(--space-3);
    }

    .modal-footer {
        padding: var(--space-2) var(--space-3);
        flex-direction: column;
        gap: var(--space-2);
    }

    .modal-footer .btn {
        width: 100%;
        margin: 0;
    }
}

/* ===== 新的控制按钮样式 ===== */
.control-btn {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: left;
    position: relative;
    overflow: hidden;
    /* 默认样式 - 确保可读性 */
    background: var(--bg-primary);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

/* 移除有问题的滑动效果，改用简单的背景变化 */
.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.control-btn span {
    flex: 1;
    margin: 0 var(--space-2);
}

.control-btn kbd {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-sm);
    padding: 2px 6px;
    font-size: 10px;
    font-family: monospace;
    color: inherit;
    opacity: 0.8;
}

.control-btn.primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--white);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.control-btn.primary:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.control-btn.secondary {
    background: linear-gradient(135deg, var(--gray-500), var(--gray-600));
    color: var(--white);
    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.2);
}

.control-btn.secondary:hover {
    background: linear-gradient(135deg, var(--gray-600), var(--gray-700));
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(107, 114, 128, 0.4);
}

.control-btn.success {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
    color: var(--white);
    box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
}

.control-btn.success:hover {
    background: linear-gradient(135deg, var(--success-600), var(--success-700));
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(34, 197, 94, 0.4);
}

.control-btn.warning {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
    color: var(--white);
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
}

.control-btn.warning:hover {
    background: linear-gradient(135deg, var(--warning-600), var(--warning-700));
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
}

.control-btn.info {
    background: linear-gradient(135deg, var(--info-500), var(--info-600));
    color: var(--white);
    box-shadow: 0 2px 4px rgba(14, 165, 233, 0.2);
}

.control-btn.info:hover {
    background: linear-gradient(135deg, var(--info-600), var(--info-700));
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4);
}

.control-btn.outline {
    background: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.control-btn.outline:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.control-btn.disabled,
.control-btn:disabled {
    background: var(--gray-200);
    color: var(--gray-500);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.control-btn:disabled::before {
    display: none;
}

.navigation-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3);
}

.navigation-buttons .control-btn {
    justify-content: center;
}

.navigation-buttons .control-btn span {
    margin: 0;
    flex: none;
}

/* kbd组件已删除，不再需要相关样式 */

/* 按功能组重新设计按钮样式，参考settings-btn */

/* 题目操作组 - 琥珀色系 */
#draw-question-btn.control-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: var(--white);
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

#draw-question-btn.control-btn:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
}

/* 系统操作组 - 考试记录使用蓝色系 */
#exam-records-btn.control-btn {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: var(--white);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

#exam-records-btn.control-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

/* 系统操作组 - 下一名考生使用靛蓝色系 */
#next-student-btn.control-btn {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    color: var(--white);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

#next-student-btn.control-btn:hover {
    background: linear-gradient(135deg, #4f46e5, #4338ca);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
}

/* ===== 新的页面底部样式 ===== */
.page-footer {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-top: 1px solid var(--gray-200);
    padding: var(--space-4) 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.copyright-info {
    text-align: center;
}

.copyright-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--space-2);
}

.separator {
    color: var(--gray-400);
}

.system-info {
    color: var(--gray-500);
    font-size: var(--font-size-xs);
}
