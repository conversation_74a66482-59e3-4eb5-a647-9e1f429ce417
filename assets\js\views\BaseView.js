/**
 * BaseView - 视图基类
 * 提供所有视图组件的通用功能和接口
 */

class BaseView {
    constructor(container) {
        this.container = container;
        this.elements = {};
        this.eventListeners = new Map();
        this.data = {};
        this.isInitialized = false;
        this.isVisible = false;
        this.eventBus = null;
        
        // 模板和样式
        this.template = '';
        this.styles = '';
        
        // 响应式配置
        this.responsive = {
            breakpoints: {
                mobile: 768,
                tablet: 992,
                desktop: 1200
            },
            currentBreakpoint: 'desktop'
        };
    }

    /**
     * 初始化视图
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }

        try {
            // 渲染模板
            await this.render();
            
            // 缓存DOM元素
            this.cacheElements();
            
            // 绑定事件
            this.bindEvents();
            
            // 设置响应式
            this.setupResponsive();
            
            // 子类初始化
            await this.onInitialize();
            
            this.isInitialized = true;
            this.emit('initialized', this);
            
        } catch (error) {
            console.error(`${this.constructor.name} 初始化失败:`, error);
            throw error;
        }
    }

    /**
     * 子类重写此方法进行具体初始化
     * @returns {Promise<void>}
     */
    async onInitialize() {
        // 子类实现
    }

    /**
     * 设置事件总线
     * @param {EventBus} eventBus - 事件总线实例
     */
    setEventBus(eventBus) {
        this.eventBus = eventBus;
    }

    /**
     * 渲染视图
     * @param {Object} data - 渲染数据
     * @returns {Promise<void>}
     */
    async render(data = null) {
        if (data) {
            this.data = { ...this.data, ...data };
        }

        // 生成HTML内容
        const html = await this.generateHTML();
        
        // 更新容器内容
        if (this.container) {
            this.container.innerHTML = html;
        }

        // 应用样式
        this.applyStyles();

        // 触发渲染完成事件
        this.emit('rendered', { data: this.data });
    }

    /**
     * 生成HTML内容
     * @returns {Promise<string>} HTML字符串
     */
    async generateHTML() {
        if (this.template) {
            return this.processTemplate(this.template, this.data);
        }
        return this.getDefaultTemplate();
    }

    /**
     * 获取默认模板
     * @returns {string} 默认HTML模板
     */
    getDefaultTemplate() {
        return '<div class="base-view">BaseView</div>';
    }

    /**
     * 处理模板
     * @param {string} template - 模板字符串
     * @param {Object} data - 数据对象
     * @returns {string} 处理后的HTML
     */
    processTemplate(template, data) {
        return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            return data[key] !== undefined ? data[key] : '';
        });
    }

    /**
     * 应用样式
     */
    applyStyles() {
        if (this.styles && !document.getElementById(`${this.constructor.name}-styles`)) {
            const styleElement = document.createElement('style');
            styleElement.id = `${this.constructor.name}-styles`;
            styleElement.textContent = this.styles;
            document.head.appendChild(styleElement);
        }
    }

    /**
     * 缓存DOM元素
     */
    cacheElements() {
        if (!this.container) return;

        // 缓存带有data-element属性的元素
        const elements = this.container.querySelectorAll('[data-element]');
        elements.forEach(element => {
            const name = element.getAttribute('data-element');
            this.elements[name] = element;
        });

        // 子类可以重写此方法添加更多元素缓存
        this.onCacheElements();
    }

    /**
     * 子类重写此方法缓存特定元素
     */
    onCacheElements() {
        // 子类实现
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        if (!this.container) return;

        // 绑定点击事件
        this.container.addEventListener('click', (event) => {
            this.handleClick(event);
        });

        // 绑定输入事件
        this.container.addEventListener('input', (event) => {
            this.handleInput(event);
        });

        // 绑定变更事件
        this.container.addEventListener('change', (event) => {
            this.handleChange(event);
        });

        // 子类可以重写此方法添加更多事件绑定
        this.onBindEvents();
    }

    /**
     * 子类重写此方法绑定特定事件
     */
    onBindEvents() {
        // 子类实现
    }

    /**
     * 处理点击事件
     * @param {Event} event - 点击事件
     */
    handleClick(event) {
        const target = event.target;
        const action = target.getAttribute('data-action');
        
        if (action) {
            event.preventDefault();
            this.handleAction(action, target, event);
        }
    }

    /**
     * 处理输入事件
     * @param {Event} event - 输入事件
     */
    handleInput(event) {
        const target = event.target;
        const field = target.getAttribute('data-field');
        
        if (field) {
            this.updateData(field, target.value);
        }
    }

    /**
     * 处理变更事件
     * @param {Event} event - 变更事件
     */
    handleChange(event) {
        const target = event.target;
        const field = target.getAttribute('data-field');
        
        if (field) {
            this.updateData(field, target.value);
        }
    }

    /**
     * 处理动作
     * @param {string} action - 动作名称
     * @param {Element} target - 目标元素
     * @param {Event} event - 原始事件
     */
    handleAction(action, target, event) {
        // 触发动作事件
        this.emit('action', {
            action,
            target,
            event,
            data: this.getActionData(target)
        });

        // 调用对应的处理方法
        const methodName = `handle${this.capitalize(action)}Action`;
        if (typeof this[methodName] === 'function') {
            this[methodName](target, event);
        }
    }

    /**
     * 获取动作数据
     * @param {Element} target - 目标元素
     * @returns {Object} 动作数据
     */
    getActionData(target) {
        const data = {};
        
        // 获取data-*属性
        Array.from(target.attributes).forEach(attr => {
            if (attr.name.startsWith('data-') && attr.name !== 'data-action') {
                const key = attr.name.replace('data-', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                data[key] = attr.value;
            }
        });
        
        return data;
    }

    /**
     * 更新数据
     * @param {string} field - 字段名
     * @param {any} value - 字段值
     */
    updateData(field, value) {
        this.data[field] = value;
        this.emit('dataChanged', { field, value, data: this.data });
    }

    /**
     * 更新元素内容
     * @param {string} elementName - 元素名称
     * @param {string} content - 内容
     */
    updateElement(elementName, content) {
        const element = this.elements[elementName];
        if (element) {
            element.textContent = content;
        }
    }

    /**
     * 更新元素HTML
     * @param {string} elementName - 元素名称
     * @param {string} html - HTML内容
     */
    updateElementHTML(elementName, html) {
        const element = this.elements[elementName];
        if (element) {
            element.innerHTML = html;
        }
    }

    /**
     * 显示视图
     * @param {Object} data - 显示数据
     * @returns {Promise<void>}
     */
    async show(data = null) {
        if (data) {
            await this.render(data);
        }

        if (this.container) {
            this.container.style.display = '';
            this.container.classList.remove('hidden');
        }

        this.isVisible = true;
        this.emit('shown', this);
    }

    /**
     * 隐藏视图
     */
    hide() {
        if (this.container) {
            this.container.style.display = 'none';
            this.container.classList.add('hidden');
        }

        this.isVisible = false;
        this.emit('hidden', this);
    }

    /**
     * 切换显示状态
     * @param {Object} data - 数据
     * @returns {Promise<void>}
     */
    async toggle(data = null) {
        if (this.isVisible) {
            this.hide();
        } else {
            await this.show(data);
        }
    }

    /**
     * 设置响应式
     */
    setupResponsive() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // 初始化响应式状态
        this.handleResize();
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        const width = window.innerWidth;
        let newBreakpoint = 'desktop';

        if (width <= this.responsive.breakpoints.mobile) {
            newBreakpoint = 'mobile';
        } else if (width <= this.responsive.breakpoints.tablet) {
            newBreakpoint = 'tablet';
        }

        if (newBreakpoint !== this.responsive.currentBreakpoint) {
            const oldBreakpoint = this.responsive.currentBreakpoint;
            this.responsive.currentBreakpoint = newBreakpoint;
            
            this.onBreakpointChange(newBreakpoint, oldBreakpoint);
            this.emit('breakpointChanged', { 
                current: newBreakpoint, 
                previous: oldBreakpoint 
            });
        }
    }

    /**
     * 处理断点变化
     * @param {string} newBreakpoint - 新断点
     * @param {string} oldBreakpoint - 旧断点
     */
    onBreakpointChange(newBreakpoint, oldBreakpoint) {
        // 更新容器类名
        if (this.container) {
            this.container.classList.remove(`breakpoint-${oldBreakpoint}`);
            this.container.classList.add(`breakpoint-${newBreakpoint}`);
        }
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     */
    emit(event, data) {
        // 触发本地监听器
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`视图事件处理器错误 (${event}):`, error);
                }
            });
        }

        // 触发全局事件总线
        if (this.eventBus) {
            this.eventBus.emit(`UI.${event}`, data);
        }
    }

    /**
     * 工具方法：首字母大写
     * @param {string} str - 字符串
     * @returns {string} 首字母大写的字符串
     */
    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    /**
     * 工具方法：防抖
     * @param {Function} func - 函数
     * @param {number} wait - 等待时间
     * @returns {Function} 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 工具方法：节流
     * @param {Function} func - 函数
     * @param {number} limit - 限制时间
     * @returns {Function} 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 销毁视图
     */
    destroy() {
        try {
            // 移除事件监听器
            this.eventListeners.clear();
            
            // 清空容器
            if (this.container) {
                this.container.innerHTML = '';
            }
            
            // 子类清理
            this.onDestroy();
            
            // 重置状态
            this.isInitialized = false;
            this.isVisible = false;
            this.data = {};
            this.elements = {};
            
            this.emit('destroyed', this);
            
        } catch (error) {
            console.error(`${this.constructor.name} 销毁失败:`, error);
        }
    }

    /**
     * 子类重写此方法进行清理工作
     */
    onDestroy() {
        // 子类实现
    }

    /**
     * 获取视图信息
     * @returns {Object} 视图信息
     */
    getInfo() {
        return {
            name: this.constructor.name,
            isInitialized: this.isInitialized,
            isVisible: this.isVisible,
            currentBreakpoint: this.responsive.currentBreakpoint,
            elementCount: Object.keys(this.elements).length,
            eventListenerCount: Array.from(this.eventListeners.values())
                .reduce((total, listeners) => total + listeners.length, 0)
        };
    }
}

// 导出BaseView类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BaseView;
} else {
    window.BaseView = BaseView;
}
